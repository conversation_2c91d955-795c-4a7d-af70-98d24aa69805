#ifdef ENABLE_VIDEO
#ifndef P_FrameManager_h
#define P_FrameManager_h

#include <memory>
#include <list>
#include <mutex>

namespace panocom
{
    class Frame;
    class VideoFrameManager
    {
    public:
        enum VideoBufferManagerType
        {
            I420_MANAGER,
        };
        static std::shared_ptr<VideoFrameManager> CreateVideoBufferManager(VideoBufferManagerType type, uint32_t maxFrames, uint32_t width, uint32_t height);

        VideoFrameManager() = default;
        virtual ~VideoFrameManager() = default;

        virtual std::shared_ptr<Frame> getVideoFrame() = 0;
    };

    class I420VideoFrameManager: public VideoFrameManager
    {
    public:
        I420VideoFrameManager(uint32_t maxFrames, uint32_t width, uint32_t height);
        ~I420VideoFrameManager() override;

        std::shared_ptr<Frame> getVideoFrame() override;
    
    private:
        uint8_t* frameBufferPool_;
        int frameBufferPoolSize_;
        std::list<Frame*> framePool_;
        std::mutex mutex_;
    };
}

#endif
#endif