#include "QCuVideoWidget.h"

using namespace panocom;

QCuVideoWidget::QCuVideoWidget(QWidget *parent): QOpenGLWidget(parent), m_index(-1), m_renderer(nullptr)
{
    jlog_init(nullptr);
    jinfo("1 QCuVideoWidget(%p) %d", this, m_index);
}

QCuVideoWidget::QCuVideoWidget(int index, QWidget *parent): QOpenGLWidget(parent), m_index(index), m_renderer(nullptr)
{
    jlog_init(nullptr);
    jinfo("2 QCuVideoWidget(%p) %d", this, m_index);
}

void QCuVideoWidget::setIndex(int index)
{
    jinfo("setIndex(%p) %d", this, index);
    m_index = index;
    update();
}

void QCuVideoWidget::initializeGL()
{
    jinfo("initializeGL");
    createRender();
}

void QCuVideoWidget::paintGL()
{
    //printf("paintGL\n");
    createRender();
    if (m_renderer) m_renderer->display();
}

void QCuVideoWidget::resizeGL(int w, int h)
{
    jinfo("resizeGL %d x %d", w, h);
    createRender();
    if (m_renderer)
    {
        w = w - w % 4;
        h = h - h % 4;
        m_renderer->resizeGL(w, h);
        m_renderer->moveVideoFrameRender(m_render, 0, 0, 0, w, h);
    }
}

void QCuVideoWidget::createRender()
{
    if (m_renderer == nullptr && m_index != -1)
    {
        int w = width();
        int h = height();
        jinfo("createRender(%p) %d %dx%d", this, m_index, w, h);
        m_renderer = new QCuRender(this, w, h);
        nlohmann::json j;
        j.clear();
        j["dev"] = m_index;
        m_capturer = VideoFrameCapturer::CreateVideoCapturer("CuIPCFrameCapturer", j.dump());
        m_capturer->setGroupId(1);
        m_render = m_renderer->createVideoFrameRender(0, 0, 0, 0, w, h);
        m_capturer->addVideoDestination(m_render);
        m_renderer->initializeGL(width(), height());
    }
}