cmake_minimum_required(VERSION 3.5)

project(FfVideoEncoder)
link_directories(${CMAKE_PREFIX_PATH}/lib)
link_libraries(MediaPipeline
        ljcore pthread  hv_static
        avcodec swscale avutil avdevice avfilter avformat  SDL2
        jrtp jthread ZLToolKit drm  DTLSTool
        opus g729 fdk-aac PNPcm asound ssl crypto
        glib-2.0 gio-2.0 gmodule-2.0 gobject-2.0
        nice pcre2-8 srtp2 aec rnnoise ffi z ptoolkit)
set(PROJECT_SOURCES
        main.cpp
)
add_executable(FfVideoEncoder ${PROJECT_SOURCES})

