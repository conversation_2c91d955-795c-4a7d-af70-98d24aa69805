#include <stdio.h>
#include <string>
#include <sdptransform/sdptransform.hpp>

static std::string sdpTemplate = R"(v=0
o=- 0 0 IN IP4 127.0.0.1
s=
t=0 0
m=audio 0 RTP/AVPF 8 111
c=IN IP4 127.0.0.1
a=rtpmap:8 PCMA/8000
a=rtpmap:111 opus/48000/2
a=ptime:20
a=sendrecv
m=video 0 RTP/AVPF 96 98
c=IN IP4 *********
a=rtpmap:96 H264/90000
a=rtpmap:98 H265/90000
a=sendrecv
)";

int main(int argc, char *argv[])
{
    auto j = sdptransform::parse(sdpTemplate);
    printf("%s\n", j.dump().c_str());
    return 0;
}