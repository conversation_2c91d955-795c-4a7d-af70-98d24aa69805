#ifndef P_AudioFrameMixer_h
#define P_AudioFrameMixer_h

#include "../FramePipeline.h"
#include "../Frame.h"
namespace panocom
{
    class AudioFrameMixer : public FramePipeline
    {
    public:
        static void RegistMixers();
        static std::vector<std::string>& GetSupportMixers();
        static bool IsSupportMixer(const std::string &mixerName);
        static std::shared_ptr<AudioFrameMixer> CreateAudioMixer(const std::string &mixerName, const std::string &jsonParams = "");

        virtual ~AudioFrameMixer() = default;      
    protected:
        static bool isRegistered;
        static std::vector<std::string> mixers_;
    };
}

#endif