#ifdef ENABLE_VIDEO
#ifdef USE_CUDA
#ifndef P_Nv12FileFrameSource_h
#define P_Nv12FileFrameSource_h

#include "file/FileFramePipeline.h"
#ifdef USE_CUDA
#include "cuda/CuCommon.h"
#endif
#include <hv/EventLoopThread.h>

namespace panocom
{
    class Nv12FileFrameSource : public FileFramePipeline
    {
    public:
        Nv12FileFrameSource(const std::string& jsonParams);
        ~Nv12FileFrameSource() override;
        void start() override;
        void stop() override;
    private:
        FILE* pf_ = nullptr;
        int width_;
        int height_;
        int hStride_;
        int vStride_;
        hv::EventLoopThread thread_;
        int frameType_;
        std::vector<uint8_t> buffer_;
        std::shared_ptr<CUcontext> ctx_;
        int fps_;
        std::string path_;
    };
}

#endif
#endif
#endif