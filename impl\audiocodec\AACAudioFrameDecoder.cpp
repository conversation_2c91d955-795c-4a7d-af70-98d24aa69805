/*
 * @Author: luo <EMAIL>
 * @Date: 2024-04-22 09:12:44
 * @LastEditors: luo <EMAIL>
 * @LastEditTime: 2025-03-10 20:15:30
 * @FilePath: /mediamanager/impl/audiocodec/AACAudioFrameDecoder.cpp
 * @Description: 音频aac解码模块
 */
#include "AACAudioFrameDecoder.h"

#include <json.hpp>

namespace panocom
{
AACAudioFrameDecoder::AACAudioFrameDecoder(const std::string &jsonParams)
    : fmt_(FRAME_FORMAT_AAC)
	, out_fmt_(FRAME_FORMAT_PCM_16000_1)
    , channels_(1)
    , sample_rate_(16000)
    , bitrate_(64000)
    , ptime_(20)
	, outbuf_size_(1024 * 20)
    , gain_(0) {
	name_ = "AACAudioFrameDecoder";
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    if (j.contains("channel")) {
        channels_ = j["channel"];
    }
    if (j.contains("samplerate")) {
        sample_rate_ = j["samplerate"];
    }
    if (j.contains("bitrate")) {
        bitrate_ = j["bitrate"];
    }
    if (channels_ == 2 && sample_rate_ == 48000) fmt_ = FRAME_FORMAT_AAC_48000_2;
	else if (channels_ == 1 && sample_rate_ == 48000) fmt_ = FRAME_FORMAT_AAC_48000_1;
    samples_per_frame_ = sample_rate_ * ptime_ * channels_ / 1000;
	// TODO: 现固定为单通道，应该在使用前才转换
	out_fmt_ = Frame::getPCMFormat(1, sample_rate_);
	if (init()) {
		outbuf_ = new uint8_t[outbuf_size_];
	} else {
		fprintf(stderr, "AACAudioFrameEncoder init falied\n");
	}
}
AACAudioFrameDecoder::~AACAudioFrameDecoder() {
    if (loop_thread_.isRunning()) loop_thread_.stop(true);
    loop_thread_.join();
	if (decoder_) {
		aacDecoder_Close(decoder_);
		decoder_ = nullptr;
	}
	if (outbuf_) delete[] outbuf_;
	outbuf_ = nullptr;
	input_buf_.Clear();
	if(ofs_.is_open()) ofs_.close();
}

bool AACAudioFrameDecoder::init() {
	if (fmt_ == FRAME_FORMAT_AAC) {
		jinfo("AacDecoder::initDecoder ADTS");
		decoder_ = aacDecoder_Open(TT_MP4_ADTS, 1);
	} else if (fmt_ == FRAME_FORMAT_AAC_48000_1 || fmt_ == FRAME_FORMAT_AAC_48000_2) {
		jinfo("AacDecoder::initDecoder MCP1");
		decoder_ = aacDecoder_Open(TT_MP4_LATM_MCP1, 1);
	}	
	int conceal_method = 2;
	aacDecoder_SetParam(decoder_, AAC_CONCEAL_METHOD, conceal_method);
	aacDecoder_SetParam(decoder_, AAC_PCM_MIN_OUTPUT_CHANNELS, 1);
	aacDecoder_SetParam(decoder_, AAC_PCM_MAX_OUTPUT_CHANNELS, 2);
    
    if (!loop_thread_.isRunning()) {
        loop_thread_.loop()->setInterval(2, [this](hv::TimerID id){
        std::lock_guard<std::mutex> lock(output_mutex_);
        if (output_buf_.Length() >= samples_per_frame_ * channels_ * sizeof(int16_t)) {
            auto out_frame = Frame::CreateFrame(out_fmt_);
            out_frame->createFrameBuffer(samples_per_frame_ * sizeof(int16_t));
            if (out_frame) {
                // TODO:
                if (channels_ == 2) {                    
                    stereo_to_mono((int16_t *)output_buf_.Data(), (int16_t *)out_frame->getFrameBuffer(), samples_per_frame_);
                } else {
                    memcpy(out_frame->getFrameBuffer(), (char *)output_buf_.Data(), samples_per_frame_ * sizeof(int16_t));
                } 
                out_frame->setGain(gain_);
                deliverFrame(out_frame);
            }
            output_buf_.Consume(samples_per_frame_ * channels_ * sizeof(int16_t));
        }
        });
    }
    loop_thread_.start();
	return true;
}
void AACAudioFrameDecoder::onFrame(const std::shared_ptr<Frame> &frame) {
	if (frame->getFrameBufferSize() == 0 || !decoder_)
		return;
	CStreamInfo *info;
	char *encoded = (char*)frame->getFrameBuffer();
	uint32_t encoded_size = frame->getFrameBufferSize();
	// input_buf_.WriteBytes((char*)frame->getFrameBuffer(), frame->getFrameBufferSize());

	// UCHAR *encoded = (UCHAR *)input_buf_.Data();
	// UINT encoded_size = input_buf_.Length();
	uint32_t valid = encoded_size;
	uint32_t size = encoded_size;

    do {
        auto err = aacDecoder_Fill(decoder_, &encoded, &encoded_size, &valid);
        if (err != AAC_DEC_OK) {
            jerror("AACDecoder_Fill !AAC_DEC_OK");
            return;
        }
        // if (encoded_size > valid) input_buf_.Consume(encoded_size - valid);
        encoded += encoded_size - valid;
        size -= encoded_size - valid;
        INT_PCM *upcm = (INT_PCM *)outbuf_;
        int unb_pcm = outbuf_size_;
        err = aacDecoder_DecodeFrame(decoder_, upcm, unb_pcm / sizeof(INT_PCM), 0);
        if (err == AAC_DEC_NOT_ENOUGH_BITS) {
            jerror("AACDecoder_DecodeFrame err: AAC_DEC_NOT_ENOUGH_BITS");
            return;
        }
        if (err != AAC_DEC_OK) {
            fprintf(stderr, "Decoding failed err: %d\n", err);
            return;
        }
        info = aacDecoder_GetStreamInfo(decoder_);
        if (!info)
            return;
        if (info && info->numChannels != channels_) {
            fprintf(stderr, "Mismatched number of channels, input %d, output %d\n", channels_, info->numChannels);
            return;
        }
        {
            gain_ = frame->getGain();
            std::lock_guard<std::mutex> lock(output_mutex_);
            if (sample_rate_ != info->sampleRate) {
                sample_rate_ == info->sampleRate;
                samples_per_frame_ = sample_rate_ * ptime_ / 1000;
                out_fmt_ = Frame::getPCMFormat(info->numChannels, info->sampleRate);
                output_buf_.Clear();
            }
            output_buf_.WriteBytes(outbuf_, info->numChannels * info->frameSize * sizeof(int16_t));
        }

        // if (!ofs_.is_open())
        //     ofs_.open("test_aac.pcm", std::ios::out | std::ios::binary);
        // ofs_.write((char *)upcm, info->numChannels * info->frameSize * 2);
    } while (size > 0);
}
} // namespace panocom