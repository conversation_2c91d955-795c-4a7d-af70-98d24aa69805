#ifndef RK_RKVIDEOFRAMESCALER_H_
#define RK_RKVIDEOFRAMESCALER_H_
#include "VideoFrameProcesser.h"
#include <hv/EventLoopThread.h>
#include "rk_frame_buffer.h"
namespace panocom
{
class RKVideoFrameScaler : public VideoFrameProcesser
{
private:
    /* data */

public:
    RKVideoFrameScaler(const std::string& jsonParams);
    ~RKVideoFrameScaler();
    void onFrame(const std::shared_ptr<Frame>& frame) override;
private:
    uint32_t width_;
    uint32_t height_;
    std::string pixel_format_;
    hv::EventLoopThread thread_;
    std::shared_ptr<RKBufferManager> frameBufferManager_;
};    
} // namespace panocom
#endif
