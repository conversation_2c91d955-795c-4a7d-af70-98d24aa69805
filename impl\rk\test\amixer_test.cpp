#include <alsa/asoundlib.h>
#include <ljcore/jlog.h>

namespace {
const char *alsa_mix_master_ctrl = "Master";
const char *default_alsa_mix_dev = "default";
void list_cards() {
    int card = -1;
    snd_ctl_card_info_t *info;
    snd_ctl_card_info_alloca(&info);

    while (snd_card_next(&card) >= 0 && card >= 0) {
        char name[32];
        snprintf(name, sizeof(name), "hw:%d", card);
        snd_ctl_t *ctl;
        if (snd_ctl_open(&ctl, name, 0) < 0) {
            continue;
        }
        if (snd_ctl_card_info(ctl, info) < 0) {
            snd_ctl_close(ctl);
            continue;
        }
        printf("Card %d: %s\n", card, snd_ctl_card_info_get_name(info));
        snd_ctl_close(ctl);
    } 
}

void volume_init(const char *alsa_mix_ctrl, snd_mixer_t **alsa_mix_handle_p, snd_mixer_elem_t **alsa_mix_elem_p) {
    int alsa_mix_index = 0;
    snd_mixer_selem_id_t *alsa_mix_sid = nullptr;

    snd_mixer_selem_id_alloca(&alsa_mix_sid);
    snd_mixer_selem_id_set_index(alsa_mix_sid, alsa_mix_index);
    snd_mixer_selem_id_set_name(alsa_mix_sid, alsa_mix_ctrl);

    if (snd_mixer_open(alsa_mix_handle_p, 0) < 0) {
        perror("Failed to open mixer.");
    }
    if (snd_mixer_attach(*alsa_mix_handle_p, default_alsa_mix_dev) < 0) {
        perror("Failed to attach mixer.");
    }
    if (snd_mixer_selem_register(*alsa_mix_handle_p, nullptr, nullptr) < 0) {
        perror("Failed to register mixer element.");
    }
    if (snd_mixer_load(*alsa_mix_handle_p) < 0) {
        perror("Failed to load mixer element.");
    }
    *alsa_mix_elem_p = snd_mixer_find_selem(*alsa_mix_handle_p, alsa_mix_sid);
    if (!*alsa_mix_elem_p) {
        perror("Failed to find mixer element.");
    }
    if (snd_mixer_selem_set_playback_volume_range(*alsa_mix_elem_p, 0, 65536) < 0) {
        perror("Failed to set playback volume range.");
    }
}

void volume_uninit(snd_mixer_t *alsa_mix_handle) {
    if (alsa_mix_handle) {
        snd_mixer_close(alsa_mix_handle);
    }
}

int32_t volume_get() {
    int64_t ll, lr, vol;
    snd_mixer_t *alsa_mix_master_handle = nullptr;
    snd_mixer_elem_t *alsa_mix_master_elem = nullptr;
    volume_init(alsa_mix_master_ctrl, &alsa_mix_master_handle, &alsa_mix_master_elem);
    snd_mixer_handle_events(alsa_mix_master_handle);
    snd_mixer_selem_get_playback_volume(alsa_mix_master_elem, SND_MIXER_SCHN_FRONT_LEFT, &ll);
    snd_mixer_selem_get_playback_volume(alsa_mix_master_elem, SND_MIXER_SCHN_FRONT_RIGHT, &lr);
    volume_uninit(alsa_mix_master_handle);
    vol = (int32_t)((ll + lr) >> 1);
    return vol;
}

int32_t volume_set(int32_t vol) {
    snd_mixer_t *alsa_mix_master_handle = nullptr;
    snd_mixer_elem_t *alsa_mix_master_elem = nullptr;
    if (vol > 65536) vol = 65536;
    if (vol < 0) vol = 0;    
    volume_init(alsa_mix_master_ctrl, &alsa_mix_master_handle, &alsa_mix_master_elem);
    snd_mixer_selem_set_playback_volume(alsa_mix_master_elem, SND_MIXER_SCHN_FRONT_LEFT, vol);
    snd_mixer_selem_set_playback_volume(alsa_mix_master_elem, SND_MIXER_SCHN_FRONT_RIGHT, vol);
    volume_uninit(alsa_mix_master_handle);
    return 0;
}

int set_volume_mute_value(int val) {
    int err = 0;
    snd_ctl_t *handle = nullptr;
    snd_ctl_elem_value_t *ctl_elem_value;

    if (!handle && (err = snd_ctl_open(&handle, default_alsa_mix_dev, 0)) < 0) {
        printf("Control open error: %s\n", snd_strerror(err));
        return err;
    }

    if ((err = snd_ctl_elem_value_malloc(&ctl_elem_value)) < 0) {
        printf("Control alloc error: %s\n", snd_strerror(err));
        snd_ctl_close(handle);
        handle = nullptr;
        return err;
    }
    
    snd_ctl_elem_value_set_numid(ctl_elem_value, 4);
    snd_ctl_elem_value_set_interface(ctl_elem_value, SND_CTL_ELEM_IFACE_MIXER);
    snd_ctl_elem_value_set_name(ctl_elem_value, "Master PlackOut Switch");
    snd_ctl_elem_value_set_integer(ctl_elem_value, 0, val);
    
    if ((err = snd_ctl_elem_write(handle, ctl_elem_value)) < 0) {
        printf("Control element write error: %s\n", snd_strerror(err));
    }

    snd_ctl_close(handle);
    handle = nullptr;
    snd_ctl_elem_value_free(ctl_elem_value);
    return err;
}

int volume_mute() {
    return set_volume_mute_value(0);
}
int volume_unmute() {
    return set_volume_mute_value(1);
}
}

int main() {
    list_cards();
    // volume_set(65536);
    // volume_set(50);
    // volume_unmute();
    volume_mute();
    printf("volumn = %d\n", volume_get());
    return 0;
}