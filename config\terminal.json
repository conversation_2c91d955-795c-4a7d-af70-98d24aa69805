{"name": "template", "video": {"capturer": {"name": "V4L2VideoFrameCapturer", "width": 1920, "height": 1080, "fps": 30, "format": "mjpeg", "dev": -1, "decoder": "CuVideoDecoder"}, "render": {"name": "CuIPCFrameRender", "shm": "cuIPC", "frameCount": 8}, "encoder": {"name": "CuVideoEncoder", "codec": [{"name": "h264", "width": 1920, "height": 1080, "fps": 30, "gop": 30, "rc": "AVBR", "bitrate": 2000000, "max-qp": 35, "min-qp": 15}, {"name": "h265", "width": 1920, "height": 1080, "fps": 30, "gop": 30, "rc": "AVBR", "bitrate": 2000000, "max-qp": 35, "min-qp": 15}]}, "decoder": {"name": "CuVideoDecoder", "codec": [{"name": "h264", "width": 1920, "height": 1080}, {"name": "h265", "width": 1920, "height": 1080}, {"name": "mjpeg", "width": 1920, "height": 1080}]}, "uplink": {"processer": [], "file-src": {"name": "H2645FileFrameSource"}, "rtp": "JRtpEngine", "rtp-proxy": "RtcRtpVideoFrameDestination"}, "downlink": {"processer": [], "file-src": {"name": "H2645FileFrameSource"}, "rtp": "JRtpEngine", "rtp-proxy": "RtcRtpVideoFrameSource"}}}