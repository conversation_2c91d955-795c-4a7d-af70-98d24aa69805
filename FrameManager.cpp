#ifdef ENABLE_VIDEO
#include "FramePipeline.h"
#include "FrameManager.h"
#include "Frame.h"

using namespace panocom;

std::shared_ptr<VideoFrameManager> VideoFrameManager::CreateVideoBufferManager(VideoBufferManagerType type, uint32_t maxFrames, uint32_t width, uint32_t height)
{
    switch (type)
    {
    case I420_MANAGER:
        return std::shared_ptr<VideoFrameManager>(new I420VideoFrameManager(maxFrames, width, height));
        break;
    default:
        break;
    }
    return nullptr;
}

I420VideoFrameManager::I420VideoFrameManager(uint32_t maxFrames, uint32_t width, uint32_t height)
{
    FN_BEGIN;
    int frameBufferSize = width * height * 3 / 2;
    frameBufferPoolSize_ = frameBufferSize * maxFrames;
    frameBufferPool_ = new uint8_t[frameBufferPoolSize_];
    for (int i = 0; i < maxFrames; ++i)
    {
        Frame *f = new I420Frame("");
        f->createFrameBuffer(frameBufferPool_ + frameBufferSize * i, width, height);
        framePool_.push_back(f);
    }
    FN_END;
}

I420VideoFrameManager::~I420VideoFrameManager()
{
    FN_BEGIN;
    delete[] frameBufferPool_;
    FN_END;
}

std::shared_ptr<Frame> I420VideoFrameManager::getVideoFrame()
{
    std::shared_ptr<Frame> ret;
    std::unique_lock<std::mutex> lock(mutex_);
    if (framePool_.size() > 0)
    {
        Frame *f = framePool_.front();
        framePool_.pop_front();
        ret = std::shared_ptr<Frame>(f, [this](Frame *f)
                                     {
                std::unique_lock<std::mutex> lock(mutex_);
                framePool_.push_back(f); });
    }
    return ret;
}
#endif