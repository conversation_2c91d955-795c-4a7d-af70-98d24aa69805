#ifndef CSPH3_CHN_H

//CH_0 -- 下行发到PSTN的语音,上行从PSTN线路收到的语音,//注意PSTN模式下，要降噪、防啸叫、AEC、LEC处理			
//CH_1 -- 下行喇叭混音，上行鹅颈语音			
//CH_2 -- 下行喇叭混音，上行前驻极体语音			
//CH_3 -- 下行喇叭混音，上行后驻极体语音			
//CH_4 -- 下行喇叭混音，上行左驻极体语音			
//CH_5 -- 下行喇叭混音，上行右驻极体语音			
//CH_6 -- 下行喇叭混音，上行耳麦麦克风			
//CH_7 -- 下行喇叭混音			

#define MIN_DELAY_MS    1
#define MAX_DELAY_MS    20
#define SAMPLE_LEN      16
#define BUF_LEN         (16 * sizeof(int16_t))
#define BUF_LEN_10MS    (BUF_LEN * 10)
#define BUF_LEN_20MS    (BUF_LEN * 20)
#define BUF_LEN_40MS    (BUF_LEN * 40)
#define CH_PSTN         0
#define CH_GOOSE        1
#define CH_MIC0         2
#define CH_MIC1         3
#define CH_MIC2         4
#define CH_MIC3         5
#define CH_HEADSET      6
#define CH_MIX          7
#define CH_MAX          8

#define CH_LP           1
#define CH_COMMON       7

#define CH_INPUT_MAX    7
#define CH_INPUT_START  1
#define CH_INPUT_END    (CH_INPUT_START + CH_INPUT_MAX)

// NOTE: 新版使用
//CH_0 -- ARM写AEC处理后的语音/下行发到PSTN的语音,ARM读作为AEC参考端语音
//CH_1 -- 下行喇叭混音，上行鹅颈语音/耳麦麦克语音合并
//CH_2 -- 下行喇叭混音，上行前驻极体语音
//CH_3 -- 下行喇叭混音，上行后驻极体语音
//CH_4 -- 下行喇叭混音，上行左驻极体语音
//CH_5 -- 下行喇叭混音，上行右驻极体语音
//CH_6 -- 下行喇叭混音，ARM读PSTN线路收到语音（做LEC处理）
//CH_7 -- ARM写LEC处理后的语音，ARM读LEC的参考端语音

// DONE: H3硬件修改
#define H3_NEW_DRIVER 1

#if H3_NEW_DRIVER
#define CH_AEC_SEND     0
#define CH_AEC_FAREND   0
#define CH_PSTN_RECV    6
#define CH_PSTN_SEND    0
#define CH_LEC_FAREND   7
#define CH_AEC_GAIN     7

#undef CH_HEADSET
#define CH_HEADSET      1

#undef CH_MIX
#define CH_MIX          0
#endif
#endif