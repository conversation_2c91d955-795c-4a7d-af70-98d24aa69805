#include "V4L2DMAVideoFrameCapturer.h"

#include <fcntl.h>
#include <linux/videodev2.h>
#include <stdlib.h>
#include <poll.h>
#include <sys/ioctl.h>
#include <sys/mman.h>
#include <unistd.h>

#include <json.hpp>
#include <rga/im2d.hpp>
#include <rga/rga.h>
#include <rockchip/mpp_frame.h>

#include "DRMDisplayManager.h"
#include "rk_frame_buffer.h"
namespace panocom
{

namespace {
// TODO: 先打表，后续可以使用配置文件或传参数进行优化；DRY
const std::vector<std::string> sip4v_dev_names { "/dev/video11", "/dev/video0" };
const std::vector<std::string> vst_dev_names { "/dev/video33", "/dev/video22", "/dev/video11", "/dev/video0" };
std::vector<int> dev_fds { -1, -1, -1, -1 };
// const std::vector<std::string> vst_capture_format { "bgr3", "nv12", "nv12", "nv12" };
std::unordered_map<std::string, RgaSURF_FORMAT> tab_s_rgapixel {
	{"nv12", RK_FORMAT_YCbCr_420_SP},
	{"nv16", RK_FORMAT_YCbCr_422_SP},
    // {"nv24", RK_FORMAT_YCbCr_444_SP},    // 不支持nv24的rga转换
    {"bgr3", RK_FORMAT_BGR_888}
};
typedef enum {
  PIX_FMT_NONE = -1,
  PIX_FMT_YUV420P,
  PIX_FMT_NV12,
  PIX_FMT_NV21,
  PIX_FMT_YUV422P,
  PIX_FMT_NV16,
  PIX_FMT_NV61,
  PIX_FMT_YUYV422,
  PIX_FMT_UYVY422,
  PIX_FMT_YUV444SP,
  PIX_FMT_RGB332,
  PIX_FMT_RGB565,
  PIX_FMT_BGR565,
  PIX_FMT_RGB888,
  PIX_FMT_BGR888,
  PIX_FMT_ARGB8888,
  PIX_FMT_ABGR8888,
  PIX_FMT_RGBA8888,
  PIX_FMT_BGRA8888,
  // Compound type
  PIX_FMT_FBC0,
  PIX_FMT_FBC2,
  PIX_FMT_MJPEG,
  PIX_FMT_NB
} PixelFormat;

void GetPixFmtNumDen(const PixelFormat& fmt, int& num, int& den)
{
    num = 0;
    den = 1;
    switch (fmt) {
        case PIX_FMT_RGB332:
            num = 1;
            break;
        case PIX_FMT_YUV420P:
        case PIX_FMT_NV12:
        case PIX_FMT_NV21:
        case PIX_FMT_FBC0:
            num = 3;
            den = 2;
            break;
        case PIX_FMT_YUV422P:
        case PIX_FMT_NV16:
        case PIX_FMT_NV61:
        case PIX_FMT_YUYV422:
        case PIX_FMT_UYVY422:
        case PIX_FMT_RGB565:
        case PIX_FMT_BGR565:
        case PIX_FMT_FBC2:
            num = 2;
            break;
        case PIX_FMT_RGB888:
        case PIX_FMT_BGR888:
        case PIX_FMT_YUV444SP:
            num = 3;
            break;
        case PIX_FMT_ARGB8888:
        case PIX_FMT_ABGR8888:
            num = 4;
            break;
        default:
            printf("unsupport get num/den for pixel fmt: %d\n", fmt);
    }
}

int CalPixFmtSize(const PixelFormat& fmt, const int width, const int height, int align) {
    int num = 0;
    int den = 0;
    int extra_hdr_size = 0;
    int pix_fmt_size = 0;
    GetPixFmtNumDen(fmt, num, den);
    // fbc image: fbc hdr + fbc body.
    // fbc w,h must be 16 align, and body offset must be 4k align
    if (fmt == PIX_FMT_FBC0 || fmt == PIX_FMT_FBC2) {
        align = 16;
        extra_hdr_size = UPALIGNTO(width, align) * UPALIGNTO(height, align) / 16;
        extra_hdr_size = UPALIGNTO(extra_hdr_size, 4096);
    }
    // mpp always require buffer align by align value
    if (align > 0)
        pix_fmt_size = UPALIGNTO(width, align) * UPALIGNTO(height, align) * num / den;
    else {
        pix_fmt_size = width * height * num / den;
    }

    return (extra_hdr_size + pix_fmt_size);
}

void GetV4L2PixFmtNumDen(const int& fmt, int& num, int& den) {
    num = 0;
    den = 1;
    switch (fmt) {
        case V4L2_PIX_FMT_RGB332:
            num = 1;
            break;
        case V4L2_PIX_FMT_YUV420:
        case V4L2_PIX_FMT_NV12:
        case V4L2_PIX_FMT_NV21:
            num = 3;
            den = 2;
            break;
        case V4L2_PIX_FMT_YUV422P:
        case V4L2_PIX_FMT_NV16:
        case V4L2_PIX_FMT_NV61:
        case V4L2_PIX_FMT_YUYV:
        case V4L2_PIX_FMT_UYVY:
        case V4L2_PIX_FMT_RGB565:
            num = 2;
            break;
        case V4L2_PIX_FMT_RGB24:
        case V4L2_PIX_FMT_BGR24:
        case V4L2_PIX_FMT_NV24:
            num = 3;
            break;
        case V4L2_PIX_FMT_ARGB32:
        case V4L2_PIX_FMT_ABGR32:
            num = 4;
            break;
        default:
            printf("unsupport get num/den for pixel fmt: %d\n", fmt);
    }
}

}

V4L2DMAVideoFrameCapturer::V4L2DMAVideoFrameCapturer(const std::string &jsonParams)
    : dev_(0)
    , format_str_("nv12")
    , fmt_(V4L2_PIX_FMT_NV12)
    , width_(1920)
    , height_(1080)
    , fps_(30)
    , fd_(-1)
    , running_(false)
    , req_count_(4)
    , device_name_(RK_HDMIRX)
    , is_mutiplannar_(false)
    , epfd_(-1) {
    name_ = "V4L2DMAVideoFrameCapturer";
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    dev_ = 0;
    if (j.contains("dev")) {
        dev_ = j["dev"];
        if (dev_ < 0) {
            dev_ = 0;
        }
    }
    app_name_ = "sip4v";
    if (j.contains("app")) {
        app_name_= j["app"];
    }
    if (j.contains("width")) {
        width_ = j["width"];
    }
    if (j.contains("height")) {
        height_ = j["height"];
    }
    if (j.contains("fps")) {
        fps_ = j["fps"];
    }
    if (j.contains("devNames") && j["devNames"].is_array()) {
        for (size_t i = 0; i < j["devNames"].size(); i++) {
            dev_names_.emplace_back(j["devNames"][i]);
            jinfo("################## %s", dev_names_.back().c_str());
        }        
    }
    // DNOE: 使用额外的缩放模块
    // target_width_ = width_;
    // if (j.contains("targetWidth")) target_width_ = j["targetWidth"];
    // target_height_ = height_;
    // if (j.contains("targetHeight")) target_height_ = j["targetHeight"];
    // jinfo("target resolution %dx%d", target_width_, target_height_);
    // Init();
    ReinitAsync();
}

V4L2DMAVideoFrameCapturer::~V4L2DMAVideoFrameCapturer()
{
    jinfo("V4L2DMAVideoFrameCapturer release");
    running_ = false;
    if (capture_future_.valid()) {
        auto status = capture_future_.wait_for(std::chrono::milliseconds(3000));
        if (status == std::future_status::timeout) {
            jerror("V4L2DMAVideoFrameCapturer capture_future_ stop timeout");
        } else if (status == std::future_status::ready) {
            bool res = capture_future_.get();
            jinfo("V4L2DMAVideoFrameCapturer capture_future_ stop status: %d", res);
        }
    }
    // if (fd_ != -1) {
    //     close(fd_);
    //     fd_ = -1;
    // }
    // auto dev = DRMDisplayManager::instance().GetDrmDev(0);
    // for (auto &bo: bo_vec_) DrmUtils::modeset_destroy_fb(dev, &bo);
    // bo_vec_.clear();
    jinfo("V4L2DMAVideoFrameCapturer release sucess");
#ifdef DEBUG_RK
    if (ofs_.is_open()) ofs_.close();
#endif
}    

bool V4L2DMAVideoFrameCapturer::Init() {
    // usleep(2000000);    // TODO: test, 这里休眠用于96k alsaplayer初始化完成，避开source change event
    uint32_t memory_type = V4L2_MEMORY_DMABUF;
    // std::string dev_name("/dev/video");
    // dev_name += std::to_string(dev_);
    const auto &dev_names = sip4v_dev_names;
    if (!dev_names_.empty()) {
        dev_names = dev_names_;
    } else if (strcasecmp(app_name_.c_str(), "sip4v") == 0) {
        dev_names = sip4v_dev_names;
    } else if (strcasecmp(app_name_.c_str(), "vst") == 0) {
        dev_names = vst_dev_names;
    }
    
    if (dev_ < 0 || dev_ > dev_names.size()) dev_ = 0;
    std::string dev_name = sip4v_dev_names[dev_];
    fd_ = open(dev_name.c_str(), O_RDWR | O_CLOEXEC, 0);
    if (fd_ == -1) {
        jerror("Failed to open video device %s: %s", dev_name.c_str(), strerror(errno));
        return false;
    }
    jinfo("open video device %s success", dev_name.c_str());
    struct v4l2_capability cap;
    memset(&cap, 0x0, sizeof(cap));
    if (ioctl(fd_, VIDIOC_QUERYCAP, &cap) < 0) {
        jerror("Query capability error: %s", strerror(errno));
        close(fd_);
        fd_ = -1;
        return false;
    }
    jinfo("driver: %s", cap.driver);
    jinfo("device: %s", cap.card);
    jinfo("bus: %s", cap.bus_info);
    jinfo("version: %d", cap.version);

    bool is_mutiplannar = false;
    if (cap.capabilities & V4L2_CAP_VIDEO_CAPTURE) {
        jinfo("Device supports video capture");
    }

    if (cap.capabilities & V4L2_CAP_VIDEO_CAPTURE_MPLANE) {
        is_mutiplannar = true;
        jinfo("Device supports video capture multiplanar");
    }

    if (!(cap.capabilities & V4L2_CAP_STREAMING)) {
        jinfo("Device does not support streaming");
        close(fd_);
        fd_ = -1;
        return false;
    }

    if (strcasecmp(cap.card, "rk_hdmirx") == 0) {
        device_name_ = RK_HDMIRX;
    } else if (strcasecmp(cap.card, "rkcif") == 0) {
        device_name_ = RKCIF;
        fmt_ = V4L2_PIX_FMT_NV12;
    }
    if (device_name_ == RK_HDMIRX) {
        struct v4l2_format fmt;
        fmt.type = (is_mutiplannar ? V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE : V4L2_BUF_TYPE_VIDEO_CAPTURE);
        if (ioctl(fd_, VIDIOC_G_FMT, &fmt) == 0) {
            jinfo("Resolution: %dx%d. Pixelformat: %c%c%c%c", fmt.fmt.pix_mp.width, fmt.fmt.pix_mp.height,
                fmt.fmt.pix_mp.pixelformat & 0xFF, (fmt.fmt.pix_mp.pixelformat >> 8) & 0xFF,
                (fmt.fmt.pix_mp.pixelformat >> 16) & 0xFF, (fmt.fmt.pix_mp.pixelformat >> 24) & 0xFF);
            width_ = fmt.fmt.pix_mp.width;
            height_ = fmt.fmt.pix_mp.height;
            fmt_ = fmt.fmt.pix_mp.pixelformat;
        }
    }
    format_str_ = PixelFormat2String(fmt_);

    struct v4l2_fmtdesc fmt_desc;
    memset(&fmt_desc, 0x0, sizeof(fmt_desc));
    fmt_desc.type = (is_mutiplannar ? V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE : V4L2_BUF_TYPE_VIDEO_CAPTURE);
    // TODO：只有非原生才支持获取支持分辨率和帧率，而且仅支持stepwise，即dev=0时不支持获取支持分辨率和帧率，且仅支持discrete
    while (ioctl(fd_, VIDIOC_ENUM_FMT, &fmt_desc) == 0) {
        jinfo(
            "{ pixelformat = %c%c%c%c, description = %s }", fmt_desc.pixelformat & 0xFF, (fmt_desc.pixelformat >> 8) & 0xFF,
            (fmt_desc.pixelformat >> 16) & 0xFF, (fmt_desc.pixelformat >> 24) & 0xFF, fmt_desc.description);
        if (device_name_ == RKCIF && fmt_desc.pixelformat == fmt_) {
            FormatInfo fmt_info { 0 };
            struct v4l2_frmsizeenum frmsize = { .index = 0, .pixel_format = fmt_desc.pixelformat };
            while (ioctl(fd_, VIDIOC_ENUM_FRAMESIZES, &frmsize) == 0) {
                if (frmsize.type == V4L2_FRMSIZE_TYPE_DISCRETE) {
                    jinfo("Discrete resolution: %dx%d", frmsize.discrete.width, frmsize.discrete.height);
                    fmt_info.width = frmsize.discrete.width;
                    fmt_info.height = frmsize.discrete.height;
                } else {
                    jinfo(
                        "Stepwise min_width: %d, max_width: %d, step_width: %d, min_height: %d, max_height: %d, step_height: %d", frmsize.stepwise.min_width,
                        frmsize.stepwise.max_width, frmsize.stepwise.step_width, frmsize.stepwise.min_height, frmsize.stepwise.max_height,
                        frmsize.stepwise.step_height);
                    fmt_info.width = frmsize.stepwise.max_width;
                    fmt_info.height = frmsize.stepwise.max_height;
                }
                struct v4l2_frmivalenum frmival = { .index = 0, .pixel_format = frmsize.pixel_format, .width = fmt_info.width, .height = fmt_info.height };
                while (ioctl(fd_, VIDIOC_ENUM_FRAMEINTERVALS, &frmival) == 0) {
                    uint32_t max_rate = 0;
                    if (frmival.type == V4L2_FRMIVAL_TYPE_DISCRETE) {
                        max_rate = frmival.discrete.denominator / frmival.discrete.numerator;
                    } else {
                        max_rate = frmival.stepwise.min.denominator / frmival.stepwise.min.numerator;
                        jinfo("max_rate: %d", max_rate);
                    }
                    fmt_info.rate = (max_rate > fmt_info.rate) ? max_rate : fmt_info.rate;
                    frmival.index++;
                }
                frmsize.index++;
            }
            width_ = fmt_info.width;
            height_ = fmt_info.height;
            fps_ = fmt_info.rate;
        }
        fmt_desc.index++;
    }

    // TODO: 不同颜色空间和平面数量
    // 固定单平面
    int count_plane = 1;
    struct v4l2_format fmt;
    memset(&fmt, 0, sizeof(fmt));
    if (is_mutiplannar) {
        fmt.type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE; 
        fmt.fmt.pix_mp.width = width_;
        fmt.fmt.pix_mp.height = height_;

        fmt.fmt.pix_mp.pixelformat = fmt_;
        fmt.fmt.pix_mp.num_planes = count_plane;
        fmt.fmt.pix_mp.field = V4L2_FIELD_ANY;
    } else {
        fmt.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
        fmt.fmt.pix.width = width_;
        fmt.fmt.pix.height = height_;
        fmt.fmt.pix.pixelformat = fmt_;
        fmt.fmt.pix.field = V4L2_FIELD_NONE;
    }

    if (ioctl(fd_, VIDIOC_S_FMT, &fmt) == -1) {
        jerror("Setting pixel format: %s", strerror(errno));
        close(fd_);
        fd_ = -1;
        return false;
    }


    // struct v4l2_streamparm setfps {0};
    // setfps.type = (is_mutiplannar ? V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE : V4L2_BUF_TYPE_VIDEO_CAPTURE);
    // setfps.parm.capture.timeperframe.numerator = 1;
    // setfps.parm.capture.timeperframe.denominator = fps_;
    // if (ioctl(fd_, VIDIOC_S_PARM, &setfps) == -1) {
    //     jerror("Setting video params: %s", strerror(errno));
    //     close(fd_);
    //     return false;
    // }
    // subscribe_event(fd_, V4L2_EVENT_SOURCE_CHANGE);

    req_count_ = 4;
    struct v4l2_requestbuffers req;
    memset(&req, 0, sizeof(req));
    req.count = req_count_;
    req.type = (is_mutiplannar ? V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE : V4L2_BUF_TYPE_VIDEO_CAPTURE);
    req.memory = memory_type;
    if (ioctl(fd_, VIDIOC_REQBUFS, &req) == -1) {
        jerror("Requesting buffer: %s", strerror(errno));
        close(fd_);
        fd_ = -1;
        return false;
    }

    auto dev = DRMDisplayManager::instance().GetDrmDev(0);
    for (uint32_t i = 0; i < req.count; ++i) {
        // create dma buf
        buffer_object bo;
        bo.width = width_;
        bo.height = height_;

        int num, den;
        GetV4L2PixFmtNumDen(fmt_, num, den);
        bo.bpp = 8 * num / den;
        DrmUtils::create_dumb_buffer(dev, bo);
        bo.dst_fd = DrmUtils::convert_to_dma_buf_fd(dev, bo.handle);
        // bo.vaddr = (uint32_t*)DrmUtils::mmap_dumb_handle(dev, bo.handle, bo.size);
        bo_vec_.push_back(bo);
        jinfo("drm_fd = %d, bpp = %d bo.vaddr = %p", bo.dst_fd, bo.bpp, bo.vaddr);

        struct v4l2_buffer buf;
        struct v4l2_plane plane;
        memset(&buf, 0x0, sizeof(buf));
        memset(&plane, 0x0, sizeof(plane));
        buf.type = (is_mutiplannar ? V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE : V4L2_BUF_TYPE_VIDEO_CAPTURE);
        buf.memory = memory_type;
        buf.index = i;
        buf.length = count_plane;
        buf.m.planes = &plane;
        if (ioctl(fd_, VIDIOC_QUERYBUF, &buf) == -1) {
            jerror("Querying buffer: %s", strerror(errno));  
            close(fd_);
            fd_ = -1;
            return false;
        }

        buf.m.planes[0].m.fd = bo.dst_fd;
        if (ioctl(fd_, VIDIOC_QBUF, &buf) == -1) {
            jerror("Queueing buffer: %s", strerror(errno));  
            close(fd_);
            fd_ = -1;
            return false;
        }
    }

    running_ = true;
    capture_future_ = std::async([this, is_mutiplannar, memory_type]() -> bool {
        enum v4l2_buf_type type = (is_mutiplannar ? V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE : V4L2_BUF_TYPE_VIDEO_CAPTURE);
        if (ioctl(fd_, VIDIOC_STREAMON, &type) == -1) {
            jerror("Starting capture: %s", strerror(errno));
            return false;
        }
        frameBufferManager_ = std::make_shared<RKBufferManager>(8);

        // struct pollfd fds;
        // fds.fd = fd_;
        // fds.events = POLLIN;
        int epfd = -1;
        struct epoll_event ev{0}, events[5]{0};
        epfd = epoll_create(1);
        ev.data.fd = fd_;
        ev.events = EPOLLET | EPOLLIN;
        epoll_ctl(epfd, EPOLL_CTL_ADD, fd_, &ev);

        /* Configure the current thread to use only RGA3_core0 or RGA3_core1. */
        imconfig(IM_CONFIG_SCHEDULER_CORE, IM_SCHEDULER_RGA3_CORE0 | IM_SCHEDULER_RGA3_CORE1);

        // struct v4l2_event v4l2_ev {0};
        while (running_ && fd_ != -1)
        {
            // auto r = poll(&fds, 2, 1000);
            // if (r > 0) {
            //     if (fds.revents & (POLLERR | POLLNVAL)) {
            //         jerror("Poll error!");
            //         break;
            //     } 
            // } else if (r == 0) {
            //     // jerror("Poll timeout!");
            //     printf("Poll timeout!\n");
            //     if (running_)
            //         continue;
            // }

            bool flag = false;
            int nfds = epoll_wait(epfd, events, 5, 2000);
            for (int i = 0; i < nfds; i++) {
                if (events[i].data.fd == fd_) {
                    flag = true;
                }
            }
            if (!flag) {
                jinfo("Epoll timeout!\n");
                connected_ = false;
                continue;
            }
            if (!running_ || fd_ == -1) break;
            // handle_event(fd_);
            connected_ = true;
            struct v4l2_buffer buf;
            struct v4l2_plane plane;
            memset(&buf, 0x0, sizeof(buf));
            memset(&plane, 0x0, sizeof(plane));
            buf.type = (is_mutiplannar ? V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE : V4L2_BUF_TYPE_VIDEO_CAPTURE);
            buf.memory = memory_type;
            buf.m.planes = &plane;
            buf.length = 1;
            int ret = ioctl(fd_, VIDIOC_DQBUF, &buf);
            if (ret == EAGAIN) {
                jerror("Dequeueing buffer again");
                continue;
            } else if (ret == -1) {
                jerror("Dequeueing buffer: %s", strerror(errno));
                // continue;
                break;
            }
            
#ifdef DEBUG_RK
            // if (!ofs_.is_open()) ofs_.open("capture.yuv", std::ios::binary | std::ios::out);
            // jinfo("size = %d", plane.bytesused);
            // ofs_.write((char*)bo_vec_[buf.index].vaddr, plane.bytesused);
#endif
            if (ret == 0) {
                auto out_frame = std::make_shared<Frame>(FRAME_FORMAT_RK);
                MppFrame frame = nullptr;

                // if (fmt_ != V4L2_PIX_FMT_NV12 || target_width_ != width_ || target_height_ != height_) {
                //     frame = frameBufferManager_->getFreeFrame(target_width_, target_height_);
                //     if (frame) {
                //         auto buffer = mpp_frame_get_buffer(frame);
                //         int dst_fd = mpp_buffer_get_fd(buffer);
                //         DrmUtils::rga_copy(dst_fd, target_width_, target_height_, RK_FORMAT_YCbCr_420_SP, plane.m.fd, width_, height_, tab_s_rgapixel[format_str_]);
                //     }
                //     // if (!ofs.is_open()) ofs.open("capture.yuv", std::ios::binary | std::ios::out);
                //     // ofs.write(mpp_buffer_get_ptr(buffer), 1920 * 1088 * 1.5);
                // } else 
                {
                    ret = mpp_frame_init(&frame);
                    ret = RKBufferManager::PrepareFrameWithFd(plane.m.fd, plane.bytesused, width_, height_, format_str_, frame);
                }
                if (frame) {
                    // out_frame->createFrameBuffer((uint8_t*)frame, mpp_frame_get_buf_size(frame));
                    // out_frame->setGroupId(getGroupId());
                    // deliverFrame(out_frame);

                    auto wrap_frame = std::make_shared<WrapRKMppFrame>(frame);
                    wrap_frame->setGroupId(getGroupId());
                    deliverFrame(wrap_frame);
                }
            }

            if (ioctl(fd_, VIDIOC_QBUF, &buf) == -1) {
                jerror("Queueing buffer: %s", strerror(errno));
                break;
            }
        }
        if (epfd != -1) close(epfd);
        epfd = -1;
        if (fd_ != -1 && (ioctl(fd_, VIDIOC_STREAMOFF, &type)) == -1) {
            jerror("Stopping capture: %s", strerror(errno));
            return false;
        }
        return true;
    });
    return true;
}

bool V4L2DMAVideoFrameCapturer::ReinitAsync() {
    running_ = true;
    frameBufferManager_ = std::make_shared<RKBufferManager>(8);
    capture_future_ = std::async([this]() -> bool {
        ConfigureDevice();
        while (running_ && fd_ > 0) {
            bool flag = false;
            int nfds = epoll_wait(epfd_, events_, 5, 2000);
            for (int i = 0; i < nfds; i++) {
                if (events_[i].data.fd == fd_) {
                    flag = true;
                }
            }
            if (!flag) {
                if (!running_) break;
                // jinfo("dev: %d Epoll timeout!\n", dev_);
                connected_ = false;
                CleanupDevice();
                ConfigureDevice();
                continue;
            }
            connected_ = true;
            struct v4l2_buffer buf;
            struct v4l2_plane plane;
            memset(&buf, 0x0, sizeof(buf));
            memset(&plane, 0x0, sizeof(plane));
            uint32_t memory_type = V4L2_MEMORY_DMABUF;
            buf.type = (is_mutiplannar_ ? V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE : V4L2_BUF_TYPE_VIDEO_CAPTURE);
            buf.memory = memory_type;
            buf.m.planes = &plane;
            buf.length = 1;
            int ret = ioctl(fd_, VIDIOC_DQBUF, &buf);
            if (ret == EAGAIN) {
                jerror("Dequeueing buffer again");
                continue;
            } else if (ret == -1) {
                jerror("Dequeueing buffer: %s", strerror(errno));
                // continue;
                break;
            }
            
#ifdef DEBUG_RK
            if (!ofs_.is_open()) ofs_.open("capture.yuv", std::ios::binary | std::ios::out);
            jinfo("size = %d", plane.bytesused);
            if (bo_vec_[buf.index].vaddr)
                ofs_.write((char*)bo_vec_[buf.index].vaddr, plane.bytesused);
#endif
            auto wrap_frame = std::make_shared<WrapRKMppFrame>();
            if (ret == 0 && wrap_frame) {
                // if (fmt_ != V4L2_PIX_FMT_NV12 || target_width_ != width_ || target_height_ != height_) 
                // {
                //     // frame = frameBufferManager_->getFreeFrame(target_width_, target_height_);
                //     ret = frameBufferManager_->getFreeFrame(target_width_, target_height_, &wrap_frame->frame_);
                //     if (ret) {
                //         jerror("mpp_frame_init failed ret = %d", ret);
                //         if (wrap_frame->frame_) {
                //             mpp_frame_deinit(&wrap_frame->frame_);
                //             wrap_frame->frame_ = nullptr;
                //         }
                //     }
                //     if (wrap_frame->frame_) {
                //         auto buffer = mpp_frame_get_buffer(wrap_frame->frame_);
                //         if (!buffer) return;
                //         int dst_fd = mpp_buffer_get_fd(buffer);
                //         DrmUtils::rga_copy(dst_fd, target_width_, target_height_, RK_FORMAT_YCbCr_420_SP, plane.m.fd, width_, height_, tab_s_rgapixel[format_str_]);
                //     }
                // } 
                // else 
                {
                    ret = mpp_frame_init(&wrap_frame->frame_);
                    if (ret) {
                        jerror("mpp_frame_init failed ret = %d", ret);
                        mpp_frame_deinit(&wrap_frame->frame_);
                        wrap_frame->frame_ = nullptr;
                    }
                    // jinfo("fd:%d, bytesused:%u, width:%u, height:%u, format:%s", plane.m.fd, plane.bytesused, width_, height_, format_str_.c_str());
                    ret = RKBufferManager::PrepareFrameWithFd(plane.m.fd, plane.bytesused, width_, height_, format_str_, wrap_frame->frame_);
                    if (ret) {
                        jerror("PrepareFrameWithFd failed ret = %d", ret);
                        mpp_frame_deinit(&wrap_frame->frame_);
                        wrap_frame->frame_ = nullptr;
                    }
                    wrap_frame->setWidth(width_);
                    wrap_frame->setHeight(height_);
                    wrap_frame->setHStride(width_);
                    wrap_frame->setVStride(height_);
                    wrap_frame->setFormatStr(format_str_);
                }
                if (wrap_frame && wrap_frame->frame_) {
                    // out_frame->createFrameBuffer((uint8_t*)frame, mpp_frame_get_buf_size(frame));
                    // out_frame->setGroupId(getGroupId());
                    // deliverFrame(out_frame);
                    wrap_frame->setGroupId(getGroupId());
                    deliverFrame(wrap_frame);
                }
            }

            if (ioctl(fd_, VIDIOC_QBUF, &buf) == -1) {
                jerror("Queueing buffer: %s", strerror(errno));
                break;
            }
        };
            
        CleanupDevice();
        return true;
    });
    connected_ = false;
    return true;
}

bool V4L2DMAVideoFrameCapturer::ConfigureDevice() {
    uint32_t memory_type = V4L2_MEMORY_DMABUF;
    const auto &dev_names = sip4v_dev_names;
    if (!dev_names_.empty()) {
        dev_names = dev_names_;
    } else if (strcasecmp(app_name_.c_str(), "sip4v") == 0) {
        dev_names = sip4v_dev_names;
    } else if (strcasecmp(app_name_.c_str(), "vst") == 0) {
        dev_names = vst_dev_names;
    }
    if (dev_ < 0 || dev_ > dev_names.size())
        dev_ = 0;
    std::string dev_name = sip4v_dev_names[dev_];
    jinfo("ConfigureDevice start %s!", dev_name.c_str());
    // fd_ = open(dev_name.c_str(), O_RDWR | O_CLOEXEC, 0);
    // if (fd_ == -1) {
    //     jerror("Failed to open video device %s: %s", dev_name.c_str(), strerror(errno));
    //     return false;
    // }
    while (running_) {
        fd_ = open(dev_name.c_str(), O_RDWR | O_CLOEXEC, 0);
        if (fd_ == -1) {
            jerror("Failed to open video device %s: %s", dev_name.c_str(), strerror(errno));
        } else {
            break;
        }
    }
    if (!running_) return false;
    // jinfo("open video device %s success", dev_name.c_str());
    struct v4l2_capability cap;
    memset(&cap, 0x0, sizeof(cap));
    if (ioctl(fd_, VIDIOC_QUERYCAP, &cap) < 0) {
        jerror("Query capability error: %s", strerror(errno));
        close(fd_);
        fd_ = -1;
        return false;
    }
    // jinfo("driver: %s", cap.driver);
    // jinfo("device: %s", cap.card);
    // jinfo("bus: %s", cap.bus_info);
    // jinfo("version: %d", cap.version);

    is_mutiplannar_ = false;
    if (cap.capabilities & V4L2_CAP_VIDEO_CAPTURE) {
        jinfo("Device supports video capture");
    }

    if (cap.capabilities & V4L2_CAP_VIDEO_CAPTURE_MPLANE) {
        is_mutiplannar_ = true;
        // jinfo("Device supports video capture multiplanar");
    }

    if (!(cap.capabilities & V4L2_CAP_STREAMING)) {
        jinfo("Device does not support streaming");
        close(fd_);
        fd_ = -1;
        return false;
    }

    if (strcasecmp(cap.card, "rk_hdmirx") == 0) {
        device_name_ = RK_HDMIRX;
    } else if (strcasecmp(cap.card, "rkcif") == 0) {
        device_name_ = RKCIF;
        fmt_ = V4L2_PIX_FMT_NV12;
    }
    if (device_name_ == RK_HDMIRX) {
        struct v4l2_format fmt;
        fmt.type = (is_mutiplannar_ ? V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE : V4L2_BUF_TYPE_VIDEO_CAPTURE);
        if (ioctl(fd_, VIDIOC_G_FMT, &fmt) == 0) {
            // jinfo("Resolution: %dx%d. Pixelformat: %c%c%c%c", fmt.fmt.pix_mp.width, fmt.fmt.pix_mp.height,
            //     fmt.fmt.pix_mp.pixelformat & 0xFF, (fmt.fmt.pix_mp.pixelformat >> 8) & 0xFF,
            //     (fmt.fmt.pix_mp.pixelformat >> 16) & 0xFF, (fmt.fmt.pix_mp.pixelformat >> 24) & 0xFF);
            width_ = fmt.fmt.pix_mp.width;
            height_ = fmt.fmt.pix_mp.height;
            fmt_ = fmt.fmt.pix_mp.pixelformat;
        }
    }
    format_str_ = PixelFormat2String(fmt_);

    struct v4l2_fmtdesc fmt_desc;
    memset(&fmt_desc, 0x0, sizeof(fmt_desc));
    fmt_desc.type = (is_mutiplannar_ ? V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE : V4L2_BUF_TYPE_VIDEO_CAPTURE);
    // TODO：只有非原生才支持获取支持分辨率和帧率，而且仅支持stepwise，即dev=0时不支持，而且仅支持stepwise
    while (ioctl(fd_, VIDIOC_ENUM_FMT, &fmt_desc) == 0) {
        // jinfo(
        //     "{ pixelformat = %c%c%c%c, description = %s }", fmt_desc.pixelformat & 0xFF, (fmt_desc.pixelformat >> 8) & 0xFF,
        //     (fmt_desc.pixelformat >> 16) & 0xFF, (fmt_desc.pixelformat >> 24) & 0xFF, fmt_desc.description);
        if (device_name_ == RKCIF && fmt_desc.pixelformat == fmt_) {
            FormatInfo fmt_info { 0 };
            struct v4l2_frmsizeenum frmsize = { .index = 0, .pixel_format = fmt_desc.pixelformat };
            while (ioctl(fd_, VIDIOC_ENUM_FRAMESIZES, &frmsize) == 0) {
                if (frmsize.type == V4L2_FRMSIZE_TYPE_DISCRETE) {
                    jinfo("Discrete resolution: %dx%d", frmsize.discrete.width, frmsize.discrete.height);
                    fmt_info.width = frmsize.discrete.width;
                    fmt_info.height = frmsize.discrete.height;
                } else {
                    // jinfo(
                    //     "Stepwise min_width: %d, max_width: %d, step_width: %d, min_height: %d, max_height: %d, step_height: %d", frmsize.stepwise.min_width,
                    //     frmsize.stepwise.max_width, frmsize.stepwise.step_width, frmsize.stepwise.min_height, frmsize.stepwise.max_height,
                    //     frmsize.stepwise.step_height);
                    fmt_info.width = frmsize.stepwise.max_width;
                    fmt_info.height = frmsize.stepwise.max_height;
                }
                struct v4l2_frmivalenum frmival = { .index = 0, .pixel_format = frmsize.pixel_format, .width = fmt_info.width, .height = fmt_info.height };
                while (ioctl(fd_, VIDIOC_ENUM_FRAMEINTERVALS, &frmival) == 0) {
                    uint32_t max_rate = 0;
                    if (frmival.type == V4L2_FRMIVAL_TYPE_DISCRETE) {
                        max_rate = frmival.discrete.denominator / frmival.discrete.numerator;
                    } else {
                        max_rate = frmival.stepwise.min.denominator / frmival.stepwise.min.numerator;
                        // jinfo("max_rate: %d", max_rate);
                    }
                    fmt_info.rate = (max_rate > fmt_info.rate) ? max_rate : fmt_info.rate;
                    frmival.index++;
                }
                frmsize.index++;
            }
            width_ = fmt_info.width;
            height_ = fmt_info.height;
            fps_ = fmt_info.rate;
        }
        fmt_desc.index++;
    }

    // TODO: 不同颜色空间和平面数量
    // 固定单平面
    int count_plane = 1;
    struct v4l2_format fmt;
    memset(&fmt, 0, sizeof(fmt));
    if (is_mutiplannar_) {
        fmt.type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE; 
        fmt.fmt.pix_mp.width = width_;
        fmt.fmt.pix_mp.height = height_;

        fmt.fmt.pix_mp.pixelformat = fmt_;
        fmt.fmt.pix_mp.num_planes = count_plane;
        fmt.fmt.pix_mp.field = V4L2_FIELD_ANY;
    } else {
        fmt.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
        fmt.fmt.pix.width = width_;
        fmt.fmt.pix.height = height_;
        fmt.fmt.pix.pixelformat = fmt_;
        fmt.fmt.pix.field = V4L2_FIELD_NONE;
    }

    if (ioctl(fd_, VIDIOC_S_FMT, &fmt) == -1) {
        jerror("Setting pixel format: %s", strerror(errno));
        close(fd_);
        fd_ = -1;
        return false;
    }

    req_count_ = 4;
    struct v4l2_requestbuffers req;
    memset(&req, 0, sizeof(req));
    req.count = req_count_;
    req.type = (is_mutiplannar_ ? V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE : V4L2_BUF_TYPE_VIDEO_CAPTURE);
    req.memory = memory_type;
    if (ioctl(fd_, VIDIOC_REQBUFS, &req) == -1) {
        jerror("Requesting buffer: %s", strerror(errno));
        close(fd_);
        fd_ = -1;
        return false;
    }

    auto dev = DRMDisplayManager::instance().GetDrmDev(0);
    for (uint32_t i = 0; i < req.count; ++i) {
        // create dma buf
        buffer_object bo;
        bo.width = width_;
        bo.height = height_;

        int num, den;
        GetV4L2PixFmtNumDen(fmt_, num, den);
        bo.bpp = 8 * num / den;
        DrmUtils::create_dumb_buffer(dev, bo);
        bo.dst_fd = DrmUtils::convert_to_dma_buf_fd(dev, bo.handle);
        // bo.vaddr = (uint8_t*)DrmUtils::mmap_dumb_handle(dev, bo.handle, bo.size);
        bo_vec_.push_back(bo);
        // jinfo("drm_fd = %d, bpp = %d bo.vaddr = %p", bo.dst_fd, bo.bpp, bo.vaddr);

        struct v4l2_buffer buf;
        struct v4l2_plane plane;
        memset(&buf, 0x0, sizeof(buf));
        memset(&plane, 0x0, sizeof(plane));
        buf.type = (is_mutiplannar_ ? V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE : V4L2_BUF_TYPE_VIDEO_CAPTURE);
        buf.memory = memory_type;
        buf.index = i;
        buf.length = count_plane;
        buf.m.planes = &plane;
        if (ioctl(fd_, VIDIOC_QUERYBUF, &buf) == -1) {
            jerror("Querying buffer: %s", strerror(errno));  
            close(fd_);
            fd_ = -1;
            return false;
        }

        buf.m.planes[0].m.fd = bo.dst_fd;
        if (ioctl(fd_, VIDIOC_QBUF, &buf) == -1) {
            jerror("Queueing buffer: %s", strerror(errno));  
            close(fd_);
            fd_ = -1;
            return false;
        }
    }
    enum v4l2_buf_type type = (is_mutiplannar_ ? V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE : V4L2_BUF_TYPE_VIDEO_CAPTURE);
    if (ioctl(fd_, VIDIOC_STREAMON, &type) == -1) {
        jerror("Starting capture: %s", strerror(errno));
        return false;
    }
    if (epfd_ != -1) {
        close(epfd_);
        epfd_ = -1;
    }
    epfd_ = -1;
    epfd_ = epoll_create(1);
    memset(&ev_, 0, sizeof(ev_));
    memset(&events_, 0, sizeof(events_));
    ev_.data.fd = fd_;
    ev_.events = EPOLLET | EPOLLIN;
    epoll_ctl(epfd_, EPOLL_CTL_ADD, fd_, &ev_);
    return true;
}

bool V4L2DMAVideoFrameCapturer::CleanupDevice() {
    // jinfo("CleanupDevice start!");
    enum v4l2_buf_type type = (is_mutiplannar_ ? V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE : V4L2_BUF_TYPE_VIDEO_CAPTURE);
    if (fd_ != -1 && (ioctl(fd_, VIDIOC_STREAMOFF, &type)) == -1) {
        jerror("Stopping capture: %s", strerror(errno));
        // return false;
    }
    if (epfd_ != -1) {
        close(epfd_);
        epfd_ = -1;
    }
    if (fd_ != -1) {
        close(fd_);
        fd_ = -1;
    }
    auto dev = DRMDisplayManager::instance().GetDrmDev(0);
    for (auto &bo: bo_vec_) {
        DrmUtils::modeset_destroy_fb(dev, &bo);
        if (bo.dst_fd != -1) {
            close(bo.dst_fd);
            bo.dst_fd = -1;
        }
    }
    bo_vec_.clear();
    // jinfo("CleanupDevice sucess");
    return true;
}

int32_t V4L2DMAVideoFrameCapturer::String2PixelFormat(const std::string &str) const {
    // TODO: 其他格式，多平面
    int32_t fmt = V4L2_PIX_FMT_NV12;
    if (strcasecmp(format_str_.c_str(), "mjpeg") == 0) {
        fmt = V4L2_PIX_FMT_MJPEG;
    } else if (strcasecmp(format_str_.c_str(), "yuyv") == 0) {
        fmt = V4L2_PIX_FMT_YUYV;
    } else if (strcasecmp(format_str_.c_str(), "nv12") == 0) {
        fmt = V4L2_PIX_FMT_NV12;
    } else if (strcasecmp(format_str_.c_str(), "nv16") == 0) {
        fmt = V4L2_PIX_FMT_NV16;
    } else if (strcasecmp(format_str_.c_str(), "bgr3") == 0) {
        fmt = V4L2_PIX_FMT_BGR24;
    } else if (strcasecmp(format_str_.c_str(), "nv24") == 0) {
        fmt = V4L2_PIX_FMT_NV24;
    }
    return fmt;
}

std::string V4L2DMAVideoFrameCapturer::PixelFormat2String(const int32_t fmt) const {
    std::string str = "nv12";
    switch (fmt)
    {
    case V4L2_PIX_FMT_NV12:
        str = "nv12";
        break;
    case V4L2_PIX_FMT_NV16:
        str = "nv16";
        break;
    case V4L2_PIX_FMT_BGR24:
        str = "bgr3";
        break;
    default:
        break;
    }
    return str;
}

int32_t V4L2DMAVideoFrameCapturer::subscribe_event(int fd, uint32_t event) {
    struct v4l2_event_subscription sub;
    sub.type = event;
    if (ioctl(fd, VIDIOC_SUBSCRIBE_EVENT, &sub) == -1) {
        jerror("Subscribing to event failed: %s", strerror(errno));
        return -1;
    }
    return 0;
}

int32_t V4L2DMAVideoFrameCapturer::handle_event(int fd) {
    struct v4l2_event ev;
    if (ioctl(fd, VIDIOC_DQEVENT, &ev) == -1) {
        if (errno == EAGAIN) {
            return -1;
        }
        jerror("Dequeuing event failed: %s", strerror(errno));
        return -1;
    }
    if (ev.type == V4L2_EVENT_SOURCE_CHANGE) {
        struct v4l2_event_src_change *src_change = (struct v4l2_event_src_change *)&ev.u.src_change;
        if (src_change->changes & V4L2_EVENT_SRC_CH_RESOLUTION) {
            printf("Resolution change detected. Reconfiguring device...");
        }
    }
    return 0;
}

} // namespace panocom
