#include <Network/sockutil.h>
#include "version.h"
#include "MediaManagerInterfaceImp.h"
#include "MediaManagerInterface.h"
#include "CodecInfo.h"
#include "macros.h"
#include "monitor/StreamMonitor.h"
#include "monitor/InterruptionConcealer.h"
#include "selector/FrameSelector.h"
#include "sdp_fmtp_utils.h"
#ifdef USE_DTLS_RTP
#include <DTLSTool/dtlsrtpsession.h>
#endif
#include <future>

using namespace panocom;
using namespace toolkit;

#define CHECK_AUDIO_SESSION_EXIST \
if (sessions_[AUDIO].find(id) == sessions_[AUDIO].end()) \
{ \
    LogW("Please CreateSession %s first", id.c_str()); \
    return MediaManagerInterface::MM_SESSION_NOT_FOUND; \
}

#define CHECK_VIDEO_SESSION_EXIST \
if (sessions_[VIDEO].find(id) == sessions_[VIDEO].end()) \
{ \
    LogW("Please CreateSession %s first", id.c_str()); \
    return MediaManagerInterface::MM_SESSION_NOT_FOUND; \
}

static std::string sdpAudioTemplate = R"(v=0
o=- 0 0 IN IP4 127.0.0.1
s=mmi
t=0 0
m=audio 0 RTP/AVP 8 111
c=IN IP4 127.0.0.1
a=rtpmap:8 PCMA/8000
a=rtpmap:111 opus/48000
a=ptime:20
a=sendrecv
)";

static std::string sdpVideoTemplate = R"(v=0
o=- 0 0 IN IP4 127.0.0.1
s=mmi
t=0 0
m=audio 0 RTP/AVP 8 111
c=IN IP4 127.0.0.1
a=rtpmap:8 PCMA/8000
a=rtpmap:111 opus/48000
a=ptime:20
a=sendrecv
m=video 0 RTP/AVP 96 98
c=IN IP4 *********
a=rtpmap:96 H264/90000
a=rtpmap:98 H265/90000
a=sendrecv
)";

/*
a=rtcp-fb:* nack
a=rtcp-fb:* ccm fir
a=rtcp-fb:* ccm tmmbr
a=rtcp-fb:* nack pli
*/

int mmi_record_audio = 0;
int mmi_record_video = 0;
int mmi_print_state = 0;

static void getMaxFrameResolution(const int max_frame_size, int &max_width, int &max_height)
{
    if (max_frame_size >= 4096 * 2160) {
        max_width = 4096;
        max_height = 2160;
    } else if (max_frame_size >= 3840 * 2160) {
        max_width = 3840;
        max_height = 2160;
    } else if (max_frame_size >= 2560 * 1440) {
        max_width = 2560;
        max_height = 1440;
    } else if (max_frame_size >= 1920 * 1080) {
        max_width = 1920;
        max_height = 1080;
    } else if (max_frame_size >= 1600 * 900) {
        max_width = 1600;
        max_height = 900;
    } else if (max_frame_size >= 1280 * 720) {
        max_width = 1280;
        max_height = 720;
    } else if (max_frame_size >= 640 * 360) {
        max_width = 640;
        max_height = 360;
    } else {
        max_width = 352;
        max_height = 288;
    }
}

std::shared_ptr<uint16_t> MediaManagerInterfaceImp::getPort()
{
    std::unique_lock<std::mutex> locker(mutex_);
    if (ports_.empty())
    {
        return nullptr;
    }
    std::shared_ptr<uint16_t> ret = std::shared_ptr<uint16_t>(new uint16_t, [this](uint16_t* port){
        std::unique_lock<std::mutex> locker(mutex_);
        ports_.insert(*port);
        delete port;
    });
    *ret = *ports_.begin();
    ports_.erase(*ret);
    return ret;
}

std::string MediaManagerInterfaceImp::getIP(const std::string& ifdev)
{
    std::string ip;
    std::vector<std::map<std::string, std::string>> devs = SockUtil::getInterfaceList();
    if (devs.empty())
    {
        LogW("GetLocalSDP getInterfaceList fail");
        return ip;
    }
    for (auto it = devs.begin(); it != devs.end(); it++)
    {
        if ((*it)["name"] == ifdev)
        {
            ip = (*it)["ip"];
            break;
        }
    }
    if (ip == "")
    {
        for (auto it = devs.begin(); it != devs.end(); it++)
        {
            if ((*it)["ip"] != "127.0.0.1" && (*it)["ip"] != "0.0.0.0" )
            {
                ip = (*it)["ip"];
                break;
            }
        }
    }
    return ip;
}

CodecInst MediaManagerInterfaceImp::codecInstFromJson(const nlohmann::json& rtp, int ptime)
{
    LogI("codecInstFromJson %s", rtp.dump().c_str());
    CodecInst inst;
    inst.rate = 8000;
    inst.chn = 1;
    inst.profile = 0;
    inst.pt = 0;
    if (rtp.contains("codec"))
    {
        std::string codec = rtp["codec"];
        memcpy(inst.name, codec.data(), codec.size() + 1);
    }
    if (rtp.contains("rate"))
    {
        inst.rate = rtp["rate"];
    }
    if (rtp.contains("payload"))
    {
        inst.pt = rtp["payload"];
    }
    if (rtp.contains("encoding"))
    {
        if (rtp["encoding"].is_number())
        {
            inst.chn = rtp["encoding"];
        }
        else if (rtp["encoding"].is_string())
        {
            std::string encoding = rtp["encoding"];
            inst.chn = atoi(encoding.c_str());
        }
    }
    if (strcasecmp(inst.name, "opus") == 0 && inst.rate == 48000) {
        inst.chn = 2;
    }
    std::list<CodecInst> insts;
    if (getCodecInst(inst.name, insts))
    {
        for (CodecInst& i : insts)
        {
            if (i.rate == inst.rate && i.chn == inst.chn)
            {
                inst.ptime = i.ptime;
                inst.fmt = i.fmt;
                break;
            }
        }
    }
    if (ptime)
    {
        inst.ptime = ptime;
    }
    return inst;
}
// TODO：需要比较fmtp
CodecInst MediaManagerInterfaceImp::codecInstFromJsonWithFmtp(const nlohmann::json& rtp, const nlohmann::json& fmtp, std::string& selected, int ptime) {
    LogI("codecInstFromJsonWithFmtp rtp: %s; fmtp: %s", rtp.dump().c_str(), fmtp.dump().c_str());
    CodecInst inst;
    inst.rate = 8000;
    inst.chn = 1;
    inst.profile = 0;
    inst.pt = 0;
    inst.tsinc = 0;
    inst.bitrate = 0;
    if (rtp.contains("codec"))
    {
        std::string codec = rtp["codec"];
        memcpy(inst.name, codec.data(), codec.size() + 1);
    }
    if (rtp.contains("rate"))
    {
        inst.rate = rtp["rate"];
    }
    if (rtp.contains("payload"))
    {
        inst.pt = rtp["payload"];
    }
    if (rtp.contains("encoding"))
    {
        if (rtp["encoding"].is_number())
        {
            inst.chn = rtp["encoding"];
        }
        else if (rtp["encoding"].is_string())
        {
            std::string encoding = rtp["encoding"];
            inst.chn = atoi(encoding.c_str());
        }
    }
    if (strcasecmp(inst.name, "opus") == 0 && inst.rate == 48000) {
        inst.chn = 2;
    }
    for (size_t i = 0; i < fmtp.size(); i++) {
        if (fmtp[i].contains("payload") && fmtp[i]["payload"] == inst.pt && fmtp[i].contains("config")) {
            nlohmann::json params = sdptransform::parseParams(fmtp[i]["config"]);
            selected = params.dump();
            if (params.contains("bitrate")) {
                inst.bitrate = params["bitrate"];
            }
            if (strcasecmp(inst.name, "h264") == 0 && params.contains(H264::kH264FmtpProfileLevelId)) {
                std::string profile_level_id_str = params[H264::kH264FmtpProfileLevelId];
                if (profile_level_id_str.size() == 6) {
                    const uint32_t profile_level_id_numeric = strtol(profile_level_id_str.c_str(), nullptr, 16);
                    if (profile_level_id_numeric != 0) {
                        // Separate into three bytes.
                        // const uint8_t level_idc =
                        //     static_cast<uint8_t>(profile_level_id_numeric & 0xFF);
                        // const uint8_t profile_iop =
                        //     static_cast<uint8_t>((profile_level_id_numeric >> 8) & 0xFF);
                        const uint8_t profile_idc =
                            static_cast<uint8_t>((profile_level_id_numeric >> 16) & 0xFF);
                        inst.profile = profile_idc;              
                    }
                }
            }
        }
    }
    std::list<CodecInst> insts;
    if (getCodecInst(inst.name, insts))
    {
        for (CodecInst& i : insts)
        {
            if (i.rate == inst.rate && i.chn == inst.chn)
            {
                if (strcasecmp(inst.name, "g7221") == 0 && i.bitrate != inst.bitrate) {
                    continue;
                }
                if (strcasecmp(inst.name, "h264") == 0 && i.profile != inst.profile) {
                    continue;
                }
                inst.ptime = i.ptime;
                inst.fmt = i.fmt;
                inst.tsinc = i.tsinc;
                inst.bitrate = i.bitrate;
                break;
            }
        }
    }
    if (ptime) {
        inst.ptime = ptime;
    }
    return inst;
}

MediaManagerInterfaceImp::MediaManagerInterfaceImp(const std::string& jsonParams) : default_audio_timeout_ms_(600), default_video_timeout_ms_(2000)
{
#ifdef USE_MLOG
    mm_logger.Open("mm");
#endif
    void *hd = NULL;
    jlog_cfg_t cfg = {0};
    hd = jini_init(JLOG_CFG_FILE);
    if (hd) 
    {
        cfg.buf_size = jini_get_int(hd, "jlog", "buf_size", 1024) << 10;
        cfg.wake_size = jini_get_int(hd, "jlog", "wake_size", 64) << 10;
        cfg.level = jini_get_int(hd, "jlog", "level", JLOG_LEVEL_INFO);
        cfg.mode = (jlog_mode_t)jini_get_int(hd, "jlog", "mode", JLOG_TO_NET);

        cfg.file.file_size = jini_get_int(hd, "jlog", "file_size", 1024) << 10;
        cfg.file.file_count = jini_get_int(hd, "jlog", "file_count", 10);
        cfg.file.file_path = jini_get(hd, "jlog", "file_path", "jlog");

        cfg.net.is_ipv6 = jini_get_int(hd, "jlog", "is_ipv6", 0) ;
        cfg.net.ip_port = jini_get_int(hd, "jlog", "ip_port", 9999) ;
        cfg.net.ip_addr = jini_get(hd, "jlog", "ip_addr", "127.0.0.1") ;

        mmi_record_audio = jini_get_int(hd, "jlog", "record_audio", 0) ;
        mmi_print_state = jini_get_int(hd, "jlog", "print_state", 0) ;
        printf("jlog mode=%d addr=%s:%d record_audio[%d] print_state[%d]\n", cfg.mode, cfg.net.ip_addr, cfg.net.ip_port, mmi_record_audio, mmi_print_state);
    }

    jlog_init(&cfg);
    jini_uninit(hd);

    workThread_.start(true);
    cbThread_.start(true);

    sessions_.resize(2);
    codecs_.resize(2);
    file_pipelines_.resize(2);

    nlohmann::json j = sdptransform::parse(sdpAudioTemplate);
    templates_.push_back(j);
    j = sdptransform::parse(sdpVideoTemplate);
    templates_.push_back(j);

    int startPort = 10000;
    int endPort = 20000;
    if (jsonParams != "") 
    {
        config_ = nlohmann::json::parse(jsonParams);
    }
    else
    {
        LoadConfig("pipeline.json");
    }
    if (config_.contains("rtp-start-port"))
    {
        startPort = config_["rtp-start-port"];
    }
    if (config_.contains("rtp-end-port"))
    {
        int port = config_["rtp-end-port"];
        if (port > startPort)
        {
            endPort = port;
        }
        else
        {
            endPort = startPort + 100;
        }
    }
    jinfo("rtp port from [%d] to [%d]", startPort, endPort);
    for (uint16_t i = startPort; i < endPort; i++)
    {
        ports_.insert(i);
    }
    jinfo("build time: %s", BUILD_TIME);
    // TEST: 测试收发电平
    // {
    //     CreateAudioRenderer(0, "");
    //     CreateAudioCapturer(0, "");
    //     SetAudioCapturerNotifyCallback(0, "", [this](const int &id, const std::string &json) {
    //         jinfo("capturer[%d] status: %s", id, json.c_str());
    //     });
    //     SetAudioRendererNotifyCallback(0, "", [this](const int &id, const std::string &json) {
    //         jinfo("renderer[%d] status: %s", id, json.c_str());
    //     });
    // }
}

MediaManagerInterfaceImp::~MediaManagerInterfaceImp()
{
    jinfo("~MediaManagerInterfaceImp begin");
    ClearSessions();
    workThread_.stop(true);
    cbThread_.stop(true);
    workThread_.join();
    cbThread_.join();
    jinfo("~MediaManagerInterfaceImp end");
}

void MediaManagerInterfaceImp::LoadConfig(const std::string& path)
{
    FILE* pf = fopen(path.c_str(), "r");
    if (pf)
    {
        fseek(pf, 0, SEEK_END);
        int len = ftell(pf);
        fseek(pf, 0, SEEK_SET);
        std::vector<uint8_t> buf;
        buf.resize(len + 1);
        memset(buf.data(), 0, len + 1);
        int ret = fread(buf.data(), 1, len, pf);
        if (ret > 0)
        {
            config_ = nlohmann::json::parse(buf.data());
        }
        fclose(pf);
        LogI("LoadConfig %s", config_.dump().c_str());
    }
}

int MediaManagerInterfaceImp::CreateSession(const std::string &id, const std::string &jsonParams)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id, &jsonParams](){
        ret = createSession(id, jsonParams);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::StartSession(const std::string &id)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id](){
        ret = startSession(id);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::PauseSession(const std::string &id)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id](){
        ret = pauseSession(id);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::StopSession(const std::string &id)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id](){
        ret = stopSession(id);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::ReleaseSession(const std::string &id)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id](){
        ret = releaseSession(id);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::ReleaseSession(const std::string& id, bool isVideo) {
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id, isVideo](){
        ret = releaseSession(id, isVideo);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::ClearSessions() {
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret](){
        ret = clearSessions();
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::GetLocalSDP(const std::string &id, std::string &sdp, const std::string &jsonParams)
{
    jinfo("GetLocalSDP [%s]\njsonParams=[%s]", id.c_str(), jsonParams.c_str());
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id, &sdp, &jsonParams]() {
#ifdef ENABLE_VIDEO
        for (size_t type = 0; type < VIDEO + 1; type++)
#else
        for (size_t type = 0; type < VIDEO; type++)
#endif
        {
            sessions_[type][id].call_out = true;
        }
        ret = getLocalSDP(id, sdp, false, jsonParams);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::SetRemoteSDP(const std::string &id, const std::string &sdp, std::string &localSdp, const std::string &jsonParams, bool startSession)
{
    jinfo("SetRemoteSDP [%s] [%s]\njsonParams=[%s]", id.c_str(), sdp.c_str(), jsonParams.c_str());
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id, &sdp, &localSdp, &jsonParams, &startSession](){
        ret = setRemoteSDP(id, sdp, localSdp, jsonParams, startSession);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::StartUplinkAudioFile(const std::string &id, const std::string &path, const std::string &jsonParams)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id, &path, &jsonParams](){
        ret = startUplinkAudioFile(id, path, jsonParams);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::StopUplinkAudioFile(const std::string &id)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id](){
        ret = stopUplinkAudioFile(id);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::StartUplinkVideoFile(const std::string &id, const std::string &path, const std::string &jsonParams)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id, &path, &jsonParams](){
        ret = startUplinkVideoFile(id, path, jsonParams);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::StopUplinkVideoFile(const std::string &id)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id](){
        ret = stopUplinkVideoFile(id);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::StartDownlinkAudioFile(const std::string &id, const std::string &path, const std::string &jsonParams)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id, &path, &jsonParams](){
        ret = startDownlinkAudioFile(id, path, jsonParams);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::StopDownlinkAudioFile(const std::string &id)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id](){
        ret = stopDownlinkAudioFile(id);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::StartPlayFile(const std::string &jsonParams)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &jsonParams](){
        ret = startPlayFile(jsonParams);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::StopPlayFile(const std::string& jsonParams)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &jsonParams](){
        ret = stopPlayFile(jsonParams);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::StartDownlinkVideoFile(const std::string &id, const std::string &path, const std::string &jsonParams)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id, &path, &jsonParams](){
        ret = startDownlinkVideoFile(id, path, jsonParams);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::StopDownlinkVideoFile(const std::string &id)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id](){
        ret = stopDownlinkVideoFile(id);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::GetLocalSDP(const std::string &id, const std::string &jsonParams, const SDPNotifyCallback &cb)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id, &jsonParams, &cb](){
        ret = getLocalSDP(id, false, jsonParams, cb);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::SetRemoteSDP(const std::string &id, const std::string &sdp, const std::string &jsonParams, const SDPNotifyCallback &cb)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id, &sdp, &jsonParams, &cb](){
        ret = setRemoteSDP(id, sdp, jsonParams, cb);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::SetRemoteMediaTimeoutListener(const std::string& id, int timeoutMS, const MediaTimeoutCallback& cb)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id, &timeoutMS, &cb](){
        ret = setRemoteMediaTimeoutListener(id, timeoutMS, cb);
        p.set_value(0);
    });
    f.wait();
    return ret;
}
// TODO: DRY
int MediaManagerInterfaceImp::SetRemoteMediaTimeoutListener2(const std::string& id, int timeoutMS, const MediaTimeoutCallback2& cb) {
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id, &timeoutMS, &cb](){
        ret = setRemoteMediaTimeoutListener2(id, timeoutMS, cb);
        p.set_value(0);
    });
    f.wait();
    return ret;    
}

int MediaManagerInterfaceImp::AddToMediaPool(const std::string poolId, const std::string &id, const WorkingMediaCallback &cb)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &poolId, &id, &cb](){
        ret = addToMediaPool(poolId, id, cb);
        p.set_value(0);
    });
    f.wait();
    return ret;
}
// TODO: DRY
int MediaManagerInterfaceImp::AddToMediaPool2(const std::string poolId, const std::string &id, const WorkingMediaCallback2 &cb)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &poolId, &id, &cb](){
        ret = addToMediaPool2(poolId, id, cb);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::RemoveFromMediaPool(const std::string poolId, const std::string &id)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &poolId, &id](){
        ret = removeFromMediaPool(poolId, id);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::ClearMediaPool(const std::string poolId)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &poolId](){
        ret = clearMediaPool(poolId);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::SetActiveMedia(const std::string poolId, const std::string &id)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &poolId, &id](){
        ret = setActiveMedia(poolId, id);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::GetActiveMediaSession(const std::string &poolId, std::string &id)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &poolId, &id](){
        ret = getActiveMediaSession(poolId, id);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::MediaCtrl(const std::string &id, const std::string &jsonParams)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id, &jsonParams](){
        ret = mediaCtrl(id, jsonParams);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::SetMediaLayout(const std::string &id, const std::list<Region> &layout, const std::string &jsonParams)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id, &layout, &jsonParams](){
        ret = setMediaLayout(id, layout, jsonParams);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

std::list<CodecInst> MediaManagerInterfaceImp::GetSupportAudioCodecs()
{
    std::list<CodecInst> ret;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret](){
        ret = getSupportAudioCodecs();
        p.set_value(0);
    });
    f.wait();
    return ret;
}

std::list<CodecInst> MediaManagerInterfaceImp::GetSupportVideoCodecs()
{
    std::list<CodecInst> ret;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret](){
        ret = getSupportVideoCodecs();
        p.set_value(0);
    });
    f.wait();
    return ret;
}

void MediaManagerInterfaceImp::SetAudioCodecPriority(const std::list<CodecInst> &codecs, const std::string &id)
{
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &codecs, &id](){
        setAudioCodecPriority(codecs, id);
        p.set_value(0);
    });
    f.wait();
}

void MediaManagerInterfaceImp::SetVideoCodecPriority(const std::list<CodecInst> &codecs, const std::string &id)
{
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &codecs, &id](){
        setVideoCodecPriority(codecs, id);
        p.set_value(0);
    });
    f.wait();
}

int MediaManagerInterfaceImp::StartPSTN(const std::string& id)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, id, &p, &ret](){
        ret = startPSTN(id);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::StopPSTN(const std::string& id)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, id, &p, &ret](){
        ret = stopPSTN(id);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::startPSTN(const std::string& id)
{
    CHECK_AUDIO_SESSION_EXIST;
    jinfo("startPSTN %s", id.c_str());
    auto capturer = createAudioCapture(id);
    sessions_[AUDIO][id].local.pipelines.insert(capturer);
    auto render = createAudioRender(id);
    sessions_[AUDIO][id].remote.pipelines.insert(render);
    nlohmann::json j;
    j["mode"] = 2; // PTSN
    {
        j["dev"] = sessions_[AUDIO][id].local.dev;
        j["aec"] = sessions_[AUDIO][id].local.aec;
        // j["lec"] = sessions_[AUDIO][id].local.lec;
        j["ns"] = sessions_[AUDIO][id].local.ns;
        j["agc"] = sessions_[AUDIO][id].local.agc;
        j["cng"] = sessions_[AUDIO][id].local.cng;
        j["anti-howling"] = sessions_[AUDIO][id].local.anti_howling;
        j["delay"] = sessions_[AUDIO][id].local.delay;
        j["log"] = sessions_[AUDIO][id].local.log;
        j["record-audio"] = sessions_[AUDIO][id].local.record_audio;
        j["use-headset"] = sessions_[AUDIO][id].local.useHeadset;
        j["mic-type"] = sessions_[AUDIO][id].local.mic_type;
    }
    sessions_[AUDIO][id].local.capturer = capturer;
    capturer->updateParam(j.dump());
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::stopPSTN(const std::string& id)
{
    CHECK_AUDIO_SESSION_EXIST;
    jinfo("stopPSTN %s", id.c_str());
    if (sessions_[AUDIO].count(id) != 0) {
        sessions_[AUDIO][id].local.capturer.reset();
    }
    int ret = MediaManagerInterface::MM_SUCCESS;
    return ret;
}

int MediaManagerInterfaceImp::AddVideoRender(const std::string& id, const FramePipeline::Ptr& ptr)
{
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, ptr, &id](){
        addVideoRender(id, ptr);
        p.set_value(0);
    });
    f.wait();
    return f.get();
}

int MediaManagerInterfaceImp::RemoveVideoRender(const std::string& id)
{
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &id](){
        removeVideoRender(id);
        p.set_value(0);
    });
    f.wait();
    return f.get();
}

void MediaManagerInterfaceImp::SetRTPStatisticsListener(const std::string& id, const std::string& jsonParam, const RTPStatisticsNotifyCallback& cb, bool use_async)
{
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &id, &jsonParam, &use_async, &cb](){
        setRTPStatisticsListener(id, jsonParam, cb, use_async);
        p.set_value(0);
    });
    f.wait();
}

int MediaManagerInterfaceImp::SetAudioCapturerNotifyCallback(const int &id, const std::string& jsonParam, const AudioCapturerNotifyCallback& callback)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &id, &jsonParam, &callback, &ret](){
        ret = setAudioCapturerNotifyCallback(id, jsonParam, callback);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::SetAudioRendererNotifyCallback(const int &id, const std::string& jsonParam, const AudioRendererNotifyCallback& callback)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &id, &jsonParam, &callback, &ret](){
        ret = setAudioRendererNotifyCallback(id, jsonParam, callback);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::UpdateAudioCapturer(const std::string &id, const std::string &jsonParams) {
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &id, &jsonParams](){
        jinfo("UpdateAudioCapturer id: %s", id.c_str());
        if (sessions_[AUDIO].find(id) != sessions_[AUDIO].end()) {
            for (auto& pipeline : sessions_[AUDIO][id].remote.pipelines) {
                if (AudioFrameCapturer::IsSupportCapturer(pipeline->name())) {
                    pipeline->updateParam(jsonParams);
                    break;
                }
            }
        }
        p.set_value(0);
    });
    f.wait();
    return f.get();
}
int MediaManagerInterfaceImp::UpdateAudioRender(const std::string &id, const std::string &jsonParams) {
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &id, &jsonParams](){
        jinfo("UpdateAudioRender id: %s", id.c_str());
        if (sessions_[AUDIO].find(id) != sessions_[AUDIO].end()) {
            for (auto& pipeline : sessions_[AUDIO][id].remote.pipelines) {
                if (AudioFrameRender::isAudioFrameRender(pipeline)) {
                    pipeline->updateParam(jsonParams);
                    break;
                }
            }
        }
        p.set_value(0);
    });
    f.wait();
    return f.get();
}

int MediaManagerInterfaceImp::UpdateAudioMixer(const std::string &id, const std::string &jsonParams) {
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &id, &jsonParams](){
        jinfo("UpdateAudioMixer id[%s]: %s", id.c_str(), jsonParams.c_str());
        updateAudioMixer(id, jsonParams);
        p.set_value(0);
    });
    f.wait();
    return f.get();    
}

int MediaManagerInterfaceImp::createSession(const std::string& id, const std::string& jsonParams)
{
    jinfo("createSession %s %s, audio: %d, video: %d", id.c_str(), jsonParams.c_str(), sessions_[AUDIO].size(), sessions_[VIDEO].size());
    if (sessions_[AUDIO].find(id) != sessions_[AUDIO].end())
    {
        jerror("createSession failed: %s exist", id.c_str());
        return MediaManagerInterface::MM_SESSION_EXIST;
    }
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    if (j.contains("isH323")) {
        sessions_[AUDIO][id].is_h323 = j["isH323"];
#ifdef ENABLE_VIDEO
    if (j.contains("isH323")) {
        sessions_[VIDEO][id].is_h323 = j["isH323"];
    }
#endif
    }
    sessions_[AUDIO][id].local = MediaEndpoint();
#ifdef USE_MLOG
    std::string logname = "mm-" + id;
    sessions_[AUDIO][id].logger.Open(logname.c_str());
#endif
    if (j.contains("local"))
    {
        if (j["local"].contains("audio"))
        {
            if (j["local"]["audio"].contains("id"))
            {
                sessions_[AUDIO][id].local.id = j["local"]["audio"]["id"];
            }
            if (j["local"]["audio"].contains("dev"))
            {
                sessions_[AUDIO][id].local.dev = j["local"]["audio"]["dev"];
            }
            //{"local":{"audio":{"lp":{"bind-port":25880,"enabled":true,"multicast-ip":"***********","multicast-port":8765,"muteNotLP":false}}}}
            if (j["local"]["audio"].contains("lp") 
             && j["local"]["audio"]["lp"].contains("enabled")
             && j["local"]["audio"]["lp"].contains("bind-port")	)
            {
                if (j["local"]["audio"]["lp"].contains("muteNotLP"))
                {
                    sessions_[AUDIO][id].local.muteNotLP = j["local"]["audio"]["lp"]["muteNotLP"];
                }
                sessions_[AUDIO][id].local.enableLP = j["local"]["audio"]["lp"]["enabled"];
                sessions_[AUDIO][id].local.bindPort = j["local"]["audio"]["lp"]["bind-port"];
				if (j["local"]["audio"]["lp"].contains("multicast-ip"))
				{
					sessions_[AUDIO][id].local.multicastIP = j["local"]["audio"]["lp"]["multicast-ip"];
				}
				else
				{
					sessions_[AUDIO][id].local.multicastIP = "";
				}
				if (j["local"]["audio"]["lp"].contains("multicast-port"))
				{
					sessions_[AUDIO][id].local.multicastPort = j["local"]["audio"]["lp"]["multicast-port"];
				}
				else
				{
					sessions_[AUDIO][id].local.multicastPort = 0;
				}
				jinfo("lp [%d] %d %s:%d [%d]", sessions_[AUDIO][id].local.enableLP, sessions_[AUDIO][id].local.bindPort,
                sessions_[AUDIO][id].local.multicastIP.c_str(), sessions_[AUDIO][id].local.multicastPort, sessions_[AUDIO][id].local.muteNotLP);
            }
            else
            {
                sessions_[AUDIO][id].local.enableLP = false;
                jinfo("lp [%d]", sessions_[AUDIO][id].local.enableLP);
            }
            if (j["local"]["audio"].contains("aec"))
            {
                sessions_[AUDIO][id].local.aec = j["local"]["audio"]["aec"];
            }
            if (j["local"]["audio"].contains("lec"))
            {
                sessions_[AUDIO][id].local.lec = j["local"]["audio"]["lec"];
            }
            if (j["local"]["audio"].contains("ns"))
            {
                sessions_[AUDIO][id].local.ns = j["local"]["audio"]["ns"];
            }
            if (j["local"]["audio"].contains("agc"))
            {
                sessions_[AUDIO][id].local.agc = j["local"]["audio"]["agc"];
            }
            if (j["local"]["audio"].contains("comfort-noise"))
            {
                sessions_[AUDIO][id].local.cng = j["local"]["audio"]["comfort-noise"];
            }
            if (j["local"]["audio"].contains("anti-howling")) {
                sessions_[AUDIO][id].local.anti_howling = j["local"]["audio"]["anti-howling"];
            }
            if (j["local"]["audio"].contains("record-audio"))
            {
                sessions_[AUDIO][id].local.record_audio = j["local"]["audio"]["record-audio"];
            }
            if (j["local"]["audio"].contains("use-headset"))
            {
                sessions_[AUDIO][id].local.useHeadset = j["local"]["audio"]["use-headset"];
            }
            if (j["local"]["audio"].contains("mic-type"))
            {
                sessions_[AUDIO][id].local.mic_type = j["local"]["audio"]["mic-type"];
            }            
            if (j["local"]["audio"].contains("delay"))
            {
                sessions_[AUDIO][id].local.delay = j["local"]["audio"]["delay"];
            }
            if (j["local"]["audio"].contains("log"))
            {
                sessions_[AUDIO][id].local.log = j["local"]["audio"]["log"];
            }
            if (j["local"]["audio"].contains("innerAec"))
            {
                sessions_[AUDIO][id].local.innerAec = j["local"]["audio"]["innerAec"];
            }
            if (j["local"]["audio"].contains("cng"))
            {
                sessions_[AUDIO][id].local.cng = j["local"]["audio"]["cng"];
            }
            if (j["local"]["audio"].contains("use-uart"))
            {
                int use_uart = j["local"]["audio"]["use-uart"];
                sessions_[AUDIO][id].local.use_uart = (use_uart > 0);
            }
            if (j["local"]["audio"].contains("use-mixer")) {
                sessions_[AUDIO][id].local.use_mixer = j["local"]["audio"]["use-mixer"];
                if (j["local"]["audio"].contains("devs") && j["local"]["audio"]["devs"].is_array()) {
                    for (size_t i = 0; i < j["local"]["audio"]["devs"].size(); i++) {
                        sessions_[AUDIO][id].local.devs.emplace(j["local"]["audio"]["devs"][i]);
                    }
                }
            }
            if (j["local"]["audio"].contains("external-hdmi")) {
                sessions_[AUDIO][id].local.external_hdmi = j["local"]["audio"]["external-hdmi"];
            }
            if (j["local"]["audio"].contains("ptime")) {
                sessions_[AUDIO][id].local.ptime = j["local"]["audio"]["ptime"];
            }
            // Test: 测试回声消除等算法
            // {
            //     sessions_[AUDIO][id].local.aec = 0;
            //     sessions_[AUDIO][id].local.ns = 0;
            //     sessions_[AUDIO][id].local.cng = 0;
            //     sessions_[AUDIO][id].local.agc = 0;
            //     sessions_[AUDIO][id].local.innerAec = false;
            // }  
            sessions_[AUDIO][id].local.params = j["local"]["audio"];
        } else {
            sessions_[AUDIO][id].local.enabled = false;
        }
#ifdef ENABLE_VIDEO
        if (j["local"].contains("video"))
        {
            if (j["local"]["video"].contains("id"))
            {
                sessions_[VIDEO][id].local.id = j["local"]["video"]["id"];
            }
            if (j["local"]["video"].contains("dev"))
            {
                sessions_[VIDEO][id].local.dev = j["local"]["video"]["dev"];
            }
            if (j["local"]["video"].contains("gpu-index"))
            {
                sessions_[VIDEO][id].local.gpu_index = j["local"]["video"]["gpu-index"];
            }
            if (j["local"]["video"].contains("pip")) {
                sessions_[VIDEO][id].local.pip = j["local"]["video"]["pip"];
            }
            if (j["local"]["video"].contains("ARQ"))
            {
                int enabled = j["local"]["video"]["ARQ"];
                sessions_[VIDEO][id].local.video_nack_enabled = (enabled > 0);
            }
            if (j["local"]["video"].contains("FEC")) {
                int enabled = j["local"]["video"]["FEC"];
                sessions_[VIDEO][id].local.video_fec_enabled = (enabled > 0);
            }

            // Test: 测试画中画
            // sessions_[VIDEO][id].local.pip = true;

            sessions_[VIDEO][id].local.params = j["local"]["video"];
            if (sessions_[VIDEO][id].local.params.contains("output") /*&& sessions_[VIDEO][id].local.params["output"].contains("videoSrc") */
                && sessions_[VIDEO][id].local.params["output"].contains("wait-pic")) {
                std::string videoSrc = sessions_[VIDEO][id].local.params["output"]["wait-pic"];
                // sessions_[VIDEO][id].remote.standby = true;
                // sessions_[VIDEO][id].remote.videoSrc = videoSrc;
                sessions_[VIDEO][id].local.standby = true;
                sessions_[VIDEO][id].local.videoSrc = videoSrc;
            }
        }
#endif
    }
    // Test: 测试NACK、FEC
    // sessions_[VIDEO][id].local.video_nack_enabled = true;
    // sessions_[VIDEO][id].local.video_fec_enabled = true;
    if (j.contains("remote"))
    {
        if (j["remote"].contains("audio"))
        {
            if (j["remote"]["audio"].contains("id"))
            {
                sessions_[AUDIO][id].remote.id = j["remote"]["audio"]["id"];
            }
            if (j["remote"]["audio"].contains("dev"))
            {
                sessions_[AUDIO][id].remote.dev = j["remote"]["audio"]["dev"];
            }
            if (j["remote"]["audio"].contains("vad"))
            {
                sessions_[AUDIO][id].remote.vad = j["remote"]["audio"]["vad"];
            }
            if (j["remote"]["audio"].contains("jitter-buffer"))
            {
                sessions_[AUDIO][id].remote.jitter_buffer = j["remote"]["audio"]["jitter-buffer"];
            }
            if (j["remote"]["audio"].contains("plc"))
            {
                sessions_[AUDIO][id].remote.plc = j["remote"]["audio"]["plc"];
            }
            if (j["remote"]["audio"].contains("use-uart"))
            {
                int use_uart = j["remote"]["audio"]["use-uart"];
                sessions_[AUDIO][id].remote.use_uart = (use_uart > 0);
            }

            if (j["remote"]["audio"].contains("devs") && j["remote"]["audio"]["devs"].is_array()) {
                for (size_t i = 0; i < j["remote"]["audio"]["devs"].size(); i++) {
                    sessions_[AUDIO][id].remote.devs.emplace(j["remote"]["audio"]["devs"][i]);
                }
                if (!sessions_[AUDIO][id].remote.devs.empty()) {
                    sessions_[AUDIO][id].remote.use_dispatcher = true;
                }
            }
            if (j["remote"]["audio"].contains("external-hdmi")) {
                sessions_[AUDIO][id].remote.external_hdmi = j["remote"]["audio"]["external-hdmi"];
            }
            if (j["local"]["audio"].contains("ptime")) {
                sessions_[AUDIO][id].remote.ptime = j["local"]["audio"]["ptime"];
            }
            if (j["remote"]["audio"].contains("lec"))
            {
                sessions_[AUDIO][id].remote.lec = j["remote"]["audio"]["lec"];
            }
            sessions_[AUDIO][id].remote.params = j["remote"]["audio"];
            if (sessions_[AUDIO][id].remote.jitter_buffer > 1 || sessions_[AUDIO][id].remote.plc > 1)
                sessions_[AUDIO][id].remote.params["useNeteq"] = true;
            // Test: 测试neteq
            // sessions_[AUDIO][id].remote.params["useNeteq"] = true;
        } else {
            sessions_[AUDIO][id].remote.enabled = false;
        }
#ifdef ENABLE_VIDEO
        if (j["remote"].contains("video"))
        {
            if (j["remote"]["video"].contains("id"))
            {
                sessions_[VIDEO][id].remote.id = j["remote"]["video"]["id"];
            }
            if (j["remote"]["video"].contains("dev"))
            {
                sessions_[VIDEO][id].remote.dev = j["remote"]["video"]["dev"];
            }
            if (j["remote"]["video"].contains("gpu-index"))
            {
                sessions_[VIDEO][id].remote.gpu_index = j["remote"]["video"]["gpu-index"];
            }
            if (j["remote"]["video"].contains("AJB"))
            {
                int enabled = j["remote"]["video"]["AJB"]; 
                sessions_[VIDEO][id].remote.video_jitter_buffer = (enabled > 0);
            }
            sessions_[VIDEO][id].remote.params = j["remote"]["video"];
        }
#endif
    }
// #ifdef ENABLE_VIDEO
//     if (sessions_[VIDEO][id].local.dev < 0)
//     {
//         sessions_[VIDEO][id].local.dev = 0;
//     }
//     if (sessions_[VIDEO][id].remote.dev < 0)
//     {
//         sessions_[VIDEO][id].remote.dev = sessions_[VIDEO][id].local.dev + 1;
//     }
//     LogI("CreateSession %s localdev = %d remotedev = %d", id.c_str(), sessions_[VIDEO][id].local.dev, sessions_[VIDEO][id].remote.dev);
// #endif
    jinfo("After createSession[%s], audio: %d, video: %d", id.c_str(), sessions_[AUDIO].size(), sessions_[VIDEO].size());
    // AlsaUtils::listAllDevices();
    return MediaManagerInterface::MM_SUCCESS; 
}

int MediaManagerInterfaceImp::startSession(const std::string& id)
{
    return MediaManagerInterface::MM_SUCCESS; 
}

int MediaManagerInterfaceImp::pauseSession(const std::string& id)
{
    return MediaManagerInterface::MM_SUCCESS; 
}

int MediaManagerInterfaceImp::stopSession(const std::string& id)
{
    return MediaManagerInterface::MM_SUCCESS; 
}

int MediaManagerInterfaceImp::releaseSession(const std::string& id)
{
    // CHECK_AUDIO_SESSION_EXIST;
    if (sessions_[AUDIO].find(id) == sessions_[AUDIO].end())
    { 
        LogW("Please AUDIO CreateSession %s first", id.c_str());
        // return MediaManagerInterface::MM_SESSION_NOT_FOUND;
    }
    if (sessions_[VIDEO].find(id) == sessions_[VIDEO].end()) 
    { 
        LogW("Please VIDEO CreateSession %s first", id.c_str()); 
        // return MediaManagerInterface::MM_SESSION_NOT_FOUND; 
    }
    jinfo("releaseSession [%s]", id.c_str());
    for (auto &kv: sessions_[AUDIO]) {
        jinfo("sessions_[AUDIO]: %s ", kv.first.c_str());
    }
    for (auto &kv: sessions_[VIDEO]) {
        jinfo("sessions_[VIDEO]: %s ", kv.first.c_str());
    }
#ifdef ENABLE_VIDEO
    closePip(id);
    CloseCaption(id);
    if (sessions_[VIDEO].find(id) != sessions_[VIDEO].end() && sessions_[VIDEO][id].local.capturer) {
        auto &capturer = sessions_[VIDEO][id].local.capturer;
        for (auto &pipeline: sessions_[VIDEO][id].local.pipelines) {
            if (capturer->isVideoDestination(pipeline)) {
                capturer->removeVideoDestination(pipeline);
            }
        }
        for (auto &pipeline: sessions_[VIDEO][id].remote.pipelines) {
            if (capturer->isVideoDestination(pipeline)) {
                capturer->removeVideoDestination(pipeline);
            }
        }
    }
#endif
    for (auto it = sessions_.begin(); it != sessions_.end(); it++)
    {
        if (it->count(id) == 0) continue;
        for (auto it2 = (*it)[id].local.pipelines.begin(); it2 != (*it)[id].local.pipelines.end(); it2++)
        {
            (*it2)->stop();
        }
        for (auto it2 = (*it)[id].remote.pipelines.begin(); it2 != (*it)[id].remote.pipelines.end(); it2++)
        {
            (*it2)->stop();
        }
        if ((*it)[id].local.use_mixer) {
            (*it)[id].local.capturers.clear();
            (*it)[id].local.processors.clear();
            (*it)[id].local.mixer.reset();
        }
        if ((*it)[id].remote.use_dispatcher) {
            (*it)[id].remote.renders.clear();
        }        
#ifdef USE_MLOG
        (*it)[id].logger.Close();
#endif
        if (it->count(id) != 0) 
        {
            it->erase(id);
            jinfo("sessions erase id[%s] size: %d", id.c_str(), it->size());
        }
    }
    jinfo("After releaseSession[%s], audio: %d, video: %d", id.c_str(), sessions_[AUDIO].size(), sessions_[VIDEO].size());
    return MediaManagerInterface::MM_SUCCESS; 
}

int MediaManagerInterfaceImp::releaseSession(const std::string& id, bool isVideo) {
    // CHECK_AUDIO_SESSION_EXIST;
    if (!isVideo && sessions_[AUDIO].find(id) == sessions_[AUDIO].end())
    { 
        LogW("Please AUDIO CreateSession %s first", id.c_str());
        return MediaManagerInterface::MM_SESSION_NOT_FOUND;
    }
    if (isVideo && sessions_[VIDEO].find(id) == sessions_[VIDEO].end()) 
    { 
        LogW("Please VIDEO CreateSession %s first", id.c_str()); 
        return MediaManagerInterface::MM_SESSION_NOT_FOUND; 
    }
    jinfo("releaseSession %s [%s]", isVideo ? "VIDEO" : "AUDIO",  id.c_str());
    auto &session = sessions_[isVideo ? VIDEO : AUDIO];
    for (auto it2 = session[id].local.pipelines.begin(); it2 != session[id].local.pipelines.end(); it2++) {
        (*it2)->stop();
    }
    for (auto it2 = session[id].remote.pipelines.begin(); it2 != session[id].remote.pipelines.end(); it2++) {
        (*it2)->stop();
    }
    if (session[id].local.use_mixer) {
        session[id].local.capturers.clear();
        session[id].local.processors.clear();
        session[id].local.mixer.reset();
    }
    if (session[id].remote.use_dispatcher) {
        session[id].remote.renders.clear();
    }
#ifdef USE_MLOG
    session[id].logger.Close();
#endif
    if (session.count(id) != 0) {
        session.erase(id);
        jinfo("%s sessions erase id[%s] left size: %d", isVideo ? "VIDEO" : "AUDIO", id.c_str(), session.size());
    }
    jinfo("After releaseSession[%s], audio: %d, video: %d", id.c_str(), sessions_[AUDIO].size(), sessions_[VIDEO].size());
    return MediaManagerInterface::MM_SUCCESS; 
}

int MediaManagerInterfaceImp::clearSessions() {
    jinfo("clearSessions audio: %d video: %d\n", sessions_[AUDIO].size(), sessions_[VIDEO].size());
    std::unordered_set<std::string> to_clear;
    if (!sessions_[AUDIO].empty()) {
        jinfo("audio sesion size: %d", sessions_[AUDIO].size());
        for (auto &kv: sessions_[AUDIO]) {
            to_clear.emplace(kv.first);
        }
    }

    // NOTE: 释放可能存在的只携带视频的会话
#ifdef ENABLE_VIDEO
    if (!sessions_[VIDEO].empty()) {
        jinfo("video sesion size: %d", sessions_[VIDEO].size());
        for (auto &kv : sessions_[VIDEO]) {
            to_clear.emplace(kv.first);
        }
    }
#endif
    for (const auto &id: to_clear) {
        std::string poolId;
        if (isInPool(poolId, id) || isWorkingId(poolId, id)) {
            removeFromMediaPool(poolId, id);
        }
    }
    for (const auto &id: to_clear) {
        releaseSession(id);
    }
    return 0;
}

void MediaManagerInterfaceImp::getPriorityCodecs(const std::string& id, std::list<CodecInst>& audioCodecs, std::list<CodecInst>& videoCodecs)
{
    if (codecs_[AUDIO].empty())
    {
        GetSupportAudioCodecs();
    }
    if (sessions_[AUDIO][id].codecs.empty())
    {
        audioCodecs = codecs_[AUDIO];
    }
    else
    {
        audioCodecs = sessions_[AUDIO][id].codecs;
    }
#ifdef ENABLE_VIDEO
    if (codecs_[VIDEO].empty())
    {
        GetSupportVideoCodecs();
    }
    if (sessions_[VIDEO][id].codecs.empty())
    {
        videoCodecs = codecs_[VIDEO];
    }
    else
    {
        videoCodecs = sessions_[VIDEO][id].codecs;
    }
#endif
}

int MediaManagerInterfaceImp::getLocalSDP(const std::string& id, std::string& sdp, bool isOffer, const std::string& jsonParams)
{
    CHECK_AUDIO_SESSION_EXIST;
    std::list<CodecInst> audioCodecs;
    std::list<CodecInst> videoCodecs;
    getPriorityCodecs(id, audioCodecs, videoCodecs);
    return getLocalSDP(id, sdp, isOffer, jsonParams, audioCodecs, videoCodecs, false);
}

void MediaManagerInterfaceImp::generateRtpMapFromPayloads(nlohmann::json& rtp, std::string payloads)
{
    if (rtp.size() == 0)
    {
        payloads += " ";
        auto pos = payloads.find(' ');
        while (pos != std::string::npos)
        {
            std::string sub = payloads.substr(0, pos);
            int payload = atoi(sub.c_str());
            CodecInst inst;
            if (getCodecInst(getFrameFormat(payload), inst))
            {
                nlohmann::json c;
                c["codec"] = inst.name;
                c["payload"] = inst.pt;
                c["rate"] = inst.rate;
                rtp.push_back(c);
            }
            payloads = payloads.substr(pos + 1, payloads.size() - pos - 1);
            pos = payloads.find(' ');
        }
    }
}

nlohmann::json MediaManagerInterfaceImp::generateRtpMapSortByPayloads(nlohmann::json& rtp, std::string payloads) {
    auto payload_arr = sdptransform::parsePayloads(payloads);
    nlohmann::json res;
    // TODO: DRY，可去掉判断
    if (rtp.size() == 0) {
        for (int payload: payload_arr) {
            CodecInst inst;
            if (getCodecInst(getFrameFormat(payload), inst))
            {
                nlohmann::json c;
                c["codec"] = inst.name;
                c["payload"] = inst.pt;
                c["rate"] = inst.rate;
                c["chn"] = inst.chn;
                res.push_back(c);
            }
        }
    } else {
        for (int payload: payload_arr) {
            bool found = false;
            for (size_t i = 0; i < rtp.size(); i++) {
                if (rtp[i].contains("payload") && rtp[i]["payload"] == payload) {
                    found = true;
                    res.push_back(rtp[i]);
                    break;
                }
            }
            if (!found && payload < 96) {
                CodecInst inst;
                if (getCodecInst(getFrameFormat(payload), inst))
                {
                    nlohmann::json c;
                    c["codec"] = inst.name;
                    c["payload"] = inst.pt;
                    c["rate"] = inst.rate;
                    c["chn"] = inst.chn;
                    res.push_back(c);
                }                
            }
        }
    }
    return res;
}

bool MediaManagerInterfaceImp::hasCodecName(nlohmann::json& rtp, const std::string &name, int &payloadType) {
    if (rtp.is_array()) {
        for (size_t i = 0; i < rtp.size(); i++) {
            if (rtp[i].contains("codec")) {
                std::string codec_name = rtp[i]["codec"];
                if (strcasecmp(codec_name.c_str(), name.c_str()) == 0 && rtp[i].contains("payload")) {
                    payloadType = rtp[i]["paylaod"];
                    return true;
                }
            }
        }
    }
    return false;
}

int MediaManagerInterfaceImp::processAnswer(const std::string& id, const std::string& remotesdp, const std::string& jsonParams)
{
    LogI("onAnswer\n%s", remotesdp.c_str());
    // 主叫应答，优先采用对方的编码
    auto sdp = sdptransform::parse(remotesdp);
    for (size_t i = 0; i < sdp["media"].size(); i++)
    {
        int index = 0;
        int ptime = 20;
        if (sdp["media"][i]["type"] == "audio")
        {
            ptime = sessions_[AUDIO][id].local.ptime;
            if (sdp["media"][i].contains("ptime"))
            {
                ptime = sdp["media"][i]["ptime"];
            }
        }
        else
        {
            index = 1;
        }
        sessions_[index][id].lastSdp = remotesdp;

        std::string payloads = sdp["media"][i]["payloads"];
        generateRtpMapFromPayloads(sdp["media"][i]["rtp"], payloads);
        // TODO：对端已经协商成功，或许可以直接使用优先级最高的编码就行
        auto sorted_rtpmap = generateRtpMapSortByPayloads(sdp["media"][i]["rtp"], payloads);
        nlohmann::json params;
        if (sdp["media"][i].contains("fmtp"))
            params = sdp["media"][i]["fmtp"];
        jinfo("rtpmap: %s", sorted_rtpmap.dump().c_str());

        if (sdp["media"][i].contains("iceUfrag") && sdp["media"][i].contains("icePwd") && sdp["media"][i].contains("candidates"))
        {
            nlohmann::json j;
            j["iceUfrag"] = sdp["media"][i]["iceUfrag"];
            j["icePwd"] = sdp["media"][i]["icePwd"];
            j["candidates"] = sdp["media"][i]["candidates"];
            sessions_[index][id].remote.stunSdp = j.dump();
        }

        if (sessions_[index][id].local.dtls)
        {
            // 检查是否有DTLS标记
            if (!sdp["media"][i].contains("fingerprint"))
            {
                A_LogW_F("MM_NEGOTIATE_FAIL");
                return MediaManagerInterface::MM_NEGOTIATE_FAIL;
            }
            else
            {
                sessions_[index][id].remote.dtls = true;
                sessions_[index][id].remote.fingerprint = sdp["media"][i]["fingerprint"]["hash"];
                std::string dtls_dir = sdp["media"][i]["setup"];
                if (dtls_dir == "active")
                {
                    sessions_[index][id].local.isServer = true;
                    sessions_[index][id].remote.isServer = false;
                }
                else if (dtls_dir == "passive")
                {
                    sessions_[index][id].local.isServer = false;
                    sessions_[index][id].remote.isServer = true;
                }
            }
        }
#ifdef ENABLE_VIDEO
        for (size_t type = 0; type < VIDEO + 1; type++)
#else
        for (size_t type = 0; type < VIDEO; type++)
#endif
        {
            for (size_t j = 0; j < sorted_rtpmap.size(); j++)
            {
                // CodecInst inst = codecInstFromJson(sorted_rtpmap[j]);
                std::string selected_fmtp;
                CodecInst inst = codecInstFromJsonWithFmtp(sorted_rtpmap[j], params, selected_fmtp);
                if (isSupportCodec(inst, (CodecType)type))
                {
                    std::string ip = sdp["origin"]["address"];
                    if (sdp.contains("connection") && sdp["connection"].contains("ip")) {
                        ip = sdp["connection"]["ip"];
                    }
                    if (sdp["media"][i].contains("connection") && sdp["media"][i]["connection"].contains("ip"))
                    {
                        ip = sdp["media"][i]["connection"]["ip"];
                    }
                    std::string dir = "sendrecv";
                    if (sdp["media"][i].contains("direction"))
                    {
                        dir = sdp["media"][i]["direction"];
                    }
                    sessions_[type][id].local.payload_type = getPT(inst.fmt, inst.profile);
                    sessions_[type][id].remote.payload_type = inst.pt;
                    // TODO: 现使用h323时发送和接收pt相反，统一一下
                    if (sessions_[type][id].is_h323) {
                        sessions_[type][id].local.payload_type = inst.pt;
                        sessions_[type][id].remote.payload_type = getPT(inst.fmt, inst.profile);                     
                    }
                    sessions_[type][id].remote.ptime = ptime;
                    makeRemoteSession(id, inst, ip, sdp["media"][i]["port"], dir, (CodecType)type);
#ifdef ENABLE_VIDEO
                    if (type == VIDEO && !selected_fmtp.empty()) {
                        auto max_frame_rate = SdpFmtpUtils::PsrseSdpForMaxFrameRate(selected_fmtp);
                        auto max_frame_size = SdpFmtpUtils::PsrseSdpForMaxFrameSize(selected_fmtp);
                        if (max_frame_rate > 0) {
                            sessions_[type][id].local.max_frame_rate = max_frame_rate;
                        } else if (max_frame_size > 0) {
                            // TODO: calculate frame rate from frame size and resolution.
                        }
                        if (max_frame_size > 0) {
                            sessions_[type][id].local.max_frame_size = max_frame_size;
                        }
                        jinfo(
                            "ProcessAnswer: sessions_[%s] max_frame_rate = %u, max_frame_size = %u", id.c_str(), sessions_[type][id].local.max_frame_rate,
                            sessions_[type][id].local.max_frame_size);
                    }
#endif
                    jinfo("############[%d] dir = %s", type, dir.c_str());
                    break;
                }
            }
        }
#ifdef ENABLE_VIDEO
        if (sdp["media"][i]["type"] == "video")
        {
            int payload_type = ULPFEC_90000_PT;
            if (sessions_[VIDEO][id].local.video_fec_enabled && hasCodecName(sdp["media"][i]["rtp"], "ulpfec", payload_type)) {
                sessions_[VIDEO][id].remote.red_payload_type = payload_type;
            } else {
                sessions_[VIDEO][id].local.video_fec_enabled = false;
            }

            sessions_[VIDEO][id].remote.mediaPart = sdp["media"][i];
        }
#endif
    }
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::processOffer(const std::string &id, const std::string &remotesdp, std::string &localSdp, const std::string &jsonParams)
{
    LogI("onOffer\n %s", remotesdp.c_str());
    nlohmann::json params;
    if (jsonParams != "") params = nlohmann::json::parse(jsonParams);
    // 被叫
    // 从自己的优选编码表里去除对端的编码表中不支持的
    std::list<CodecInst> codecs[2];
    getPriorityCodecs(id, codecs[AUDIO], codecs[VIDEO]);
#ifdef ENABLE_VIDEO
    for (auto &codec: codecs[VIDEO]) {
        jinfo("Video supported codec type: %s %d %d", codec.name, codec.profile, codec.pt);
    }
    for (size_t type = 0; type < VIDEO + 1; type++) {
#else
    for (size_t type = 0; type < VIDEO; type++) {
#endif
        if (sessions_[type][id].negotiated) {
            jinfo("sessions_[type][id].local.codec %s", sessions_[type][id].local.codec.name);
            for (auto it = codecs[type].begin(); it != codecs[type].end(); it++) {
                if (*it == sessions_[type][id].local.codec) {
                    if (strcasecmp(it->name, "G7221") == 0 && sessions_[type][id].local.codec.bitrate != it->bitrate) {
                        continue;
                    }
                    auto inst = *it;
                    codecs[type].erase(it);
                    codecs[type].push_front(inst);
                    break;
                }
            }
        }
    }
    int ret = MediaManagerInterface::MM_SUCCESS;
    struct TempMediaInfo
    {
        CodecInst inst;
        std::string ip;
        int port = -1;
        std::string dir;
        std::list<CodecInst> codecs;
        nlohmann::json mjson;
        nlohmann::json params;  // fmtp
        bool dtls = false;
        bool isServer = false;
        std::string fingerprint;
    };
    TempMediaInfo info[2];
    auto sdp = sdptransform::parse(remotesdp);
    LogI("onOffer json\n %s", sdp.dump().c_str());
    bool found_video = false;
    nlohmann::json dir;
    for (size_t i = 0; i < sdp["media"].size(); i++)
    {
        // TODO: To process "application", "text" and "message"
        std::string media_type = sdp["media"][i]["type"];
        if (media_type != "audio" && media_type != "video") {
            jwarn("Unknown media type: %s", media_type.c_str());
            continue;
        }
        if (found_video && media_type == "video") continue;
        if (media_type == "video") found_video = true;
        int index = sdp["media"][i]["type"] == "audio" ? 0 : 1;
        sessions_[index][id].lastSdp = remotesdp;
        if (sdp["media"][i].contains("fingerprint")) {
            info[index].dtls = true;
            info[index].fingerprint = sdp["media"][i]["fingerprint"]["hash"];
            sessions_[index][id].remote.dtls = true;
            sessions_[index][id].remote.fingerprint = info[index].fingerprint;
            std::string dtls_dir = sdp["media"][i]["setup"];
            if (dtls_dir == "active") {
                sessions_[index][id].local.isServer = true;
                sessions_[index][id].remote.isServer = false;
            } else if (dtls_dir == "passive") {
                sessions_[index][id].local.isServer = false;
                sessions_[index][id].remote.isServer = true;
            }
        }
        for (size_t i = 0; i < sdp["invalid"].size(); ++i) {
            // a=roomInfo:{"id" : 1, "ip" : "*********", "port" : 8765}
            if (StartsWith(sdp["invalid"][i]["value"], "roomInfo:")) {
                std::string str = sdp["invalid"][i]["value"];
                std::string roomInfo = str.substr(strlen("roomInfo:"));
                jinfo("roominfo: %s", roomInfo.c_str());
                nlohmann::json j = nlohmann::json::parse(roomInfo);
                if (j.contains("ip")) {
                    sessions_[index][id].local.multicastIP = j["ip"];
                }
                if (j.contains("port")) {
                    sessions_[index][id].local.multicastPort = j["port"];
                }
                if (j.contains("id")) {
                    sessions_[index][id].local.roomid = j["id"];
                }
                jinfo(
                    "##############id: %s, ip: %s, port: %d, roomid: %d", id, sessions_[index][id].local.multicastIP.c_str(), sessions_[index][id].local.multicastPort,
                    sessions_[index][id].local.roomid);
                break;
            }
        }
        // if (index == AUDIO && sdp["media"][i].contains("roomInfo"))
		// {
		// 	std::string roomInfo = sdp["media"][i]["roomInfo"];
        //     jinfo("roominfo: %s", roomInfo.c_str());
		// 	nlohmann::json j = nlohmann::json::parse(roomInfo);
		// 	if (j.contains("ip"))
		// 	{
		// 		sessions_[index][id].local.multicastIP = j["ip"];
		// 	}
		// 	if (j.contains("port"))
		// 	{
		// 		sessions_[index][id].local.multicastPort = j["port"];
		// 	}
		// 	if (j.contains("roomid"))
		// 	{
		// 		sessions_[index][id].local.roomid = j["roomid"];
		// 	}
		// }

        if (index == VIDEO)
        {
            sessions_[index][id].remote.mediaPart = sdp["media"][i];
        }

        info[index].mjson = sdp["media"][i]["rtp"];
        info[index].params = sdp["media"][i]["fmtp"];
        info[index].ip = sdp["origin"]["address"];
        if (sdp.contains("connection") && sdp["connection"].contains("ip"))
        {
            info[index].ip = sdp["connection"]["ip"];
        }
        if (sdp["media"][i].contains("connection") && sdp["media"][i]["connection"].contains("ip"))
        {
            info[index].ip = sdp["media"][i]["connection"]["ip"];
        }
        info[index].port = sdp["media"][i]["port"];
        info[index].dir = "sendrecv";
        if (sdp["media"][i].contains("direction"))
        {
            info[index].dir = sdp["media"][i]["direction"];
        }
        if (info[index].dir == "sendonly")
        {
            dir[media_type] = "recvonly";
            jinfo("#############################media_type[%s] dir = recvonly", media_type.c_str());
        }
        else if (info[index].dir == "recvonly")
        {
            dir[media_type] = "sendonly";
            jinfo("#############################media_type[%s] dir = sendonly", media_type.c_str());
        }
        std::string payloads = sdp["media"][i]["payloads"];
        // generateRtpMapFromPayloads(info[index].mjson, payloads);

        info[index].mjson = generateRtpMapSortByPayloads(sdp["media"][i]["rtp"], payloads);
        jinfo("processOffer rtpmap: %s", info[index].mjson.dump().c_str());
    }
    params["direction"] = dir;
    params["found_video"] = found_video;
    std::vector<bool> found_codec(VIDEO + 1, false);
#ifdef ENABLE_VIDEO
    for (size_t type = 0; type < VIDEO + 1; type++)
#else
    for (size_t type = 0; type < VIDEO; type++)
#endif
    {
        for (auto &codec: codecs[type]) {
            jinfo("codecs: name %s pt %d samplerate %d profile %d", codec.name, codec.pt, codec.rate, codec.profile);
        }
        for (auto it = codecs[type].begin(); it != codecs[type].end(); it++)
        {
            bool isFound = false;
            for (size_t i = 0; i < info[type].mjson.size(); i++)
            {
                std::string selected_fmtp;
                CodecInst inst = codecInstFromJsonWithFmtp(info[type].mjson[i], info[type].params, selected_fmtp);
                LogI("%s codec %s %d %d", (type == AUDIO ? "audio" : "video"), inst.name, inst.profile, inst.pt);
                if (inst == *it)
                {
                    if (strcasecmp(inst.name, "G7221") == 0 && inst.bitrate != it->bitrate) {
                        continue;
                    }
                    sessions_[type][id].local.payload_type = getPT(inst.fmt, inst.profile);
                    sessions_[type][id].remote.payload_type = inst.pt;
                    // TODO: 现使用h323时发送和接收pt相反，最好统一一下
                    if (sessions_[type][id].is_h323) {
                        sessions_[type][id].local.payload_type = inst.pt;
                        sessions_[type][id].remote.payload_type = getPT(inst.fmt, inst.profile);                     
                    }
                    inst.pt = getPT(inst.fmt, inst.profile);
                    info[type].inst = inst;
                    isFound = true;
#ifdef ENABLE_VIDEO
                    if (type == VIDEO && !selected_fmtp.empty()) {
                        auto max_frame_rate = SdpFmtpUtils::PsrseSdpForMaxFrameRate(selected_fmtp);
                        auto max_frame_size = SdpFmtpUtils::PsrseSdpForMaxFrameSize(selected_fmtp);
                        if (max_frame_rate > 0) {
                            sessions_[type][id].local.max_frame_rate = max_frame_rate;
                        } else if (max_frame_size > 0) {
                            // TODO: calculate frame rate from frame size and resolution.
                        }
                        if (max_frame_size > 0) {
                            sessions_[type][id].local.max_frame_size = max_frame_size;
                        }
                        jinfo(
                            "processOffer: sessions_[%s] max_frame_rate = %u, max_frame_size = %u", id.c_str(), sessions_[type][id].local.max_frame_rate,
                            sessions_[type][id].local.max_frame_size);
                    }
#endif
                    jinfo("negotiated codec name %s pt %d samplerate %d", inst.name, inst.pt, inst.rate);
                    break;
                }
            }
            if (isFound)
            {
                found_codec[type] = true;
                info[type].codecs.push_back(info[type].inst);
                break;
            }
        }
    }
#ifdef ENABLE_VIDEO
    {
        int payload_type = ULPFEC_90000_PT;
        if (sessions_[VIDEO][id].local.video_fec_enabled && hasCodecName(info[VIDEO].mjson, "ulpfec", payload_type)) {
            sessions_[VIDEO][id].remote.red_payload_type = payload_type;
        } else {
            sessions_[VIDEO][id].local.video_fec_enabled = false;
        }
    }
#endif
    if (!found_codec[AUDIO] && !found_codec[VIDEO]) return MediaManagerInterface::MM_NO_SUPPOETED_CODEC;
    ret = getLocalSDP(id, localSdp, true, params.dump(), info[AUDIO].codecs, info[VIDEO].codecs, info[AUDIO].dtls);
    if (ret == MediaManagerInterface::MM_SUCCESS)
    {
#ifdef ENABLE_VIDEO
        for (size_t type = 0; type < VIDEO + 1; type++)
#else
        for (size_t type = 0; type < VIDEO; type++)
#endif
        {
            makeRemoteSession(id, info[type].inst, info[type].ip, info[type].port, info[type].dir, (CodecType)type);
        }
    }
    return ret;
}

bool MediaManagerInterfaceImp::isOffer(const std::string& id)
{
    if (sessions_[AUDIO].find(id) != sessions_[AUDIO].end() && sessions_[AUDIO][id].callstate == MediaSession::CALL_ANSWER)
    {
        return false;
    }
    if (sessions_[VIDEO].find(id) != sessions_[VIDEO].end() && sessions_[VIDEO][id].callstate == MediaSession::CALL_ANSWER)
    {
        return false;
    }
    return true;
}

int MediaManagerInterfaceImp::setRemoteSDP(const std::string& id, const std::string& remotesdp, std::string& localSdp, const std::string& jsonParams, bool startSession)
{
    CHECK_AUDIO_SESSION_EXIST;
    int ret = MediaManagerInterface::MM_SUCCESS;
    if (sessions_[AUDIO].find(id) != sessions_[AUDIO].end() && sessions_[AUDIO][id].lastSdp == remotesdp)
    {
        return MediaManagerInterface::MM_INVALID_PARAMETER;
    }
#ifdef ENABLE_VIDEO
    if (sessions_[VIDEO].find(id) != sessions_[VIDEO].end() && sessions_[VIDEO][id].lastSdp == remotesdp)
    {
        return MediaManagerInterface::MM_INVALID_PARAMETER;
    }
#endif
    if (!isOffer(id))
    {
        ret = processAnswer(id, remotesdp, jsonParams);
    }
    else
    {
        ret = processOffer(id, remotesdp, localSdp, jsonParams);
    }
    if (ret == MediaManagerInterface::MM_SUCCESS)
    {
        ret = doMediaPipeline(id);
        sessions_[AUDIO][id].callstate = MediaSession::CALL_COMPLETED;
        sessions_[VIDEO][id].callstate = MediaSession::CALL_COMPLETED;
    }
    else
    {
        A_LogW_F("doMediaPipeline fail");
    }
    return ret;
}

std::string MediaManagerInterfaceImp::GetDefaultIP()
{
    return getIP("");
}

int MediaManagerInterfaceImp::startUplinkAudioFile(const std::string& id, const std::string& path, const std::string& jsonParams)
{
    CHECK_AUDIO_SESSION_EXIST;
    if (config_["audio"].contains("uplink") && config_["audio"]["uplink"].contains("file-src") && config_["audio"]["uplink"]["file-src"].contains("name"))
    {
        FramePipeline::Ptr encoder;
        for (auto pipeline: sessions_[AUDIO][id].local.pipelines)
        {
            if (AudioFrameEncoder::IsAudioFrameEncoder(pipeline))
            {
                encoder = pipeline;
                break;
            }
        }
        if (encoder)
        {
            std::string fileSourceName = config_["audio"]["uplink"]["file-src"]["name"];
            LogI("CreateFileSource %s", fileSourceName.c_str());
            sessions_[AUDIO][id].local.file = FileFramePipeline::CreateFileSource(fileSourceName, jsonParams);
            if (sessions_[AUDIO][id].local.file)
            {
                auto srcs = encoder->getAudioSources();
                for (auto src: srcs)
                {
                    auto source = src.lock();
                    if (source)
                    {
                        source->removeAudioDestination(encoder);
                    }
                }
                sessions_[AUDIO][id].local.file->addAudioDestination(encoder);
                sessions_[AUDIO][id].local.temps = srcs;
            }
            else
            {
                LogI("CreateFileSource %s fail", fileSourceName.c_str());
            }
        }
    }
    else
    {
        return MediaManagerInterface::MM_INVALID_PARAMETER;
    }
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::stopUplinkAudioFile(const std::string& id)
{
    CHECK_AUDIO_SESSION_EXIST;
    if (sessions_[AUDIO][id].local.file)
    {
        auto dsts = sessions_[AUDIO][id].local.file->getAudioDestinations();
        for (auto dst : dsts)
        {
            auto dest = dst.lock();
            if (dest)
            {
                sessions_[AUDIO][id].local.file->removeAudioDestination(dest);
            }
        }
        for (auto src : sessions_[AUDIO][id].local.temps)
        {
            auto source = src.lock();
            if (source)
            {
                for (auto dst : dsts)
                {
                    auto dest = dst.lock();
                    if (dest)
                    {
                        source->addAudioDestination(dest);
                    }
                }
            }
        }
        sessions_[AUDIO][id].local.file.reset();
        sessions_[AUDIO][id].local.temps.clear();
    }
    else
    {
        return MediaManagerInterface::MM_INVALID_PARAMETER;
    }
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::startUplinkVideoFile(const std::string& id, const std::string& path, const std::string& jsonParams)
{
#ifdef ENABLE_VIDEO
    CHECK_VIDEO_SESSION_EXIST;
    if (config_["video"].contains("uplink") && config_["video"]["uplink"].contains("file-src") && config_["video"]["uplink"]["file-src"].contains("name"))
    {
        FramePipeline::Ptr rtp;
        for (auto pipeline: sessions_[VIDEO][id].local.pipelines)
        {
            if (RtpEngine::IsRTPEngine(pipeline))
            {
                rtp = pipeline;
                break;
            }
        }
        if (rtp)
        {
            std::string fileSourceName = config_["video"]["uplink"]["file-src"]["name"];
            LogI("CreateFileSource %s", fileSourceName.c_str());
            sessions_[VIDEO][id].local.file = FileFramePipeline::CreateFileSource(fileSourceName, jsonParams);
            if (sessions_[VIDEO][id].local.file)
            {
                auto srcs = rtp->getVideoSources();
                for (auto src: srcs)
                {
                    auto source = src.lock();
                    if (source)
                    {
                        source->removeVideoDestination(rtp);
                    }
                }
                sessions_[VIDEO][id].local.file->addVideoDestination(rtp);
            }
            else
            {
                LogI("CreateFileSource %s fail", fileSourceName.c_str());
            }
        }
    }
    else
    {
        return MediaManagerInterface::MM_INVALID_PARAMETER;
    }
#endif
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::stopUplinkVideoFile(const std::string& id)
{
#ifdef ENABLE_VIDEO
    CHECK_VIDEO_SESSION_EXIST;
    if (sessions_[VIDEO][id].local.file)
    {
        auto dsts = sessions_[VIDEO][id].local.file->getVideoDestinations();
        for (auto dst : dsts)
        {
            auto dest = dst.lock();
            if (dest)
            {
                sessions_[VIDEO][id].local.file->removeVideoDestination(dest);
            }
        }
        for (auto src : sessions_[VIDEO][id].local.temps)
        {
            auto source = src.lock();
            if (source)
            {
                for (auto dst : dsts)
                {
                    auto dest = dst.lock();
                    if (dest)
                    {
                        source->addVideoDestination(dest);
                    }
                }
            }
        }
        sessions_[VIDEO][id].local.file.reset();
        sessions_[VIDEO][id].local.temps.clear();
    }
    else
    {
        return MediaManagerInterface::MM_INVALID_PARAMETER;
    }
#endif
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::startDownlinkAudioFile(const std::string& id, const std::string& path, const std::string& jsonParams)
{
    CHECK_AUDIO_SESSION_EXIST;
    jinfo("startDownlinkAudioFile [%s] %s %s", id.c_str(), path.c_str(), jsonParams.c_str());
    if (sessions_[AUDIO].count(id) != 0 && sessions_[AUDIO][id].remote.file) {
        jwarn("[%s] filesource existed.", id.c_str());
        return MediaManagerInterface::MM_SUCCESS;

    }
    if (config_["audio"].contains("downlink") && config_["audio"]["downlink"].contains("file-src") && config_["audio"]["downlink"]["file-src"].contains("name"))
    {
        FramePipeline::Ptr decoder;
        for (auto pipeline: sessions_[AUDIO][id].remote.pipelines)
        {
            if (AudioFrameDecoder::IsAudioFrameDecoder(pipeline))
            {
                decoder = pipeline;
                break;
            }
        }
        if (decoder)
        {
            std::string fileSourceName = config_["audio"]["downlink"]["file-src"]["name"];
            LogI("CreateFileSource %s", fileSourceName.c_str());
            sessions_[AUDIO][id].remote.file = FileFramePipeline::CreateFileSource(fileSourceName, jsonParams);
            if (sessions_[AUDIO][id].remote.file)
            {
                auto dsts = decoder->getAudioDestinations();
                for (auto dst: dsts)
                {
                    auto dest = dst.lock();
                    if (dest)
                    {
                        decoder->removeAudioDestination(dest);
                        sessions_[AUDIO][id].remote.file->addAudioDestination(dest);
                        LogI("[%s] -> render[%s]", sessions_[AUDIO][id].remote.file->name().c_str(), dest->name().c_str());
                    }
                }
            }
            else
            {
                LogI("CreateFileSource %s fail", fileSourceName.c_str());
            }
        }
        else
        {
            // 会话还没建立
            std::string fileSourceName = config_["audio"]["downlink"]["file-src"]["name"];
            sessions_[AUDIO][id].remote.file = FileFramePipeline::CreateFileSource(fileSourceName, jsonParams);
            std::shared_ptr<AudioFrameRender> render =  MediaManagerInterfaceImp::createAudioRender(id);
            sessions_[AUDIO][id].remote.file->addAudioDestination(render);
            render->resetConnectState(true);
            // std::string codec; 
            // if (config_["audio"]["downlink"]["file-src"].contains("codec"))
            // {
            //     codec = config_["audio"]["downlink"]["file-src"]["codec"];
            // }
            // std::list<CodecInst> codecs;
            // if (codec != "" &&  getCodecInst(codec.c_str(), codecs))
            // {
            //     std::shared_ptr<AudioFrameDecoder> decoder = MediaManagerInterfaceImp::createAudioDecoder(*codecs.begin());
            //     sessions_[AUDIO][id].remote.file->addAudioDestination(dest);
            // }
        }
    }
    else
    {
        return MediaManagerInterface::MM_INVALID_PARAMETER;
    }
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::stopDownlinkAudioFile(const std::string& id)
{
    jinfo("stopDownlinkAudioFile [%s]", id.c_str());
    CHECK_AUDIO_SESSION_EXIST;
    if (sessions_[AUDIO].count(id) != 0 && !sessions_[AUDIO][id].remote.file) {
        jwarn("[%s] filesource not existed.", id.c_str());
        return MediaManagerInterface::MM_INVALID_PARAMETER;;
    }
    if (sessions_[AUDIO][id].remote.file)
    {
        FramePipeline::Ptr decoder;
        for (auto pipeline: sessions_[AUDIO][id].remote.pipelines)
        {
            if (AudioFrameDecoder::IsAudioFrameDecoder(pipeline))
            {
                decoder = pipeline;
                break;
            }
        }
        auto dsts = sessions_[AUDIO][id].remote.file->getAudioDestinations();
        for (auto dst : dsts)
        {
            auto dest = dst.lock();
            if (dest)
            {
                sessions_[AUDIO][id].remote.file->removeAudioDestination(dest);
                if (decoder) decoder->addAudioDestination(dest);
            }
        }
        sessions_[AUDIO][id].remote.file.reset();
        sessions_[AUDIO][id].remote.temps.clear();
    }
    else
    {
        return MediaManagerInterface::MM_INVALID_PARAMETER;
    }
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::startDownlinkVideoFile(const std::string& id, const std::string& path, const std::string& jsonParams)
{
#ifdef ENABLE_VIDEO
    CHECK_VIDEO_SESSION_EXIST;
    if (config_["video"].contains("downlink") && config_["video"]["downlink"].contains("file-src") && config_["video"]["downlink"]["file-src"].contains("name"))
    {
        FramePipeline::Ptr decoder;
        for (auto pipeline: sessions_[VIDEO][id].remote.pipelines)
        {
            if (VideoFrameDecoder::IsVideoFrameDecoder(pipeline))
            {
                decoder = pipeline;
                break;
            }
        }
        if (decoder)
        {
            std::string fileSourceName = config_["video"]["downlink"]["file-src"]["name"];
            LogI("CreateFileSource %s", fileSourceName.c_str());
            sessions_[VIDEO][id].remote.file = FileFramePipeline::CreateFileSource(fileSourceName, jsonParams);
            if (sessions_[VIDEO][id].remote.file)
            {
                auto srcs = decoder->getVideoSources();
                for (auto src: srcs)
                {
                    auto source = src.lock();
                    if (source)
                    {
                        source->removeVideoDestination(decoder);
                    }
                }
                sessions_[VIDEO][id].remote.file->addVideoDestination(decoder);
                sessions_[VIDEO][id].remote.temps = srcs;
            }
            else
            {
                LogI("CreateFileSource %s fail", fileSourceName.c_str());
            }
        }
    }
    else
    {
        return MediaManagerInterface::MM_INVALID_PARAMETER;
    }
#endif
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::stopDownlinkVideoFile(const std::string& id)
{
#ifdef ENABLE_VIDEO
    CHECK_VIDEO_SESSION_EXIST;
    if (sessions_[VIDEO][id].remote.file)
    {
        FramePipeline::Ptr decoder;
        for (auto pipeline: sessions_[VIDEO][id].remote.pipelines)
        {
            if (VideoFrameDecoder::IsVideoFrameDecoder(pipeline))
            {
                decoder = pipeline;
                break;
            }
        }
        sessions_[VIDEO][id].remote.file->removeVideoDestination(decoder);

        auto srcs = sessions_[VIDEO][id].remote.temps;
        for (auto src : srcs)
        {
            auto source = src.lock();
            if (source)
            {
                source->addVideoDestination(decoder);
            }
        }
        sessions_[VIDEO][id].remote.file.reset();
        sessions_[VIDEO][id].remote.temps.clear();
    }
    else
    {
        return MediaManagerInterface::MM_INVALID_PARAMETER;
    }
#endif
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::doMediaPipeline(const std::string& id)
{
    LogI("========== doMediaPipeline %s ==========", id.c_str());
    int ret = MediaManagerInterface::MM_SUCCESS;
    if (sessions_[AUDIO].find(id) != sessions_[AUDIO].end())
    {
        switch (sessions_[AUDIO][id].dir)
        {
        case MediaSession::INACTIVE:
            ret = inactivePipeline(AUDIO, id);
            break;
        case MediaSession::SENDONLY:
            ret = sendOnlyPipeline(AUDIO, id);
            break;
        case MediaSession::RECVONLY:
            ret = recvOnlyPipeline(AUDIO, id);
            break;
        case MediaSession::SENDRECV:
            ret = sendrecvPipeline(AUDIO, id);
            break;
        default:
            LogI("audio [%s] dir(%d) unknown value", id.c_str(), sessions_[AUDIO][id].dir);
            ret = MediaManagerInterface::MM_INVALID_PARAMETER;
            break;
        }
    }

    if (ret != MediaManagerInterface::MM_SUCCESS)
    {
        jerror("doMediaPipeline AUDIO failed ret %d", ret);
        // return ret;
    }
#ifdef ENABLE_VIDEO
    if (sessions_[VIDEO].find(id) != sessions_[VIDEO].end())
    {
        switch (sessions_[VIDEO][id].dir)
        {
        case MediaSession::INACTIVE:
            ret = inactivePipeline(VIDEO, id);
            break;
        case MediaSession::SENDONLY:
            ret = sendOnlyPipeline(VIDEO, id);
            break;
        case MediaSession::RECVONLY:
            ret = recvOnlyPipeline(VIDEO, id);
            break;
        case MediaSession::SENDRECV:
            ret = sendrecvPipeline(VIDEO, id);
            break;
        default:
            LogI("video [%s] dir(%d) unknown value", id.c_str(), sessions_[VIDEO][id].dir);
            ret = MediaManagerInterface::MM_INVALID_PARAMETER;
            break;
        }
    }
    if (ret != MediaManagerInterface::MM_SUCCESS)
    {
        jerror("doMediaPipeline VIDEO failed ret %d", ret);
        // return ret;
    }
#endif
    return ret;
}

int MediaManagerInterfaceImp::inactivePipeline(CodecType type, const std::string& id)
{
    jinfo("inactivePipeline [%s][%s]", type == AUDIO? "audio" : "video", id.c_str());
#ifdef ENABLE_VIDEO
    if (type == VIDEO && sessions_[VIDEO][id].local.capturer) {
        auto &capturer = sessions_[VIDEO][id].local.capturer;
        for (auto &pipeline: sessions_[VIDEO][id].local.pipelines) {
            if (capturer->isVideoDestination(pipeline)) {
                capturer->removeVideoDestination(pipeline);
            }
        }
        for (auto &pipeline: sessions_[VIDEO][id].remote.pipelines) {
            if (capturer->isVideoDestination(pipeline)) {
                capturer->removeVideoDestination(pipeline);
            }
        }
    }
#endif
    if (!sessions_[type][id].local.pipelines.empty())
    {
        for (auto &pipeline : sessions_[type][id].local.pipelines)
        {
            pipeline->stop();
        }
    }
    sessions_[type][id].local.pipelines.clear();
    sessions_[type][id].local.rtcps.clear();
    sessions_[type][id].local.capturer.reset();
    sessions_[type][id].local.file.reset();
    sessions_[type][id].local.render.reset();
    if (sessions_[type][id].local.use_mixer) {
        sessions_[type][id].local.capturers.clear();
        sessions_[type][id].local.processors.clear();
        sessions_[type][id].local.mixer.reset();
    }

    std::string poolId = "";
    if (isInPool(poolId, id) || isWorkingId(poolId, id))
    {
        mediaPools_[poolId].pipelines[type].capturer_ref--;
        if (mediaPools_[poolId].pipelines[type].capturer_ref <= 0)
            mediaPools_[poolId].pipelines[type].capturer.reset();
    }
    if (!sessions_[type][id].remote.pipelines.empty())
    {
        for (auto &pipeline : sessions_[type][id].remote.pipelines)
        {
            pipeline->stop();
        }
    }
    sessions_[type][id].remote.pipelines.clear();
    sessions_[type][id].remote.rtcps.clear();
    sessions_[type][id].remote.capturer.reset();
    sessions_[type][id].remote.file.reset();
    sessions_[type][id].remote.render.reset();
    sessions_[type][id].remote.renders.clear();
    sessions_[type][id].local.state = MediaEndpoint::IDLE;
    sessions_[type][id].remote.state = MediaEndpoint::IDLE;
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::sendOnlyPipeline(CodecType type, const std::string& id)
{
    jinfo("sendOnlyPipeline [%s][%s]", type == AUDIO? "audio" : "video", id.c_str());
    int ret = MediaManagerInterface::MM_SUCCESS;
    if (!sessions_[type][id].remote.pipelines.empty())
    {
        for (auto &pipeline : sessions_[type][id].remote.pipelines)
        {
            if (RtpEngine::IsRTPEngine(pipeline)) {
              auto it = std::find(sessions_[type][id].local.pipelines.begin(), sessions_[type][id].local.pipelines.end(), pipeline);
              if (it == sessions_[type][id].local.pipelines.end()) pipeline->stop();
            } else {
                pipeline->stop();
            }
        }
    }
    sessions_[type][id].remote.pipelines.clear();
    sessions_[type][id].remote.rtcps.clear();
    sessions_[type][id].remote.capturer.reset();
    sessions_[type][id].remote.file.reset();
    sessions_[type][id].remote.render.reset();
    sessions_[type][id].remote.stream_monitor.reset();
    // if (sessions_[type][id].remote.use_dispatcher) 
    {
        sessions_[type][id].remote.renders.clear();
    }  
    if (sessions_[type][id].local.state != MediaEndpoint::WORKING)
    {
        switch (type)
        {
        case AUDIO:
            ret = addAudioSendPipeline(id);
            break;
#ifdef ENABLE_VIDEO
        case VIDEO:
            ret = addVideoSendPipeline(id);
            break;
#endif
        default:
            break;
        }
    }
    if (ret == MediaManagerInterface::MM_SUCCESS)
    {
        sessions_[type][id].local.state = MediaEndpoint::WORKING;
        sessions_[type][id].remote.state = MediaEndpoint::IDLE;
    }
    return ret;
}

int MediaManagerInterfaceImp::recvOnlyPipeline(CodecType type, const std::string& id)
{
    jinfo("recvOnlyPipeline [%s][%s]", type == AUDIO? "audio" : "video", id.c_str());
    int ret = MediaManagerInterface::MM_SUCCESS;
#ifdef ENABLE_VIDEO
    if (type == VIDEO && sessions_[VIDEO][id].local.capturer) {
        auto &capturer = sessions_[VIDEO][id].local.capturer;
        for (auto &pipeline: sessions_[VIDEO][id].local.pipelines) {
            if (VideoFrameEncoder::IsVideoFrameEncoder(pipeline)) {
                capturer->removeVideoDestination(pipeline);
                break;
            }
        }
    }
#endif
    if (!sessions_[type][id].local.pipelines.empty())
    {
        for (auto &pipeline : sessions_[type][id].local.pipelines)
        {
            if (RtpEngine::IsRTPEngine(pipeline)) {
              auto it = std::find(sessions_[type][id].remote.pipelines.begin(), sessions_[type][id].remote.pipelines.end(), pipeline);
              if (it == sessions_[type][id].local.pipelines.end()) pipeline->stop();
            } else {
                pipeline->stop();
            }
        }
    }
    sessions_[type][id].local.pipelines.clear();
    sessions_[type][id].local.rtcps.clear();
    sessions_[type][id].local.capturer.reset();
    sessions_[type][id].local.file.reset();
    sessions_[type][id].local.render.reset();
    if (sessions_[type][id].local.use_mixer) {
        sessions_[type][id].local.capturers.clear();
        sessions_[type][id].local.processors.clear();
        sessions_[type][id].local.mixer.reset();
    }
    std::string poolId = "";
    if (isInPool(poolId, id) || isWorkingId(poolId, id))
    {
        mediaPools_[poolId].pipelines[type].capturer_ref--;
        if (mediaPools_[poolId].pipelines[type].capturer_ref <= 0)
            mediaPools_[poolId].pipelines[type].capturer.reset();
    }
    if (sessions_[type][id].remote.state != MediaEndpoint::WORKING)
    {
        switch (type)
        {
        case AUDIO:
            ret = addAudioRecvPipeline(id);
            break;
#ifdef ENABLE_VIDEO
        case VIDEO:
            ret = addVideoRecvPipeline(id);
            break;
#endif
        default:
            break;
        }
    }
    if (ret == MediaManagerInterface::MM_SUCCESS)
    {
        sessions_[type][id].remote.state = MediaEndpoint::WORKING;
        sessions_[type][id].local.state = MediaEndpoint::IDLE;
    }
    return ret;
}

int MediaManagerInterfaceImp::sendrecvPipeline(CodecType type, const std::string& id)
{
    jinfo("sendrecvPipeline [%s][%s]", type == AUDIO? "audio" : "video", id.c_str());
    int ret = MediaManagerInterface::MM_SUCCESS;
    // TODO：优先创建媒体发送端，兼容回声消除模块（待优化）
    if (sessions_[type][id].local.state != MediaEndpoint::WORKING)
    {
        switch (type)
        {
        case AUDIO:
            ret = addAudioSendPipeline(id);
            break;
#ifdef ENABLE_VIDEO
        case VIDEO:
            ret = addVideoSendPipeline(id);
            break;
#endif
        default:
            break;
        }
    }
    // TODO: 判断是否connected，改变的参数
    else if (sessions_[type][id].local.codec_changed || sessions_[type][id].local.addr_changed) {
        switch (type)
        {
        case AUDIO:
            ret = updateAudioSendPipeline(id);
            break;
#ifdef ENABLE_VIDEO
        case VIDEO:
            ret = updateVideoSendPipeline(id);
            break;
#endif
        default:
            break;
        }
        sessions_[type][id].local.codec_changed = false;
    }
    if (ret == MediaManagerInterface::MM_SUCCESS)
    {
        sessions_[type][id].local.state = MediaEndpoint::WORKING;
    } else {
        LogW("addSendPipeline fail!");
    }

    if (sessions_[type][id].remote.state != MediaEndpoint::WORKING)
    {
        switch (type)
        {
        case AUDIO:
            ret = addAudioRecvPipeline(id);
            break;
#ifdef ENABLE_VIDEO
        case VIDEO:
            ret = addVideoRecvPipeline(id);
            // Test: 测试断流检测
            // setRemoteMediaTimeoutListener2(id, 6000, [](const std::string&, bool isVideo, bool connected) {
            //     jinfo("connected %d isVideo %d", connected, isVideo);
            // });
            break;
#endif
        default:
            break;
        }
    } 
    // TODO: 判断是否connected，改变的参数
    else if (sessions_[type][id].remote.codec_changed || sessions_[type][id].remote.addr_changed) {
        switch (type)
        {
        case AUDIO:
            ret = updateAudioRecvPipeline(id);
            break;
#ifdef ENABLE_VIDEO
        case VIDEO:
            ret = updateVideoRecvPipeline(id);
            break;
#endif
        default:
            break;
        }
        sessions_[type][id].remote.codec_changed = false;
    }
    if (ret == MediaManagerInterface::MM_SUCCESS)
    {
        sessions_[type][id].remote.state = MediaEndpoint::WORKING;
    }
    else
    {
        LogW("addRecvPipeline fail!");
        // return ret;
    }
    return ret;
}

int MediaManagerInterfaceImp::addAudioSendPipeline(const std::string &id) {
    FOOT_PRINT;
    int ret = MediaManagerInterface::MM_SUCCESS;
    if (sessions_[AUDIO][id].local.rtpPort && *sessions_[AUDIO][id].local.rtpPort == 0) {
        return ret;
    }
    if (!sessions_[AUDIO][id].local.enabled) {
        return ret;
    }
    if (config_.contains("audio") && sessions_[AUDIO][id].local.found_codec) {
        {
            std::string poolId = "";
            if (isInPool(poolId, id) && mediaPools_[poolId].workingId == "") {
                setWorkingId(poolId, id);
            }
        }
        do {
            jinfo("addAudioSendPipeline use-mixer: %d", sessions_[AUDIO][id].local.use_mixer);
            if (!sessions_[AUDIO][id].local.use_mixer) {
                auto capturer = createAudioCapture(id);
                if (!capturer) {
                    A_LogW_F("createAudioCapture fail!");
                    ret = MediaManagerInterface::MM_PIPELINE_FAIL;
                    break;
                } else {
                    nlohmann::json j;
                    j["dev"] = sessions_[AUDIO][id].local.dev;
                    j["aec"] = sessions_[AUDIO][id].local.aec;
                    // j["lec"] = sessions_[AUDIO][id].local.lec;
                    j["ns"] = sessions_[AUDIO][id].local.ns;
                    j["agc"] = sessions_[AUDIO][id].local.agc;
                    j["cng"] = sessions_[AUDIO][id].local.cng;
                    j["anti-howling"] = sessions_[AUDIO][id].local.anti_howling;
                    j["delay"] = sessions_[AUDIO][id].local.delay;
                    j["log"] = sessions_[AUDIO][id].local.log;
                    j["record-audio"] = sessions_[AUDIO][id].local.record_audio;
                    j["use-headset"] = sessions_[AUDIO][id].local.useHeadset;
                    j["mic-type"] = sessions_[AUDIO][id].local.mic_type;
                    if (sessions_[AUDIO][id].local.enableLP) {
                        j["mode"] = 1; // LP
                        j["localIP"] = GetDefaultIP();
                        j["bindPort"] = sessions_[AUDIO][id].local.bindPort;
                        j["multiIP"] = sessions_[AUDIO][id].local.multicastIP;
                        j["multiPort"] = sessions_[AUDIO][id].local.multicastPort;
                        j["muteNotLP"] = sessions_[AUDIO][id].local.muteNotLP;
                    }
                    capturer->updateParam(j.dump());
                    sessions_[AUDIO][id].local.capturer = capturer;
                }
                std::shared_ptr<FramePipeline> capturerNext = capturer;
                if (sessions_[AUDIO][id].local.use_uart) {
                    auto decoder = AudioFrameDecoder::CreateAudioDecoder("AudioFrameDecoderWrapper");
                    if (decoder) {
                        capturerNext = decoder;
                        capturer->addAudioDestination(capturerNext);
                        LogI(
                            "capturer([%s]-[%p]) -> capturerNext([%s]-[%p])", capturer->name().c_str(), capturer.get(), capturerNext->name().c_str(),
                            capturerNext.get());
                    } else {
                        LogW("create AudioFrameDecoderWrapper fail!");
                        ret = MediaManagerInterface::MM_PIPELINE_FAIL;
                        break;
                    }
                    auto controller = AudioFrameProcesser::CreateAudioProcesser("PCMGainController");
                    if (controller) {
                        capturerNext = controller;
                        sessions_[AUDIO][id].local.pipelines.insert(decoder);
                        decoder->addAudioDestination(capturerNext);
                        LogI(
                            "decoder([%s]-[%p]) -> capturerNext([%s]-[%p])", decoder->name().c_str(), decoder.get(), capturerNext->name().c_str(),
                            capturerNext.get());                        
                    } else {
                        LogW("create PCMGainController fail!");
                    }
                    {
                        nlohmann::json j;
                        j["timeoutMS"] = 1000;
                        auto concealer = std::make_shared<InterruptionConcealer>(j.dump());
                        if (concealer) {
                            sessions_[AUDIO][id].local.pipelines.insert(capturerNext);
                            capturerNext->addAudioDestination(concealer);
                            LogI(
                                "controller([%s]-[%p]) -> concealer([%s]-[%p])", capturerNext->name().c_str(), capturerNext.get(), concealer->name().c_str(),
                                concealer.get());
                            capturerNext = concealer;
                        } else {
                            LogW("create InterruptionConcealer fail!");
                        }
                    }
                }
                // TEST: 测试PCMGainController
                // {
                //     auto controller = AudioFrameProcesser::CreateAudioProcesser("PCMGainController");
                //     if (controller) {
                //         capturerNext = controller;
                //         capturer->addAudioDestination(capturerNext);
                //         LogI(
                //             "capturer([%s]-[%p]) -> capturerNext([%s]-[%p])", capturer->name().c_str(), capturer.get(), controller->name().c_str(),
                //             controller.get());
                //     } else {
                //         LogW("capturer PCMGainController fail!");
                //     }
                // }

                std::vector<std::shared_ptr<AudioFrameProcesser>> processers;
                ret = createAudioUplinkProcesser(id, processers);
                if (ret != MediaManagerInterface::MM_SUCCESS) {
                    A_LogW_F("createAudioUplinkProcesser fail!");
                    break;
                }

                auto encoder = createAudioEncoder(sessions_[AUDIO][id].local.codec);
                if (!encoder) {
                    A_LogW_F("createAudioEncoder fail!");
                    ret = MediaManagerInterface::MM_PIPELINE_FAIL;
                    break;
                }

                std::shared_ptr<FramePipeline> encoderPrev = encoder;
                if (!sessions_[AUDIO][id].local.use_uart) {
                    nlohmann::json j;
                    j["use-default-gain"] = false;
                    auto controller = AudioFrameProcesser::CreateAudioProcesser("PCMGainController");
                    if (controller) {
                        controller->addAudioDestination(encoderPrev);
                        LogI(
                            "controller([%s]-[%p]) -> encoderPrev([%s]-[%p])", controller->name().c_str(), controller.get(), encoderPrev->name().c_str(),
                            encoderPrev.get());
                        encoderPrev = controller;
                        sessions_[AUDIO][id].local.gainController = controller;
                        sessions_[AUDIO][id].local.pipelines.insert(encoderPrev);
                    } else {
                        LogW("create PCMGainController fail!");
                    }
                }

                auto rtp = createAudioRtp(id, true);
                if (!rtp) {
                    A_LogW_F("createAudioRtp fail!");
                    ret = MediaManagerInterface::MM_PIPELINE_FAIL;
                    break;
                }

                if (!processers.empty()) {
                    capturerNext->addAudioDestination(processers[0]);
                    A_LogI_F("capture -> %s", processers[0]->name().c_str());
                    for (size_t i = 0; i < processers.size() - 1; i++) {
                        processers[i]->addAudioDestination(processers[i + 1]);
                        A_LogI_F("processer [%s] -> [%s]", processers[i]->name().c_str(), processers[i + 1]->name().c_str());
                    }
                    A_LogI_F("[%s] -> encoderPrev[%s]", processers[processers.size() - 1]->name().c_str(), encoderPrev->name().c_str());
                    processers[processers.size() - 1]->addAudioDestination(encoderPrev);
                } else {
                    capturerNext->addAudioDestination(encoderPrev);
                    A_LogI_F("capture[%s] -> encoderPrev[%s]", capturerNext->name().c_str(), encoderPrev->name().c_str());

                }
                encoder->addAudioDestination(rtp);
                A_LogI_F("encoder[%s] -> rtp[%s]", encoder->name().c_str(), rtp->name().c_str());
                sessions_[AUDIO][id].local.pipelines.insert(capturer);
                sessions_[AUDIO][id].local.pipelines.insert(capturerNext);
                for (size_t i = 0; i < processers.size(); i++) {
                    sessions_[AUDIO][id].local.pipelines.insert(processers[i]);
                }
                sessions_[AUDIO][id].local.pipelines.insert(encoder);
                sessions_[AUDIO][id].local.pipelines.insert(rtp);
            } else {
                // 本地混音
                bool result = createAudioCapturers(id);
                if (result) {
                    auto mixer = createAudioMixer(id);
                    if (!mixer) {
                        A_LogW_F("createAudioMixer fail!");
                        ret = MediaManagerInterface::MM_PIPELINE_FAIL;
                        break;
                    }
                    if (isActiveStream(id)) {
                        for (auto &pair: sessions_[AUDIO][id].local.capturers) {
                            std::vector<std::shared_ptr<AudioFrameProcesser>> processers;
                            ret = createAudioUplinkProcesser(id, processers);
                            if (ret != MediaManagerInterface::MM_SUCCESS) {
                                A_LogW_F("createAudioUplinkProcesser %d fail!", pair.first);
                                continue;
                            }
                            auto &capturer = pair.second;
                            if (!processers.empty()) {
                                capturer->addAudioDestination(processers[0]);
                                A_LogI_F("[%d]capture -> %s-%p", pair.first, processers[0]->name().c_str(), processers[0].get());
                                for (size_t i = 0; i < processers.size() - 1; i++) {
                                    processers[i]->addAudioDestination(processers[i + 1]);
                                    A_LogI_F("processer [%s-%p] -> [%s-%p]", processers[i]->name().c_str(), processers[i].get(), processers[i + 1]->name().c_str(), processers[i + 1].get());
                                }
                                A_LogI_F("[%s-%p] -> mixer[%s-%p]", processers[processers.size() - 1]->name().c_str(), processers[processers.size() - 1].get(), mixer->name().c_str(), mixer.get());
                                processers[processers.size() - 1]->addAudioDestination(mixer);
                                for (size_t i = 0; i < processers.size(); i++) {
                                    sessions_[AUDIO][id].local.processors[pair.first].emplace_back(processers[i]);
                                }   
                            } else {
                                capturer->setGroupId(pair.first);
                                capturer->addAudioDestination(mixer);
                                A_LogI_F("dev[%d] capture[%s-%p] -> mixer[%s-%p]", pair.first, capturer->name().c_str(), capturer.get(), mixer->name().c_str(), mixer.get());
                            }
                        }
                    }
                    sessions_[AUDIO][id].local.mixer = mixer;
                    sessions_[AUDIO][id].local.pipelines.insert(mixer);
                    auto encoder = createAudioEncoder(sessions_[AUDIO][id].local.codec);
                    if (!encoder) {
                        A_LogW_F("createAudioEncoder fail!");
                        ret = MediaManagerInterface::MM_PIPELINE_FAIL;
                        break;
                    }

                    auto rtp = createAudioRtp(id, true);
                    if (!rtp) {
                        A_LogW_F("createAudioRtp fail!");
                        ret = MediaManagerInterface::MM_PIPELINE_FAIL;
                        break;
                    }
                    mixer->addAudioDestination(encoder);

                    // NOTE: 博鼎测试可测试性需求
                    // TEST: 博鼎测试可测试性需求
                    int external_hdmi = sessions_[AUDIO][id].local.external_hdmi;
                    if (external_hdmi >= 0 && external_hdmi <= 1) {
                        auto external_render = createDefaultAudioRender(external_hdmi, "AlsaAudioFrameRender");
                        if (external_render && sessions_[AUDIO][id].call_out) {
                            mixer->addAudioDestination(external_render);
                            sessions_[AUDIO][id].local.pipelines.insert(external_render);
                            A_LogI_F(
                                "mixer[%s-%p] -> render[%s-%p]", mixer->name().c_str(), mixer.get(), external_render->name().c_str(), external_render.get());
                        }
                    }
                    A_LogI_F("mixer[%s-%p] -> encoder[%s-%p]", mixer->name().c_str(), mixer.get(), encoder->name().c_str(), encoder.get());
                    encoder->addAudioDestination(rtp);
                    A_LogI_F("encoder[%s-%p] -> rtp[%s-%p]", encoder->name().c_str(), encoder.get(), rtp->name().c_str(), rtp.get());
                    sessions_[AUDIO][id].local.pipelines.insert(encoder);
                    sessions_[AUDIO][id].local.pipelines.insert(rtp);
                }
            }
        } while (0);
    }
    return ret;
}

int MediaManagerInterfaceImp::addAudioRecvPipeline(const std::string& id)
{
    FOOT_PRINT;
    int ret = MediaManagerInterface::MM_SUCCESS;
    if (sessions_[AUDIO][id].remote.rtpPort && *sessions_[AUDIO][id].remote.rtpPort == 0) {
        return ret;
    }
    if (!sessions_[AUDIO][id].remote.enabled) {
        return ret;
    }
    if (config_.contains("audio") && sessions_[AUDIO][id].remote.found_codec) {
        FOOT_PRINT;
        {
            std::string poolId = "";
            if (isInPool(poolId, id) && mediaPools_[poolId].workingId == "") {
                setWorkingId(poolId, id);
            }
        }
        do {
            if (!sessions_[AUDIO][id].remote.use_dispatcher) {
                auto render = createAudioRender(id);
                // auto decoder = createAudioDecoder(sessions_[AUDIO][id].remote.codec);
                if (!sessions_[AUDIO][id].remote.use_uart) {
                    // auto decoder = createAudioDecoder(id);
                    auto decoder = createAudioDecoderWrapper(id);
                    if (!decoder) {
                        A_LogW_F("createAudioDecoder fail!");
                        ret = MediaManagerInterface::MM_PIPELINE_FAIL;
                        break;
                    }

                    auto rtp = createAudioRtp(id, false);
                    if (!rtp) {
                        A_LogW_F("createAudioRtp fail!");
                        ret = MediaManagerInterface::MM_PIPELINE_FAIL;
                        break;
                    }
                    // TODO
                    bool neteq = false;
                    if (sessions_[AUDIO][id].remote.params.contains("useNeteq"))
                        neteq = sessions_[AUDIO][id].remote.params["useNeteq"];
                    if (neteq) {
                        rtp->addDataDestination(decoder);
                    } else {
                        rtp->addAudioDestination(decoder);
                    }

                    {
                        auto stream_monitor = std::make_shared<StreamMonitor>("");
                        if (stream_monitor)
                        {
                            if (neteq)
                            {
                                rtp->addDataDestination(stream_monitor);
                            }
                            else
                            {
                                rtp->addAudioDestination(stream_monitor);
                            }
                            sessions_[AUDIO][id].remote.stream_monitor = stream_monitor;
                            A_LogI_F("rtp[%s] -> monitor[%s]", rtp->name().c_str(), stream_monitor->name().c_str());
                            setStreamMonitorTimeoutCallback(id);
                            std::string poolId;
                            if (isInPool(poolId, id) || isWorkingId(poolId, id)) {
                                setMediaPoolStreamMonitorCallback(poolId, id);
                                nlohmann::json j;
                                j["is-master"] = isWorkingId(poolId, id);
                                j["timeoutMS"] = default_audio_timeout_ms_;
                                stream_monitor->updateParam(j.dump());
                            }
                        }
                    }

                    A_LogI_F("rtp[%s] -> decoder[%s]", rtp->name().c_str(), decoder->name().c_str());
                    std::vector<std::shared_ptr<AudioFrameProcesser>> processers;
                    ret = createAudioDownlinkProcesser(id, processers);
                    if (ret != MediaManagerInterface::MM_SUCCESS) {
                        A_LogW_F("createAudioDownlinkProcesser fail!");
                        break;
                    }
                    if (!processers.empty()) {
                        decoder->addAudioDestination(processers[0]);
                        A_LogI_F("decoder[%s] -> [%s]", decoder->name().c_str(), processers[0]->name().c_str());
                        for (size_t i = 0; i < processers.size() - 1; i++) {
                            processers[i]->addAudioDestination(processers[i + 1]);
                            A_LogI_F("[%s] -> [%s]", processers[i]->name().c_str(), processers[i + 1]->name().c_str());
                        }
                        // processers[processers.size() - 1]->addAudioDestination(render);
                    }
                    // else
                    std::shared_ptr<FramePipeline> renderPrev = render;
                    {
                        nlohmann::json j;
                        j["use-default-gain"] = false;
                        auto controller = AudioFrameProcesser::CreateAudioProcesser("PCMGainController", j.dump());
                        if (controller) {
                            controller->addAudioDestination(renderPrev);
                            LogI(
                                "controller([%s]-[%p]) -> renderPrev([%s]-[%p])", controller->name().c_str(), controller.get(), renderPrev->name().c_str(),
                                renderPrev.get());
                            renderPrev = controller;
                            sessions_[AUDIO][id].remote.gainController = controller;
                            sessions_[AUDIO][id].remote.pipelines.insert(renderPrev);
                        } else {
                            LogW("create PCMGainController fail!");
                        }
                    }
                    {
                        if (renderPrev) {
                            decoder->addAudioDestination(renderPrev);
                            A_LogI_F("decoder[%s-%p] -> renderPrev[%s-%p]", decoder->name().c_str(), decoder.get(), renderPrev->name().c_str(), renderPrev.get());
                        }
                    }

                    sessions_[AUDIO][id].remote.pipelines.insert(rtp);
                    sessions_[AUDIO][id].remote.pipelines.insert(decoder);
                    for (size_t i = 0; i < processers.size(); i++) {
                        sessions_[AUDIO][id].remote.pipelines.insert(processers[i]);
                    }
                } else {
                    auto rtp = createAudioRtp(id, false);
                    if (!rtp) {
                        A_LogW_F("createAudioRtp fail!");
                        ret = MediaManagerInterface::MM_PIPELINE_FAIL;
                        break;
                    }
                    {
                        auto stream_monitor = std::make_shared<StreamMonitor>("");
                        if (stream_monitor)
                        {
                            rtp->addAudioDestination(stream_monitor);
                            sessions_[AUDIO][id].remote.stream_monitor = stream_monitor;
                            A_LogI_F("rtp[%s] -> monitor[%s]", rtp->name().c_str(), stream_monitor->name().c_str());
                            setStreamMonitorTimeoutCallback(id);
                            std::string poolId;
                            if (isInPool(poolId, id) || isWorkingId(poolId, id)) {
                                setMediaPoolStreamMonitorCallback(poolId, id);
                                nlohmann::json j;
                                j["is-master"] = isWorkingId(poolId, id);
                                j["timeoutMS"] = default_audio_timeout_ms_;
                                stream_monitor->updateParam(j.dump());
                            }
                        }
                    }
                    if (render) {
                        rtp->addAudioDestination(render);
                        A_LogI_F("rtpengine[%s-%p] -> render[%s-%p]", rtp->name().c_str(), rtp.get(), render->name().c_str(), render.get());
                    }
                    sessions_[AUDIO][id].remote.pipelines.insert(rtp);
                }

                if (render) {
                    sessions_[AUDIO][id].remote.pipelines.insert(render);
                    sessions_[AUDIO][id].remote.render = render;
                    // setRemoteMediaTimeoutCallback(id);
                    // std::string poolId;
                    // if (isInPool(poolId, id) || isWorkingId(poolId, id)) {
                    //     setMediaPoolStreamCallback(poolId, id);
                    // }
                }
            }
            // TODO: DRY 
            else {
                // auto decoder = createAudioDecoder(id);
                auto decoder = createAudioDecoderWrapper(id);
                if (!decoder) {
                    A_LogW_F("createAudioDecoder fail!");
                    ret = MediaManagerInterface::MM_PIPELINE_FAIL;
                    break;
                }

                auto rtp = createAudioRtp(id, false);
                if (!rtp) {
                    A_LogW_F("createAudioRtp fail!");
                    ret = MediaManagerInterface::MM_PIPELINE_FAIL;
                    break;
                }
                // TODO
                bool neteq = false;
                if (sessions_[AUDIO][id].remote.params.contains("useNeteq"))
                    neteq = sessions_[AUDIO][id].remote.params["useNeteq"];
                if (neteq) {
                    rtp->addDataDestination(decoder);
                } else {
                    rtp->addAudioDestination(decoder);
                }
                A_LogI_F("rtp[%s] -> decoder[%s]", rtp->name().c_str(), decoder->name().c_str());
                std::string poolId;
                {
                    auto stream_monitor = std::make_shared<StreamMonitor>("");
                    if (stream_monitor) {
                        if (neteq) {
                            rtp->addDataDestination(stream_monitor);
                        } else {
                            rtp->addAudioDestination(stream_monitor);
                        }
                        sessions_[AUDIO][id].remote.stream_monitor = stream_monitor;
                        A_LogI_F("rtp[%s] -> monitor[%s]", rtp->name().c_str(), stream_monitor->name().c_str());
                        setStreamMonitorTimeoutCallback(id);
                        std::string poolId;
                        if (isInPool(poolId, id) || isWorkingId(poolId, id)) {
                            setMediaPoolStreamMonitorCallback(poolId, id);
                            nlohmann::json j;
                            j["is-master"] = isWorkingId(poolId, id);
                            j["timeoutMS"] = default_audio_timeout_ms_;
                            stream_monitor->updateParam(j.dump());
                        }
                    }                    
                }

                // TODO: 不同通道可以使用不同的音频处理模块
                std::vector<std::shared_ptr<AudioFrameProcesser>> processers;
                ret = createAudioDownlinkProcesser(id, processers);
                if (ret != MediaManagerInterface::MM_SUCCESS) {
                    A_LogW_F("createAudioDownlinkProcesser fail!");
                    break;
                }
                if (!processers.empty()) {
                    decoder->addAudioDestination(processers[0]);
                    A_LogI_F("decoder[%s] -> [%s]", decoder->name().c_str(), processers[0]->name().c_str());
                    for (size_t i = 0; i < processers.size() - 1; i++) {
                        processers[i]->addAudioDestination(processers[i + 1]);
                        A_LogI_F("[%s] -> [%s]", processers[i]->name().c_str(), processers[i + 1]->name().c_str());
                    }
                    // processers[processers.size() - 1]->addAudioDestination(render);
                }
                if (createAudioRenders(id)) {
                    for (auto &pair : sessions_[AUDIO][id].remote.renders) {
                        decoder->addAudioDestination(pair.second);
                        A_LogI_F(
                            "decoder[%s-%p] -> [%d]render[%s-%p]", decoder->name().c_str(), decoder.get(), pair.first, pair.second->name().c_str(),
                            pair.second.get());
                    }
                }

                // NOTE: 博鼎测试可测试性需求
                // TEST: 博鼎测试可测试性需求
                int external_hdmi = sessions_[AUDIO][id].remote.external_hdmi;
                if (external_hdmi >= 0 && external_hdmi <= 1) {
                    auto external_render = createDefaultAudioRender(external_hdmi, "AlsaAudioFrameRender");
                    if (external_render && !sessions_[AUDIO][id].call_out) {
                        decoder->addAudioDestination(external_render);
                        sessions_[AUDIO][id].remote.pipelines.insert(external_render);
                        A_LogI_F(
                            "decoder[%s-%p] -> render[%s-%p]", decoder->name().c_str(), decoder.get(), external_render->name().c_str(), external_render.get());
                    }
                }
                sessions_[AUDIO][id].remote.pipelines.insert(rtp);
                sessions_[AUDIO][id].remote.pipelines.insert(decoder);
                for (size_t i = 0; i < processers.size(); i++) {
                    sessions_[AUDIO][id].remote.pipelines.insert(processers[i]);
                }
            }

        } while (0);
    }
    return ret;
}

std::shared_ptr<AudioFrameCapturer> MediaManagerInterfaceImp::createAudioCapture(const std::string& id)
{
    // std::string workingId = getWorkingIdBySessionId(id);
    // if (workingId != id && sessions_[AUDIO].count(workingId) != 0 && sessions_[AUDIO][workingId].local.capturer) {
    //     jinfo("createAudioCapture[%s]#############workingId = %s", id.c_str(), workingId.c_str());
    //     return std::dynamic_pointer_cast<AudioFrameCapturer>(sessions_[AUDIO][workingId].local.capturer);
    // }
    std::string poolId = "";
    if (isInPool(poolId, id) || isWorkingId(poolId, id))
    {
        if (mediaPools_[poolId].pipelines[AUDIO].capturer) {
            mediaPools_[poolId].pipelines[AUDIO].capturer_ref++;
            return std::dynamic_pointer_cast<AudioFrameCapturer>(mediaPools_[poolId].pipelines[AUDIO].capturer);
        }
    }
    std::shared_ptr<AudioFrameCapturer> ret;
    if ((config_["audio"].contains("capturer") && config_["audio"]["capturer"].contains("name")) || sessions_[AUDIO][id].local.use_uart)
    {
        nlohmann::json j;
        j["samplerate"] = 16000;
        if (config_["audio"]["capturer"].contains("samplerate"))
        {
            j["samplerate"] = config_["audio"]["capturer"]["samplerate"];
        }
        j["channel"] = 1;
        if (config_["audio"]["capturer"].contains("channel"))
        {
            j["channel"] = config_["audio"]["capturer"]["channel"];
        }
        j["maxChn"] = 4;
        if (config_["audio"]["capturer"].contains("maxChn"))
            j["maxChn"] = config_["audio"]["capturer"]["maxChn"];
        int dev = 0;
        if (sessions_[AUDIO][id].local.dev >= 0)
        {
            dev = sessions_[AUDIO][id].local.dev;
        }
        else if (config_["audio"]["capturer"].contains("dev"))
        {
            dev = config_["audio"]["capturer"]["dev"];
        }
        if (config_["audio"].contains("debug"))
            j["debug"] = config_["audio"]["debug"];
        j["dev"] = dev;
        if (sessions_[AUDIO][id].local.ptime > 0)
        {
            j["ptime"] = sessions_[AUDIO][id].local.ptime;
        }
        if (!sessions_[AUDIO][id].local.use_uart) {
            std::string name = config_["audio"]["capturer"]["name"];
            LogI("CreateAudioCapturer %s", name.c_str());
            if (audio_capturers_.find(dev) != audio_capturers_.end() && audio_capturers_[dev].pipeline) {
                ret = std::dynamic_pointer_cast<AudioFrameCapturer>(audio_capturers_[dev].pipeline);
            } else {
                ret = AudioFrameCapturer::CreateAudioCapturer(name, j.dump());
            }
        } else {
            if (sessions_[AUDIO][id].local.params.contains("gain")) {
                j["gain"] = sessions_[AUDIO][id].local.params["gain"];
                j["local-gain"] = sessions_[AUDIO][id].local.params["gain"];
            }
                
            jinfo("Create UartReader: %s", j.dump().c_str());
            ret = AudioFrameCapturer::CreateAudioCapturer("UartReader", j.dump());
        }

    }
    else
    {
        LogI("CreateAudioCapturer fail");
    }
    if (mediaPools_.count(poolId) != 0 && ret) {
        mediaPools_[poolId].pipelines[AUDIO].capturer = ret;
        mediaPools_[poolId].pipelines[AUDIO].capturer_ref++;
    }
    return ret;
}

std::shared_ptr<AudioFrameCapturer> MediaManagerInterfaceImp::createAudioCapturer(const std::string& id, const int dev) {
    std::shared_ptr<AudioFrameCapturer> ret;
    if ((config_["audio"].contains("capturer") && config_["audio"]["capturer"].contains("name")) || sessions_[AUDIO][id].local.use_uart)
    {
        nlohmann::json j;
        j["samplerate"] = 16000;
        if (config_["audio"]["capturer"].contains("samplerate"))
        {
            j["samplerate"] = config_["audio"]["capturer"]["samplerate"];
        }
        j["channel"] = 1;
        if (config_["audio"]["capturer"].contains("channel"))
        {
            j["channel"] = config_["audio"]["capturer"]["channel"];
        }
        j["maxChn"] = 4;
        if (config_["audio"]["capturer"].contains("maxChn"))
            j["maxChn"] = config_["audio"]["capturer"]["maxChn"];
        if (config_["audio"].contains("debug"))
            j["debug"] = config_["audio"]["debug"];
        j["dev"] = dev;
        if (!sessions_[AUDIO][id].local.use_uart) {
            std::string name = config_["audio"]["capturer"]["name"];
            LogI("CreateAudioCapturer %s", name.c_str());
            if (audio_capturers_.find(dev) != audio_capturers_.end() && audio_capturers_[dev].pipeline) {
                ret = std::dynamic_pointer_cast<AudioFrameCapturer>(audio_capturers_[dev].pipeline);
            } else {
                ret = AudioFrameCapturer::CreateAudioCapturer(name, j.dump());
            }
        } else {
            ret = AudioFrameCapturer::CreateAudioCapturer("UartReader", j.dump());
        }
    }
    else
    {
        LogI("CreateAudioCapturer fail");
    }
    return ret;
}

bool MediaManagerInterfaceImp::createAudioCapturers(const std::string& id) {
    std::string workingId = getWorkingIdBySessionId(id);
    if (workingId != id && sessions_[AUDIO].count(workingId) != 0 && !sessions_[AUDIO][workingId].local.capturers.empty()) {
        jinfo("createAudioCapturers[%s]#############workingId = %s", id.c_str(), workingId.c_str());
        // for (auto &kv: sessions_[AUDIO][workingId].local.capturers) {
        //     sessions_[AUDIO][id].local.capturers[kv.first] = kv.second;
        // }
        return true;
    }
    if (config_["audio"].contains("capturer") && config_["audio"]["capturer"].contains("name")) {
        nlohmann::json j;
        j["samplerate"] = 16000;
        if (config_["audio"]["capturer"].contains("samplerate")) {
            j["samplerate"] = config_["audio"]["capturer"]["samplerate"];
        }
        j["channel"] = 1;
        if (config_["audio"]["capturer"].contains("channel")) {
            j["channel"] = config_["audio"]["capturer"]["channel"];
        }
        j["maxChn"] = 8;
        if (config_["audio"]["capturer"].contains("maxChn")) {
            j["maxChn"] = config_["audio"]["capturer"]["maxChn"];
        }
        std::string name = config_["audio"]["capturer"]["name"];
        for (int dev: sessions_[AUDIO][id].local.devs) {
            if (audio_capturers_.find(dev) != audio_capturers_.end() && audio_capturers_[dev].pipeline) {
                sessions_[AUDIO][id].local.capturers[dev] = std::dynamic_pointer_cast<AudioFrameCapturer>(audio_capturers_[dev].pipeline);
            } else {
                j["dev"] = dev;
                auto ret = AudioFrameCapturer::CreateAudioCapturer(name, j.dump());
                sessions_[AUDIO][id].local.capturers[dev] = ret;
            }
            LogI("CreateAudioCapturers[%d] %s", dev, name.c_str());
        }
    } else return false;
    return true;
}

int MediaManagerInterfaceImp::createAudioUplinkProcesser(const std::string& id, std::vector<std::shared_ptr<AudioFrameProcesser>>& processers)
{
    processers.clear();
    int ret = MediaManagerInterface::MM_SUCCESS;
    if (config_["audio"].contains("uplink") && config_["audio"]["uplink"].contains("processer"))
    {
        for (size_t i = 0; i < config_["audio"]["uplink"]["processer"].size(); i++)
        {
            if (config_["audio"]["uplink"]["processer"][i].contains("name"))
            {
                std::string processorName = config_["audio"]["uplink"]["processer"][i]["name"];
                nlohmann::json j;
                std::shared_ptr<AudioFrameProcesser> processer = nullptr;
                if (processorName == "AudioFrameResampler")
                {
                    j["src"]["samplerate"] = 16000;
                    j["src"]["channel"] = 1;
                    j["dst"]["samplerate"] = 48000;
                    j["dst"]["channel"] = 1;
                    if (config_["audio"]["uplink"]["processer"][i].contains("src") && config_["audio"]["uplink"]["processer"][i]["src"].contains("samplerate"))
                    {
                        j["src"]["samplerate"] = config_["audio"]["uplink"]["processer"][i]["src"]["samplerate"];
                    }
                    if (config_["audio"]["uplink"]["processer"][i].contains("src") && config_["audio"]["uplink"]["processer"][i]["src"].contains("channel"))
                    {
                        j["src"]["channel"] = config_["audio"]["uplink"]["processer"][i]["src"]["channel"];
                    }
                    if (config_["audio"]["uplink"]["processer"][i].contains("dst") && config_["audio"]["uplink"]["processer"][i]["dst"].contains("samplerate"))
                    {
                        j["dst"]["samplerate"] = config_["audio"]["uplink"]["processer"][i]["dst"]["samplerate"];
                    }
                    if (config_["audio"]["uplink"]["processer"][i].contains("dst") && config_["audio"]["uplink"]["processer"][i]["dst"].contains("channel"))
                    {
                        j["dst"]["channel"] = config_["audio"]["uplink"]["processer"][i]["dst"]["channel"];
                    }
                }
                else
                {
                    if (processorName == "AudioFramePacer")
                    {
                        j["ptime"] = sessions_[AUDIO][id].local.codec.ptime;
                    }

                    j["samplerate"] = 16000;
                    if (config_["audio"]["uplink"]["processer"][i].contains("samplerate"))
                    {
                        j["samplerate"] = config_["audio"]["uplink"]["processer"][i]["samplerate"];
                    }
                    j["channel"] = 1;
                    if (config_["audio"]["uplink"]["processer"][i].contains("channel"))
                    {
                        j["channel"] = config_["audio"]["uplink"]["processer"][i]["channel"];
                    }
                }

                LogI("create processor %s [%s]", processorName.c_str(), j.dump().c_str());
                bool flag = false;
                if (processorName == "RtcAudioFrameAec") {
                    j["aec"] = sessions_[AUDIO][id].local.aec;
                    j["agc"] = sessions_[AUDIO][id].local.agc;
                    j["ns"] = sessions_[AUDIO][id].local.ns;
                    j["cng"] = sessions_[AUDIO][id].local.cng;
                    j["log"] = sessions_[AUDIO][id].local.log;
                    j["anti-howling"] = sessions_[AUDIO][id].local.anti_howling;
                    jinfo("innerAec:%d",sessions_[AUDIO][id].local.innerAec);
                    if (!sessions_[AUDIO][id].local.innerAec
                        && (sessions_[AUDIO][id].local.aec > 1 || sessions_[AUDIO][id].local.agc > 1 || sessions_[AUDIO][id].local.ns > 1
                            || sessions_[AUDIO][id].local.cng > 1 || sessions_[AUDIO][id].local.anti_howling > 1)) {
                        processer = AudioFrameProcesser::CreateAudioProcesser(processorName, j.dump());
                        if(processer){
                            auto& pipelines = sessions_[AUDIO][id].remote.pipelines;
                            for(const auto& pip : pipelines){
                                if(pip && pip->name() == "AudioFrameAsFarEnd"){
                                    auto sourceList = pip->getAudioSources();
                                    bool isAdded = std::any_of(sourceList.begin(), sourceList.end(), [&](auto source){
                                        auto ptr = source.lock();
                                        return ptr && ptr->name() == "RtcAudioFrameAec";
                                    });
                                    if(!isAdded)
                                        pip->addAudioDestination(processer);
                                    break;
                                }
                            }
                        }
                    } else {
                        flag = true;
                    }
                } else if (processorName == "RtcAudioFrameLec") {
                    if (!sessions_[AUDIO][id].local.innerAec && sessions_[AUDIO][id].local.lec > 1) {
                        processer = AudioFrameProcesser::CreateAudioProcesser(processorName, j.dump());
                    } else {
                        flag = true;
                    } 
                } else {
                    processer = AudioFrameProcesser::CreateAudioProcesser(processorName, j.dump());
                }
                if (processer)
                {
                    processers.push_back(processer);
                }
                else if (!flag)
                {
                    processers.clear();
                    ret = MediaManagerInterface::MM_PIPELINE_FAIL;
                    break;
                }
            }
        }
    }
    return ret;
}

std::shared_ptr<AudioFrameEncoder> MediaManagerInterfaceImp::createAudioEncoder(const CodecInst& codec)
{
    std::shared_ptr<AudioFrameEncoder> ret;
    if (config_["audio"].contains("encoder") && config_["audio"]["encoder"].contains("name"))
    {
        nlohmann::json j;
        j["codec"] = codec.name;
        j["samplerate"] = codec.rate;
        j["bitrate"] = codec.bitrate;
        if (strcasecmp(codec.name, "MP4A-LATM") == 0) {
            j["samplerate"] = 48000;
            j["bitrate"] = 64000;
        }
        j["channel"] = codec.chn;
        if (config_["audio"].contains("debug"))
            j["debug"] = config_["audio"]["debug"];
        LogI("CreateAudioEncoder %s", j.dump().c_str());
        ret = AudioFrameEncoder::CreateAudioEncoder(config_["audio"]["encoder"]["name"], j.dump());
    }
    return ret;
}

bool MediaManagerInterfaceImp::isActiveStream(const std::string& id)
{
    for (auto& pair : mediaPools_)
    {
        auto it = std::find(pair.second.poolIds.begin(), pair.second.poolIds.end(), id);
        if (it != pair.second.poolIds.end() && pair.second.workingId != id)
        {
            return false;
        }
    }
    return true;
}

std::string MediaManagerInterfaceImp::getWorkingIdBySessionId(const std::string& id)  const {
    for (auto& pair : mediaPools_)
    {
        auto it = std::find(pair.second.poolIds.begin(), pair.second.poolIds.end(), id);
        if (it != pair.second.poolIds.end() && pair.second.workingId != id)
        {
            return pair.second.workingId;
        }
    }
    return id;    
}

bool MediaManagerInterfaceImp::isInPool(std::string& poolId, const std::string& id)
{
    for (auto& pair : mediaPools_)
    {
        auto it = std::find(pair.second.poolIds.begin(), pair.second.poolIds.end(), id);
        if (it != pair.second.poolIds.end())
        {
            poolId = pair.first;
            return true;
        }
    }
    return false;
}

bool MediaManagerInterfaceImp::isWorkingId(std::string& poolId, const std::string& id)
{
    for (auto& pair : mediaPools_)
    {
        if (pair.second.workingId == id) {
            poolId = pair.first;
            return true;
        }
    }
    return false;
}

std::shared_ptr<AudioFrameRender> MediaManagerInterfaceImp::createAudioRender(const std::string& id)
{
    std::shared_ptr<AudioFrameRender> ret;
    if (!isActiveStream(id))
    {
        return ret;
    }
    if ((config_["audio"].contains("render") && config_["audio"]["render"].contains("name")) || sessions_[AUDIO][id].remote.use_uart)
    {
        //if (dev >= 0)
        {
            nlohmann::json j;
            j["samplerate"] = 16000;
            if (config_["audio"]["render"].contains("samplerate"))
            {
                j["samplerate"] = config_["audio"]["render"]["samplerate"];
            }
            j["channel"] = 1;
            if (config_["audio"]["render"].contains("channel"))
            {
                j["channel"] = config_["audio"]["render"]["channel"];
            }
            if (config_["audio"]["render"].contains("dev-name"))
            {
                j["devName"] = config_["audio"]["render"]["dev-name"];
            }
            int dev = 0;
            if (sessions_[AUDIO][id].remote.dev >= 0)
            {
                dev = sessions_[AUDIO][id].remote.dev;
            }
            j["dev"] = dev;
            if (audio_renderers_.count(dev) != 0 && audio_renderers_[dev].pipeline) {
                return std::dynamic_pointer_cast<AudioFrameRender>(audio_renderers_[dev].pipeline);
            }
            else if (config_["audio"]["render"].contains("dev"))
            {
                j["dev"] = config_["audio"]["render"]["dev"];
            }
            j["maxChn"] = 4;
            if (config_["audio"]["render"].contains("maxChn"))
                j["maxChn"] = config_["audio"]["render"]["maxChn"];
            if (config_["audio"].contains("debug"))
                j["debug"] = config_["audio"]["debug"];
            if (!sessions_[AUDIO][id].remote.use_uart) {
                std::string name = config_["audio"]["render"]["name"];
                LogI("CreateAudioRender %s dev = %d", name.c_str(), sessions_[AUDIO][id].remote.dev);
                ret = AudioFrameRender::CreateAudioRender(name, j.dump());
            } else {
                j["codec"] = sessions_[AUDIO][id].local.codec.fmt;
                if (sessions_[AUDIO][id].remote.params.contains("gain")) {
                    j["gain"] = sessions_[AUDIO][id].remote.params["gain"];
                    j["remote-gain"] = sessions_[AUDIO][id].remote.params["gain"];
                }               
                jinfo("Create UartWriter: %s", j.dump().c_str());
                ret = AudioFrameRender::CreateAudioRender("UartWriter", j.dump());
            }
        }
    }
    else
    {
        LogI("CreateAudioRender fail");
    }
    return ret;
}

std::shared_ptr<AudioFrameRender> MediaManagerInterfaceImp::createAudioRender(const std::string& id, const int dev) {
    std::shared_ptr<AudioFrameRender> ret;
    if (!isActiveStream(id))
    {
        return ret;
    }
    if (audio_renderers_.count(dev) != 0 && audio_renderers_[dev].pipeline) {
        return std::dynamic_pointer_cast<AudioFrameRender>(audio_renderers_[dev].pipeline);
    }
    if ((config_["audio"].contains("render") && config_["audio"]["render"].contains("name")) || sessions_[AUDIO][id].local.use_uart)
    {
        //if (dev >= 0)
        {
            nlohmann::json j;
            j["samplerate"] = 16000;
            if (config_["audio"]["render"].contains("samplerate"))
            {
                j["samplerate"] = config_["audio"]["render"]["samplerate"];
            }
            j["channel"] = 1;
            if (config_["audio"]["render"].contains("channel"))
            {
                j["channel"] = config_["audio"]["render"]["channel"];
            }
            if (config_["audio"]["render"].contains("dev-name"))
            {
                j["devName"] = config_["audio"]["render"]["dev-name"];
            }
            j["dev"] = dev;
            j["maxChn"] = 4;
            if (config_["audio"]["render"].contains("maxChn"))
                j["maxChn"] = config_["audio"]["render"]["maxChn"];
            if (config_["audio"].contains("debug"))
                j["debug"] = config_["audio"]["debug"];
            if (!sessions_[AUDIO][id].local.use_uart) {
                std::string name = config_["audio"]["render"]["name"];
                LogI("CreateAudioRender %s dev = %d", name.c_str(), sessions_[AUDIO][id].remote.dev);
                ret = AudioFrameRender::CreateAudioRender(name, j.dump());
            } else {
                ret = AudioFrameRender::CreateAudioRender("UartWriter", j.dump());
            }
        }
    }
    else
    {
        LogI("CreateAudioRender fail");
    }
    return ret;    
}

bool MediaManagerInterfaceImp::createAudioRenders(const std::string& id) {
    if (!isActiveStream(id))
    {
        return false;
    }
    if ((config_["audio"].contains("render") && config_["audio"]["render"].contains("name"))) {
        nlohmann::json j;
        j["samplerate"] = 16000;
        if (config_["audio"]["render"].contains("samplerate")) {
            j["samplerate"] = config_["audio"]["render"]["samplerate"];
        }
        j["channel"] = 1;
        if (config_["audio"]["render"].contains("channel")) {
            j["channel"] = config_["audio"]["render"]["channel"];
        }
        if (config_["audio"]["render"].contains("dev-name")) {
            j["devName"] = config_["audio"]["render"]["dev-name"];
        }
        j["maxChn"] = 4;
        if (config_["audio"]["render"].contains("maxChn"))
            j["maxChn"] = config_["audio"]["render"]["maxChn"];
        std::string name = config_["audio"]["render"]["name"];
        for (int dev: sessions_[AUDIO][id].remote.devs) {
            if (audio_renderers_.count(dev) != 0 && audio_renderers_[dev].pipeline) {
                sessions_[AUDIO][id].remote.renders[dev] = audio_renderers_[dev].pipeline;
                continue;
            }
            j["dev"] = dev;
            LogI("CreateAudioRenders[%d] %s", dev, name.c_str());
            auto ret = AudioFrameRender::CreateAudioRender(name, j.dump());
            sessions_[AUDIO][id].remote.renders[dev] = ret;
        }
    } else return false;
    return true;
}

int MediaManagerInterfaceImp::createAudioDownlinkProcesser(const std::string& id, std::vector<std::shared_ptr<AudioFrameProcesser>>& processers)
{
    processers.clear();
    int ret = MediaManagerInterface::MM_SUCCESS;
    if (config_["audio"].contains("downlink") && config_["audio"]["downlink"].contains("processer"))
    {
        for (size_t i = 0; i < config_["audio"]["downlink"]["processer"].size(); i++)
        {
            if (config_["audio"]["downlink"]["processer"][i].contains("name"))
            {
                nlohmann::json j;
                j["samplerate"] = 16000;
                if (config_["audio"]["downlink"]["processer"][i].contains("samplerate"))
                {
                    j["samplerate"] = config_["audio"]["downlink"]["processer"][i]["samplerate"];
                }
                j["channel"] = 1;
                if (config_["audio"]["downlink"]["processer"][i].contains("channel"))
                {
                    j["channel"] = config_["audio"]["downlink"]["processer"][i]["channel"];
                }
                std::string processName = config_["audio"]["downlink"]["processer"][i]["name"];
                LogI("create processor %s", processName.c_str());
                std::shared_ptr<AudioFrameProcesser> processer = nullptr;
                bool flag = false;
                if (processName == "AudioFrameAsFarEnd") {
                    if (!sessions_[AUDIO][id].local.innerAec
                        && (sessions_[AUDIO][id].local.aec > 1 || sessions_[AUDIO][id].local.agc > 1 || sessions_[AUDIO][id].local.ns > 1
                            || sessions_[AUDIO][id].local.cng)) {
                        processer = AudioFrameProcesser::CreateAudioProcesser(processName, j.dump());
                    } else {
                        flag = true;
                    }
                } else if (processName == "AudioFrameAsLineIn") {
                    // TODO
                    if (!sessions_[AUDIO][id].local.innerAec && sessions_[AUDIO][id].local.lec > 1) {
                        processer = AudioFrameProcesser::CreateAudioProcesser(processName, j.dump());
                    }
                } else {
                    processer = AudioFrameProcesser::CreateAudioProcesser(processName, j.dump());
                }
                if (processer)
                {
                    processers.push_back(processer);
                    if (config_["audio"]["downlink"]["processer"][i].contains("bind"))
                    {
                        std::string bindProcesserName = config_["audio"]["downlink"]["processer"][i]["bind"];
                        LogI("find bindProcess %s", bindProcesserName.c_str());
                        bool isFound = false;
                        for (auto it = sessions_[AUDIO][id].local.pipelines.begin(); it != sessions_[AUDIO][id].local.pipelines.end(); ++it)
                        {
                            if ((*it)->name() == bindProcesserName)
                            {
                                isFound = true;
                                processer->addAudioDestination(*it);
                                LogI("downlink processer [%s] bind uplink [%s]", processName.c_str(), bindProcesserName.c_str());
                                break;
                            }
                        }
                        if (sessions_[AUDIO][id].local.use_mixer) {
                            for (auto &pair: sessions_[AUDIO][id].local.processors) {
                                for (size_t i = 0; i < pair.second.size(); i++) {
                                    if (pair.second[i]->name() == bindProcesserName)
                                    {
                                        isFound = true;
                                        processer->addAudioDestination(pair.second[i]);
                                        LogI("one of downlink processers [%s] bind uplink [%s]", processName.c_str(), bindProcesserName.c_str());
                                        break;
                                    }
                                }
                            }  
                        }
                        if (!isFound)
                        {
                            LogI("find bindProcess %s fail", bindProcesserName.c_str());
                        }
                    }
                }
                else if (!flag)
                {
                    ret = MediaManagerInterface::MM_PIPELINE_FAIL;
                    break;
                }
            }
        }
    }
    return ret;
}

std::shared_ptr<AudioFrameDecoder> MediaManagerInterfaceImp::createAudioDecoder(const CodecInst& codec)
{
    std::shared_ptr<AudioFrameDecoder> ret;
    if (config_["audio"].contains("decoder") && config_["audio"]["decoder"].contains("name"))
    {
        nlohmann::json j;
        j["codec"] = codec.name;
        j["samplerate"] = codec.rate;
        j["clockrate"] = codec.rate;
        j["bitrate"] = codec.bitrate;
        // TODO:
        if (strcasecmp(codec.name, "MP4A-LATM") == 0) {
            j["samplerate"] = 48000;
            j["bitrate"] = 64000;
            j["clockrate"] = 90000;
        }
        j["channel"] = codec.chn;
        j["encodetype"] = codec.pt;
        if (config_["audio"].contains("debug"))
            j["debug"] = config_["audio"]["debug"];
        ret = AudioFrameDecoder::CreateAudioDecoder(config_["audio"]["decoder"]["name"], j.dump());
    }
    return ret;
}

std::shared_ptr<AudioFrameDecoder> MediaManagerInterfaceImp::createAudioDecoder(const std::string& id) {
    auto codec = sessions_[AUDIO][id].local.codec;
    std::shared_ptr<AudioFrameDecoder> ret;
    if (config_["audio"].contains("decoder") && config_["audio"]["decoder"].contains("name"))
    {
        nlohmann::json j;
        j["codec"] = codec.name;
        j["samplerate"] = codec.rate;
        j["clockrate"] = codec.rate;
        j["bitrate"] = codec.bitrate;
        if (strcasecmp(codec.name, "MP4A-LATM") == 0) {
            j["samplerate"] = 48000;
            j["bitrate"] = 64000;
            j["clockrate"] = 90000;
        }
        j["channel"] = codec.chn;
        // j["encodetype"] = codec.pt;
        j["encodetype"] = sessions_[AUDIO][id].local.payload_type;
        if (config_["audio"].contains("debug"))
            j["debug"] = config_["audio"]["debug"];
        // DONE: aac也能使用neteq模块
        bool useNeteq = false;
        if (sessions_[AUDIO][id].remote.params.contains("useNeteq"))
            useNeteq = sessions_[AUDIO][id].remote.params["useNeteq"];
        if (useNeteq/* && strcasecmp(codec.name, "MP4A-LATM") != 0*/)
            ret = AudioFrameDecoder::CreateAudioDecoder("NetEqAudioDecoder", j.dump());
        else 
            ret = AudioFrameDecoder::CreateAudioDecoder("native", j.dump());
    }
    return ret;    
}

std::shared_ptr<AudioFrameDecoder> MediaManagerInterfaceImp::createAudioDecoderWrapper(const std::string &id) {
    if (sessions_[AUDIO].find(id) == sessions_[AUDIO].end()) {
        LogW("Please Create Audio Session %s first", id.c_str());
        return nullptr;
    }
    jinfo("createAudioDecoderWrapper id[%s]", id.c_str());
    nlohmann::json j;
    bool useNeteq = false;
    if (sessions_[AUDIO][id].remote.params.contains("useNeteq"))
        useNeteq = sessions_[AUDIO][id].remote.params["useNeteq"];
    j["native"] = !useNeteq;
    return AudioFrameDecoder::CreateAudioDecoder("AudioFrameDecoderWrapper", j.dump());
}

std::shared_ptr<AudioFrameMixer> MediaManagerInterfaceImp::createAudioMixer(const std::string& id, bool remote) {
    std::string workingId = getWorkingIdBySessionId(id);
    if (workingId != id && sessions_[AUDIO].count(workingId) != 0 && sessions_[AUDIO][workingId].local.mixer) {
        jinfo("createAudioMixer[%s]#############workingId = %s", id.c_str(), workingId.c_str());
        return std::dynamic_pointer_cast<AudioFrameMixer>(sessions_[AUDIO][workingId].local.mixer);
    }
    // std::string poolId = "";
    // if (isInPool(poolId, id) || isWorkingId(poolId, id))
    // {
    //     if (mediaPools_[poolId].pipelines[AUDIO].mixer) 
    //         return std::dynamic_pointer_cast<AudioFrameMixer>(mediaPools_[poolId].pipelines[AUDIO].mixer);
    // }
    std::shared_ptr<AudioFrameMixer> ret;
    if (config_["audio"].contains("downlink") && config_["audio"]["downlink"].contains("mixer") && config_["audio"]["downlink"]["mixer"].contains("name")) {
        // if (sessions_[AUDIO][id].remote.dev > 0) {
        nlohmann::json j;
        j["dev"] = sessions_[AUDIO][id].remote.dev;
        j["samplerate"] = 16000;
        if (config_["audio"]["downlink"]["mixer"].contains("samplerate")) {
            j["samplerate"] = config_["audio"]["downlink"]["mixer"]["samplerate"];
        }
        j["channel"] = 1;
        if (config_["audio"]["downlink"]["mixer"].contains("channel")) {
            j["channel"] = config_["audio"]["downlink"]["mixer"]["channel"];
        }
        j["ptime"] = 20;
        if (config_["audio"]["downlink"]["mixer"].contains("ptime")) {
            j["ptime"] = config_["audio"]["downlink"]["mixer"]["ptime"];
        }
        j["remote"] = remote;
        std::string name = config_["audio"]["downlink"]["mixer"]["name"];
        ret = AudioFrameMixer::CreateAudioMixer(name, j.dump());
        jinfo("CreateAudioMixer %s dev = %d", name.c_str(), sessions_[AUDIO][id].remote.dev);
        // }
    }
    // if (mediaPools_.count(poolId) != 0) mediaPools_[poolId].pipelines[AUDIO].mixer = ret;
    return ret;
}

std::shared_ptr<RtpEngine> MediaManagerInterfaceImp::createAudioRtp(const std::string& id, bool uplink)
{
    std::shared_ptr<RtpEngine> ret;
    std::string tag = uplink ? "uplink" : "downlink";
    if (!sessions_[AUDIO][id].local.rtpPort)
    {
        return ret;
    }
    if (!sessions_[AUDIO][id].remote.rtpPort)
    {
        return ret;
    }
    if (config_["audio"][tag].contains("rtp"))
    {
        std::string rtpName = config_["audio"][tag]["rtp"];
        for (auto it = sessions_[AUDIO][id].local.pipelines.begin(); it != sessions_[AUDIO][id].local.pipelines.end(); ++it)
        {
            if ((*it)->name() == rtpName)
            {
                LogI("find %s-%p", rtpName.c_str(), it->get());
                ret = std::dynamic_pointer_cast<RtpEngine>(*it);
                break;
            }
        }
        for (auto it = sessions_[AUDIO][id].remote.pipelines.begin(); it != sessions_[AUDIO][id].remote.pipelines.end(); ++it)
        {
            if ((*it)->name() == rtpName)
            {
                LogI("find %s-%p", rtpName.c_str(), it->get());
                ret = std::dynamic_pointer_cast<RtpEngine>(*it);
                break;
            }
        }
        if (!ret)
        {
            nlohmann::json j;
            j["bindIp"] = sessions_[AUDIO][id].local.ip;
            if (sessions_[AUDIO][id].local.bindIp != "") {
                j["bindIp"] = sessions_[AUDIO][id].local.bindIp;
            }
            j["bindPort"] = *sessions_[AUDIO][id].local.rtpPort;
            j["payloadType"] = sessions_[AUDIO][id].local.codec.pt;
            j["local_payload_type"] = sessions_[AUDIO][id].local.payload_type;
            j["remote_payload_type"] = sessions_[AUDIO][id].remote.payload_type;
            j["fmt"] = sessions_[AUDIO][id].local.codec.fmt;
            j["dtls"]  = sessions_[AUDIO][id].local.dtls;
            j["ptime"] = sessions_[AUDIO][id].local.ptime;
            nlohmann::json dst;
            dst["ip"] = sessions_[AUDIO][id].remote.ip;
            dst["port"] = *sessions_[AUDIO][id].remote.rtpPort;
            j["clock_rate"] = getClockrate(sessions_[AUDIO][id].local.payload_type);
            j["dst"] = nlohmann::json::array();
            j["dst"].push_back(dst);
            // j["is_h323"] = sessions_[AUDIO][id].is_h323;
            bool useNeteq = false;
            if (sessions_[AUDIO][id].remote.params.contains("useNeteq"))
                useNeteq = sessions_[AUDIO][id].remote.params["useNeteq"];
            j["rawRtp"] = useNeteq;

            std::string rtpName = config_["audio"][tag]["rtp"];
            LogI("audio[%s-%d-%d-%d] CreateRTPEngine(%s) %s", sessions_[AUDIO][id].local.codec.name, sessions_[AUDIO][id].local.codec.pt, sessions_[AUDIO][id].local.codec.rate, sessions_[AUDIO][id].local.codec.chn, rtpName.c_str(), j.dump().c_str());

            ret = RtpEngine::CreateRTPEngine(rtpName, j.dump());
        }
        if (ret)
        {
            ret->start();
            nlohmann::json j;
            j["rtp"]["fingerprint"] = sessions_[AUDIO][id].remote.fingerprint;
            j["rtp"]["isServer"] = sessions_[AUDIO][id].remote.isServer;
            j["rtcp"]["fingerprint"] = sessions_[AUDIO][id].remote.fingerprint;
            j["rtcp"]["isServer"] = sessions_[AUDIO][id].remote.isServer;

            nlohmann::json dst;
            dst["ip"] = sessions_[AUDIO][id].remote.ip;
            dst["port"] = *sessions_[AUDIO][id].remote.rtpPort;

            j["dst"] = nlohmann::json::array();
            j["dst"].push_back(dst);
            j["ptime"] = sessions_[AUDIO][id].local.ptime;
            // j["is_h323"] = sessions_[AUDIO][id].is_h323;
            j["clock_rate"] = getClockrate(sessions_[AUDIO][id].local.payload_type);
            if (sessions_[AUDIO][id].remote.stunSdp != "")
            {
                j["sdp"] = nlohmann::json::parse(sessions_[AUDIO][id].remote.stunSdp);
            }
            std::string update = j.dump();
            ret->updateParam(update);
        }
    }
    setRTPStatisticsCallback(id);
    return ret;
}

#ifdef ENABLE_VIDEO
int MediaManagerInterfaceImp::addVideoSendPipeline(const std::string& id)
{
    FOOT_PRINT;
    int ret = MediaManagerInterface::MM_SUCCESS;
    if (sessions_[VIDEO][id].remote.rtpPort && *sessions_[VIDEO][id].remote.rtpPort == 0) {
        return ret;
    }
    if (config_.contains("video") && sessions_[VIDEO][id].local.found_codec && sessions_[VIDEO][id].local.dev >= 0)
    {
        {
            std::string poolId = "";
            if (isInPool(poolId, id) && mediaPools_[poolId].workingId == "") {
                setWorkingId(poolId, id);
            }
        }
        do
        {
            int dev = sessions_[VIDEO][id].local.dev;
            std::shared_ptr<VideoFrameCapturer> capturer;
            if (video_capturers_.count(dev) != 0 && video_capturers_[dev].pipeline) {
                capturer = std::dynamic_pointer_cast<VideoFrameCapturer>(video_capturers_[dev].pipeline);
                video_capturers_[dev].ref++;
            } else {
                capturer = createVideoCapturer(sessions_[VIDEO][id].local.dev, sessions_[VIDEO][id].local.gpu_index);
                video_capturers_[dev].pipeline = capturer;
                video_capturers_[dev].ref++;
            }
            // auto capturer = createVideoCapturer(sessions_[VIDEO][id].local.dev, sessions_[VIDEO][id].local.gpu_index);
            if (!capturer)
            {
                LogW("createVideoCapturer fail!");
                ret = MediaManagerInterface::MM_PIPELINE_FAIL;
                break;
            }
            LogI("addVideoSendPipeline createVideoCapturer dev %d [%p]", dev, capturer.get());

            std::shared_ptr<FramePipeline> capturerNext = capturer;

            // TEST: 测试等待画面
            if (sessions_[VIDEO][id].local.standby)
            {
                nlohmann::json temp;
                temp["path"] = sessions_[VIDEO][id].local.videoSrc;
                jinfo("local createFileSource %s", temp.dump().c_str());
                auto standbyFramePipeline = FileFramePipeline::CreateFileSource("RKFileFrameReader", temp.dump());
                auto frame_selector = FrameSelector::Create("");
                frame_selector->registerInput(capturerNext);
                frame_selector->registerInput(standbyFramePipeline);
                standbyFramePipeline->addVideoDestination(frame_selector);
                capturerNext->addVideoDestination(frame_selector);
                frame_selector->setSelectedInput(standbyFramePipeline);
                frame_selector->setPriorityInput(capturerNext);
                // FIXME: 该函数最后会insert capturer，因此此处可不需要insert capturerNext，但最好可以统一insert capturerNext，以避免遗漏。
                capturerNext = frame_selector;
                sessions_[VIDEO][id].local.pipelines.insert(standbyFramePipeline);
            }


            if (config_["video"].contains("capturer") && config_["video"]["capturer"].contains("decoder"))
            {
                CodecInst inst;
                strcpy(inst.name, "mjpeg");
                std::string decodeType = config_["video"]["capturer"]["decoder"];
                auto decoder = createVideoDecoder(inst, decodeType, sessions_[VIDEO][id].local.gpu_index);
                if (decoder)
                {
                    capturerNext = decoder;
                    capturer->addVideoDestination(capturerNext);
                    LogI("capturer([%s]-[%p]) -> capturerNext([%s]-[%p])", capturer->name().c_str(), capturer.get(), capturerNext->name().c_str(), capturerNext.get());
                }
                else
                {
                    LogW("capturer createVideoDecoder fail!");
                    ret = MediaManagerInterface::MM_PIPELINE_FAIL;
                    break;
                }
            }
            bool have_local_render = false;
            if (sessions_[VIDEO][id].local.params.contains("localRender")) {
                int localRender = sessions_[VIDEO][id].local.params["localRender"];
                have_local_render = localRender > 0;
            }
            // TEST: 画中画
            // have_local_render = true;
            std::shared_ptr<VideoFrameRender> render = nullptr;
            if (have_local_render /*&& isActiveStream(id)*/) {
                LogI("addVideoSendPipeline createVideoRender %d", sessions_[VIDEO][id].local.dev);
                int dev = sessions_[VIDEO][id].remote.dev;
                if (sessions_[VIDEO][id].remote.renders.count(dev) != 0 && sessions_[VIDEO][id].remote.renders[dev]) {
                    render = std::dynamic_pointer_cast<VideoFrameRender>(sessions_[VIDEO][id].remote.renders[dev]);
                } else {
                    render = createVideoRender(sessions_[VIDEO][id].remote.dev, id, sessions_[VIDEO][id].remote.gpu_index);
                    sessions_[VIDEO][id].remote.renders[dev] = render;
                }
                render->loadPresetLayouts(2, false);
                // if (video_capturers_.count(dev) != 0) {
                //     render = std::dynamic_pointer_cast<VideoFrameRender>(video_capturers_[dev].render);
                //     video_capturers_[dev].ref++;
                // } else {
                //     render = createVideoRender(sessions_[VIDEO][id].remote.dev, id, sessions_[VIDEO][id].remote.gpu_index);
                //     video_capturers_[dev].render = render;
                //     video_capturers_[dev].ref++;
                // }
            }
            // TEST: 测试画中画和字幕PC
            // nlohmann::json temp_rect;
            // temp_rect.clear();
            // temp_rect["dev"] = 0;
            // openPip(id, temp_rect.dump());
            // temp_rect.clear();
            // temp_rect["dev"] = 3;
            // temp_rect["layout"]["x"] = 0.25f; // 字幕PC x坐标
            // temp_rect["layout"]["y"] = 0.75f; // 字幕PC y坐标
            // temp_rect["layout"]["w"] = 0.5f; // 字幕PC 宽度
            // temp_rect["layout"]["h"] = 0.25f; // 字幕PC 高度
            // temp_rect["layout"]["srcX"] = 0.25f; // 字幕PC 源x坐标"
            // temp_rect["layout"]["srcY"] = 0.75f; // 字幕PC 源y坐标"
            // temp_rect["layout"]["srcW"] = 0.5f;  // 字幕PC 源宽度"
            // temp_rect["layout"]["srcH"] = 0.25f; // 字幕PC 源高度"
            // openCaption(id, temp_rect.dump());

            if (render)
            {
                nlohmann::json rect;
                rect["top"] = 0.75;
                rect["left"] = 0.75;
                rect["width"] = 0.25;
                rect["height"] = 0.25;
                rect["zpos"] = 1;
                render->setPlane(1, rect.dump());
                capturerNext->setGroupId(1);
                capturerNext->addVideoDestination(render);
                LogI("capturerNext([%s]-[%p]) -> render([%s]-[%p])", capturerNext->name().c_str(), capturerNext.get(), render->name().c_str(), render.get());
            }
            else
            {
                LogW("local createVideoRender fail!");
            }

            auto encoder = createVideoEncoder(sessions_[VIDEO][id].local.codec, id, sessions_[VIDEO][id].local.gpu_index);
            if (!encoder)
            {
                LogW("createVideoEncoder fail!");
                ret = MediaManagerInterface::MM_PIPELINE_FAIL;
                break;
            }

            capturerNext->addVideoDestination(encoder);
            LogI("capturerNext([%s]-[%p]) -> encoder([%s]-[%p])", capturerNext->name().c_str(), capturerNext.get(), encoder->name().c_str(), encoder.get());
            std::shared_ptr<FramePipeline> rtpTransport;
            std::shared_ptr<FramePipeline> rtcpTransport;
            //auto rtp = createVideoRtp(id, false, rtpTransport, rtcpTransport);
            auto rtp = createVideoRtp(id, true);
            if (!rtp)
            {
                LogW("createVideoRtp fail!");
                ret = MediaManagerInterface::MM_PIPELINE_FAIL;
                break;
            }

            encoder->addVideoDestination(rtp);
            LogI("encoder([%s]-[%p]) -> rtp([%s]-[%p])", encoder->name().c_str(), encoder.get(), rtp->name().c_str(), rtp.get());

            sessions_[VIDEO][id].local.pipelines.insert(capturer);
            sessions_[VIDEO][id].local.capturer = capturer;
            sessions_[VIDEO][id].local.pipelines.insert(capturerNext);
            if (render)
            {
                sessions_[VIDEO][id].local.pipelines.insert(render);
            }
            sessions_[VIDEO][id].local.pipelines.insert(encoder);
            sessions_[VIDEO][id].local.pipelines.insert(rtp);
            if (rtpTransport) sessions_[VIDEO][id].local.pipelines.insert(rtpTransport);
            if (rtcpTransport) sessions_[VIDEO][id].local.rtcps.insert(rtcpTransport);
            ret = MediaManagerInterface::MM_SUCCESS;
            setVideoEncoderNotifyCallback(id);
        } while (0);
    }
    return ret;
}

int MediaManagerInterfaceImp::addVideoRecvPipeline(const std::string& id)
{
    FOOT_PRINT;
    int ret = MediaManagerInterface::MM_SUCCESS;
    if (sessions_[VIDEO][id].remote.rtpPort && *sessions_[VIDEO][id].remote.rtpPort == 0) {
        return ret;
    }
    if (config_.contains("video") && sessions_[VIDEO][id].remote.found_codec)
    {
        {
            std::string poolId = "";
            if (isInPool(poolId, id) && mediaPools_[poolId].workingId == "") {
                setWorkingId(poolId, id);
            }
        }
        do
        {
            LogI("addVideoRecvPipeline createVideoRender %d", sessions_[VIDEO][id].remote.dev);
            // auto render = createVideoRender(sessions_[VIDEO][id].remote.dev, id, sessions_[VIDEO][id].remote.gpu_index);
            std::shared_ptr<VideoFrameRender> render = nullptr;
            int dev = sessions_[VIDEO][id].remote.dev;
            if (sessions_[VIDEO][id].remote.renders.count(dev) != 0 && sessions_[VIDEO][id].remote.renders[dev]) {
                render = std::dynamic_pointer_cast<VideoFrameRender>(sessions_[VIDEO][id].remote.renders[dev]);;
            } else {
                render = createVideoRender(sessions_[VIDEO][id].remote.dev, id, sessions_[VIDEO][id].remote.gpu_index);
                sessions_[VIDEO][id].remote.renders[dev] = render;
            }
            std::string decodeType;
            if (config_["video"]["decoder"].contains("name"))
            {
                decodeType = config_["video"]["decoder"]["name"];
            }
            auto decoder = createVideoDecoder(sessions_[VIDEO][id].remote.codec, decodeType, sessions_[VIDEO][id].remote.gpu_index);
            if (!decoder)
            {
                LogW("createVideoDecoder fail!");
                ret = MediaManagerInterface::MM_PIPELINE_FAIL;
                break;
            }
            decoder->setGroupId(0); 
            std::shared_ptr<FramePipeline> rtpTransport;
            std::shared_ptr<FramePipeline> rtcpTransport;
            //auto rtp = createVideoRtp(id, false, rtpTransport, rtcpTransport);
            auto rtp = createVideoRtp(id, false);
            if (!rtp)
            {
                LogW("createVideoRtp fail!");
                ret = MediaManagerInterface::MM_PIPELINE_FAIL;
                break;
            }

            rtp->addVideoDestination(decoder);
            LogI("RTP([%s]-[%p]) -> decoder([%s]-[%p])", rtp->name().c_str(), rtp.get(), decoder->name().c_str(), decoder.get());
            
            {
                auto stream_monitor = std::make_shared<StreamMonitor>("");
                if (stream_monitor)
                {
                    rtp->addVideoDestination(stream_monitor);
                    sessions_[VIDEO][id].remote.stream_monitor = stream_monitor;
                    A_LogI_F("rtp[%s] -> monitor[%s]", rtp->name().c_str(), stream_monitor->name().c_str());
                    setStreamMonitorTimeoutCallback(id);
                    std::string poolId;
                    if (isInPool(poolId, id) || isWorkingId(poolId, id)) {
                        setMediaPoolStreamMonitorCallback(poolId, id);
                        nlohmann::json j;
                        j["is-master"] = isWorkingId(poolId, id);
                        j["timeoutMS"] = default_video_timeout_ms_;
                        stream_monitor->updateParam(j.dump());
                    }
                }
            }
            decoder->setGroupId(0);
            std::shared_ptr<FramePipeline> rendererPrevious = decoder;
            // TEST: 测试等待画面
            if (sessions_[VIDEO][id].remote.standby)
            {
                nlohmann::json temp;
                temp["path"] = sessions_[VIDEO][id].remote.videoSrc;
                auto standbyFramePipeline = FileFramePipeline::CreateFileSource("RKFileFrameReader", temp.dump());
                standbyFramePipeline->setGroupId(0);
                auto frame_selector = FrameSelector::Create("");
                frame_selector->registerInput(rendererPrevious);
                frame_selector->registerInput(standbyFramePipeline);
                standbyFramePipeline->addVideoDestination(frame_selector);
                rendererPrevious->addVideoDestination(frame_selector);
                frame_selector->setSelectedInput(standbyFramePipeline);
                frame_selector->setPriorityInput(rendererPrevious);
                sessions_[VIDEO][id].remote.pipelines.insert(rendererPrevious);
                rendererPrevious = frame_selector;
                sessions_[VIDEO][id].remote.pipelines.insert(standbyFramePipeline);
            }

            if (render)
            {
                render->setPlane(0, "");
                // auto mixer = createVideoMixer(id);
                // if (mixer) {
                //     decoder->addVideoDestination(mixer);
                //     sessions_[VIDEO][id].remote.mixer = mixer;
                //     LogI("decoder([%s]-[%p]) -> mixer([%s]-[%p])", decoder->name().c_str(), decoder.get(), mixer->name().c_str(), mixer.get());
                //     mixer->addVideoDestination(render);
                //     LogI("mixer([%s]-[%p]) -> render([%s]-[%p])", mixer->name().c_str(), mixer.get(), render->name().c_str(), render.get());
                // } else {
                //     decoder->addVideoDestination(render);
                //     LogI("decoder([%s]-[%p]) -> render([%s]-[%p])", decoder->name().c_str(), decoder.get(), render->name().c_str(), render.get());
                // }
                render->setSourceId(rendererPrevious.get(), 0);
                rendererPrevious->addVideoDestination(render);
                LogI("rendererPrevious([%s]-[%p]) -> render([%s]-[%p])", rendererPrevious->name().c_str(), rendererPrevious.get(), render->name().c_str(), render.get());
                sessions_[VIDEO][id].remote.pipelines.insert(render);
            }
            sessions_[VIDEO][id].remote.rendererPrevious = rendererPrevious;
            sessions_[VIDEO][id].remote.pipelines.insert(rendererPrevious);
            sessions_[VIDEO][id].remote.pipelines.insert(rtp);
            if (rtpTransport) sessions_[VIDEO][id].remote.pipelines.insert(rtpTransport);
            if (rtcpTransport) sessions_[VIDEO][id].remote.rtcps.insert(rtcpTransport);
            setVideoDecoderNotifyCallback(id);
        } while (0);
    }
    return ret;
}

int MediaManagerInterfaceImp::updateVideoSendPipeline(const std::string& id) {
    FOOT_PRINT;
    int ret = MediaManagerInterface::MM_SUCCESS;
    if (sessions_[VIDEO][id].remote.rtpPort && *sessions_[VIDEO][id].remote.rtpPort == 0) {
        return ret;
    }
    if (sessions_[VIDEO][id].local.addr_changed) {
        std::shared_ptr<RtpEngine> rtp_engine;
        for (auto& pipeline : sessions_[VIDEO][id].local.pipelines)
        {
            if (RtpEngine::IsRTPEngine(pipeline))
            {
                LogI("updateVideoSendPipeline[%s] find %s-%p", id.c_str(), pipeline->name().c_str(), pipeline.get());
                rtp_engine = std::dynamic_pointer_cast<RtpEngine>(pipeline);
                break;
            }
        }
        if (rtp_engine) {
            nlohmann::json j;
            j["clear"] = true;
            nlohmann::json dst;
            dst["ip"] = sessions_[VIDEO][id].remote.ip;
            dst["port"] = *sessions_[VIDEO][id].remote.rtpPort;
            j["dst"] = nlohmann::json::array();
            j["dst"].push_back(dst);

            j["local_payload_type"] = sessions_[VIDEO][id].local.payload_type;
            j["remote_payload_type"] = sessions_[VIDEO][id].remote.payload_type;
            j["fmt"] = sessions_[VIDEO][id].local.codec.fmt;
            j["clock_rate"] = getClockrate(sessions_[VIDEO][id].local.payload_type);
            rtp_engine->updateParam(j.dump());
        }
    }
    if (config_.contains("video") && sessions_[VIDEO].find(id) != sessions_[VIDEO].end())
    {
        auto new_encoder = createVideoEncoder(sessions_[VIDEO][id].local.codec, id, sessions_[VIDEO][id].local.gpu_index);
        if (!new_encoder)
        {
            A_LogW_F("createVideoEncoder fail!");
            return MediaManagerInterface::MM_PIPELINE_FAIL;
        }
        std::shared_ptr<VideoFrameEncoder> old_encoder;
        for (auto& pipeline : sessions_[VIDEO][id].local.pipelines)
        {
            if (VideoFrameEncoder::IsVideoFrameEncoder(pipeline))
            {
                old_encoder = std::dynamic_pointer_cast<VideoFrameEncoder>(pipeline);
                break;
            }
        }
        if (old_encoder)
        {
            old_encoder->stop();
            auto srcs = old_encoder->getVideoSources();
            for (auto src : srcs)
            {
                auto source = src.lock();
                if (source)
                {
                    source->removeVideoDestination(old_encoder);
                    source->addVideoDestination(new_encoder);
                    A_LogI_F("source[%s] -> new_encoder[%s]", source->name().c_str(), new_encoder->name().c_str());
                }
            }
            auto dests = old_encoder->getVideoDestinations();
            for (auto &dest: dests) {
                auto destination = dest.lock();
                if (destination) {
                    old_encoder->removeVideoDestination(destination);
                    new_encoder->addVideoDestination(destination);
                    A_LogI_F("new_encoder[%s] -> destination[%s]", new_encoder->name().c_str(), destination->name().c_str());
                }

                if (config_["video"]["uplink"].contains("rtp")) {
                    std::string rtpName = config_["video"]["uplink"]["rtp"];
                    if (destination->name() == rtpName) {
                        LogI("find %s-%p", rtpName.c_str(), destination.get());
                        auto rtp_engine = std::dynamic_pointer_cast<RtpEngine>(destination);
                        nlohmann::json j;
                        j["bindIp"] = sessions_[VIDEO][id].local.ip;
                        if (sessions_[VIDEO][id].local.bindIp != "") {
                            j["bindIp"] = sessions_[VIDEO][id].local.bindIp;
                        }
                        j["bindPort"] = *sessions_[VIDEO][id].local.rtpPort;
                        j["payloadType"] = sessions_[VIDEO][id].local.codec.pt;
                        j["local_payload_type"] = sessions_[VIDEO][id].local.payload_type;
                        j["remote_payload_type"] = sessions_[VIDEO][id].remote.payload_type;
                        j["fmt"] = sessions_[VIDEO][id].local.codec.fmt;
                        rtp_engine->updateParam(j.dump());
                    }
                }
            }
            sessions_[VIDEO][id].local.pipelines.erase(old_encoder);
        } else {
            LogI("updateVideoSendPipeline id(%s) do not exist encoder", id.c_str());
            return MediaManagerInterface::MM_PIPELINE_FAIL;
        }
        sessions_[VIDEO][id].local.pipelines.insert(new_encoder);
        setVideoEncoderNotifyCallback(id);
    }
    return ret;
}
int MediaManagerInterfaceImp::updateVideoRecvPipeline(const std::string& id) {
    FOOT_PRINT;
    jinfo("addr_changed: %d, codec_changed: %d", sessions_[VIDEO][id].remote.addr_changed, sessions_[VIDEO][id].remote.codec_changed);
    int ret = MediaManagerInterface::MM_SUCCESS;
    if (sessions_[VIDEO][id].remote.rtpPort && *sessions_[VIDEO][id].remote.rtpPort == 0) {
        return ret;
    }
    if (sessions_[VIDEO][id].remote.addr_changed) {
        std::shared_ptr<RtpEngine> rtp_engine;
        for (auto& pipeline : sessions_[VIDEO][id].remote.pipelines)
        {
            if (RtpEngine::IsRTPEngine(pipeline))
            {
                LogI("updateVideoRecvPipeline[%s] find %s-%p", id.c_str(), pipeline->name().c_str(), pipeline.get());
                rtp_engine = std::dynamic_pointer_cast<RtpEngine>(pipeline);
                break;
            }
        }
        if (rtp_engine) {
            nlohmann::json j;
            j["clear"] = true;
            nlohmann::json dst;
            dst["ip"] = sessions_[VIDEO][id].remote.ip;
            dst["port"] = *sessions_[VIDEO][id].remote.rtpPort;
            j["dst"] = nlohmann::json::array();
            j["dst"].push_back(dst);

            j["local_payload_type"] = sessions_[VIDEO][id].local.payload_type;
            j["remote_payload_type"] = sessions_[VIDEO][id].remote.payload_type;
            j["fmt"] = sessions_[VIDEO][id].local.codec.fmt;
            rtp_engine->updateParam(j.dump());
        }
    }
    if (config_.contains("video") && sessions_[VIDEO].find(id) != sessions_[VIDEO].end() && sessions_[VIDEO][id].remote.found_codec
        && sessions_[VIDEO][id].remote.codec_changed) {
        std::string decodeType;
        if (config_["video"]["decoder"].contains("name")) {
            decodeType = config_["video"]["decoder"]["name"];
        }
        auto new_decoder = createVideoDecoder(sessions_[VIDEO][id].remote.codec, decodeType, sessions_[VIDEO][id].remote.gpu_index);
        if (!new_decoder)
        {
            A_LogW_F("createVideoDecoder fail!");
            return MediaManagerInterface::MM_PIPELINE_FAIL;
        }
        std::shared_ptr<VideoFrameDecoder> old_decoder;
        for (auto& pipeline : sessions_[VIDEO][id].remote.pipelines)
        {
            if (VideoFrameDecoder::IsVideoFrameDecoder(pipeline))
            {
                old_decoder = std::dynamic_pointer_cast<VideoFrameDecoder>(pipeline);
                break;
            }
        }
        if (old_decoder)
        {
            old_decoder->stop();
            auto srcs = old_decoder->getVideoSources();
            for (auto src : srcs)
            {
                auto source = src.lock();
                if (source)
                {
                    source->removeVideoDestination(old_decoder);
                    source->addVideoDestination(new_decoder);
                    A_LogI_F("source[%s] -> new_decoder[%s]", source->name().c_str(), new_decoder->name().c_str());
                }
            }
            auto dests = old_decoder->getVideoDestinations();
            for (auto &dest: dests) {
                auto destination = dest.lock();
                if (destination) {
                    old_decoder->removeVideoDestination(destination);
                    new_decoder->addVideoDestination(destination);
                    if (sessions_[VIDEO][id].remote.standby && FrameSelector::IsFrameSelector(destination)) {
                        std::shared_ptr<FrameSelector> selector = std::dynamic_pointer_cast<FrameSelector>(destination);
                        selector->unregisterInput(old_decoder);
                        selector->registerInput(new_decoder);
                        selector->setPriorityInput(new_decoder);
                    }
                    if (VideoFrameRender::IsSupportRender(destination->name())) {
                        auto renderer = std::dynamic_pointer_cast<VideoFrameRender>(destination);
                        int32_t source_id = -1;
                        if (renderer->getSourceId(old_decoder.get(), source_id)) {
                            renderer->setSourceId(new_decoder.get(), source_id);
                        }
                        renderer->unsetSourceId(old_decoder.get());
                        sessions_[VIDEO][id].remote.rendererPrevious = new_decoder;
                    }
                    A_LogI_F("new_decoder[%s] -> destination[%s]", new_decoder->name().c_str(), destination->name().c_str());

                }
            }
            sessions_[VIDEO][id].remote.pipelines.erase(old_decoder);
        }
        else
        {
            LogI("updateVideoRecvPipeline id(%s) do not exist decoder", id.c_str());
            return MediaManagerInterface::MM_PIPELINE_FAIL;
        }
        sessions_[VIDEO][id].remote.pipelines.insert(new_decoder);
        setVideoDecoderNotifyCallback(id);
    }
    return ret;
}

std::shared_ptr<VideoFrameCapturer> MediaManagerInterfaceImp::createVideoCapturer(int dev, int gpuIndex)
{
    // std::string workingId = getWorkingIdBySessionId(id);
    // if (workingId != id && sessions_[VIDEO].count(workingId) != 0 && sessions_[VIDEO][workingId].local.capturer) {
    //     jinfo("createVideoCapturer[%s]#############workingId = %s", id.c_str(), workingId.c_str());
    //     return std::dynamic_pointer_cast<VideoFrameCapturer>(sessions_[VIDEO][workingId].local.capturer);
    // }
    // std::string poolId = "";
    // if (isInPool(poolId, id) || isWorkingId(poolId, id))
    // {
    //     if (mediaPools_[poolId].pipelines[VIDEO].capturer) {
    //         mediaPools_[poolId].pipelines[VIDEO].capturer_ref++;
    //         return std::dynamic_pointer_cast<VideoFrameCapturer>(mediaPools_[poolId].pipelines[VIDEO].capturer);
    //     }
    // }
    std::shared_ptr<VideoFrameCapturer> ret;
    if (video_capturers_.count(dev) != 0 && video_capturers_[dev].pipeline) {
        ret = std::dynamic_pointer_cast<VideoFrameCapturer>(video_capturers_[dev].pipeline);
        video_capturers_[dev].ref++;
    } else if (config_["video"].contains("capturer") && config_["video"]["capturer"].contains("name"))
    {
        // nlohmann::json &encoder_params = sessions_[VIDEO][id].local.params;
        nlohmann::json j;
        j["width"] = 1920;
        if (config_["video"]["capturer"].contains("width"))
        {
            j["width"] = config_["video"]["capturer"]["width"];
        }
        j["height"] = 1080;
        if (config_["video"]["capturer"].contains("height"))
        {
            j["height"] = config_["video"]["capturer"]["height"];
        }
        j["fps"] = 30;
        if (config_["video"]["capturer"].contains("fps"))
        {
            j["fps"] = config_["video"]["capturer"]["fps"];
        }
        if (config_["video"]["capturer"].contains("format"))
        {
            j["format"] = config_["video"]["capturer"]["format"];
        }
        if (config_["video"]["capturer"].contains("dev"))
        {
            j["dev"] = config_["video"]["capturer"]["dev"];
        }
        if (config_["video"]["capturer"].contains("devNames"))
        {
            j["devNames"] = config_["video"]["capturer"]["devNames"];
        }
        if (dev > -1)
            j["dev"] = dev;
        if (config_["video"]["capturer"].contains("app")) {
            j["app"] = config_["video"]["capturer"]["app"];
        }
        // if (encoder_params.contains("input") && encoder_params["input"].contains("width")) {
        //     j["targetWidth"] = encoder_params["input"]["width"];
        // }
        // if (encoder_params.contains("input") && encoder_params["input"].contains("height")) {
        //     j["targetHeight"] = encoder_params["input"]["height"];
        // }
        j["gpuIndex"] = gpuIndex;
        ret = VideoFrameCapturer::CreateVideoCapturer(config_["video"]["capturer"]["name"], j.dump());
        if (ret) {
            jinfo("CreateVideoCapturer dev = %d %p", dev, ret.get());
            video_capturers_[dev].pipeline = ret;
            video_capturers_[dev].ref++;
        }
    }
    // if (mediaPools_.count(poolId) != 0 && ret) 
    // {
    //     mediaPools_[poolId].pipelines[VIDEO].capturer = ret;
    //     mediaPools_[poolId].pipelines[VIDEO].capturer_ref++;
    // }
    return ret;
}

std::shared_ptr<VideoFrameEncoder> MediaManagerInterfaceImp::createVideoEncoder(const CodecInst& codec, const std::string& id, int gpuIndex)
{
    std::shared_ptr<VideoFrameEncoder> ret;
    if (config_["video"].contains("encoder") && config_["video"]["encoder"].contains("name"))
    {
        nlohmann::json j;
        nlohmann::json &encoder_params = sessions_[VIDEO][id].local.params;
        std::string encodeType = config_["video"]["encoder"]["name"];
        if (config_["video"]["encoder"].contains("codec"))
        {
            size_t i = 0;
            for (i = 0; i < config_["video"]["encoder"]["codec"].size(); i++)
            {
                if (config_["video"]["encoder"]["codec"][i].contains("name"))
                {
                    std::string codecName = config_["video"]["encoder"]["codec"][i]["name"];
                    LogI("createVideoEncoder [%s] [%s]", codecName.c_str(), codec.name);
                    if (strcasecmp(codecName.c_str(), codec.name) == 0) {
                        j["codec"] = codecName;
                        j["width"] = 1920;
                        if (encoder_params.contains("input") && encoder_params["input"].contains("width")) {
                            j["width"] = encoder_params["input"]["width"];
                        } else if (config_["video"]["encoder"]["codec"][i].contains("width")) {
                            j["width"] = config_["video"]["encoder"]["codec"][i]["width"];
                        }
                        j["height"] = 1080;
                        if (encoder_params.contains("input") && encoder_params["input"].contains("height")) {
                            j["height"] = encoder_params["input"]["height"];
                        } else if (config_["video"]["encoder"]["codec"][i].contains("height")) {
                            j["height"] = config_["video"]["encoder"]["codec"][i]["height"];
                        }
                        j["fps"] = 30;
                        if (encoder_params.contains("input") && encoder_params["input"].contains("frameRate")) {
                            j["fps"] = encoder_params["input"]["frameRate"];
                        } else if (config_["video"]["encoder"]["codec"][i].contains("fps")) {
                            j["fps"] = config_["video"]["encoder"]["codec"][i]["fps"];
                        }

                        if (sessions_[VIDEO][id].local.max_frame_size > 0) {
                            j["maxFrameSize"] = sessions_[VIDEO][id].local.max_frame_size; // max frame size in bytes
                            int width = j["width"], height = j["height"];
                            if ((width * height) >= sessions_[VIDEO][id].local.max_frame_size) {
                                int new_width, new_height;
                                getMaxFrameResolution(sessions_[VIDEO][id].local.max_frame_size, new_width, new_height);
                                j["width"] = new_width;
                                j["height"] = new_height;
                            }
                        }

                        j["gop"] = 30;
                        if (encoder_params.contains("input") && encoder_params["input"].contains("gop")) {
                            j["gop"] = encoder_params["input"]["gop"];
                        } else if (config_["video"]["encoder"]["codec"][i].contains("gop")) {
                            j["gop"] = config_["video"]["encoder"]["codec"][i]["gop"];
                        }
                        j["rc"] = "CBR";
                        if (encoder_params.contains("input") && encoder_params["input"].contains("bitrateType")) {
                            j["rc"] = encoder_params["input"]["bitrateType"];
                        } else if (config_["video"]["encoder"]["codec"][i].contains("rc")) {
                            j["rc"] = config_["video"]["encoder"]["codec"][i]["rc"];
                        }
                        j["bitrate"] = 2000000;
                        if (encoder_params.contains("input") && encoder_params["input"].contains("avgBitrate")) {
                            j["bitrate"] = encoder_params["input"]["avgBitrate"];
                        } else if (config_["video"]["encoder"]["codec"][i].contains("bitrate")) {
                            j["bitrate"] = config_["video"]["encoder"]["codec"][i]["bitrate"];
                        }
                        j["maxBitrate"] = 2000000;
                        if (encoder_params.contains("input") && encoder_params["input"].contains("maxBitrate")) {
                            j["maxBitrate"] = encoder_params["input"]["maxBitrate"];
                        } else if (config_["video"]["encoder"]["codec"][i].contains("maxBitrate")) {
                            j["maxBitrate"] = config_["video"]["encoder"]["codec"][i]["maxBitrate"];
                        }
                        j["format"] = "nv12";
                        if (config_["video"]["encoder"]["codec"][i].contains("format")) {
                            j["format"] = config_["video"]["encoder"]["codec"][i]["format"];
                        }
                        j["profile"] = 66;
                        
                        j["gpuIndex"] = gpuIndex;
                        LogI("CreateVideoEncoder %s", j.dump().c_str());
                        ret = VideoFrameEncoder::CreateVideoEncoder(encodeType, j.dump());
                        break;
                    }
                }
            }
        }
    }
    return ret;
}

std::shared_ptr<VideoFrameRender> MediaManagerInterfaceImp::createVideoRender(int dev, const std::string& id, int gpuIndex)
{
    std::shared_ptr<VideoFrameRender> ret;
    if (!isActiveStream(id))
        return ret;
    if (dev < 0) dev = 0;
    if (config_["video"].contains("render") && config_["video"]["render"].contains("name") && dev >= 0)
    {
        nlohmann::json j;
        nlohmann::json &render_params = sessions_[VIDEO][id].local.params;
        j["dev"] = dev;
        std::string renderName = config_["video"]["render"]["name"];
        j["fps"] = 60;
        if (render_params.contains("output") && render_params["output"].contains("frameRate")) {
            j["fps"] = render_params["output"]["frameRate"];
        }
        j["width"] = 1920;
        if (render_params.contains("output") && render_params["output"].contains("width")) {
            j["width"] = render_params["output"]["width"];
        }
        j["height"] = 1080;
        if (render_params.contains("output") && render_params["output"].contains("height")) {
            j["height"] = render_params["output"]["height"];
        }
        j["gpuIndex"] = gpuIndex;
        j["z"] = 0;
        j["pip"] = sessions_[VIDEO][id].local.pip;       
        LogI("CreateVideoRender(%s) %s", renderName.c_str(), j.dump().c_str());
        ret = VideoFrameRender::CreateVideoRender(renderName, j.dump());
    }
    return ret;
}

std::shared_ptr<VideoFrameRender> MediaManagerInterfaceImp::createVideoRender(int dev, const std::string& id, int gpuIndex, int x, int y, int width, int height) {
    std::shared_ptr<VideoFrameRender> ret;
    if (!isActiveStream(id))
        return ret;
    if (dev < 0) dev = 0;
    if (config_["video"].contains("render") && config_["video"]["render"].contains("name") && dev >= 0)
    {
        nlohmann::json j;
        nlohmann::json &render_params = sessions_[VIDEO][id].local.params;
        j["dev"] = dev;
        std::string renderName = config_["video"]["render"]["name"];
        j["fps"] = 60;
        if (render_params.contains("output") && render_params["output"].contains("frameRate")) {
            j["fps"] = render_params["output"]["frameRate"];
        }
        // 渲染的分辨率
        j["width"] = 1920;
        if (render_params.contains("output") && render_params["output"].contains("width")) {
            j["width"] = render_params["output"]["width"];
        }
        j["height"] = 1080;
        if (render_params.contains("output") && render_params["output"].contains("height")) {
            j["height"] = render_params["output"]["height"];
        }
        // 图层分辨率和位置
        j["x"] = x;
        j["y"] = y;
        j["plane_width"] = width;
        j["plane_height"] = height;
        j["z"] = 1;
        // nlohmann::json r;
        // r["id"] = 1;
        // r["rect"]["x"] = x;
        // r["rect"]["y"] = y;
        // r["rect"]["w"] = width;
        // r["rect"]["h"] = height;
        // j["videoLayout"].push_back(r);
        LogI("CreateVideoRender(%s) %s", renderName.c_str(), j.dump().c_str());
        ret = VideoFrameRender::CreateVideoRender(renderName, j.dump());
    }
    return ret;    
}

std::shared_ptr<VideoFrameDecoder> MediaManagerInterfaceImp::createVideoDecoder(const CodecInst& codec, const std::string& decodeType, int gpuIndex)
{
    std::shared_ptr<VideoFrameDecoder> ret;
    nlohmann::json j;
    if (config_["video"]["decoder"].contains("codec"))
    {
        size_t i = 0;
        for (i = 0; i < config_["video"]["decoder"]["codec"].size(); i++)
        {
            if (config_["video"]["decoder"]["codec"][i].contains("name"))
            {
                std::string codecName = config_["video"]["decoder"]["codec"][i]["name"];
                if (strcasecmp(codecName.c_str(),codec.name) == 0)
                {
                    j["codec"] = codecName;
                    j["width"] = 1920;
                    if (config_["video"]["decoder"]["codec"][i].contains("width"))
                    {
                        j["width"] = config_["video"]["decoder"]["codec"][i]["width"];
                    }
                    j["height"] = 1080;
                    if (config_["video"]["decoder"]["codec"][i].contains("height"))
                    {
                        j["height"] = config_["video"]["decoder"]["codec"][i]["height"];
                    }
                    j["gpuIndex"] = gpuIndex;
                    LogI("createVideoDecoder %s", j.dump().c_str());
                    ret = VideoFrameDecoder::CreateVideoDecoder(decodeType, j.dump());
                    break;
                }
            }
        }
    }
    return ret;
}

std::shared_ptr<RtpEngine> MediaManagerInterfaceImp::createVideoRtp(const std::string& id, bool uplink, std::shared_ptr<FramePipeline>& rtpTransport, std::shared_ptr<FramePipeline>& rtcpTransport)
{
    std::shared_ptr<RtpEngine> ret;
    if (!sessions_[VIDEO][id].local.rtpPort)
    {
        return ret;
    }
    if (!sessions_[VIDEO][id].remote.rtpPort)
    {
        return ret;
    }
    std::string tag = uplink ? "uplink" : "downlink";
    if (config_["video"][tag].contains("rtp-transport"))
    {
        std::string rtpTransportName = config_["video"][tag]["rtp-transport"];
        for (auto it = sessions_[VIDEO][id].local.pipelines.begin(); it != sessions_[VIDEO][id].local.pipelines.end(); ++it)
        {
            if ((*it)->name() == rtpTransportName)
            {
                LogI("find %s-%p", rtpTransportName.c_str(), it->get());
                rtpTransport = *it;
                break;
            }
        }
        if (!rtpTransport)
        {
            nlohmann::json j;
            j["bindIp"] = sessions_[VIDEO][id].local.ip;
            if (sessions_[VIDEO][id].local.bindIp != "") {
                j["bindIp"] = sessions_[VIDEO][id].local.bindIp;
            }
            j["bindPort"] = *sessions_[VIDEO][id].local.rtpPort;
            j["payloadType"] = sessions_[VIDEO][id].local.codec.pt;
            j["local_payload_type"] = sessions_[VIDEO][id].local.payload_type;
            j["remote_payload_type"] = sessions_[VIDEO][id].remote.payload_type;
            j["fmt"] = FRAME_FORMAT_RTP;

            nlohmann::json dst;
            dst["ip"] = sessions_[VIDEO][id].remote.ip;
            dst["port"] = *sessions_[VIDEO][id].remote.rtpPort;

            j["dst"] = nlohmann::json::array();
            j["dst"].push_back(dst);
            
            LogI("create rtpTransport(%s) %s", rtpTransportName.c_str(), j.dump().c_str());
            rtpTransport = FramePipeline::CreateFramePipeline(rtpTransportName, j.dump());
        }
    }
    if (config_["video"][tag].contains("rtcp-transport"))
    {
        std::string rtcpTransportName = config_["video"][tag]["rtcp-transport"];
        for (auto it = sessions_[VIDEO][id].local.rtcps.begin(); it != sessions_[VIDEO][id].local.rtcps.end(); ++it)
        {
            if ((*it)->name() == rtcpTransportName)
            {
                LogI("find %s-%p", rtcpTransportName.c_str(), it->get());
                rtcpTransport = *it;
                break;
            }
        }
        if (!rtcpTransport)
        {
            nlohmann::json j;
            j["bindIp"] = sessions_[VIDEO][id].local.ip;
            if (sessions_[VIDEO][id].local.bindIp != "") {
                j["bindIp"] = sessions_[VIDEO][id].local.bindIp;
            }
            j["bindPort"] = *sessions_[VIDEO][id].local.rtcpPort;
            j["payloadType"] = sessions_[VIDEO][id].local.codec.pt;
            j["local_payload_type"] = sessions_[VIDEO][id].local.payload_type;
            j["remote_payload_type"] = sessions_[VIDEO][id].remote.payload_type;
            j["fmt"] = FRAME_FORMAT_RTCP;

            nlohmann::json dst;
            dst["ip"] = sessions_[VIDEO][id].remote.ip;
            dst["port"] = *sessions_[VIDEO][id].remote.rtcpPort;

            j["dst"] = nlohmann::json::array();
            j["dst"].push_back(dst);

            LogI("create rtcpTransport(%s) %s", rtcpTransportName.c_str(), j.dump().c_str());
            rtcpTransport = FramePipeline::CreateFramePipeline(rtcpTransportName, j.dump());
        }
    }
    if (config_["video"][tag].contains("rtp"))
    {
        std::string rtpName = config_["video"][tag]["rtp"];
        for (auto it = sessions_[VIDEO][id].local.pipelines.begin(); it != sessions_[VIDEO][id].local.pipelines.end(); ++it)
        {
            if ((*it)->name() == rtpName)
            {
                LogI("find %s-%p", rtpName.c_str(), it->get());
                ret = std::dynamic_pointer_cast<RtpEngine>(*it);
                break;
            }
        }
        if (!ret)
        {
            nlohmann::json j;
            j["bindIp"] = sessions_[VIDEO][id].local.ip;
            if (sessions_[VIDEO][id].local.bindIp != "") {
                j["bindIp"] = sessions_[VIDEO][id].local.bindIp;
            }
            j["bindPort"] = *sessions_[VIDEO][id].local.rtpPort;
            j["payloadType"] = sessions_[VIDEO][id].local.codec.pt;
            j["local_payload_type"] = sessions_[VIDEO][id].local.payload_type;
            j["remote_payload_type"] = sessions_[VIDEO][id].remote.payload_type;
            j["fmt"] = sessions_[VIDEO][id].local.codec.fmt;

            nlohmann::json dst;
            dst["ip"] = sessions_[VIDEO][id].remote.ip;
            dst["port"] = *sessions_[VIDEO][id].remote.rtpPort;

            j["dst"] = nlohmann::json::array();
            j["dst"].push_back(dst);
            j["clock_rate"] = getClockrate(sessions_[VIDEO][id].local.payload_type);
            LogI("video CreateRTPEngine(%s) %s", rtpName.c_str(), j.dump().c_str());
            ret = RtpEngine::CreateRTPEngine(rtpName, j.dump());
        }
    }
    if (ret && rtpTransport)
    {
        if (uplink)
        {
            ret->addDataDestination(rtpTransport);
            LogI("uplink rtpengine([%s]-[%p]) -> rtpTransport([%s]-[%p])", ret->name().c_str(), ret.get(), rtpTransport->name().c_str(), rtpTransport.get());
            if (rtcpTransport)
            {
                LogI("uplink rtpengine([%s]-[%p]) -> rtcpTransport([%s]-[%p])", ret->name().c_str(), ret.get(), rtcpTransport->name().c_str(), rtcpTransport.get());
                ret->addDataDestination(rtcpTransport);
                rtcpTransport->addDataDestination(ret);
            }
        }
        else
        {
            rtpTransport->addDataDestination(ret);
            LogI("downlink rtpTransport([%s]-[%p]) -> rtpengine([%s]-[%p])", rtpTransport->name().c_str(), rtpTransport.get(), ret->name().c_str(), ret.get());
            if (rtcpTransport)
            {
                rtcpTransport->addDataDestination(ret);
                LogI("downlink rtcpTransport([%s]-[%p]) -> rtpengine([%s]-[%p])", rtcpTransport->name().c_str(), rtcpTransport.get(), ret->name().c_str(), ret.get());
                ret->addDataDestination(rtcpTransport);
            }
        }
    }
    setRTPStatisticsCallback(id);
    return ret;
}

std::shared_ptr<RtpEngine> MediaManagerInterfaceImp::createVideoRtp(const std::string& id, bool uplink)
{
    std::shared_ptr<RtpEngine> ret;
    if (!sessions_[VIDEO][id].local.rtpPort)
    {
        return ret;
    }
    if (!sessions_[VIDEO][id].remote.rtpPort)
    {
        return ret;
    }
    std::string tag = uplink ? "uplink" : "downlink";
    if (config_["video"][tag].contains("rtp"))
    {
        std::string rtpName = config_["video"][tag]["rtp"];
        // 收发使用同一个rtpengine
        for (auto it = sessions_[VIDEO][id].local.pipelines.begin(); it != sessions_[VIDEO][id].local.pipelines.end(); ++it)
        {
            if ((*it)->name() == rtpName)
            {
                LogI("find %s-%p", rtpName.c_str(), it->get());
                ret = std::dynamic_pointer_cast<RtpEngine>(*it);
                break;
            }
        }
        for (auto it = sessions_[VIDEO][id].remote.pipelines.begin(); it != sessions_[VIDEO][id].remote.pipelines.end(); ++it)
        {
            if ((*it)->name() == rtpName)
            {
                LogI("find %s-%p", rtpName.c_str(), it->get());
                ret = std::dynamic_pointer_cast<RtpEngine>(*it);
                break;
            }
        }
        if (!ret)
        {
            nlohmann::json j;
            j["bindIp"] = sessions_[VIDEO][id].local.ip;
            if (sessions_[VIDEO][id].local.bindIp != "") {
                j["bindIp"] = sessions_[VIDEO][id].local.bindIp;
            }
            j["bindPort"] = *sessions_[VIDEO][id].local.rtpPort;
            j["payloadType"] = sessions_[VIDEO][id].local.codec.pt;
            j["local_payload_type"] = sessions_[VIDEO][id].local.payload_type;
            j["remote_payload_type"] = sessions_[VIDEO][id].remote.payload_type;
            j["fmt"] = sessions_[VIDEO][id].local.codec.fmt;
            j["dtls"]  = sessions_[VIDEO][id].local.dtls;

            nlohmann::json dst;
            dst["ip"] = sessions_[VIDEO][id].remote.ip;
            dst["port"] = *sessions_[VIDEO][id].remote.rtpPort;

            j["dst"] = nlohmann::json::array();
            j["dst"].push_back(dst);

            bool use_rtp_proxy
                = (sessions_[VIDEO][id].local.video_nack_enabled || sessions_[VIDEO][id].local.video_fec_enabled
                   || sessions_[VIDEO][id].remote.video_jitter_buffer);
            if (use_rtp_proxy)
            {
                j["NACK"] = sessions_[VIDEO][id].local.video_nack_enabled;
                j["ULPFEC"] = sessions_[VIDEO][id].local.video_fec_enabled;
                j["AJB"] = sessions_[VIDEO][id].remote.video_jitter_buffer;
                j["remote_ulpfec_payload_type"] = sessions_[VIDEO][id].remote.ulpfec_payload_type;
                j["local_ulpfec_payload_type"] = sessions_[VIDEO][id].local.ulpfec_payload_type;
                j["local_media"] = sessions_[VIDEO][id].local.mediaPart;
                j["remote_media"] = sessions_[VIDEO][id].remote.mediaPart;
            }
            if (config_["video"]["uplink"].contains("rtp-proxy") && use_rtp_proxy) {
                j["rtpProxy"]["destination"] = config_["video"]["uplink"]["rtp-proxy"];
            }

            if (config_["video"]["downlink"].contains("rtp-proxy") && use_rtp_proxy) {
                j["rtpProxy"]["source"] = config_["video"]["downlink"]["rtp-proxy"];
            }

            nlohmann::json &encoder_params = sessions_[VIDEO][id].local.params;
            j["avgBitrate"] = 3000;
            if (encoder_params.contains("input") && encoder_params["input"].contains("avgBitrate")) {
                j["avgBitrate"] = encoder_params["input"]["avgBitrate"];
            }
            j["maxBitrate"] = 4000;
            if (encoder_params.contains("input") && encoder_params["input"].contains("maxBitrate")) {
                j["maxBitrate"] = encoder_params["input"]["maxBitrate"];
            }    
            int start_bitrate = j["avgBitrate"];
            j["startBitrate"] = start_bitrate * 2 / 3;
            if (start_bitrate <= 0) {
                j["startBitrate"] = 4000;
                j["maxBitrate"] = 4000;
            }
            j["is_h323"] = sessions_[VIDEO][id].is_h323;
            j["clock_rate"] = getClockrate(sessions_[VIDEO][id].local.payload_type);
            std::string rtpName = config_["video"][tag]["rtp"];
            LogI(
                "video[%s-%d-%d-%d] CreateRTPEngine(%s) %s", sessions_[VIDEO][id].local.codec.name, sessions_[VIDEO][id].local.codec.pt,
                sessions_[VIDEO][id].local.codec.rate, sessions_[VIDEO][id].local.codec.chn, rtpName.c_str(), j.dump().c_str());

            ret = RtpEngine::CreateRTPEngine(rtpName, j.dump());
        }
        if (ret)
        {
            ret->start();
            nlohmann::json j;
            j["rtp"]["fingerprint"] = sessions_[VIDEO][id].remote.fingerprint;
            j["rtp"]["isServer"] = sessions_[VIDEO][id].remote.isServer;
            j["rtcp"]["fingerprint"] = sessions_[VIDEO][id].remote.fingerprint;
            j["rtcp"]["isServer"] = sessions_[VIDEO][id].remote.isServer;

            nlohmann::json dst;
            dst["ip"] = sessions_[VIDEO][id].remote.ip;
            dst["port"] = *sessions_[VIDEO][id].remote.rtpPort;

            j["dst"] = nlohmann::json::array();
            j["dst"].push_back(dst);
            j["is_h323"] = sessions_[VIDEO][id].is_h323;
            j["clock_rate"] = getClockrate(sessions_[VIDEO][id].local.payload_type);
            if (sessions_[VIDEO][id].remote.stunSdp != "")
            {
                j["sdp"] = nlohmann::json::parse(sessions_[VIDEO][id].remote.stunSdp);
            }
            std::string update = j.dump();
            ret->updateParam(update);
        }
    }
    return ret;
}

std::shared_ptr<VideoFrameProcesser> MediaManagerInterfaceImp::createVideoPreprocessor(int dev, const std::string& id) {
    std::shared_ptr<VideoFrameProcesser> ret;
    if (config_["video"].contains("capturer") && config_["video"]["capturer"].contains("name"))
    {
        nlohmann::json &encoder_params = sessions_[VIDEO][id].local.params;
        nlohmann::json j;
        j["width"] = 1920;
        if (encoder_params.contains("input") && encoder_params["input"].contains("width")) {
            j["width"] = encoder_params["input"]["width"];
        } else if (config_["video"]["Preprocessor"].contains("width")) {
            j["width"] = config_["video"]["Preprocessor"]["width"];
        }
        j["height"] = 1080;
        if (encoder_params.contains("input") && encoder_params["input"].contains("height")) {
            j["height"] = encoder_params["input"]["height"];
        } else if (config_["video"]["Preprocessor"].contains("height")) {
            j["height"] = config_["video"]["Preprocessor"]["height"];
        }
        j["fps"] = 30;
        if (config_["video"]["Preprocessor"].contains("fps"))
        {
            j["fps"] = config_["video"]["Preprocessor"]["fps"];
        }
        if (config_["video"]["Preprocessor"].contains("format"))
        {
            j["format"] = config_["video"]["Preprocessor"]["format"];
        }
        if (config_["video"]["Preprocessor"].contains("dev"))
        {
            j["dev"] = config_["video"]["Preprocessor"]["dev"];
        }
        if (dev > -1)
            j["dev"] = dev;
        if (config_["video"]["capturer"].contains("app")) {
            j["app"] = config_["video"]["capturer"]["app"];
        }
        jinfo("CreateVideoPreprocessor dev = %d", dev);
        ret = VideoFrameProcesser::CreateVideoProcesser(config_["video"]["Preprocessor"]["name"], j.dump());
    }
    return ret;
}

std::shared_ptr<VideoFrameMixer> MediaManagerInterfaceImp::createVideoMixer(const std::string& id) {
    std::string workingId = getWorkingIdBySessionId(id);
    if (workingId != id && sessions_[VIDEO].count(workingId) != 0 && sessions_[VIDEO][workingId].remote.mixer) {
        jinfo("createVideoMixer[%s]#############workingId = %s", id.c_str(), workingId.c_str());
        return std::dynamic_pointer_cast<VideoFrameMixer>(sessions_[VIDEO][workingId].remote.mixer);
    }
    std::shared_ptr<VideoFrameMixer> ret;
    nlohmann::json j;
    j["dev"] = sessions_[VIDEO][id].remote.dev;
    j["width"] = 1920;
    j["height"] = 1080;
    std::string name = "RKVideoFrameMixer";
    ret = VideoFrameMixer::CreateVideoMixer(name, j.dump());
    jinfo("CreateVideoMixer %s dev = %d", name.c_str(), sessions_[VIDEO][id].remote.dev);
    return ret;
}

bool MediaManagerInterfaceImp::getCameraStatus(const std::string& jsonParams) {
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    int dev = 0, gpuIndex = 0;
    if (j.contains("dev")) dev = j["dev"];
    if (dev < 0) return false;
    if (j.contains("gpuIndex")) gpuIndex = j["gpuIndex"];
    std::shared_ptr<VideoFrameCapturer> capturer;
    if (video_capturers_.count(dev) != 0 && video_capturers_[dev].pipeline) {
        capturer = std::dynamic_pointer_cast<VideoFrameCapturer>(video_capturers_[dev].pipeline);
        video_capturers_[dev].ref++;
    } else {
        capturer = createVideoCapturer(dev, gpuIndex);
        video_capturers_[dev].pipeline = capturer;
        video_capturers_[dev].ref++;
    }
    if (!capturer) return false;
    return capturer->GetConnectStatus();
}
#endif

int MediaManagerInterfaceImp::getLocalSDP(const std::string& id, bool isOffer, const std::string& jsonParams, const SDPNotifyCallback& cb)
{
    CHECK_AUDIO_SESSION_EXIST;
    std::string sdp;
    int ret = GetLocalSDP(id, sdp, jsonParams);
    if (ret != MediaManagerInterface::MM_SUCCESS)
    {
        return ret;
    }
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    if (!j.contains("stun-server") || !j.contains("stun-port"))
    {
        return MediaManagerInterface::MM_INVALID_PARAMETER;
    }
    bool useVideo = false;
#ifdef ENABLE_VIDEO
    if (j.contains("video"))
    {
        useVideo = j["video"];
    }
    if (!config_.contains("video"))
    {
        useVideo = false;
    }
#endif
    j["stun"] = true;
    j["id"] = id;
    makeStunSdp(id, sdp, useVideo, j.dump(), cb); 
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::processOffer(const std::string& id, const std::string& remotesdp, const std::string& jsonParams, const SDPNotifyCallback& cb)
{
    LogI("onOffer2\n %s", remotesdp.c_str());
    // 被叫
    // 从自己的优选编码表里去除对端的编码表中不支持的
    std::list<CodecInst> codecs[2];
    getPriorityCodecs(id, codecs[AUDIO], codecs[VIDEO]);
    int ret = MediaManagerInterface::MM_SUCCESS;
    struct TempMediaInfo
    {
        CodecInst inst;
        std::string ip;
        int port = -1;
        std::string dir;
        std::list<CodecInst> codecs;
        nlohmann::json mjson;
        nlohmann::json fmtp;
        bool dtls = false;
        bool isServer = false;
        std::string fingerprint;
    };
    TempMediaInfo info[2];
    auto sdp = sdptransform::parse(remotesdp);
    LogI("onOffer json\n %s", sdp.dump().c_str());
    for (size_t i = 0; i < sdp["media"].size(); i++)
    {
        // TODO: To process "application", "text" and "message"
        std::string media_type = sdp["media"][i]["type"];
        if (media_type != "audio" && media_type != "video") {
            jwarn("Unknown media type: ", media_type.c_str());
            continue;
        }
        int index = sdp["media"][i]["type"] == "audio" ? 0 : 1;
        if (sdp["media"][i].contains("iceUfrag") && sdp["media"][i].contains("icePwd") && sdp["media"][i].contains("candidates"))
        {
            nlohmann::json j;
            j["iceUfrag"] = sdp["media"][i]["iceUfrag"];
            j["icePwd"] = sdp["media"][i]["icePwd"];
            j["candidates"] = sdp["media"][i]["candidates"];
            sessions_[index][id].remote.stunSdp = j.dump();
        }
        if (sdp["media"][i].contains("fingerprint"))
        {
            info[index].dtls = true;
            info[index].fingerprint = sdp["media"][i]["fingerprint"]["hash"];
            sessions_[index][id].remote.dtls = true;
            sessions_[index][id].remote.fingerprint = info[index].fingerprint;
            std::string dtls_dir = sdp["media"][i]["setup"];
            if (dtls_dir == "active")
            {
                sessions_[index][id].local.isServer = true;
                sessions_[index][id].remote.isServer = false;
            }
            else if (dtls_dir == "passive")
            {
                sessions_[index][id].local.isServer = false;
                sessions_[index][id].remote.isServer = true;
            }
        }
		if (index == AUDIO && sdp["media"][i].contains("roomInfo"))
		{
			std::string roomInfo = sdp["media"][i]["roomInfo"];
			nlohmann::json j = nlohmann::json::parse(roomInfo);
			if (j.contains("ip"))
			{
				sessions_[index][id].local.multicastIP = j["ip"];
			}
			if (j.contains("port"))
			{
				sessions_[index][id].local.multicastPort = j["port"];
			}
		}

        info[index].mjson = sdp["media"][i]["rtp"];
        info[index].fmtp = sdp["media"][i]["fmtp"];
        info[index].ip = sdp["origin"]["address"];
        if (sdp.contains("connection") && sdp["connection"].contains("ip"))
            info[index].ip = sdp["connection"]["ip"];
        if (sdp["media"][i].contains("connection") && sdp["media"][i]["connection"].contains("ip"))
        {
            info[index].ip = sdp["media"][i]["connection"]["ip"];
        }
        info[index].port = sdp["media"][i]["port"];
        info[index].dir = "sendrecv";
        if (sdp["media"][i].contains("direction"))
        {
            info[index].dir = sdp["media"][i]["direction"];
        }

        std::string payloads = sdp["media"][i]["payloads"];
        generateRtpMapFromPayloads(info[index].mjson, payloads);
    }
#ifdef ENABLE_VIDEO
    for (size_t type = 0; type < VIDEO + 1; type++)
#else
    for (size_t type = 0; type < VIDEO; type++)
#endif
    {
        for (auto it = codecs[type].begin(); it != codecs[type].end(); it++)
        {
            bool isFound = false;
            for (size_t i = 0; i < info[type].mjson.size(); i++)
            {
                CodecInst inst = codecInstFromJson(info[type].mjson[i]);
                LogI("%s codec %s", (type == AUDIO ? "audio" : "video"), inst.name);
                if (inst == *it)
                {
                    info[type].inst = inst;
                    isFound = true;
                    break;
                }
            }
            if (isFound)
            {
                info[type].codecs.push_back(info[type].inst);
                break;
            }
        }
    }
    std::string localsdp;
    ret = getLocalSDP(id, localsdp, true, jsonParams, info[AUDIO].codecs, info[VIDEO].codecs, info[AUDIO].dtls);
    if (ret == MediaManagerInterface::MM_SUCCESS)
    {
#ifdef ENABLE_VIDEO
        for (size_t type = 0; type < VIDEO + 1; type++)
#else
        for (size_t type = 0; type < VIDEO; type++)
#endif
        {
            makeRemoteSession(id, info[type].inst, info[type].ip, info[type].port, info[type].dir, (CodecType)type);
        }
    }
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    bool useVideo = !info[VIDEO].codecs.empty();
    j["dtls"] = info[AUDIO].dtls;
    j["stun"] = true;
    j["id"] = id;
    makeStunSdp(id, localsdp, useVideo, j.dump(), cb);
    return ret;
}

void MediaManagerInterfaceImp::makeStunSdp(const std::string& id, const std::string& sdp, bool useVideo, const std::string& jsonParams, const SDPNotifyCallback& cb)
{
    std::string params = jsonParams;
    std::string rtpName[2] { "JRtpEngine", "JRtpEngine" };
    if (config_.contains("audio") && config_["audio"].contains("uplink") && config_["audio"]["uplink"].contains("rtp")) {
        rtpName[AUDIO] = config_["audio"]["uplink"]["rtp"];
    }
#ifdef ENABLE_VIDEO
    if (config_.contains("video") && config_["video"].contains("uplink") && config_["video"]["uplink"].contains("rtp")) {
        rtpName[VIDEO] = config_["video"]["uplink"]["rtp"];
    }
    for (size_t type = 0; type < VIDEO + 1; type++)
#else
    for (size_t type = 0; type < VIDEO; type++)
#endif
    {
        if (type == VIDEO)
        {
            nlohmann::json j;
            if (params != "") j = nlohmann::json::parse(params);
            bool use_rtp_proxy
                = (sessions_[VIDEO][id].local.video_nack_enabled || sessions_[VIDEO][id].local.video_fec_enabled
                   || sessions_[VIDEO][id].remote.video_jitter_buffer);
            if (config_["video"]["uplink"].contains("rtp-proxy") && use_rtp_proxy)
            {
                j["rtpProxy"]["destination"] = config_["video"]["uplink"]["rtp-proxy"];
            }

            if (config_["video"]["downlink"].contains("rtp-proxy") && use_rtp_proxy)
            {
                j["rtpProxy"]["source"] = config_["video"]["downlink"]["rtp-proxy"];
            }
            params = j.dump();
        }
        auto rtp = RtpEngine::CreateRTPEngine(rtpName[type], params);
        if (rtp)
        {
            sessions_[type][id].local.pipelines.insert(rtp);
            sessions_[type][id].cb = cb;
            rtp->setNotify("sdp", "", nullptr, [this, useVideo, type, id, sdp](const std::string& key, const std::string& stunSdp, void* param) {
                workThread_.loop()->runInLoop([=](){
                    jinfo("sdp notify %d %s\n--------------\n%s\n-----------------", type, id.c_str(), stunSdp.c_str());
                    sessions_[type][id].local.stunSdp = stunSdp;
                    if (!useVideo || (sessions_[AUDIO][id].local.stunSdp != "" && sessions_[VIDEO][id].local.stunSdp != ""))
                    {
                        nlohmann::json sdpJson = sdptransform::parse(sdp);
                        for (size_t i = 0; i < sdpJson["media"].size(); i++)
                        {
                            int index = sdpJson["media"][i]["type"] == "audio"? 0 : 1;
                            nlohmann::json stunJson = sdptransform::parse(sessions_[index][id].local.stunSdp);
                            sdpJson["media"][i]["iceUfrag"] = stunJson["iceUfrag"];
                            sdpJson["media"][i]["icePwd"] = stunJson["icePwd"];
                            sdpJson["media"][i]["candidates"] = stunJson["candidates"];
                        }
                        std::string newsdp = sdptransform::write(sdpJson);
                        if (sessions_[type][id].cb)
                        {
                            sessions_[type][id].cb(id, newsdp, MediaManagerInterface::MM_SUCCESS);
                        }

                        if ((useVideo && sessions_[AUDIO][id].local.stunSdp != "" && sessions_[AUDIO][id].remote.stunSdp != "" &&
                            sessions_[AUDIO][id].local.stunSdp != "" && sessions_[AUDIO][id].remote.stunSdp != "") ||
                            (!useVideo && sessions_[AUDIO][id].local.stunSdp != "" && sessions_[AUDIO][id].remote.stunSdp != ""))
                        {
                            doMediaPipeline(id);
                        }
                    }
                });
            });
            rtp->start();
        }
    }
}

int MediaManagerInterfaceImp::setRemoteSDP(const std::string& id, const std::string& remotesdp, const std::string& jsonParams, const SDPNotifyCallback& cb)
{
    CHECK_AUDIO_SESSION_EXIST;
    int ret = MediaManagerInterface::MM_SUCCESS;
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    if (!j.contains("stun-server") || !j.contains("stun-port"))
    {
        return MediaManagerInterface::MM_INVALID_PARAMETER;
    }
    if (sessions_[AUDIO][id].local.ip != "")
    {
        ret = processAnswer(id, remotesdp, jsonParams);
        if (ret == MediaManagerInterface::MM_SUCCESS)
        {
            ret = doMediaPipeline(id);
        }
        else
        {
            A_LogW_F("doMediaPipeline fail");
        }
    }
    else
    {
        ret = processOffer(id, remotesdp, jsonParams, cb);
    }
    return ret;
}

bool MediaManagerInterfaceImp::isSupportCodec(const CodecInst& codec, CodecType type)
{
    LogI(
        "isSupportCodec %d %d %s %d %d %d %d %d %d", codec.fmt, codec.pt, codec.name, codec.chn, codec.rate, codec.bitrate, codec.ptime, codec.tsinc,
        codec.profile);
    for (auto findcodec : codecs_[type]) {
        LogI(
            "findcodec %d %d %s %d %d %d %d %d %d", findcodec.fmt, findcodec.pt, findcodec.name, findcodec.chn, findcodec.rate, findcodec.bitrate,
            findcodec.ptime, findcodec.tsinc, findcodec.profile);
    }
    // TODO: 不应该区分g7221
    auto iter = std::find(codecs_[type].begin(), codecs_[type].end(), codec);
    if (iter != codecs_[type].end() && strcasecmp(iter->name, "G7221") == 0 && iter->bitrate != codec.bitrate) {
        return false;
    }
    // bool ret = std::find(codecs_[type].begin(), codecs_[type].end(), codec) != codecs_[type].end();
    bool ret = iter != codecs_[type].end();
    LogI("isSupportCodec(%d) %s %d %d", ret, codec.name, codec.chn, codec.rate);
    return ret;
}

void MediaManagerInterfaceImp::switchAudio(const std::string& oldId, const std::string& newId)
{
    if (oldId == newId) {
        jwarn("switchAudio failed: same sessionId[%s]", oldId.c_str());
        return;
    }
    if (sessions_[AUDIO].find(oldId) == sessions_[AUDIO].end()) {
        jwarn("switchAudio failed: sessionId[%s] is not existed", oldId.c_str());
        return;
    }
    if (sessions_[AUDIO][oldId].remote.use_dispatcher && !sessions_[AUDIO][oldId].remote.renders.empty()
        && sessions_[AUDIO].find(newId) != sessions_[AUDIO].end()) {
        sessions_[AUDIO][newId].remote.renders.clear();
        for (auto &kv : sessions_[AUDIO][oldId].remote.renders) {
            auto audioRender = std::dynamic_pointer_cast<AudioFrameRender>(kv.second);
            auto srcs = audioRender->getAudioSources();
            for (auto src : srcs)
            {
                auto source = src.lock();
                if (source)
                {
                    jinfo("removeAudioDestination %s-%p %s-%p", source->name().c_str(), source, audioRender->name().c_str(), audioRender);
                    source->removeAudioDestination(audioRender);
                }
            }
            if (sessions_[AUDIO].find(newId) != sessions_[AUDIO].end())
            {
                for (auto &decoder : sessions_[AUDIO][newId].remote.pipelines)
                {
                    if (AudioFrameDecoder::IsAudioFrameDecoder(decoder))
                    {
                        decoder->addAudioDestination(audioRender);
                        LogI("switchAudio decoder[%p]->audioRender[%p] newId(%s)!!!!!!!!!!!!", decoder.get(), audioRender.get(), newId.c_str());
                        break;
                    }
                }
                // sessions_[AUDIO][newId].remote.pipelines.insert(audioRender);
                audioRender->resetConnectState(true);
                sessions_[AUDIO][newId].remote.renders[kv.first] = audioRender;
            }
            else
            {
                if (newId != "") LogI("switchAudio newId(%s) do not exist", newId.c_str());
            }
            if (sessions_[AUDIO][oldId].local.use_mixer) {
                sessions_[AUDIO][newId].local.capturers.clear();
                for (auto &kv: sessions_[AUDIO][oldId].local.capturers) {
                    sessions_[AUDIO][newId].local.capturers[kv.first] = kv.second;
                }
            }
        }
        if (sessions_[AUDIO].find(newId) != sessions_[AUDIO].end() && sessions_[AUDIO][oldId].remote.stream_monitor) {
            auto &monitor = sessions_[AUDIO][oldId].remote.stream_monitor;
            if (monitor) {
                nlohmann::json j;
                j["is-master"] = false;
                j["timeoutMS"] = 0;
                monitor->updateParam(j.dump());                
            }

            // auto srcs = monitor->getDataSources();
            // for (auto &src : srcs) {
            //     auto source = src.lock();
            //     if (source) {
            //         jinfo("removeAudioDestination %s-%p %s-%p", source->name().c_str(), source, monitor->name().c_str(), monitor);
            //         source->removeDataDestination(monitor);
            //     }
            // }
            if (sessions_[AUDIO].find(newId) != sessions_[AUDIO].end()) {
                // for (auto &pipeline : sessions_[AUDIO][newId].remote.pipelines) {
                //     if (RtpEngine::IsRTPEngine(pipeline)) {
                //         bool useNeteq = false;
                //         if (sessions_[AUDIO][newId].remote.params.contains("useNeteq"))
                //             useNeteq = sessions_[AUDIO][newId].remote.params["useNeteq"];
                //         if (useNeteq)
                //             pipeline->addDataDestination(monitor);
                //         else 
                //             pipeline->addAudioDestination(monitor);
                //         LogI("switchAudio RtpEngine[%p]->StreamMonitor[%p] newId(%s)!!!!!!!!!!!!", pipeline.get(), monitor.get(), newId.c_str());
                //         break;
                //     }
                // }
                // sessions_[AUDIO][newId].remote.stream_monitor = monitor;
                auto &monitor = sessions_[AUDIO][newId].remote.stream_monitor;
                if (monitor) {
                    nlohmann::json j;
                    j["is-master"] = true;
                    j["timeoutMS"] = default_audio_timeout_ms_;
                    monitor->updateParam(j.dump());
                    monitor->resetConnectState(true);                    
                }
            }
        }
    } else {
        std::shared_ptr<AudioFrameRender> audioRender;
        for (auto& pileline : sessions_[AUDIO][oldId].remote.pipelines)
        {
            if (AudioFrameRender::isAudioFrameRender(pileline))
            {
                audioRender = std::dynamic_pointer_cast<AudioFrameRender>(pileline);
                jinfo("switchAudio audioRender name = %s", audioRender->name().c_str());
                break;
            }
        }
        if (audioRender && sessions_[AUDIO].find(newId) != sessions_[AUDIO].end())
        {
            if (sessions_[AUDIO][oldId].remote.stream_monitor) {
                auto &monitor = sessions_[AUDIO][oldId].remote.stream_monitor;
                if (monitor) {
                    nlohmann::json j;
                    j["is-master"] = false;
                    j["timeoutMS"] = 0;
                    monitor->updateParam(j.dump());
                }

                // auto srcs = monitor->getDataSources();
                // for (auto &src : srcs) {
                //     auto source = src.lock();
                //     if (source) {
                //         jinfo("removeAudioDestination %s-%p %s-%p", source->name().c_str(), source, monitor->name().c_str(), monitor);
                //         source->removeDataDestination(monitor);
                //     }
                // }
                if (sessions_[AUDIO].find(newId) != sessions_[AUDIO].end()) {
                    // for (auto &pipeline : sessions_[AUDIO][newId].remote.pipelines) {
                    //     if (RtpEngine::IsRTPEngine(pipeline)) {
                    //         bool useNeteq = false;
                    //         if (sessions_[AUDIO][newId].remote.params.contains("useNeteq"))
                    //             useNeteq = sessions_[AUDIO][newId].remote.params["useNeteq"];
                    //         if (useNeteq)
                    //             pipeline->addDataDestination(monitor);
                    //         else 
                    //             pipeline->addAudioDestination(monitor);
                    //         LogI("switchAudio RtpEngine[%p]->StreamMonitor[%p] newId(%s)!!!!!!!!!!!!", pipeline.get(), monitor.get(), newId.c_str());
                    //         break;
                    //     }
                    // }
                    // sessions_[AUDIO][newId].remote.stream_monitor = monitor;
                    // monitor->resetConnectState(true);
                    auto &monitor = sessions_[AUDIO][newId].remote.stream_monitor;
                    if (monitor) {
                        nlohmann::json j;
                        j["is-master"] = true;
                        j["timeoutMS"] = default_audio_timeout_ms_;
                        monitor->updateParam(j.dump());
                        monitor->resetConnectState(true);
                    }
                }
            }
            auto srcs = audioRender->getAudioSources();
            for (auto src : srcs)
            {
                auto source = src.lock();
                if (source)
                {
                    jinfo("removeAudioDestination %s-%p %s-%p", source->name().c_str(), source, audioRender->name().c_str(), audioRender);
                    source->removeAudioDestination(audioRender);
                }
            }
            if (sessions_[AUDIO].find(newId) != sessions_[AUDIO].end())
            {
                for (auto &pipeline : sessions_[AUDIO][newId].remote.pipelines)
                {
                    if (!sessions_[AUDIO][newId].remote.use_uart) {
                        if (AudioFrameDecoder::IsAudioFrameDecoder(pipeline))
                        {
                            pipeline->addAudioDestination(audioRender);
                            LogI("switchAudio pipeline[%p]->audioRender[%p] newId(%s)!!!!!!!!!!!!", pipeline.get(), audioRender.get(), newId.c_str());
                            break;
                        }
                    } else  {
                        if (RtpEngine::IsRTPEngine(pipeline)) {
                            pipeline->addAudioDestination(audioRender);
                            LogI("switchAudio pipeline[%p]->audioRender[%p] newId(%s)!!!!!!!!!!!!", pipeline.get(), audioRender.get(), newId.c_str());
                            break;
                        }
                    }
                }
                sessions_[AUDIO][newId].remote.pipelines.insert(audioRender);
                sessions_[AUDIO][newId].remote.render = audioRender;
                audioRender->resetConnectState(true);
            }
            else
            {
                if (newId != "") LogI("switchAudio newId(%s) do not exist", newId.c_str());
            }
        }
        else
        {
            LogI("switchAudio oldId(%s) do not exist render", oldId.c_str());
        }
    }
}

void MediaManagerInterfaceImp::switchVideo(const std::string& oldId, const std::string& newId)
{
#ifdef ENABLE_VIDEO    
    jinfo("switchVideo videoRender oldId = %s newId = %s", oldId.c_str(), newId.c_str());
    if (oldId == newId) {
        jwarn("switchVideo failed: same sessionId[%s]", oldId.c_str());
        return;
    }
    if (sessions_[VIDEO].find(oldId) == sessions_[VIDEO].end()) {
        jwarn("switchVideo failed: sessionId[%s] is not existed", oldId.c_str());
        return;
    }
    std::shared_ptr<VideoFrameRender> videoRender;
    for (auto& render : sessions_[VIDEO][oldId].remote.pipelines)
    {
        if (VideoFrameRender::isVideoFrameRender(render))
        {
            videoRender = std::dynamic_pointer_cast<VideoFrameRender>(render);
            jinfo("switchVideo videoRender name = %s", videoRender->name().c_str());
            break;
        }
    }
    if (videoRender && sessions_[VIDEO].find(newId) != sessions_[VIDEO].end())
    {
        if (sessions_[VIDEO][oldId].remote.stream_monitor) {
            auto &monitor = sessions_[VIDEO][oldId].remote.stream_monitor;
            // auto srcs = monitor->getVideoSources();
            // for (auto &src : srcs) {
            //     auto source = src.lock();
            //     if (source) {
            //         jinfo("removeVideoDestination %s-%p %s-%p", source->name().c_str(), source, monitor->name().c_str(), monitor);
            //         source->removeVideoDestination(monitor);
            //     }
            // }
            if (monitor) {
                nlohmann::json j;
                j["is-master"] = false;
                j["timeoutMS"] = 0;
                monitor->updateParam(j.dump());
            }

            if (sessions_[VIDEO].find(newId) != sessions_[VIDEO].end()) {
                // for (auto &pipeline : sessions_[VIDEO][newId].remote.pipelines) {
                //     if (RtpEngine::IsRTPEngine(pipeline)) {
                //         pipeline->addVideoDestination(monitor);
                //         LogI("switchVideo RtpEngine[%p]->StreamMonitor[%p] newId(%s)!!!!!!!!!!!!", pipeline.get(), monitor.get(), newId.c_str());
                //         break;
                //     }
                // }
                // sessions_[VIDEO][newId].remote.stream_monitor = monitor;
                // monitor->resetConnectState(true);
                auto &monitor = sessions_[VIDEO][newId].remote.stream_monitor;
                if (monitor) {
                    nlohmann::json j;
                    j["is-master"] = true;
                    j["timeoutMS"] = default_video_timeout_ms_;
                    monitor->updateParam(j.dump());
                    monitor->resetConnectState(true);                    
                }

            }
        }
        int32_t source_id = 0;
        auto old_rendererPrevious = sessions_[VIDEO][oldId].remote.rendererPrevious.lock();
        if (old_rendererPrevious) {
            videoRender->getSourceId(old_rendererPrevious.get(), source_id);
        }
        auto srcs = videoRender->getVideoSources();
        for (auto src : srcs)
        {
            auto source = src.lock();
            if (source)
            {
                videoRender->unsetSourceId(source.get());
                source->removeVideoDestination(videoRender);
            }
        }
        if (sessions_[VIDEO].find(newId) != sessions_[VIDEO].end())
        {
            // for (auto &pipeline : sessions_[VIDEO][newId].remote.pipelines)
            // {
            //     if (VideoFrameDecoder::IsVideoFrameDecoder(pipeline))
            //     {
            //         pipeline->addVideoDestination(videoRender);
            //         LogI("switchVideo pipeline[%p]->videoRender[%p] newId(%s)!!!!!!!!!!!!", pipeline.get(), videoRender.get(), newId.c_str());
            //         break;
            //     }
            // }
            auto rendererPrevious = sessions_[VIDEO][newId].remote.rendererPrevious.lock();
            if (rendererPrevious) {
                videoRender->setSourceId(rendererPrevious.get(), source_id);
                rendererPrevious->addVideoDestination(videoRender);
                LogI("switchVideo videoRenderPrevious[%p]->videoRender[%p] oldId(%s)", rendererPrevious.get(), videoRender.get(), newId.c_str());
            }
            sessions_[VIDEO][oldId].remote.pipelines.erase(videoRender);
            sessions_[VIDEO][oldId].local.render.reset();
            sessions_[VIDEO][newId].remote.pipelines.insert(videoRender);
            videoRender->resetConnectState(true);
        }
        else
        {
            if (newId != "") LogI("switchVideo newId(%s) do not exist", newId.c_str());
        }
    }
    else
    {
        LogI("switchVideo oldId(%s) do not exist render", oldId.c_str());
    }
#endif
}

// NOTE: 只设置id，不改变媒体流
void MediaManagerInterfaceImp::setWorkingId(const std::string poolId, const std::string& id, const std::string& oldId)
{
    for (auto it = mediaPools_[poolId].poolIds.begin(); it != mediaPools_[poolId].poolIds.end(); it++)
    {
        if (*it == id)
        {
            mediaPools_[poolId].poolIds.erase(it);
            break;
        }
    }
    if (mediaPools_[poolId].workingId != "" && id != oldId)
    {
        mediaPools_[poolId].poolIds.push_back(mediaPools_[poolId].workingId);
    }
    mediaPools_[poolId].workingId = id;
    jinfo("setWorkingId [%s] [%s]", poolId.c_str(), id.c_str());
    if (mediaPools_[poolId].cb)
    {
        mediaPools_[poolId].cb(poolId, id);
    }
    if (mediaPools_[poolId].cb2)
    {
        mediaPools_[poolId].cb2(poolId, id, oldId);
    }
}

std::string MediaManagerInterfaceImp::getWorkingId(const std::string &poolId) {
    if (mediaPools_.count(poolId) != 0)
        return mediaPools_[poolId].workingId;
    return "";
}

int MediaManagerInterfaceImp::setRemoteMediaTimeoutListener(const std::string& id, int timeoutMS, const MediaTimeoutCallback& cb)
{
    jinfo("setMediaTimeoutListener id=[%s] timeout=[%dms]", id.c_str(), timeoutMS);
    CHECK_AUDIO_SESSION_EXIST;
    sessions_[AUDIO][id].remote.timeoutMS = timeoutMS;
    sessions_[AUDIO][id].remote.timeoutCB = cb;
#ifdef ENABLE_VIDEO 
    if (sessions_[VIDEO].find(id) != sessions_[VIDEO].end())
    {
        sessions_[VIDEO][id].remote.timeoutMS = timeoutMS;
        sessions_[VIDEO][id].remote.timeoutCB = cb;
    }
#endif
    // setRemoteMediaTimeoutCallback(id);
    setStreamMonitorTimeoutCallback(id);
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::setRemoteMediaTimeoutListener2(const std::string& id, int timeoutMS, const MediaTimeoutCallback2& cb)
{
    jinfo("setMediaTimeoutListener2 id=[%s] timeout=[%dms]", id.c_str(), timeoutMS);
    CHECK_AUDIO_SESSION_EXIST;
    sessions_[AUDIO][id].remote.timeoutMS = timeoutMS;
    sessions_[AUDIO][id].remote.timeoutCB2 = cb;
#ifdef ENABLE_VIDEO 
    if (sessions_[VIDEO].find(id) != sessions_[VIDEO].end())
    {
        sessions_[VIDEO][id].remote.timeoutMS = timeoutMS;
        sessions_[VIDEO][id].remote.timeoutCB2 = cb;
    }
#endif
    // setRemoteMediaTimeoutCallback(id);
    setStreamMonitorTimeoutCallback(id);
    return MediaManagerInterface::MM_SUCCESS;
}

void MediaManagerInterfaceImp::setRemoteMediaTimeoutCallback(const std::string& id)
{
    // TODO: 当前仅同源只支持一路渲染备份，如果需要支持多路渲染，需要升级代码
    for (auto& render : sessions_[AUDIO][id].remote.pipelines)
    {
        if (AudioFrameRender::isAudioFrameRender(render))
        {
            if (sessions_[AUDIO][id].remote.timeoutMS <= 0) break;
            jinfo("setRemoteMediaTimeoutCallback Audio Render timout %d", sessions_[AUDIO][id].remote.timeoutMS);
            render->setFrameTimeoutNotify(sessions_[AUDIO][id].remote.timeoutMS, &sessions_[AUDIO][id], [this, id](long ms, void *param) {
                if (&sessions_[AUDIO][id] == param) {
                    if (sessions_[AUDIO][id].remote.timeoutCB && ms > 0) {
                        sessions_[AUDIO][id].remote.timeoutCB(id, false);
                    }
                    if (sessions_[AUDIO][id].remote.timeoutCB2) {
                        if (ms <= 0)
                            sessions_[AUDIO][id].remote.timeoutCB2(id, false, true);
                        else
                            sessions_[AUDIO][id].remote.timeoutCB2(id, false, false);
                    }
                }
            });
            break;
        }
    }
    
#ifdef ENABLE_VIDEO    
    for (auto& render : sessions_[VIDEO][id].remote.pipelines)
    {
        if (VideoFrameRender::isVideoFrameRender(render))
        {
            if (sessions_[VIDEO][id].remote.timeoutMS <= 0) break;
            jinfo("setRemoteMediaTimeoutCallback Video Render timout %d", sessions_[VIDEO][id].remote.timeoutMS);
            render->setFrameTimeoutNotify(sessions_[VIDEO][id].remote.timeoutMS, &sessions_[VIDEO][id], [this, id](long ms, void* param) {
                if (&sessions_[VIDEO][id] == param && ms > 0)
                {
                    if (sessions_[VIDEO][id].remote.timeoutCB)
                    {
                        sessions_[VIDEO][id].remote.timeoutCB(id, true);
                    }
                }
                if (sessions_[VIDEO][id].remote.timeoutCB2) {
                    if (ms <= 0)
                        sessions_[VIDEO][id].remote.timeoutCB2(id, true, true);
                    else
                        sessions_[VIDEO][id].remote.timeoutCB2(id, true, false);
                }
            });
            break;
        }
    }
#endif
}

void MediaManagerInterfaceImp::setMediaPoolStreamCallback(const std::string poolId, const std::string& id)
{
    // 当前仅同源只支持一路渲染备份，如果需要支持多路渲染，需要升级代码
    jinfo("setMediaPoolStreamCallback %s %s", poolId.c_str(), id.c_str());
    for (auto& render : sessions_[AUDIO][id].remote.pipelines)
    {
        if (AudioFrameRender::isAudioFrameRender(render))
        {
            // TODO:
            void *temp = &sessions_[AUDIO][id];
            render->setFrameTimeoutNotify2(default_audio_timeout_ms_, &sessions_[AUDIO][id], [this, poolId, id, temp](long ms, void *param) {
                jinfo("setMediaPoolStreamCallback Audio FrameTimeoutNotify2 timeout %d", ms);
                if (temp == param && ms > 0) {
                    std::string oldId = getWorkingId(poolId);
                    jinfo("AUDIO [%s] [%s] timeout %d!!!!!!!!!", poolId.c_str(), oldId.c_str(), ms);
                    if (mediaPools_.count(poolId) == 0) return;
                    if (oldId == mediaPools_[poolId].workingId && !mediaPools_[poolId].poolIds.empty()) {
                        // 切换音频
                        std::string newId = *mediaPools_[poolId].poolIds.begin();
                        switchAudio(oldId, newId);
                        
                        // 音视频保持同步
                        if (sessions_[VIDEO].find(oldId) != sessions_[VIDEO].end())
                        {
                            switchVideo(oldId, newId);
                        }
                        setWorkingId(poolId, newId, oldId);
                        jinfo("oldId[%s] -> newId[%s]", oldId.c_str(), newId.c_str());
                    } else if (mediaPools_[poolId].poolIds.empty()) {
                        if (mediaPools_[poolId].cb)
                        {
                            mediaPools_[poolId].cb(poolId, oldId);
                        }
                        if (mediaPools_[poolId].cb2)
                        {
                            mediaPools_[poolId].cb2(poolId, oldId, oldId);
                        }
                        jinfo("oldId[%s] -> newId[%s]", oldId.c_str(), oldId.c_str());
                    }
                    if (sessions_[AUDIO].find(oldId) != sessions_[AUDIO].end() && sessions_[AUDIO][oldId].remote.timeoutCB && ms > 0)
                    {
                        sessions_[AUDIO][oldId].remote.timeoutCB(oldId, false);
                    }
                    if (sessions_[AUDIO].find(oldId) != sessions_[AUDIO].end() && sessions_[AUDIO][oldId].remote.timeoutCB2) {
                        if (ms <= 0)
                            sessions_[AUDIO][oldId].remote.timeoutCB2(oldId, false, true);
                        else
                            sessions_[AUDIO][oldId].remote.timeoutCB2(oldId, false, false);
                    }
                }
            });
            break;
        }
    }
    
#ifdef ENABLE_VIDEO    
    for (auto& render : sessions_[VIDEO][id].remote.pipelines)
    {
        if (VideoFrameRender::isVideoFrameRender(render))
        {
            // TODO:
            void *temp = &sessions_[VIDEO][id];
            render->setFrameTimeoutNotify(default_video_timeout_ms_, &sessions_[VIDEO][id], [this, poolId, id, temp](long ms, void *param) {
                jinfo("Video FrameTimeoutNotify2 timeout %d", ms);
                if (temp == param && ms > 0) {
                    std::string oldId = getWorkingId(poolId);
                    jinfo("VIDEO [%s] [%s] timeout %d!!!!!!!!!", poolId.c_str(), oldId.c_str(), ms);
                    if (mediaPools_.count(poolId) == 0) return;
                    if (oldId == mediaPools_[poolId].workingId && !mediaPools_[poolId].poolIds.empty()) {
                        // 切换音频
                        // mediaPools_[poolId].poolIds.push_back(mediaPools_[poolId].poolIds.front());
                        std::string newId = *mediaPools_[poolId].poolIds.begin();
                        switchVideo(oldId, newId);
                        
                        // 音视频保持同步
                        if (sessions_[AUDIO].find(id) != sessions_[AUDIO].end())
                        {
                            switchAudio(oldId, newId);
                        }
                        setWorkingId(poolId, newId, oldId);
                        jinfo("oldId[%s] -> newId[%s]", oldId.c_str(), newId.c_str());
                    } else if (mediaPools_[poolId].poolIds.empty()) {
                        if (mediaPools_[poolId].cb)
                        {
                            mediaPools_[poolId].cb(poolId, oldId);
                        }
                        if (mediaPools_[poolId].cb2)
                        {
                            mediaPools_[poolId].cb2(poolId, oldId, oldId);
                        }
                        jinfo("Nothing working done. oldId[%s] -> newId[%s]", oldId.c_str(), oldId.c_str());
                    }
                    if (sessions_[VIDEO][oldId].remote.timeoutCB && ms > 0)
                    {
                        sessions_[VIDEO][oldId].remote.timeoutCB(id, true);
                    }
                    if (sessions_[VIDEO][oldId].remote.timeoutCB2) {
                        if (ms <= 0)
                            sessions_[VIDEO][oldId].remote.timeoutCB2(oldId, true, true);
                        else
                            sessions_[VIDEO][oldId].remote.timeoutCB2(oldId, true, false);
                    }
                }
            });
            break;
        }
    }
#endif
}

void MediaManagerInterfaceImp::setStreamMonitorTimeoutCallback(const std::string& id) {
    // TODO: 当前仅同源只支持一路渲染备份，如果需要支持多路渲染，需要升级代码
    if (sessions_[AUDIO].count(id) != 0 && sessions_[AUDIO][id].remote.stream_monitor) {
        auto monitor = sessions_[AUDIO][id].remote.stream_monitor;
        if (sessions_[AUDIO][id].remote.timeoutMS <= 0) return;
        jinfo("setStreamMonitorTimeoutCallback Audio StreamMonitor timout %d", sessions_[AUDIO][id].remote.timeoutMS);
        monitor->setFrameTimeoutNotify(sessions_[AUDIO][id].remote.timeoutMS, &sessions_[AUDIO][id], [this, id](long ms, void *param) {
            jinfo("setStreamMonitorTimeoutCallback[%s] Audio FrameTimeoutNotify2 timeout %dms", id.c_str(), ms);
            if (&sessions_[AUDIO][id] == param) {
                cbThread_.loop()->runInLoop([this, ms, id](){
                    if (sessions_[AUDIO].count(id) == 0) return;
                    if (sessions_[AUDIO][id].remote.timeoutCB && ms > 0) {
                        sessions_[AUDIO][id].remote.timeoutCB(id, false);
                    } else {
                        if (sessions_[AUDIO][id].remote.timeoutCB2) {
                            if (ms <= 0)
                                sessions_[AUDIO][id].remote.timeoutCB2(id, false, true);
                            else
                                sessions_[AUDIO][id].remote.timeoutCB2(id, false, false);
                        } 
                    }

                });
            } });
    }
#ifdef ENABLE_VIDEO    
    if (sessions_[VIDEO].count(id) != 0 && sessions_[VIDEO][id].remote.stream_monitor) {
        auto monitor = sessions_[VIDEO][id].remote.stream_monitor;
        if (sessions_[VIDEO][id].remote.timeoutMS <= 0) return;
        jinfo("setStreamMonitorTimeoutCallback Video StreamMonitor timout %d", sessions_[VIDEO][id].remote.timeoutMS);
        monitor->setFrameTimeoutNotify(sessions_[VIDEO][id].remote.timeoutMS, &sessions_[VIDEO][id], [this, id](long ms, void *param) {
            jinfo("setStreamMonitorTimeoutCallback[%s] Video FrameTimeoutNotify2 timeout %dms", id.c_str(), ms);
            if (&sessions_[VIDEO][id] == param) {
                cbThread_.loop()->runInLoop([this, ms, id]() {
                    if (sessions_[VIDEO].count(id) == 0) return;
                    if (sessions_[VIDEO][id].remote.timeoutCB && ms > 0) {
                        sessions_[VIDEO][id].remote.timeoutCB(id, true);
                    } else {
                        if (sessions_[VIDEO][id].remote.timeoutCB2) {
                            if (ms <= 0)
                                sessions_[VIDEO][id].remote.timeoutCB2(id, true, true);
                            else
                                sessions_[VIDEO][id].remote.timeoutCB2(id, true, false);
                        } 
                    }
                });
            }
        });
    }
#endif
}

void MediaManagerInterfaceImp::setMediaPoolStreamMonitorCallback(const std::string poolId, const std::string& id) {
    // 当前仅同源只支持一路渲染备份，如果需要支持多路渲染，需要升级代码
    jinfo("setMediaPoolStreamMonitorCallback %s %s", poolId.c_str(), id.c_str());
    if (sessions_[AUDIO].count(id) != 0 && sessions_[AUDIO][id].remote.stream_monitor)
    {
        auto monitor = sessions_[AUDIO][id].remote.stream_monitor;
        {
            // TODO:
            monitor->setFrameTimeoutNotify2(default_audio_timeout_ms_, &mediaPools_[poolId], [this, poolId, id](long ms, void *param) {
                jinfo("setMediaPoolStreamMonitorCallback[%s] Audio FrameTimeoutNotify2 timeout %dms", id.c_str(), ms);
                if (mediaPools_.count(poolId) == 0) return;
                if (&mediaPools_[poolId] == param && ms > 0) {
                    std::string oldId = getWorkingId(poolId);
                    jinfo("AUDIO [%s] [%s] [%s] timeout %dms > %dms!!!!!!!!!", poolId.c_str(), id.c_str(), oldId.c_str(), ms, default_audio_timeout_ms_);
                    if (id == mediaPools_[poolId].workingId && !mediaPools_[poolId].poolIds.empty()) {
                        std::string newId = "";
                        for(auto &backupid: mediaPools_[poolId].poolIds) {
                            auto &monitor = sessions_[AUDIO][backupid].remote.stream_monitor;
                            if (monitor && monitor->getConnectState()) {
                                newId = backupid;
                                break;
                            }                            
                        }
                        if (newId == "") {
                            auto &monitor = sessions_[AUDIO][oldId].remote.stream_monitor;
                            if (monitor) {
                                monitor->resetConnectState(true);
                            }   
                            if (mediaPools_[poolId].cb)
                            {
                                mediaPools_[poolId].cb(poolId, oldId);
                            }
                            if (mediaPools_[poolId].cb2)
                            {
                                mediaPools_[poolId].cb2(poolId, oldId, oldId);
                            }
                            jinfo("[AUDIO]: None is active. oldId[%s] working.", oldId.c_str());
                            return;
                        }
                        // FIXME: 
                        // if (sessions_[AUDIO].count(oldId) != 0 && sessions_[AUDIO][oldId].dir != 3) {
                        //     jinfo("[%s] dir is not sendrecv", oldId.c_str());
                        //     return;
                        // }
                        // 切换音频
                        // std::string newId = *mediaPools_[poolId].poolIds.begin();
                        switchAudio(oldId, newId);
                        
                        // 音视频保持同步
                        if (sessions_[VIDEO].find(oldId) != sessions_[VIDEO].end())
                        {
                            switchVideo(oldId, newId);
                        }
                        setWorkingId(poolId, newId, oldId);
                        jinfo("oldId[%s] -> newId[%s]", oldId.c_str(), newId.c_str());
                    } else if (mediaPools_[poolId].poolIds.empty() && id == mediaPools_[poolId].workingId) {
                        auto &monitor = sessions_[AUDIO][oldId].remote.stream_monitor;
                        if (monitor) {
                            monitor->resetConnectState(true);
                        }   
                        if (mediaPools_[poolId].cb)
                        {
                            mediaPools_[poolId].cb(poolId, oldId);
                        }
                        if (mediaPools_[poolId].cb2)
                        {
                            mediaPools_[poolId].cb2(poolId, oldId, oldId);
                        }
                        jinfo("oldId[%s] -> newId[%s](mediaPools empty)", oldId.c_str(), oldId.c_str());
                    } else if (id != mediaPools_[poolId].workingId) {
                        auto &monitor = sessions_[AUDIO][id].remote.stream_monitor;
                        if (monitor) {
                            monitor->resetConnectState(true);
                        }   
                        if (mediaPools_[poolId].cb)
                        {
                            mediaPools_[poolId].cb(poolId, id);
                        }
                        if (mediaPools_[poolId].cb2)
                        {
                            mediaPools_[poolId].cb2(poolId, oldId, id);
                        }
                        jinfo("Id[%s] timeout, workingId[%s]", id.c_str(), oldId.c_str());
                    }
                    // if (sessions_[AUDIO].find(oldId) != sessions_[AUDIO].end() && sessions_[AUDIO][oldId].remote.timeoutCB && ms > 0)
                    // {
                    //     sessions_[AUDIO][oldId].remote.timeoutCB(oldId, false);
                    // }
                    // if (sessions_[AUDIO].find(oldId) != sessions_[AUDIO].end() && sessions_[AUDIO][oldId].remote.timeoutCB2) {
                    //     if (ms <= 0)
                    //         sessions_[AUDIO][oldId].remote.timeoutCB2(oldId, false, true);
                    //     else
                    //         sessions_[AUDIO][oldId].remote.timeoutCB2(oldId, false, false);
                    // }
                }
            });
        }
    }
    
#ifdef ENABLE_VIDEO    
    if (sessions_[VIDEO].count(id) != 0 && sessions_[VIDEO][id].remote.stream_monitor) {
        auto monitor = sessions_[VIDEO][id].remote.stream_monitor;
        {
            // TODO:
            monitor->setFrameTimeoutNotify2(default_video_timeout_ms_, &mediaPools_[poolId], [this, poolId, id](long ms, void *param) {
                jinfo("setMediaPoolStreamMonitorCallback[%s] Video FrameTimeoutNotify2 timeout %d > %dms", id.c_str(), ms, default_video_timeout_ms_);
                if (mediaPools_.count(poolId) == 0) return;
                if (&mediaPools_[poolId] == param && ms > 0) {
                    std::string oldId = getWorkingId(poolId);
                    jinfo("VIDEO [%s] [%s] [%s] timeout %d!!!!!!!!!", poolId.c_str(), id.c_str(), oldId.c_str(), ms);
                    if (id == mediaPools_[poolId].workingId && !mediaPools_[poolId].poolIds.empty()) {
                        std::string newId = "";
                        for (auto &backupid : mediaPools_[poolId].poolIds) {
                            auto &monitor = sessions_[VIDEO][backupid].remote.stream_monitor;
                            if (monitor && monitor->getConnectState()) {
                                newId = backupid;
                                break;
                            }
                        }
                        if (newId == "") {
                            auto &monitor = sessions_[VIDEO][oldId].remote.stream_monitor;
                            if (monitor) {
                                monitor->resetConnectState(true);
                            }
                            if (mediaPools_[poolId].cb) {
                                mediaPools_[poolId].cb(poolId, oldId);
                            }
                            if (mediaPools_[poolId].cb2) {
                                mediaPools_[poolId].cb2(poolId, oldId, oldId);
                            }
                            jinfo("[Video]: None is active. oldId[%s] working.", oldId.c_str());
                            return;
                        }
                        // FIXME: 
                        // if (sessions_[VIDEO].count(oldId) != 0 && sessions_[VIDEO][oldId].dir != 3) {
                        //     jinfo("[%s] dir is not sendrecv", oldId.c_str());
                        //     return;
                        // }
                        // 切换音频
                        // mediaPools_[poolId].poolIds.push_back(mediaPools_[poolId].poolIds.front());
                        // std::string newId = *mediaPools_[poolId].poolIds.begin();
                        switchVideo(oldId, newId);
                        
                        // 音视频保持同步
                        if (sessions_[AUDIO].find(id) != sessions_[AUDIO].end())
                        {
                            switchAudio(oldId, newId);
                        }
                        setWorkingId(poolId, newId, oldId);
                        jinfo("oldId[%s] -> newId[%s]", oldId.c_str(), newId.c_str());
                    } else if (mediaPools_[poolId].poolIds.empty() && id == mediaPools_[poolId].workingId) {
                        auto &monitor = sessions_[VIDEO][oldId].remote.stream_monitor;
                        if (monitor) {
                            monitor->resetConnectState(true);
                        }   
                        if (mediaPools_[poolId].cb)
                        {
                            mediaPools_[poolId].cb(poolId, oldId);
                        }
                        if (mediaPools_[poolId].cb2)
                        {
                            mediaPools_[poolId].cb2(poolId, oldId, oldId);
                        }
                        jinfo("Nothing working done. oldId[%s] -> newId[%s]", oldId.c_str(), oldId.c_str());
                    } else if (id != mediaPools_[poolId].workingId) {
                        auto &monitor = sessions_[VIDEO][id].remote.stream_monitor;
                        if (monitor) {
                            monitor->resetConnectState(true);
                        }   
                        if (mediaPools_[poolId].cb)
                        {
                            mediaPools_[poolId].cb(poolId, id);
                        }
                        if (mediaPools_[poolId].cb2)
                        {
                            mediaPools_[poolId].cb2(poolId, oldId, id);
                        }
                        jinfo("Id[%s] timeout, workingId[%s]", id.c_str(), oldId.c_str());
                    }
                    // if (sessions_[VIDEO][oldId].remote.timeoutCB && ms > 0)
                    // {
                    //     sessions_[VIDEO][oldId].remote.timeoutCB(id, true);
                    // }
                    // if (sessions_[VIDEO][oldId].remote.timeoutCB2) {
                    //     if (ms <= 0)
                    //         sessions_[VIDEO][oldId].remote.timeoutCB2(oldId, true, true);
                    //     else
                    //         sessions_[VIDEO][oldId].remote.timeoutCB2(oldId, true, false);
                    // }
                }
            });
        }
    }
#endif
}

int MediaManagerInterfaceImp::addToMediaPool(const std::string poolId, const std::string& id, const WorkingMediaCallback& cb)
{
    jinfo("addToMediaPool poolId=[%s] id=[%s]", poolId.c_str(), id.c_str());
    CHECK_AUDIO_SESSION_EXIST;
    if (mediaPools_[poolId].workingId == id)
    {
        return MediaManagerInterface::MM_INVALID_PARAMETER;
    }
    for (auto &i : mediaPools_[poolId].poolIds)
    {
        if (i == id)
        {
            return MediaManagerInterface::MM_INVALID_PARAMETER;
        }
    }

    // setMediaPoolStreamCallback(poolId, id);
    setMediaPoolStreamMonitorCallback(poolId, id);

    mediaPools_[poolId].cb = cb;
    // if (mediaPools_[poolId].workingId == "")
    // {
    //     jinfo("addToMediaPool poolId=[%s] workingId=[%s]", poolId.c_str(), id.c_str());
    //     mediaPools_[poolId].workingId = id;
    //     if (mediaPools_[poolId].cb)
    //     {
    //         mediaPools_[poolId].cb(poolId, id);
    //     }
    // }
    // else
    {
        mediaPools_[poolId].poolIds.push_back(id);
        switchAudio(id, "");
        switchVideo(id, "");
    }
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::addToMediaPool2(const std::string poolId, const std::string& id, const WorkingMediaCallback2& cb)
{
    jinfo("addToMediaPool2 poolId=[%s] id=[%s]", poolId.c_str(), id.c_str());
    CHECK_AUDIO_SESSION_EXIST;
    if (mediaPools_[poolId].workingId == id)
    {
        return MediaManagerInterface::MM_INVALID_PARAMETER;
    }
    for (auto &i : mediaPools_[poolId].poolIds)
    {
        if (i == id)
        {
            return MediaManagerInterface::MM_INVALID_PARAMETER;
        }
    }

    // setMediaPoolStreamCallback(poolId, id);
    setMediaPoolStreamMonitorCallback(poolId, id);

    mediaPools_[poolId].cb2 = cb;
    // if (mediaPools_[poolId].workingId == "")
    // {
    //     jinfo("addToMediaPool poolId=[%s] workingId=[%s]", poolId.c_str(), id.c_str());
    //     mediaPools_[poolId].workingId = id;
    //     if (mediaPools_[poolId].cb2)
    //     {
    //         mediaPools_[poolId].cb2(poolId, id, "");
    //     }
    // }
    // else
    {
        mediaPools_[poolId].poolIds.push_back(id);
        switchAudio(id, "");
        switchVideo(id, "");
    }
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::removeFromMediaPool(const std::string poolId, const std::string& id)
{
    jinfo("removeFromMediaPool poolId=[%s] id=[%s]", poolId.c_str(), id.c_str());
    CHECK_AUDIO_SESSION_EXIST;
    if (mediaPools_[poolId].workingId == id)
    {
        if (!mediaPools_[poolId].poolIds.empty())
        {
            std::string newId = *mediaPools_[poolId].poolIds.begin();
            switchAudio(id, newId);
            if (sessions_[VIDEO].find(id) != sessions_[VIDEO].end())
            {
                switchVideo(id, newId);
            }
            setWorkingId(poolId, newId);
        } else mediaPools_[poolId].workingId = "";
    }
    // else
    {
        if (mediaPools_.find(poolId) != mediaPools_.end())
        {
            for (auto it = mediaPools_[poolId].poolIds.begin(); it != mediaPools_[poolId].poolIds.end(); ++it)
            {
                if (*it == id)
                {
                    mediaPools_[poolId].poolIds.erase(it);
                    break;
                }
            }
        }
    }
    if (mediaPools_[poolId].workingId == "" && mediaPools_[poolId].poolIds.empty()) {
        mediaPools_.erase(poolId);
    }
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::clearMediaPool(const std::string poolId)
{
    int ret = MediaManagerInterface::MM_SUCCESS;
    if (mediaPools_.find(poolId) != mediaPools_.end())
    {
        ret = MediaManagerInterface::MM_SUCCESS;
        mediaPools_[poolId].workingId = "";
        mediaPools_.erase(poolId);
    }
    return ret;
}

int MediaManagerInterfaceImp::setActiveMedia(const std::string poolId, const std::string& id)
{
    CHECK_AUDIO_SESSION_EXIST;
    if (mediaPools_.find(poolId) != mediaPools_.end())
    {
        if (mediaPools_[poolId].workingId != id)
        {
            switchAudio(mediaPools_[poolId].workingId, id);
            if (sessions_[VIDEO].find(id) != sessions_[VIDEO].end())
            {
                switchVideo(mediaPools_[poolId].workingId, id);
            }
            mediaPools_[poolId].workingId = id;
            mediaPools_[poolId].poolIds.push_back(mediaPools_[poolId].poolIds.front());
            mediaPools_[poolId].poolIds.pop_front();
            if (mediaPools_[poolId].cb)
            {
                mediaPools_[poolId].cb(poolId, id);
            }
            if (mediaPools_[poolId].cb2)
            {
                mediaPools_[poolId].cb2(poolId, id, "");
            }
        }
    }
    else
    {
        return MediaManagerInterface::MM_INVALID_PARAMETER;
    }
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::getActiveMediaSession(const std::string& poolId, std::string &id) {
    jwarn("getActiveMediaSession poolId[%s]  session[%s].", poolId.c_str(), id.c_str());
    if (mediaPools_.find(poolId) != mediaPools_.end())
    {
        id = mediaPools_[poolId].workingId;
        if (id == "") {
            jwarn("poolId[%s] has no working session.", poolId.c_str());
            return MediaManagerInterface::MM_INVALID_PARAMETER;
        }
        return MediaManagerInterface::MM_SUCCESS;
    }
    jerror("poolId[%s] does not exist.", poolId.c_str());
    return MediaManagerInterface::MM_INVALID_PARAMETER;
}

int MediaManagerInterfaceImp::mediaCtrl(const std::string& id, const std::string& jsonParams)
{
    CHECK_AUDIO_SESSION_EXIST;
    jinfo("mediaCtrl id[%s] jsonParams: %s", id.c_str(), jsonParams.c_str());
    if (sessions_[AUDIO][id].local.capturer)
    {
        sessions_[AUDIO][id].local.capturer->updateParam(jsonParams);
    }
    updateGain(id, jsonParams);
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::setMediaLayout(const std::string& id, const std::list<Region>& layout, const std::string& jsonParams)
{
    return MediaManagerInterface::MM_SUCCESS;
}

std::list<CodecInst> MediaManagerInterfaceImp::getSupportAudioCodecs()
{
    return GetSupportCodecs(AUDIO);
}

std::list<CodecInst> MediaManagerInterfaceImp::getSupportVideoCodecs()
{
    return GetSupportCodecs(VIDEO);
}

std::list<CodecInst> MediaManagerInterfaceImp::GetSupportCodecs(CodecType type)
{
    if (type == AUDIO)
    {
        if (codecs_[type].empty()) codecs_[type] = AudioFrameEncoder::GetSupportCodecs();
        LogI("======list audio codec start======");
        for (auto codec : codecs_[type])
        {
            LogI("%s", codec.name);
        }
        LogI("======list audio codec done======");
        return AudioFrameEncoder::GetSupportCodecs();
    }
#ifdef ENABLE_VIDEO
    else if (type == VIDEO)
    {
        if (codecs_[type].empty()) codecs_[type] = VideoFrameEncoder::GetSupportCodecs();
        LogI("======list video codec start======");
        for (auto codec : codecs_[type])
        {
            LogI("%s", codec.name);
        }
        LogI("======list video codec done======");
        return VideoFrameEncoder::GetSupportCodecs();
    }
#endif
    else
    {
        return std::list<CodecInst>();
    }

}

void MediaManagerInterfaceImp::setAudioCodecPriority(const std::list<CodecInst>& codecs, const std::string& id)
{
    // TODO: 直接使用传下来的配置
    if (id == "")
    {
        // codecs_[AUDIO] = codecs;
        codecs_[AUDIO].clear();
        // Test: 测试g722.1
        // {
        //     std::list<CodecInst> temp;
        //     getCodecInst("g7221", temp);
        //     for (auto &codec: temp) codecs_[AUDIO].push_back(codec);
        // }
        for (auto &inst: codecs) {
            if (strcasecmp(inst.name, "aac") == 0) {
                std::list<CodecInst> temp;
                if (getCodecInst("MP4A-LATM", temp)) {
                    for (auto &codec: temp) codecs_[AUDIO].push_back(codec);
                }
            } else codecs_[AUDIO].push_back(inst);
        }
        for (auto &codec : codecs_[AUDIO]) {
            // jinfo("setAudioCodecPriority: %s", codec.name);
            jinfo(
                "setAudioCodecPriority: { name: %s, pt: %d, rate: %d, chn: %d, tsinc: %d, bitrate: %d, profile: %d, ptime: %d }", codec.name, codec.pt,
                codec.rate, codec.chn, codec.tsinc, codec.bitrate, codec.profile, codec.ptime);
        }
    } else {
        jinfo("setAudioCodecPriority id: %s", id.c_str());
        if (sessions_[AUDIO].find(id) != sessions_[AUDIO].end()) {
            // sessions_[AUDIO][id].codecs = codecs;
            sessions_[AUDIO][id].codecs.clear();
            for (auto &inst : codecs) {
                if (strcasecmp(inst.name, "aac") == 0) {
                    std::list<CodecInst> temp;
                    if (getCodecInst("MP4A-LATM", temp)) {
                        for (auto &codec : temp)
                            sessions_[AUDIO][id].codecs.push_back(codec);
                    }
                } else
                    sessions_[AUDIO][id].codecs.push_back(inst);
            }
            for (auto &codec : sessions_[AUDIO][id].codecs)
                jinfo(
                    "setAudioCodecPriority id [%s]: { name: %s, pt: %d, rate: %d, chn: %d, tsinc: %d, bitrate: %d, profile: %d, ptime: %d }", id.c_str(),
                    codec.name, codec.pt, codec.rate, codec.chn, codec.tsinc, codec.bitrate, codec.profile, codec.ptime);
        }
    }
}

void MediaManagerInterfaceImp::setVideoCodecPriority(const std::list<CodecInst>& codecs, const std::string& id)
{   
    if (id == "")
    {
        codecs_[VIDEO] = codecs;
        for (auto &codec : codecs_[VIDEO]) {
            jinfo(
                "setVideoCodecPriority: { name: %s, pt: %d, rate: %d, chn: %d, tsinc: %d, bitrate: %d, profile: %d, ptime: %d }", codec.name, codec.pt,
                codec.rate, codec.chn, codec.tsinc, codec.bitrate, codec.profile, codec.ptime);
        }
    } else {
        if (sessions_[VIDEO].find(id) != sessions_[VIDEO].end()) {
            sessions_[VIDEO][id].codecs = codecs;
        }
        for (auto &codec : sessions_[VIDEO][id].codecs)
            jinfo(
                "setVideoCodecPriority id [%s]: { name: %s, pt: %d, rate: %d, chn: %d, tsinc: %d, bitrate: %d, profile: %d, ptime: %d }", id.c_str(),
                codec.name, codec.pt, codec.rate, codec.chn, codec.tsinc, codec.bitrate, codec.profile, codec.ptime);
    }
}

int MediaManagerInterfaceImp::addVideoRender(const std::string& id, const FramePipeline::Ptr& ptr)
{
    jinfo("addVideoRender custom %s", id.c_str());
#ifdef ENABLE_VIDEO
    if (sessions_[VIDEO].find(id) != sessions_[VIDEO].end())
    {
        std::shared_ptr<VideoFrameRender> videoRender;
        for (auto& render : sessions_[VIDEO][id].remote.pipelines)
        {
            if (VideoFrameRender::isVideoFrameRender(render))
            {
                videoRender = std::dynamic_pointer_cast<VideoFrameRender>(render);
                break;
            }
        }
        if (videoRender)
        {
            videoRender->stop();
            auto srcs = videoRender->getVideoSources();
            for (auto src : srcs)
            {
                auto source = src.lock();
                if (source)
                {
                    source->removeVideoDestination(videoRender);
                    source->addVideoDestination(ptr);
                }
            }
            sessions_[VIDEO][id].remote.render = videoRender;
        }
        else
        {
            LogI("addVideoRender id(%s) do not exist render", id.c_str());
            return MediaManagerInterface::MM_PIPELINE_FAIL;
        }
    }
    else
    {
        return MediaManagerInterface::MM_SESSION_NOT_FOUND;
    }
#endif
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::removeVideoRender(const std::string& id)
{
    jinfo("removeVideoRender custom %s", id.c_str());
#ifdef ENABLE_VIDEO
    if (sessions_[VIDEO].find(id) != sessions_[VIDEO].end())
    {
        std::shared_ptr<VideoFrameRender> videoRender;
        for (auto& render : sessions_[VIDEO][id].remote.pipelines)
        {
            if (VideoFrameRender::isVideoFrameRender(render))
            {
                videoRender = std::dynamic_pointer_cast<VideoFrameRender>(render);
                break;
            }
        }
        
        if (videoRender)
        {
            videoRender->stop();
            auto srcs = videoRender->getVideoSources();
            for (auto src : srcs)
            {
                auto source = src.lock();
                if (source)
                {
                    source->removeVideoDestination(videoRender);
                    if (sessions_[VIDEO][id].remote.render)
                    {
                        source->addVideoDestination(sessions_[VIDEO][id].remote.render);
                    }
                }
            }
            if (sessions_[VIDEO][id].remote.render)
            {
                sessions_[VIDEO][id].remote.render->start();
            }
            sessions_[VIDEO][id].remote.render.reset();
        }
        else
        {
            LogI("removeVideoRender id(%s) do not exist render", id.c_str());
            return MediaManagerInterface::MM_PIPELINE_FAIL;
        }
    }
    else
    {
        return MediaManagerInterface::MM_SESSION_NOT_FOUND;
    }
#endif
    return MediaManagerInterface::MM_SUCCESS;
}

void MediaManagerInterfaceImp::setRTPStatisticsListener(const std::string& id, const std::string& jsonParam, const RTPStatisticsNotifyCallback& cb, bool use_async)
{
    if (sessions_[AUDIO].find(id) != sessions_[AUDIO].end())
    {
        sessions_[AUDIO][id].rtpParams = jsonParam;
        sessions_[AUDIO][id].rtpcb = cb;
        sessions_[AUDIO][id].commoncb = cb;
        sessions_[AUDIO][id].notify_async = use_async;
    }
#ifdef ENABLE_VIDEO
    if (sessions_[VIDEO].find(id) != sessions_[VIDEO].end())
    {
        sessions_[VIDEO][id].rtpParams = jsonParam;
        sessions_[VIDEO][id].rtpcb = cb;
        sessions_[VIDEO][id].commoncb = cb;
        sessions_[VIDEO][id].notify_async = use_async;
    }
    setVideoDecoderNotifyCallback(id);
    setVideoEncoderNotifyCallback(id);
#endif
    setRTPStatisticsCallback(id);
}

void MediaManagerInterfaceImp::setRTPStatisticsCallback(const std::string &id) {
    if (sessions_[AUDIO].count(id) != 0) {
        for (auto &pipeline : sessions_[AUDIO][id].remote.pipelines) {
            if (RtpEngine::IsRTPEngine(pipeline)) {
                jinfo("setRTPStatisticsCallback audio %s", id.c_str());
                auto cb = sessions_[AUDIO][id].rtpcb;
                bool use_async = sessions_[AUDIO][id].notify_async;
                if (cb) {
                    // rtp->setNotify("RTPStatistics", sessions_[AUDIO][id].rtpParams, this, [id, cb](const std::string& key, const std::string& json, void*
                    // param){
                    //     cb(id, json);
                    // }, use_async);
                    pipeline->setNotify2(
                        id, "RTPStatistics", sessions_[AUDIO][id].rtpParams, this,
                        [cb](const std::string &id, const std::string &key, const std::string &json, void *param) { cb(id, json); }, use_async);
                }
                break;
            }
        }
    }

#ifdef ENABLE_VIDEO
    if (sessions_[VIDEO].count(id) != 0) {
        for (auto &pipeline : sessions_[VIDEO][id].remote.pipelines) {
            if (RtpEngine::IsRTPEngine(pipeline)) {
                auto cb = sessions_[VIDEO][id].rtpcb;
                bool use_async = sessions_[VIDEO][id].notify_async;
                if (cb) {
                    // rtp->setNotify("RTPStatistics", sessions_[VIDEO][id].rtpParams, this, [id, cb](const std::string& key, const std::string& json, void*
                    // param){
                    //     cb(id, json);
                    // }, use_async);
                    pipeline->setNotify2(
                        id, "RTPStatistics", sessions_[VIDEO][id].rtpParams, this,
                        [cb](const std::string &id, const std::string &key, const std::string &json, void *param) { cb(id, json); }, use_async);
                }
                break;
            }
        }
    }
#endif
}

int MediaManagerInterfaceImp::setAudioCapturerNotifyCallback(const int &deviceId, const std::string& jsonParam, const AudioCapturerNotifyCallback& callback) {
    jinfo("setAudioCapturerNotifyCallback [%d] [%s]", deviceId, jsonParam.c_str());
    int ret = MediaManagerInterface::MM_SUCCESS;
    if (!callback || deviceId < 0) return MediaManagerInterface::MM_INVALID_PARAMETER;
    if (audio_capturers_.find(deviceId) == audio_capturers_.end() || audio_capturers_[deviceId].pipeline == nullptr) return MediaManagerInterface::MM_ERROR_DEVICE_NOT_OPENED;
    auto capturer = audio_capturers_[deviceId].pipeline;
    capturer->setNotify("AudioCapturerStatus", jsonParam, this, [callback, deviceId](const std::string& key, const std::string& json, void* param) {
        callback(deviceId, json);
    });
    return ret;
}

int MediaManagerInterfaceImp::setAudioRendererNotifyCallback(const int &deviceId, const std::string& jsonParam, const AudioRendererNotifyCallback& callback) {
    jinfo("setAudioRendererNotifyCallback [%d] [%s]", deviceId, jsonParam.c_str());
    int ret = MediaManagerInterface::MM_SUCCESS;
    if (!callback || deviceId < 0) return MediaManagerInterface::MM_INVALID_PARAMETER;
    if (audio_renderers_.find(deviceId) == audio_renderers_.end() || audio_renderers_[deviceId].pipeline == nullptr) return MediaManagerInterface::MM_ERROR_DEVICE_NOT_OPENED;
    auto renderer = audio_renderers_[deviceId].pipeline;
    renderer->setNotify("AudioRendererStatus", jsonParam, this, [callback, deviceId](const std::string& key, const std::string& json, void* param) {
        callback(deviceId, json);
    });
    return ret;    
}

int MediaManagerInterfaceImp::getLocalSDP(const std::string& id, std::string& sdp, bool isOffer, const std::string& jsonParams, const std::list<CodecInst>& audioCodecs, const std::list<CodecInst>& videoCodecs, bool dtls)
{
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    bool useVideo = false;
    if (j.contains("video"))
    {
        useVideo = j["video"];
    }
    if (!config_.contains("video"))
    {
        useVideo = false;
    }
    bool useAudio = true;
    if (!config_.contains("audio"))
    {
        useAudio = false;
    }
    if (j.contains("dtls"))
    {
        dtls = j["dtls"];
    }
    std::string ip;
    if (j.contains("ip"))
    {
        ip = j["ip"];
    }
    std::string dir = "sendrecv";
    if (j.contains("dir"))
    {
        dir = j["dir"];
    }
    std::string ifdev;
    if (ip == "")
    {
        if (j.contains("if")) 
            ifdev = j["if"];
        ip = getIP(ifdev);
    }
    if (ip == "")
    {
        return MediaManagerInterface::MM_FETCH_IP_FAIL;
    }
    std::string bind_ip = "";
    if (j.contains("bind_ip")) {
        bind_ip = j["bind_ip"];
        LogI("use bind_ip  %s", bind_ip.c_str());
    }
    LogI("use ip %s", ip.c_str());
    if (isOffer && j.contains("found_video")) {
        bool found_video = j["found_video"];
        useVideo = (useVideo && found_video);
    }
    auto sdpJson = templates_[useVideo ? VIDEO : AUDIO];
    sdpJson["origin"]["address"] = ip;
    for (size_t i = 0; i < sdpJson["media"].size(); i++)
    {
        if (sdpJson["media"][i]["type"] == "audio" && sessions_[AUDIO][id].local.roomid >= 0) {
            nlohmann::json val;
            val["value"] = "roomid:" + std::to_string(sessions_[AUDIO][id].local.roomid);
            sdpJson["invalid"].push_back(val);
        }
        sdpJson["media"][i]["connection"]["ip"] = ip;
        LogI("getLocalSDP id=[%s] useAudio = %d", id.c_str(), useAudio);
        if (sdpJson["media"][i]["type"] == "audio" && useAudio)
        {
            LogI("getLocalSDP id=[%s] m-line = audio", id.c_str());
            sessions_[AUDIO][id].callstate = isOffer ? MediaSession::CALL_OFFER : MediaSession::CALL_ANSWER;
            makeLocalSession(id, sdpJson["media"][i], ip, dir, audioCodecs, AUDIO, dtls, bind_ip);
            if (j.contains("direction") && j["direction"].contains("audio"))   
                sdpJson["media"][i]["direction"] = j["direction"]["audio"];
            else sdpJson["media"][i]["direction"] = dir;
        }
        else if (sdpJson["media"][i]["type"] == "video")
        {
            LogI("getLocalSDP id=[%s] m-line = video", id.c_str());
            sessions_[VIDEO][id].callstate = isOffer ? MediaSession::CALL_OFFER : MediaSession::CALL_ANSWER;
            makeLocalSession(id, sdpJson["media"][i], ip, dir, videoCodecs, VIDEO, dtls, bind_ip);
            if (j.contains("direction") && j["direction"].contains("video"))  
                sdpJson["media"][i]["direction"] = j["direction"]["video"];
            else sdpJson["media"][i]["direction"] = dir;
        }
        // sdpJson["media"][i]["direction"] = dir;
    }
    sdp = sdptransform::write(sdpJson);
    LogI("getLocalSDP id=[%s] sdp=\n%s", id.c_str(), sdp.c_str());
    return MediaManagerInterface::MM_SUCCESS; 
}

std::list<CodecInst> MediaManagerInterfaceImp::findMatchCodec(const std::list<CodecInst>& codecs, CodecType type)
{
    std::list<CodecInst> ret;
    for (const CodecInst& inst : codecs)
    {
        for (const CodecInst& inst2 : codecs_[type])
        {
            if (inst == inst2)
            {
                ret.push_back(inst);
                break;
            }
        }
    }
    return ret;
}

void MediaManagerInterfaceImp::makeLocalSession(const std::string& id, nlohmann::json& media, const std::string& ip, const std::string& dir, const std::list<CodecInst>& codecs, CodecType type, bool dtls, const std::string &bind_ip)
{
    // if (!sessions_[type][id].local.mediaPart.is_null())
    // {
    //     media = sessions_[type][id].local.mediaPart;
    // }
    // else
    {
        std::string fingerprint;
#ifdef USE_JRTP
#ifdef USE_DTLS_RTP
        fingerprint = DTLSRTPSession::GetFingerprint();
#endif
#endif
        if (dtls)
        {
            media["fingerprint"]["type"] = "sha-256";
            media["fingerprint"]["hash"] = fingerprint;
            sessions_[type][id].local.dtls = true;
            sessions_[type][id].local.fingerprint = fingerprint;
            if (sessions_[type][id].remote.fingerprint != "")
            {
                sessions_[type][id].local.isServer = !sessions_[type][id].remote.isServer;
                media["setup"] = sessions_[type][id].local.isServer ? "passive" : "active";
            }
            else
            {
                media["setup"] = "actpass";
            }
        }
        std::shared_ptr<uint16_t> rtpPort;
        std::shared_ptr<uint16_t> rtcpPort;
        if (dir == "inactive")
        {
            rtpPort = std::make_shared<uint16_t>();
            *rtpPort = 0;
            rtcpPort = std::make_shared<uint16_t>();
            *rtcpPort = 0;
        }
        else if (!sessions_[type][id].local.rtpPort || *sessions_[type][id].local.rtpPort == 0)
        {
            rtpPort = getPort();
            rtcpPort = getPort();
            if (!rtpPort)
            {
                rtpPort = std::make_shared<uint16_t>();
                *rtpPort = 0;
            }
            if (!rtcpPort)
            {
                rtcpPort = std::make_shared<uint16_t>();
                *rtcpPort = 0;
            }
        } else {
            rtpPort = sessions_[type][id].local.rtpPort;
            rtcpPort = sessions_[type][id].local.rtcpPort;
        }
        
        T_LogI("makeLocalSession id=[%s] port=[%d] dir=[%s]", id.c_str(), *rtpPort, dir.c_str());
        media["port"] = *rtpPort;
        sessions_[type][id].local.ip = ip;
        sessions_[type][id].local.bindIp = bind_ip;
        sessions_[type][id].local.rtpPort = rtpPort;
        sessions_[type][id].local.rtcpPort = rtcpPort;
        media["rtp"].clear();
        // 呼入带宽
        if (type == VIDEO) {
            nlohmann::json bandwidth;
            bandwidth["type"] = "AS";
            bandwidth["limit"] = 7680;
            media["bandwidth"].push_back(bandwidth);
        }

        std::string payloads;
        for (const CodecInst& inst : codecs)
        {
            nlohmann::json codec;
            nlohmann::json fmtp;
            nlohmann::json rtcpFb;
            codec["codec"] = inst.name;
            codec["payload"] = inst.pt;
            codec["rate"] = inst.rate;
            if (inst.chn > 1)
            {
                codec["encoding"] = inst.chn;
            }
            media["rtp"].push_back(codec);
            payloads += std::to_string(inst.pt);
            payloads += " ";
            // TODO: 根据编码器的功能和性能配置fmtp，不在此处加判断来区分编码类型
            if (type == VIDEO && strcasecmp(inst.name, "h264") == 0) {
                std::string profile_string = H264::ProfileLevelIdToString(H264::ProfileLevelId(H264::GetProfileEnum(inst.profile), (panocom::H264::Level)0x28));
                H264::CodecParameterMap codec_params;
                codec_params[H264::kH264FmtpProfileLevelId] = profile_string;
                codec_params[H264::kH264FmtpLevelAsymmetryAllowed] = "1";
                codec_params[H264::kH264FmtpPacketizationMode] = "1";
                std::string config_string = H264::FmtpConfigToString(codec_params);

                fmtp["config"] = config_string;// "profile-level-id=42e02a;packetization-mode=1";
                fmtp["payload"] = inst.pt;
                media["fmtp"].push_back(fmtp);
                if (sessions_[VIDEO][id].local.video_nack_enabled) {
                    rtcpFb["payload"] = inst.pt;
                    rtcpFb["type"] = "nack";
                    rtcpFb["subtype"] = "pli";
                    media["rtcpFb"].push_back(rtcpFb);
                }
                if (sessions_[VIDEO][id].remote.video_jitter_buffer) {
                    rtcpFb["payload"] = inst.pt;
                    rtcpFb["type"] = "goog-remb";
                    media["rtcpFb"].push_back(rtcpFb);

                    rtcpFb["type"] = "transport-cc";
                    media["rtcpFb"].push_back(rtcpFb);
                }


            }
            if (type == VIDEO && strcasecmp(inst.name, "h265") == 0) {
                fmtp["config"] = "profile-id=1;level-id=123;max-lsr=250675200;max-lps=8355840;max-fps=6000";
                fmtp["payload"] = inst.pt;
                media["fmtp"].push_back(fmtp);
                if (sessions_[VIDEO][id].local.video_nack_enabled) {
                    rtcpFb["payload"] = inst.pt;
                    rtcpFb["type"] = "nack";
                    rtcpFb["subtype"] = "pli";
                    media["rtcpFb"].push_back(rtcpFb);
                }
                if (sessions_[VIDEO][id].remote.video_jitter_buffer) {
                    rtcpFb["payload"] = inst.pt;
                    rtcpFb["type"] = "goog-remb";
                    media["rtcpFb"].push_back(rtcpFb);

                    rtcpFb["type"] = "transport-cc";
                    media["rtcpFb"].push_back(rtcpFb);
                }


            }
            if (type == AUDIO && strcasecmp(inst.name, "MP4A-LATM") == 0) {
                fmtp["config"] = "bitrate=64000;profile-level-id=24;object=23";
                fmtp["payload"] = inst.pt;
                media["fmtp"].push_back(fmtp);
            }
            if (type == AUDIO && strcasecmp(inst.name, "G7221") == 0) {
                std::string fmtp_str{"bitrate="};
                fmtp_str += std::to_string(inst.bitrate);
                fmtp["config"] = fmtp_str;
                fmtp["payload"] = inst.pt;
                media["fmtp"].push_back(fmtp);
            }
        }
        // TODO: 添加fec，red等
        if (type == VIDEO && sessions_[type][id].local.video_fec_enabled) {
            payloads += std::to_string(sessions_[type][id].local.ulpfec_payload_type);
            nlohmann::json codec;
            codec["codec"] = "ulpfec";
            codec["payload"] = sessions_[type][id].local.ulpfec_payload_type;
            codec["rate"] = 90000;
            media["rtp"].push_back(codec);
        }
        if (type == VIDEO && sessions_[type][id].remote.video_jitter_buffer) {
            nlohmann::json extmap;
            //假如对方先提供的extmap:id，本端改为一样
            int value = getExtIdFromMediaJson(sessions_[type][id].remote.mediaPart, "http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01");
            extmap["value"] = (value <= 0) ? 3 : value; //要求接收端支持
            extmap["uri"] = "http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01";
            media["ext"].push_back(extmap);

            value = getExtIdFromMediaJson(sessions_[type][id].remote.mediaPart, "http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time");
            extmap["value"] = (value <= 0) ? 2 : value;
            extmap["uri"] = "http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time";
            media["ext"].push_back(extmap);
        }
        if (payloads.size() > 0 && payloads[payloads.size() - 1] == ' ')
        {
            payloads = payloads.substr(0, payloads.size() - 1);
        }
        media["payloads"] = payloads;
        if (*rtpPort == 0)
        {
            media["direction"] = "inactive";
        }
        else
        {
            media["direction"] = dir;
        }
        sessions_[type][id].local.mediaPart = media;
    }
}

void MediaManagerInterfaceImp::makeRemoteSession(const std::string &id, const CodecInst &codec, const std::string& ip, int port, const std::string &dir, CodecType type, bool found_codec)
{
    T_LogI("makeRemoteSession id=[%s] port=[%d] dir=[%s] codec=[%s-%d-%d]", id.c_str(), port, dir.c_str(), codec.name, codec.rate, codec.chn);
    // TODO: 需要仅在保持恢复中codec不变，update时要改变
    if (!sessions_[type][id].negotiated) {
        sessions_[type][id].local.codec = codec;
        sessions_[type][id].remote.codec = codec;
        sessions_[type][id].negotiated = true;
        sessions_[type][id].remote.ip = ip;
        sessions_[type][id].remote.rtpPort = std::make_shared<uint16_t>();
        *sessions_[type][id].remote.rtpPort = port;
        sessions_[type][id].remote.rtcpPort = std::make_shared<uint16_t>();
        *sessions_[type][id].remote.rtcpPort = port + 1;
        if (dir == "sendrecv")
        {
            sessions_[type][id].dir = MediaSession::SENDRECV;
        }
        else if (dir == "sendonly")
        {
            sessions_[type][id].dir = MediaSession::RECVONLY;
        }
        else if (dir == "recvonly")
        {
            sessions_[type][id].dir = MediaSession::SENDONLY;
        }
        else if (dir == "inactive")
        {
            sessions_[type][id].dir = MediaSession::INACTIVE;
        }
    } else {
        if (sessions_[type][id].local.codec == codec) {
            sessions_[type][id].local.codec_changed = false;
        } else {
            sessions_[type][id].local.codec = codec;
            sessions_[type][id].local.codec_changed = true;
        }
        if (sessions_[type][id].remote.codec == codec) {
            sessions_[type][id].remote.codec_changed = false;
        } else {
            sessions_[type][id].remote.codec = codec;
            sessions_[type][id].remote.codec_changed = true;            
        }
        if (sessions_[type][id].remote.ip != ip || *sessions_[type][id].remote.rtpPort != port) {
            sessions_[type][id].remote.addr_changed = true;
            sessions_[type][id].remote.ip = ip;
            sessions_[type][id].remote.rtpPort = std::make_shared<uint16_t>();
            *sessions_[type][id].remote.rtpPort = port;
            sessions_[type][id].remote.rtcpPort = std::make_shared<uint16_t>();
            *sessions_[type][id].remote.rtcpPort = port + 1;
        } else {
            sessions_[type][id].remote.addr_changed = false;
        }
        int temp_dir = MediaSession::INACTIVE;
        if (dir == "sendrecv")
        {
            temp_dir = MediaSession::SENDRECV;
        }
        else if (dir == "sendonly")
        {
            temp_dir = MediaSession::RECVONLY;
        }
        else if (dir == "recvonly")
        {
            temp_dir = MediaSession::SENDONLY;
        }
        else if (dir == "inactive")
        {
            temp_dir = MediaSession::INACTIVE;
        }
        if (sessions_[type][id].dir != temp_dir) {
            sessions_[type][id].dir = temp_dir;
            sessions_[type][id].dir_changed = true;
        } else {
            sessions_[type][id].dir_changed = false;
        }
    }
    sessions_[type][id].local.found_codec = found_codec;
    sessions_[type][id].remote.found_codec = found_codec;
}

bool MediaManagerInterfaceImp::GetConnectorStatus(const std::string& jsonParams) {
#ifdef ENABLE_VIDEO
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    int dev = 0, card = 0;
    if (j.contains("dev")) dev = j["dev"];
    if (j.contains("card")) card = j["card"];
    return VideoFrameRender::GetConnectorStatus(dev, card);
#else
    return false;
#endif
}

bool MediaManagerInterfaceImp::GetCameraStatus(const std::string &jsonParams) {
    FOOT_PRINT;
#ifdef ENABLE_VIDEO
    bool ret = false;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &jsonParams](){
        ret = getCameraStatus(jsonParams);
        p.set_value(0);
    });
    f.wait();
    return ret;  
#else 
    return false;
#endif
}

int MediaManagerInterfaceImp::updateAudioSendPipeline(const std::string& id) {
    FOOT_PRINT;
    int ret = MediaManagerInterface::MM_SUCCESS;
    if (sessions_[AUDIO][id].local.rtpPort && *sessions_[AUDIO][id].local.rtpPort == 0) {
        return ret;
    }
    if (!sessions_[AUDIO][id].local.enabled) {
        return ret;
    }
    if (sessions_[AUDIO][id].local.addr_changed) {
        std::shared_ptr<RtpEngine> rtp_engine;
        for (auto& pipeline : sessions_[AUDIO][id].local.pipelines)
        {
            if (RtpEngine::IsRTPEngine(pipeline))
            {
                LogI("updateAudioSendPipeline[%s] find %s-%p", id.c_str(), pipeline->name().c_str(), pipeline.get());
                rtp_engine = std::dynamic_pointer_cast<RtpEngine>(pipeline);
                break;
            }
        }
        if (rtp_engine) {
            nlohmann::json j;
            j["clear"] = true;
            nlohmann::json dst;
            dst["ip"] = sessions_[AUDIO][id].remote.ip;
            dst["port"] = *sessions_[AUDIO][id].remote.rtpPort;
            j["dst"] = nlohmann::json::array();
            j["dst"].push_back(dst);

            j["local_payload_type"] = sessions_[AUDIO][id].local.payload_type;
            j["remote_payload_type"] = sessions_[AUDIO][id].remote.payload_type;
            j["fmt"] = sessions_[AUDIO][id].local.codec.fmt;
            j["clock_rate"] = getClockrate(sessions_[AUDIO][id].local.payload_type);
            rtp_engine->updateParam(j.dump());
        }
    }
    if (config_.contains("audio") && sessions_[AUDIO].find(id) != sessions_[AUDIO].end() && sessions_[AUDIO][id].local.codec_changed)
    {
        auto new_encoder = createAudioEncoder(sessions_[AUDIO][id].local.codec);
        if (!new_encoder)
        {
            A_LogW_F("createAudioEncoder fail!");
            return MediaManagerInterface::MM_PIPELINE_FAIL;
        }
        std::shared_ptr<AudioFrameEncoder> old_encoder;
        for (auto& pipeline : sessions_[AUDIO][id].local.pipelines)
        {
            if (AudioFrameEncoder::IsAudioFrameEncoder(pipeline))
            {
                old_encoder = std::dynamic_pointer_cast<AudioFrameEncoder>(pipeline);
                break;
            }
        }
        if (old_encoder)
        {
            old_encoder->stop();
            auto srcs = old_encoder->getAudioSources();
            for (auto src : srcs)
            {
                auto source = src.lock();
                if (source)
                {
                    source->removeAudioDestination(old_encoder);
                    source->addAudioDestination(new_encoder);
                    A_LogI_F("source[%s] -> new_encoder[%s]", source->name().c_str(), new_encoder->name().c_str());
                }
            }
            auto dests = old_encoder->getAudioDestinations();
            for (auto &dest: dests) {
                auto destination = dest.lock();
                if (destination) {
                    old_encoder->removeAudioDestination(destination);
                    new_encoder->addAudioDestination(destination);
                    A_LogI_F("new_encoder[%s] -> destination[%s]", new_encoder->name().c_str(), destination->name().c_str());
                }

                if (config_["audio"]["uplink"].contains("rtp")) {
                    std::string rtpName = config_["audio"]["uplink"]["rtp"];
                    if (destination->name() == rtpName) {
                        LogI("find %s-%p", rtpName.c_str(), destination.get());
                        auto rtp_engine = std::dynamic_pointer_cast<RtpEngine>(destination);
                        nlohmann::json j;
                        j["bindIp"] = sessions_[AUDIO][id].local.ip;
                        if (sessions_[AUDIO][id].local.bindIp != "") {
                            j["bindIp"] = sessions_[AUDIO][id].local.bindIp;
                        }
                        j["bindPort"] = *sessions_[AUDIO][id].local.rtpPort;
                        j["payloadType"] = sessions_[AUDIO][id].local.codec.pt;
                        j["local_payload_type"] = sessions_[AUDIO][id].local.payload_type;
                        j["remote_payload_type"] = sessions_[AUDIO][id].remote.payload_type;
                        j["fmt"] = sessions_[AUDIO][id].local.codec.fmt;
                        j["clock_rate"] = getClockrate(sessions_[AUDIO][id].local.payload_type);
                        rtp_engine->updateParam(j.dump());
                    }
                }
            }
            sessions_[AUDIO][id].local.pipelines.erase(old_encoder);
        } else {
            LogI("updateAudioSendPipeline id(%s) do not exist encoder", id.c_str());
            return MediaManagerInterface::MM_PIPELINE_FAIL;
        }
        sessions_[AUDIO][id].local.pipelines.insert(new_encoder);
    }
    return ret;
}

int MediaManagerInterfaceImp::updateAudioRecvPipeline(const std::string& id) {
    FOOT_PRINT;
    jinfo("addr_changed: %d, codec_changed: %d", sessions_[AUDIO][id].remote.addr_changed, sessions_[AUDIO][id].remote.codec_changed);
    int ret = MediaManagerInterface::MM_SUCCESS;
    if (sessions_[AUDIO][id].remote.rtpPort && *sessions_[AUDIO][id].remote.rtpPort == 0) {
        return ret;
    }
    if (!sessions_[AUDIO][id].remote.enabled) {
        return ret;
    }
    if (sessions_[AUDIO][id].remote.addr_changed) {
        std::shared_ptr<RtpEngine> rtp_engine;
        for (auto& pipeline : sessions_[AUDIO][id].remote.pipelines)
        {
            if (RtpEngine::IsRTPEngine(pipeline))
            {
                LogI("updateAudioRecvPipeline[%s] find %s-%p", id.c_str(), pipeline->name().c_str(), pipeline.get());
                rtp_engine = std::dynamic_pointer_cast<RtpEngine>(pipeline);
                break;
            }
        }
        if (rtp_engine) {
            nlohmann::json j;
            j["clear"] = true;
            nlohmann::json dst;
            dst["ip"] = sessions_[AUDIO][id].remote.ip;
            dst["port"] = *sessions_[AUDIO][id].remote.rtpPort;
            j["dst"] = nlohmann::json::array();
            j["dst"].push_back(dst);

            j["local_payload_type"] = sessions_[AUDIO][id].local.payload_type;
            j["remote_payload_type"] = sessions_[AUDIO][id].remote.payload_type;
            j["fmt"] = sessions_[AUDIO][id].remote.codec.fmt;
            j["clock_rate"] = getClockrate(sessions_[AUDIO][id].local.payload_type);
            rtp_engine->updateParam(j.dump());
        }
    }
    if (config_.contains("audio") && sessions_[AUDIO].find(id) != sessions_[AUDIO].end() && sessions_[AUDIO][id].remote.found_codec
        && sessions_[AUDIO][id].remote.codec_changed) {
        if (sessions_[AUDIO][id].remote.use_uart) {
            nlohmann::json j;
            j["codec"] = sessions_[AUDIO][id].local.codec.fmt;
            if (sessions_[AUDIO][id].remote.render) {
                sessions_[AUDIO][id].remote.render->updateParam(j.dump());
            }
        }
        // FIXEME: 不需要重新创建接收流
        // auto new_decoder = createAudioDecoder(id);
        auto new_decoder = createAudioDecoderWrapper(id);
        if (!new_decoder) {
            A_LogW_F("createAudioDecoder fail!");
            return MediaManagerInterface::MM_PIPELINE_FAIL;
        }
        std::shared_ptr<AudioFrameDecoder> old_decoder;
        for (auto &pipeline : sessions_[AUDIO][id].remote.pipelines) {
            if (AudioFrameDecoder::IsAudioFrameDecoder(pipeline))
            {
                old_decoder = std::dynamic_pointer_cast<AudioFrameDecoder>(pipeline);
                break;
            }
        }
        if (old_decoder)
        {
            old_decoder->stop();
            auto srcs = old_decoder->getAudioSources();
            for (auto src : srcs)
            {
                auto source = src.lock();
                if (source)
                {
                    source->removeAudioDestination(old_decoder);
                    source->addAudioDestination(new_decoder);
                    A_LogI_F("source[%s] -> new_decoder[%s]", source->name().c_str(), new_decoder->name().c_str());
                }
            }
            auto data_srcs = old_decoder->getDataSources();
            for (auto src : data_srcs)
            {
                auto source = src.lock();
                if (source)
                {
                    source->removeDataDestination(old_decoder);
                    source->addDataDestination(new_decoder);
                    A_LogI_F("source[%s] -> new_decoder[%s]", source->name().c_str(), new_decoder->name().c_str());
                }
            }
            auto dests = old_decoder->getAudioDestinations();
            for (auto &dest: dests) {
                auto destination = dest.lock();
                if (destination) {
                    old_decoder->removeAudioDestination(destination);
                    new_decoder->addAudioDestination(destination);
                    A_LogI_F("new_decoder[%s] -> destination[%s]", new_decoder->name().c_str(), destination->name().c_str());
                }
            }
            auto data_dests = old_decoder->getDataDestinations();
            for (auto &dest: data_dests) {
                auto destination = dest.lock();
                if (destination) {
                    old_decoder->removeDataDestination(destination);
                    new_decoder->addDataDestination(destination);
                    A_LogI_F("new_decoder[%s] -> destination[%s]", new_decoder->name().c_str(), destination->name().c_str());
                }
            }
            sessions_[AUDIO][id].remote.pipelines.erase(old_decoder);
        }
        else
        {
            LogI("updateAudioRecvPipeline id(%s) do not exist decoder", id.c_str());
            return MediaManagerInterface::MM_PIPELINE_FAIL;
        }
        sessions_[AUDIO][id].remote.pipelines.insert(new_decoder);
    }
    return ret;
}

int MediaManagerInterfaceImp::startPlayFile(const std::string& jsonParams) {
    jinfo("startPlayFile: %s", jsonParams.c_str());
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    if (j.contains("audio") && config_.contains("audio") && config_["audio"].contains("render")) {
        if (j["audio"].contains("dev")) {
            nlohmann::json config;
            if (j["audio"].contains("path")) config["path"] = j["audio"]["path"];
            if (j["audio"].contains("loop")) config["loop"] = j["audio"]["loop"];
            std::string fileSourceName = config_["audio"]["downlink"]["file-src"]["name"];
            LogI("CreateFileSource %s", fileSourceName.c_str());
            int dev = j["audio"]["dev"];
            if (file_pipelines_[AUDIO].count(dev) != 0) {
                StopPlayFile(jsonParams);
            }
            int channel = 1;
            if (j["audio"].contains("channel")) channel = j["audio"]["channel"];
            auto source = FileFramePipeline::CreateFileSource(fileSourceName, config.dump());
            if (!source) {
                jerror("CreateFileSource[%s] failed", fileSourceName.c_str());
                return MediaManagerInterface::MM_SUCCESS;
            }
            auto render = createLocalAudioRender(dev);
            if (!render) {
                jerror("createLocalAudioRender[%d] failed", dev);
                return MediaManagerInterface::MM_SUCCESS;                
            }
            source->setGroupId(channel);
            source->addAudioDestination(render);
            std::set<FramePipeline::Ptr> pipelines;
            pipelines.emplace(source);
            pipelines.emplace(render);
            file_pipelines_[AUDIO][dev] = pipelines;
            return MediaManagerInterface::MM_SUCCESS;
        }
    }
#ifdef ENABLE_VIDEO
    if (j.contains("video") && config_.contains("video")) {
        
    }
#endif    
    return MediaManagerInterface::MM_INVALID_PARAMETER;
}

int MediaManagerInterfaceImp::stopPlayFile(const std::string& jsonParams) {
    jinfo("stopPlayFile: %s", jsonParams.c_str());
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);

    if (j.contains("audio") && j["audio"].contains("dev")) {
        int dev = j["audio"]["dev"];
        if (file_pipelines_[AUDIO].count(dev) != 0) {
            for (auto &pipeline: file_pipelines_[AUDIO][dev]) {
                pipeline->stop();
            }
            file_pipelines_[AUDIO][dev].clear();
            file_pipelines_[AUDIO].erase(dev);
            return MediaManagerInterface::MM_SUCCESS;
        }
    }
#ifdef ENABLE_VIDEO
#endif
    return MediaManagerInterface::MM_INVALID_PARAMETER;;
}

int MediaManagerInterfaceImp::LoopbackTest(const std::string &id, const std::string &jsonParm) {
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id, &jsonParm](){
        ret = loopbackTest(id, jsonParm);
        p.set_value(0);
    });
    f.wait();
    return ret;
}
int MediaManagerInterfaceImp::StopLoopbackTest(const std::string &id) {
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id](){
        ret = stopLoopbackTest(id);
        p.set_value(0);
    });
    f.wait();
    return ret;    
}

std::shared_ptr<AudioFrameRender> MediaManagerInterfaceImp::createLocalAudioRender(const int dev) {
    std::shared_ptr<AudioFrameRender> ret;
    if (audio_renderers_.count(dev) != 0 && audio_renderers_[dev].pipeline) {
        return std::dynamic_pointer_cast<AudioFrameRender>(audio_renderers_[dev].pipeline);
    }
    if (config_["audio"].contains("render") && config_["audio"]["render"].contains("name"))
    {
        //if (dev >= 0)
        {
            nlohmann::json j;
            j["samplerate"] = 16000;
            if (config_["audio"]["render"].contains("samplerate"))
            {
                j["samplerate"] = config_["audio"]["render"]["samplerate"];
            }
            j["channel"] = 1;
            if (config_["audio"]["render"].contains("channel"))
            {
                j["channel"] = config_["audio"]["render"]["channel"];
            }
            if (config_["audio"]["render"].contains("dev-name"))
            {
                j["devName"] = config_["audio"]["render"]["dev-name"];
            }
            j["dev"] = dev;
            j["maxChn"] = 8;
            if (config_["audio"]["capturer"].contains("maxChn"))
                j["maxChn"] = config_["audio"]["capturer"]["maxChn"];
            if (config_["audio"].contains("debug"))
                j["debug"] = config_["audio"]["debug"];
            std::string name = config_["audio"]["render"]["name"];
            LogI("CreateAudioRender %s dev = %d", name.c_str(), dev);
            ret = AudioFrameRender::CreateAudioRender(name, j.dump());
        }
    }
    else
    {
        LogI("CreateAudioRender fail");
    }
    return ret;    
}

std::shared_ptr<AudioFrameRender> MediaManagerInterfaceImp::createDefaultAudioRender(const int dev, const std::string &name) {
    std::string config = AudioFrameRender::loadDefaultConfig(name);
    if (config != "") {
        nlohmann::json j;
        j = nlohmann::json::parse(config);
        j["dev"] = dev;
        LogI("createDefaultAudioRender %s dev = %d", name.c_str(), dev);
        return AudioFrameRender::CreateAudioRender(name, j.dump());
    }
    return nullptr;
}

/*
    {
        "audio": {
            "capturer": {
                "name": "AudioSpec",
                "samplerate": 16000,
                "channel": 1,
                "dev": -1,
                "maxChn": 8
            },
            "render": {
                "name": "AudioSpec",
                "samplerate": 16000,
                "channel": 1,
                "dev": -1,
                "maxChn": 8
            }
        }
    }
*/
int MediaManagerInterfaceImp::loopbackTest(const std::string &id, const std::string& jsonParm) {
    jinfo("loopbackTest[%s]: %s", id.c_str(), jsonParm.c_str());
    nlohmann::json j;
    if (jsonParm != "") j = nlohmann::json::parse(jsonParm);
    else return -1;
    if (j.contains("audio") && j["audio"].contains("capturer") && j["audio"].contains("renderer")) {
        nlohmann::json params;
        std::string capturer_name = "AudioSpec";
        if (j["audio"]["capturer"].contains("name")) {
            capturer_name = j["audio"]["capturer"]["name"];
		}
		else if (config_["audio"].contains("capturer") && config_["audio"]["capturer"].contains("name")) {
			capturer_name = config_["audio"]["capturer"]["name"];
		}
        params["dev"] = 0;
        if (j["audio"]["capturer"].contains("dev")) params["dev"] = j["audio"]["capturer"]["dev"];
        int dev = params["dev"];
        params["samplerate"] = 16000;
        if (j["audio"]["capturer"].contains("samplerate")) {
            params["samplerate"] = j["audio"]["capturer"]["samplerate"];
        }
        params["channel"] = 1;
        if (j["audio"]["capturer"].contains("channel")) {
            params["channel"] = j["audio"]["capturer"]["channel"];
        }
        std::shared_ptr<AudioFrameCapturer> capturer;
        if (audio_capturers_.count(dev) != 0 && audio_capturers_[dev].pipeline) {
            capturer = std::dynamic_pointer_cast<AudioFrameCapturer>(audio_capturers_[dev].pipeline);
        }
        else capturer = AudioFrameCapturer::CreateAudioCapturer(capturer_name, params.dump());
        if (!capturer) return -1;
        params.clear();
        std::string renderer_name = "AudioSpec";
        if (j["audio"]["renderer"].contains("name")) {
            renderer_name = j["audio"]["renderer"]["name"];
        }
		else if (config_["audio"].contains("renderer") && config_["audio"]["renderer"].contains("name")) {
			renderer_name = config_["audio"]["renderer"]["name"];
		}
        params["dev"] = 0;
        if (j["audio"]["renderer"].contains("dev")) params["dev"] = j["audio"]["renderer"]["dev"];
        dev = params["dev"];
        params["samplerate"] = 16000;
        if (j["audio"]["renderer"].contains("samplerate")) {
            params["samplerate"] = j["audio"]["renderer"]["samplerate"];
        }
        params["channel"] = 1;
        if (j["audio"]["renderer"].contains("channel")) {
            params["channel"] = j["audio"]["renderer"]["channel"];
        }
        std::shared_ptr<AudioFrameRender> renderer;
        if (audio_renderers_.count(dev) && audio_renderers_[dev].pipeline) {
            renderer = std::dynamic_pointer_cast<AudioFrameRender>(audio_renderers_[dev].pipeline);
        }
        else {
            renderer = AudioFrameRender::CreateAudioRender(renderer_name, params.dump());
        }
        if (!renderer) return -1;
        capturer->addAudioDestination(renderer);
        local_loopback_[id].capturer = capturer;
        local_loopback_[id].renderer = renderer;
    } 
#ifdef ENABLE_VIDEO
    else if (j.contains("video") && j["video"].contains("capturer") && j["video"].contains("renderer")) {
        nlohmann::json params;
        std::string capturer_name = "V4L2DMAVideoFrameCapturer";
        if (j["video"]["capturer"].contains("name")) {
            capturer_name = j["video"]["capturer"]["name"];
        }
		else if (config_["video"].contains("capturer") && config_["video"]["capturer"].contains("name")) {
			capturer_name = config_["video"]["capturer"]["name"];
		}
		params["dev"] = 0;
        if (j["video"]["capturer"].contains("dev")) params["dev"] = j["video"]["capturer"]["dev"];
        int dev = params["dev"];
		if (j["video"]["capturer"].contains("app"))
		{
			params["app"] = j["video"]["capturer"]["app"];
		}
		else if (config_["video"].contains("capturer") && config_["video"]["capturer"].contains("app"))
		{
			params["app"] = config_["video"]["capturer"]["app"];
		}
        if (config_["video"]["capturer"].contains("devNames"))
        {
            params["devNames"] = config_["video"]["capturer"]["devNames"];
        }
        std::shared_ptr<VideoFrameCapturer> capturer;
        if (video_capturers_.count(dev) != 0 && video_capturers_[dev].pipeline) {
            capturer = std::dynamic_pointer_cast<VideoFrameCapturer>(video_capturers_[dev].pipeline);
            video_capturers_[dev].ref++;
        } else {
            capturer = VideoFrameCapturer::CreateVideoCapturer(capturer_name, params.dump());
            video_capturers_[dev].pipeline = capturer;
            video_capturers_[dev].ref++;
        }
        if (!capturer) return -1;
        params.clear();
        std::string renderer_name = "RKVideoRender";
        if (j["video"]["renderer"].contains("name")) {
            renderer_name = j["video"]["renderer"]["name"];
        }
		else if (config_["video"].contains("renderer") && config_["video"]["renderer"].contains("name")) {
			renderer_name = config_["video"]["renderer"]["name"];
		}
        params["dev"] = 0;
        if (j["video"]["renderer"].contains("dev")) params["dev"] = j["video"]["renderer"]["dev"];
		auto renderer = VideoFrameRender::CreateVideoRender(renderer_name, params.dump());
        if (!renderer) return -1;
        capturer->setGroupId(0);
        renderer->setSourceId(capturer.get(), 0);
        capturer->addVideoDestination(renderer);
        local_loopback_[id].capturer = capturer;
        local_loopback_[id].renderer = renderer;
    } 
#endif
    else return -1;
    return 0;
}

int MediaManagerInterfaceImp::stopLoopbackTest(const std::string &id) {
    jinfo("StopLoopbackTest[%s]", id.c_str());
    if (local_loopback_.count(id) != 0) {
        if (local_loopback_[id].capturer && local_loopback_[id].renderer) {
            local_loopback_[id].capturer->removeVideoDestination(local_loopback_[id].renderer);
        }
#ifdef ENABLE_VIDEO
        if (!VideoFrameCapturer::IsVideoFrameCapturer(local_loopback_[id].capturer))
#endif
        local_loopback_[id].capturer->stop();
        local_loopback_[id].renderer->stop();
        local_loopback_.erase(id);
    }
    return 0;
}

// TODO: DRY
int MediaManagerInterfaceImp::updateAudioMixer(const std::string &id, const std::string &jsonParams) {
    CHECK_AUDIO_SESSION_EXIST;
    int ret = MediaManagerInterface::MM_SUCCESS;
    if (sessions_[AUDIO].find(id) != sessions_[AUDIO].end() && isActiveStream(id)) {
        nlohmann::json j;
        if (jsonParams != "")
            j = nlohmann::json::parse(jsonParams);
        if (j.contains("local") && j["local"].contains("audio") && j["local"]["audio"].contains("devs") && j["local"]["audio"]["devs"].is_array()) {
            jinfo("Local updateAudioMixer[%s] %s", id.c_str(), jsonParams.c_str());
            if (sessions_[AUDIO][id].local.mixer) {
                std::unordered_set<int> devs;
                for (size_t i = 0; i < j["local"]["audio"]["devs"].size(); i++) {
                    devs.emplace(j["local"]["audio"]["devs"][i]);
                }
                std::vector<int> to_erase;
                for (int dev : sessions_[AUDIO][id].local.devs) {
                    if (devs.count(dev) == 0) {
                        to_erase.emplace_back(dev);
                        if (sessions_[AUDIO][id].local.capturers.count(dev) != 0 && sessions_[AUDIO][id].local.capturers[dev].get()) {
                            auto &capturer = sessions_[AUDIO][id].local.capturers[dev];
                            const auto &dests = capturer->getAudioDestinations();
                            for (auto &dest : dests) {
                                auto destination = dest.lock();
                                if (destination) {
                                    capturer->removeAudioDestination(destination);
                                    A_LogI_F(
                                        "removeAudioDestination [%d] capturer[%s-%p] / destination[%s-%p]", dev, capturer->name().c_str(), capturer.get(),
                                        destination->name().c_str(), destination.get());
                                }
                            }
                        }
                        if (sessions_[AUDIO][id].local.processors.count(dev) != 0 && !sessions_[AUDIO][id].local.processors[dev].empty()) {
                            auto &processors = sessions_[AUDIO][id].local.processors[dev];
                            const auto &dests = processors.back()->getAudioDestinations();
                            for (auto &dest : dests) {
                                auto destination = dest.lock();
                                if (destination) {
                                    processors.back()->removeAudioDestination(destination);
                                    A_LogI_F(
                                        "removeAudioDestination [%d] processors.back()[%s-%p] / destination[%s-%p]", dev, processors.back()->name().c_str(),
                                        processors.back().get(), destination->name().c_str(), destination.get());
                                }
                            }
                        }
                    }
                }
                for (int dev : to_erase) {
                    jinfo("updateAudioMixer [%s] to erase: %d", id.c_str(), dev);
                    if (sessions_[AUDIO][id].local.devs.count(dev) != 0)
                        sessions_[AUDIO][id].local.devs.erase(dev);
                    if (sessions_[AUDIO][id].local.capturers.count(dev) != 0)
                        sessions_[AUDIO][id].local.capturers.erase(dev);
                    if (sessions_[AUDIO][id].local.processors.count(dev) != 0)
                        sessions_[AUDIO][id].local.processors.erase(dev);
                }
                for (int dev : devs) {
                    if (sessions_[AUDIO][id].local.devs.count(dev) == 0) {
                        jinfo("updateAudioMixer [%s] to add: %d", id.c_str(), dev);
                        auto capturer = createAudioCapturer(id, dev);
                        if (capturer) {
                            std::vector<std::shared_ptr<AudioFrameProcesser>> processers;
                            ret = createAudioUplinkProcesser(id, processers);
                            if (ret != MediaManagerInterface::MM_SUCCESS) {
                                A_LogW_F("createAudioUplinkProcesser %d fail!", dev);
                                continue;
                            }
                            if (!processers.empty()) {
                                capturer->addAudioDestination(processers[0]);
                                A_LogI_F("[%d]capture -> %s-%p", dev, processers[0]->name().c_str(), processers[0].get());
                                for (size_t i = 0; i < processers.size() - 1; i++) {
                                    processers[i]->addAudioDestination(processers[i + 1]);
                                    A_LogI_F(
                                        "processer [%s-%p] -> [%s-%p]", processers[i]->name().c_str(), processers[i].get(), processers[i + 1]->name().c_str(),
                                        processers[i + 1].get());
                                }
                                A_LogI_F(
                                    "[%s-%p] -> mixer[%s-%p]", processers[processers.size() - 1]->name().c_str(), processers[processers.size() - 1].get(),
                                    sessions_[AUDIO][id].local.mixer->name().c_str(), sessions_[AUDIO][id].local.mixer.get());
                                processers[processers.size() - 1]->addAudioDestination(sessions_[AUDIO][id].local.mixer);
                                for (size_t i = 0; i < processers.size(); i++) {
                                    sessions_[AUDIO][id].local.processors[dev].emplace_back(processers[i]);
                                }
                            } else {
                                capturer->setGroupId(dev);
                                capturer->addAudioDestination(sessions_[AUDIO][id].local.mixer);
                                A_LogI_F(
                                    "dev[%d] capture[%s-%p] -> mixer[%s-%p]", dev, capturer->name().c_str(), capturer.get(),
                                    sessions_[AUDIO][id].local.mixer->name().c_str(), sessions_[AUDIO][id].local.mixer.get());
                            }
                            sessions_[AUDIO][id].local.capturers[dev] = capturer;
                        }
                    }
                    sessions_[AUDIO][id].local.devs.emplace(dev);
                }
            }
        }
        if (j.contains("remote") && j["remote"].contains("audio") && j["remote"]["audio"].contains("devs") && j["remote"]["audio"]["devs"].is_array()) {
            jinfo("Remote updateAudioMixer[%s] %s", id.c_str(), jsonParams.c_str());
            std::unordered_set<int> devs;
            for (size_t i = 0; i < j["remote"]["audio"]["devs"].size(); i++) {
                devs.emplace(j["remote"]["audio"]["devs"][i]);
            }
            std::vector<int> to_erase;
            for (int dev : sessions_[AUDIO][id].remote.devs) {
                if (devs.count(dev) == 0) {
                    to_erase.emplace_back(dev);
                    if (sessions_[AUDIO][id].remote.renders.count(dev) != 0 && sessions_[AUDIO][id].remote.renders[dev]) {
                        auto &render = sessions_[AUDIO][id].remote.renders[dev];
                        const auto &audio_sources = render->getAudioSources();
                        for (auto &src : audio_sources) {
                            auto source = src.lock();
                            if (source) {
                                source->removeAudioDestination(render);
                                A_LogI_F(
                                    "removeAudioDestination [%d] deocder[%s-%p] / render[%s-%p]", dev, source->name().c_str(), source.get(),
                                    render->name().c_str(), render.get());
                            }
                        }
                    }
                }
            }
            for (int dev : to_erase) {
                if (sessions_[AUDIO][id].remote.devs.count(dev) != 0)
                    sessions_[AUDIO][id].remote.devs.erase(dev);
                if (sessions_[AUDIO][id].remote.renders.count(dev) != 0)
                    sessions_[AUDIO][id].remote.renders.erase(dev);
            }
            for (int dev : devs) {
                if (sessions_[AUDIO][id].remote.devs.count(dev) == 0) {
                    auto render = createAudioRender(id, dev);
                    if (render) {
                        for (auto pipeline: sessions_[AUDIO][id].remote.pipelines)
                        {
                            if (AudioFrameDecoder::IsAudioFrameDecoder(pipeline))
                            {
                                pipeline->addAudioDestination(render);
                                break;
                            }
                        }
                        sessions_[AUDIO][id].remote.renders[dev] = render;
                    }
                    sessions_[AUDIO][id].remote.devs.emplace(dev);
                }
            }
        }
    }
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::updateGain(const std::string &id, const std::string &jsonParams) {
    CHECK_AUDIO_SESSION_EXIST;
    int ret = MediaManagerInterface::MM_SUCCESS;
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    // FIXME：区分使用内置和外置音量调节
    if (j.contains("local-gain")){
        int gain = j["local-gain"];
        jinfo("[%sl ocalgain : %d", id.c_str(), gain);
        gain = std::min(12, std::max(-12, gain));
        if (sessions_[AUDIO][id].local.capturer)
        {
            nlohmann::json params;
            params["gain"] = gain;
            params["local-gain"] = gain;
            sessions_[AUDIO][id].local.capturer->updateParam(params.dump());
        }
    }
    if (j.contains("remote-gain")) {
        int gain = j["remote-gain"];
        jinfo("[%s] remotegain : %d", id.c_str(), gain);
        gain = std::min(12, std::max(-12, gain));
        if (sessions_[AUDIO][id].remote.render)
        {
            nlohmann::json params;
            params["gain"] = gain;
            params["remote-gain"] = gain;
            sessions_[AUDIO][id].remote.render->updateParam(params.dump());
        }        
    }
    if (j.contains("local-gain") || j.contains("local-muted")) {
        if (sessions_[AUDIO][id].local.gainController.lock()) {
            auto controller = sessions_[AUDIO][id].local.gainController.lock();
            nlohmann::json params;
            if (j.contains("local-gain")) {
                int gain = j["local-gain"];
                gain = std::min(12, std::max(-12, gain));
                params["gain"] = gain;
            }
            if (j.contains("local-muted")) {
                bool mute = j["local-muted"];
                params["muted"] = mute;
            }
            controller->updateParam(params);
        }
    }
    if (j.contains("remote-gain") || j.contains("remote-muted")) {
        if (sessions_[AUDIO][id].local.gainController.lock()) {
            auto controller = sessions_[AUDIO][id].remote.gainController.lock();
            nlohmann::json params;
            if (j.contains("remote-gain")) {
                int gain = j["remote-gain"];
                gain = std::min(12, std::max(-12, gain));
                params["gain"] = gain;
            }
            if (j.contains("remote-muted")) {
                bool mute = j["remote-muted"];
                params["muted"] = mute;
            }
            controller->updateParam(params);
        }
    }
    return ret;
}

int panocom::MediaManagerInterfaceImp::getExtIdFromMediaJson(const nlohmann::json & mediaPart, const std::string & uri)
{
    if (!mediaPart.is_null() && mediaPart.contains("ext"))
    {
        auto rtp_extmap = mediaPart["ext"];
        if (rtp_extmap.is_array())
        {
            for (auto iter = rtp_extmap.begin(); iter != rtp_extmap.end(); iter++)
            {
                if (uri == (*iter)["uri"])
                {
                    int id = (*iter)["value"];
                    return id;
                }
            }
        }
    }
    return 0;
}

int MediaManagerInterfaceImp::OpenPip(const std::string &id, const std::string& jsonParm) {
    int ret = MediaManagerInterface::MM_SUCCESS;
#ifdef ENABLE_VIDEO
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id, &jsonParm](){
        ret = openPip(id, jsonParm);
        p.set_value(0);
    });
    f.wait();
#endif
    return ret;    
}
int MediaManagerInterfaceImp::ClosePip(const std::string &id) {
    int ret = MediaManagerInterface::MM_SUCCESS;
#ifdef ENABLE_VIDEO
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id](){
        ret = closePip(id);
        p.set_value(0);
    });
    f.wait();
#endif
    return ret;    
}
int MediaManagerInterfaceImp::OpenCaption(const std::string &id, const std::string& jsonParm) {
    int ret = MediaManagerInterface::MM_SUCCESS;
#ifdef ENABLE_VIDEO
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id, &jsonParm](){
        ret = openCaption(id, jsonParm);
        p.set_value(0);
    });
    f.wait();
#endif
    return ret;    
}
int MediaManagerInterfaceImp::CloseCaption(const std::string &id) {
    int ret = MediaManagerInterface::MM_SUCCESS;
#ifdef ENABLE_VIDEO
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id](){
        ret = closeCaption(id);
        p.set_value(0);
    });
    f.wait();
#endif
    return ret;    
}

int MediaManagerInterfaceImp::CreateAudioCapturer(const int &id, const std::string& jsonParm) {
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id, &jsonParm](){
        ret = createAudioCapturer(id, jsonParm);
        p.set_value(0);
    });
    f.wait();
    return ret;
}

int MediaManagerInterfaceImp::CreateAudioRenderer(const int &id, const std::string& jsonParm) {
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id, &jsonParm](){
        ret = createAudioRenderer(id, jsonParm);
        p.set_value(0);
    });
    f.wait();
    return ret;    
}

int MediaManagerInterfaceImp::GetAudioCapturerStatus(const int id, std::string &jsonParam) {
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id, &jsonParam](){
        ret = getAudioCapturerStatus(id, jsonParam);
        p.set_value(0);
    });
    f.wait();
    return ret;   
}

int MediaManagerInterfaceImp::DestroyAudioCapturer(const int &id) {
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id](){
        ret = destroyAudioCapturer(id);
        p.set_value(0);
    });
    f.wait();
    return ret;    
}

int MediaManagerInterfaceImp::DestroyAudioRenderer(const int &id) {
    int ret = MediaManagerInterface::MM_SUCCESS;
    std::promise<int> p;
    std::future<int> f = p.get_future();
    workThread_.loop()->runInLoop([this, &p, &ret, &id](){
        ret = destroyAudioRenderer(id);
        p.set_value(0);
    });
    f.wait();
    return ret;    
}

#ifdef ENABLE_VIDEO
int MediaManagerInterfaceImp::openPip(const std::string &id, const std::string& jsonParams) {
    CHECK_VIDEO_SESSION_EXIST;
    jinfo("openPip id:%s jsonParams:%s", id.c_str(), jsonParams.c_str());
    int ret = MediaManagerInterface::MM_SUCCESS;
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    int dev = -1;
    if (j.contains("dev"))
        dev = j["dev"];
    if (dev == -1)
        dev = 0;
    int type = 1;
    if (j.contains("layout-type"))
        type = j["layout-type"];
    bool reverse = false;
    if (j.contains("reverse"))
        reverse = j["reverse"];
    std::shared_ptr<VideoFrameRender> render = nullptr;
    int renderer_dev = sessions_[VIDEO][id].remote.dev;
    if (sessions_[VIDEO][id].remote.renders.count(renderer_dev) != 0 && sessions_[VIDEO][id].remote.renders[renderer_dev]) {
        render = std::dynamic_pointer_cast<VideoFrameRender>(sessions_[VIDEO][id].remote.renders[renderer_dev]);
    } else {
        render = createVideoRender(renderer_dev, id, sessions_[VIDEO][id].remote.gpu_index);
        sessions_[VIDEO][id].remote.renders[renderer_dev] = render;
    }
    std::shared_ptr<VideoFrameCapturer> capturer;
    if (video_capturers_.count(dev) != 0 && video_capturers_[dev].pipeline) {
        capturer = std::dynamic_pointer_cast<VideoFrameCapturer>(video_capturers_[dev].pipeline);
        video_capturers_[dev].ref++;
    } else {
        capturer = createVideoCapturer(dev, 0);
        video_capturers_[dev].pipeline = capturer;
        video_capturers_[dev].ref++;
    }
    if (render && capturer && type != 0) {
        render->loadPresetLayouts(type, reverse);
        auto rendererPrevious = sessions_[VIDEO][id].remote.rendererPrevious.lock();
        if (reverse) {
            if (rendererPrevious) {
                render->setSourceId(rendererPrevious.get(), 1);
            }
            render->setSourceId(capturer.get(), 0);
        } else {
            if (rendererPrevious) {
                render->setSourceId(rendererPrevious.get(), 0);
            }
            render->setSourceId(capturer.get(), 1);
        } 
            
        capturer->addVideoDestination(render);
        sessions_[VIDEO][id].local.dev = dev;
        sessions_[VIDEO][id].local.render = render;
        sessions_[VIDEO][id].local.capturer = capturer;
    }
    return ret;    
}
int MediaManagerInterfaceImp::closePip(const std::string &id) {
    CHECK_VIDEO_SESSION_EXIST;
    jinfo("closePip id=%s", id.c_str());
    int ret = MediaManagerInterface::MM_SUCCESS;
    if (sessions_[VIDEO][id].local.capturer && sessions_[VIDEO][id].local.render) {
        auto capturer = std::dynamic_pointer_cast<VideoFrameCapturer>(sessions_[VIDEO][id].local.capturer);
        auto renderer = std::dynamic_pointer_cast<VideoFrameRender>(sessions_[VIDEO][id].local.render);
        auto rendererPrevious = sessions_[VIDEO][id].remote.rendererPrevious.lock();
        if (rendererPrevious)
            renderer->setSourceId(rendererPrevious.get(), 0);
        renderer->unsetSourceId(capturer.get());
        capturer->removeVideoDestination(renderer);
        renderer->removeInput(1);
    }
    return ret;    
}
int MediaManagerInterfaceImp::openCaption(const std::string &id, const std::string& jsonParams) {
    CHECK_VIDEO_SESSION_EXIST;
    jinfo("openCaption id: %s params: %s", id.c_str(), jsonParams.c_str());
    int ret = MediaManagerInterface::MM_SUCCESS;
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    int dev = -1;
    if (j.contains("dev"))
        dev = j["dev"];
    if (dev == -1)
        dev = 2;
    float x = 0.0f, y = 0.0f, w = 1.0f, h = 1.0f;
    float srcX = 0.0f, srcY = 0.0f, srcW = 1.0f, srcH = 1.0f;
    if (j.contains("layout")) {
        if (j["layout"].contains("x")) x = j["layout"]["x"];
        if (j["layout"].contains("y")) y = j["layout"]["y"];
        if (j["layout"].contains("w")) w = j["layout"]["w"];
        if (j["layout"].contains("h")) h = j["layout"]["h"];
        if (j["layout"].contains("srcX")) srcX = j["layout"]["srcX"];
        if (j["layout"].contains("srcY")) srcY = j["layout"]["srcY"];
        if (j["layout"].contains("srcW")) srcW = j["layout"]["srcW"];
        if (j["layout"].contains("srcH")) srcH = j["layout"]["srcH"];
        {
            int x_num = 0, y_num = 0, w_num = 1, h_num = 1;
            int x_den = 1, y_den = 1, w_den = 1, h_den = 1;
            int srcX_num = 0, srcY_num = 0, srcW_num = 1, srcH_num = 1;
            int srcX_den = 1, srcY_den = 1, srcW_den = 1, srcH_den = 1;
            if (j["layout"].contains("x-num")) x_num = j["layout"]["x-num"];
            if (j["layout"].contains("y-num")) y_num = j["layout"]["y-num"];
            if (j["layout"].contains("w-num")) w_num = j["layout"]["w-num"];
            if (j["layout"].contains("h-num")) h_num = j["layout"]["h-num"];
            if (j["layout"].contains("x-den")) x_den = j["layout"]["x-den"];
            if (j["layout"].contains("y-den")) y_den = j["layout"]["y-den"];
            if (j["layout"].contains("w-den")) w_den = j["layout"]["w-den"];
            if (j["layout"].contains("h-den")) h_den = j["layout"]["h-den"];
            if (j["layout"].contains("srcX-num")) srcX_num = j["layout"]["srcX-num"];
            if (j["layout"].contains("srcY-num")) srcY_num = j["layout"]["srcY-num"];
            if (j["layout"].contains("srcW-num")) srcW_num = j["layout"]["srcW-num"];
            if (j["layout"].contains("srcH-num")) srcH_num = j["layout"]["srcH-num"];
            if (j["layout"].contains("srcX-den")) srcX_den = j["layout"]["srcX-den"];
            if (j["layout"].contains("srcY-den")) srcY_den = j["layout"]["srcY-den"];
            if (j["layout"].contains("srcW-den")) srcW_den = j["layout"]["srcW-den"];
            if (j["layout"].contains("srcH-den")) srcH_den = j["layout"]["srcH-den"];
            x = x_num * 1.0f / x_den;
            y = y_num * 1.0f / y_den;
            w = w_num * 1.0f / w_den;
            h = h_num * 1.0f / h_den;
            srcX = srcX_num * 1.0f / srcX_den;
            srcY = srcY_num * 1.0f / srcY_den;
            srcW = srcW_num * 1.0f / srcW_den;
            srcH = srcH_num * 1.0f / srcH_den;
        }
    }
    std::shared_ptr<VideoFrameRender> render = nullptr;
    int renderer_dev = sessions_[VIDEO][id].remote.dev;
    if (sessions_[VIDEO][id].remote.renders.count(renderer_dev) != 0 && sessions_[VIDEO][id].remote.renders[renderer_dev]) {
        render = std::dynamic_pointer_cast<VideoFrameRender>(sessions_[VIDEO][id].remote.renders[renderer_dev]);
    } else {
        render = createVideoRender(renderer_dev, id, sessions_[VIDEO][id].remote.gpu_index);
        sessions_[VIDEO][id].remote.renders[renderer_dev] = render;
    }
    std::shared_ptr<VideoFrameCapturer> capturer;
    if (video_capturers_.count(dev) != 0 && video_capturers_[dev].pipeline) {
        capturer = std::dynamic_pointer_cast<VideoFrameCapturer>(video_capturers_[dev].pipeline);
        video_capturers_[dev].ref++;
    } else {
        capturer = createVideoCapturer(dev, 0);
        video_capturers_[dev].pipeline = capturer;
        video_capturers_[dev].ref++;
    }
    if (render && capturer) {
        render->addInput(2, LayoutRect(x, y, w, h, srcX, srcY, srcW, srcH, true));
        // capturer->setGroupId(2);
        render->setSourceId(capturer.get(), 2);
        capturer->addVideoDestination(render);
        sessions_[VIDEO][id].local.render = render;
        sessions_[VIDEO][id].local.caption_capturer = capturer;
    }
    return ret;    
}
int MediaManagerInterfaceImp::closeCaption(const std::string &id) {
    CHECK_VIDEO_SESSION_EXIST;
    jinfo("Close caption: %s", id.c_str());
    int ret = MediaManagerInterface::MM_SUCCESS;
    if (sessions_[VIDEO][id].local.caption_capturer && sessions_[VIDEO][id].local.render) {
        auto capturer = std::dynamic_pointer_cast<VideoFrameCapturer>(sessions_[VIDEO][id].local.caption_capturer);
        auto renderer = std::dynamic_pointer_cast<VideoFrameRender>(sessions_[VIDEO][id].local.render);
        renderer->unsetSourceId(capturer.get());
        capturer->removeVideoDestination(renderer);
        renderer->removeInput(2);
    }
    return ret;    
}
#endif

int MediaManagerInterfaceImp::getAudioCapturerStatus(const int id, std::string &jsonParam) {
    int ret = MediaManagerInterface::MM_SUCCESS;
    if (audio_capturers_.find(id) == audio_capturers_.end() || audio_capturers_[id].pipeline == nullptr) {
        jerror("AudioCapturer %d not found.", id);
        return MediaManagerInterface::MM_ERROR_INVALID_ID;
    }
    auto capturer = audio_capturers_[id].pipeline;
    capturer->getStatus(jsonParam);
    return ret;
}

int MediaManagerInterfaceImp::createAudioCapturer(const int & deviceId, const std::string& jsonParam) {
    int ret = MediaManagerInterface::MM_SUCCESS;
    if (deviceId < 0) return MediaManagerInterface::MM_ERROR_INVALID_ID;
    if (audio_capturers_.count(deviceId) != 0  && audio_capturers_[deviceId].pipeline) {
        return MediaManagerInterface::MM_ERROR_DEVICE_ALREADY_OPENED;
    }
    if ((config_["audio"].contains("capturer") && config_["audio"]["capturer"].contains("name")))
    {
        nlohmann::json j;
        j["samplerate"] = 16000;
        if (config_["audio"]["capturer"].contains("samplerate"))
        {
            j["samplerate"] = config_["audio"]["capturer"]["samplerate"];
        }
        j["channel"] = 1;
        if (config_["audio"]["capturer"].contains("channel"))
        {
            j["channel"] = config_["audio"]["capturer"]["channel"];
        }
        j["maxChn"] = 8;
        if (config_["audio"]["capturer"].contains("maxChn"))
            j["maxChn"] = config_["audio"]["capturer"]["maxChn"];
        j["dev"] = deviceId;

        std::string name = config_["audio"]["capturer"]["name"];
        LogI("CreateAudioCapturer %s", name.c_str());
        auto capturer = AudioFrameCapturer::CreateAudioCapturer(name, j.dump());
        if (capturer)
        {
            audio_capturers_[deviceId].pipeline = std::move(capturer);
            audio_capturers_[deviceId].ref++;
            // audio_capturers_[deviceId].pipeline->SetCallback(this);
            LogI("CreateAudioCapturer success");
        } else
        { 
            LogI("CreateAudioCapturer fail");
            ret = MediaManagerInterface::MM_ERROR_CONFIG_INVALID;            
        }
    }
    else
    {
        LogI("CreateAudioCapturer fail");
        ret = MediaManagerInterface::MM_ERROR_CONFIG_INVALID;
    }
    return ret;
}

int MediaManagerInterfaceImp::destroyAudioCapturer(const int &deviceId) {
    int ret = MediaManagerInterface::MM_SUCCESS;
    if (audio_capturers_.find(deviceId) != audio_capturers_.end()) {
        audio_capturers_.erase(deviceId);
    }
    else {
        jerror("DestroyAudioCapturer device id %d not found", deviceId);
        ret = MediaManagerInterface::MM_ERROR_DEVICE_NOT_OPENED;
    }
    return ret;
}

int MediaManagerInterfaceImp::createAudioRenderer(const int & deviceId, const std::string& jsonParam) {
    int ret = MediaManagerInterface::MM_SUCCESS;
    if (deviceId < 0) return MediaManagerInterface::MM_ERROR_INVALID_ID;
    if (audio_renderers_.count(deviceId) && audio_renderers_[deviceId].pipeline) {
        return MediaManagerInterface::MM_ERROR_DEVICE_ALREADY_OPENED;
    }
    if ((config_["audio"].contains("render") && config_["audio"]["render"].contains("name")))
    {
        nlohmann::json j;
        j["samplerate"] = 16000;
        if (config_["audio"]["render"].contains("samplerate"))
        {
            j["samplerate"] = config_["audio"]["render"]["samplerate"];
        }
        j["channel"] = 1;
        if (config_["audio"]["render"].contains("channel"))
        {
            j["channel"] = config_["audio"]["render"]["channel"];
        }
        j["maxChn"] = 8;
        if (config_["audio"]["render"].contains("maxChn"))
            j["maxChn"] = config_["audio"]["render"]["maxChn"];
        j["dev"] = deviceId;

        std::string name = config_["audio"]["render"]["name"];
        LogI("createAudioRenderer %s", name.c_str());
        auto renderer = AudioFrameRender::CreateAudioRender(name, j.dump());
        if (renderer)
        {
            audio_renderers_[deviceId].pipeline = std::move(renderer);
            audio_renderers_[deviceId].ref++;
            // audio_renderers_[deviceId].pipeline->SetCallback(this);
            LogI("createAudioRenderer success");
        } else
        { 
            LogI("createAudioRenderer fail");
            ret = MediaManagerInterface::MM_ERROR_CONFIG_INVALID;            
        }
    }
    else
    {
        LogI("CreateAudioCapturer fail");
        ret = MediaManagerInterface::MM_ERROR_CONFIG_INVALID;
    }
    return ret;
}

int MediaManagerInterfaceImp::destroyAudioRenderer(const int &deviceId) {
    int ret = MediaManagerInterface::MM_SUCCESS;
    if (audio_renderers_.find(deviceId) != audio_renderers_.end()) {
        audio_renderers_.erase(deviceId);
    } else {
        jerror("destroyAudioRenderer device id %d not found", deviceId);
        ret = MediaManagerInterface::MM_ERROR_DEVICE_NOT_OPENED;
    }
    return ret;
}

int MediaManagerInterfaceImp::setAudioEncoderNotifyCallback(const std::string &id) {
    int ret = MediaManagerInterface::MM_SUCCESS;
    return ret;
}
int MediaManagerInterfaceImp::setAudioDecoderNotifyCallback(const std::string &id) {
    int ret = MediaManagerInterface::MM_SUCCESS;
    return ret;
}
#ifdef ENABLE_VIDEO
int MediaManagerInterfaceImp::setVideoEncoderNotifyCallback(const std::string &id) {
    jinfo("setVideoEncoderNotifyCallback %s", id.c_str());
    int ret = MediaManagerInterface::MM_SUCCESS;
    if (sessions_[VIDEO].count(id) != 0) {
        for (auto &pipeline : sessions_[VIDEO][id].local.pipelines) {
            if (VideoFrameEncoder::IsVideoFrameEncoder(pipeline)) {
                auto cb = sessions_[VIDEO][id].commoncb;
                bool use_async = sessions_[VIDEO][id].notify_async;
                if (cb) {
                    pipeline->setNotify2(
                        id, "VideoEncoderStatus", sessions_[VIDEO][id].rtpParams, this,
                        [cb](const std::string &id, const std::string &key, const std::string &json, void *param) { 
                            cb(id, json); 
                        }, use_async);
                }
                break;
            }
        }
    }
    return ret;
}
int MediaManagerInterfaceImp::setVideoDecoderNotifyCallback(const std::string &id) {
    jinfo("setVideoDecoderNotifyCallback %s", id.c_str());
    int ret = MediaManagerInterface::MM_SUCCESS;
    if (sessions_[VIDEO].count(id) != 0) {
        for (auto &pipeline : sessions_[VIDEO][id].remote.pipelines) {
            if (VideoFrameDecoder::IsVideoFrameDecoder(pipeline)) {
                auto cb = sessions_[VIDEO][id].commoncb;
                bool use_async = sessions_[VIDEO][id].notify_async;
                if (cb) {
                    pipeline->setNotify2(
                        id, "VideoDecoderStatus", sessions_[VIDEO][id].rtpParams, this,
                        [cb](const std::string &id, const std::string &key, const std::string &json, void *param) { 
                            cb(id, json); 
                        }, use_async);
                }
                break;
            }
        }
    }
    return ret;
}
#endif

#ifdef USE_ALSA
std::vector<AlsaHWPcmDevice> MediaManagerInterfaceImp::ListAudioHWDeviceInfos() {
    return AlsaUtils::ListAlsaPcmDevices();
}

std::vector<AlsaPcmDeviceInfo> MediaManagerInterfaceImp::ListAudioDeviceInfos() {
    return AlsaUtils::GetAllAlsaPcmDevices();
}

#endif