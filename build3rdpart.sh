#!/bin/bash
#rm build -rf
CURDIR=$(pwd)/3rdpart/source
# mkdir -p ${CURDIR}/build/jthread
# mkdir -p ${CURDIR}/build/jrtplib
# mkdir -p ${CURDIR}/build/libv4l2cpp
# mkdir -p ${CURDIR}/build/libyuv
# mkdir -p ${CURDIR}/build/SDL2
# mkdir -p ${CURDIR}/build/ZLToolKit
# mkdir -p ${CURDIR}/build/libhv
# mkdir -p ${CURDIR}/build/libsdptransform
# mkdir -p ${CURDIR}/build/rtc.aec

# cd ${CURDIR}/build/rtc.aec
# cmake -DCMAKE_INSTALL_PREFIX=${CURDIR}/../ ${CURDIR}/rtc.aec
# make -j16
# make install

# cd ${CURDIR}/build/jthread
# cmake -DCMAKE_INSTALL_PREFIX=${CURDIR}/../ ${CURDIR}/jthread
# make -j4
# make install

# cd ${CURDIR}/build/jrtplib
# cmake -DCMAKE_INSTALL_PREFIX=${CURDIR}/../ ${CURDIR}/jrtplib
# make -j4
# make install

# cd ${CURDIR}/build/libv4l2cpp
# cmake -DCMAKE_INSTALL_PREFIX=${CURDIR}/../ ${CURDIR}/libv4l2cpp
# make -j4
# cd ${CURDIR}/libv4l2cpp/inc/* ${CURDIR}/../include
# cp liblibv4l2cpp.a ${CURDIR}/../lib/

# cd ${CURDIR}/build/libyuv
# cmake -DCMAKE_INSTALL_PREFIX=${CURDIR}/../ ${CURDIR}/libyuv
# make -j4
# make install

# cd ${CURDIR}/build/SDL2
# cmake -DCMAKE_INSTALL_PREFIX=${CURDIR}/../ ${CURDIR}/SDL2
# make -j4
# make install

# cd ${CURDIR}/build/ZLToolKit
# cmake -DCMAKE_INSTALL_PREFIX=${CURDIR}/../ ${CURDIR}/ZLToolKit
# make -j4
# make install

# cd ${CURDIR}/ljcore
# rm objects -rf
# make -j4
# cp common/jlog.h ${CURDIR}/../include/
# cp objects/ljcore/build/libljcore* ${CURDIR}/../lib/

# cd ${CURDIR}/build/libhv
# cmake -DCMAKE_INSTALL_PREFIX=${CURDIR}/../ ${CURDIR}/libhv
# make -j4
# make install

# cd ${CURDIR}/build/libsdptransform
# cmake -DCMAKE_INSTALL_PREFIX=${CURDIR}/../ ${CURDIR}/libsdptransform
# make -j4
# make install

# cd ${CURDIR}/opus-1.1.2
# make distclean
# ./configure --prefix=${CURDIR}/../
# make -j16
# make install

# cd ${CURDIR}/rnnoise
# ./autogen.sh
# make distclean
# ./configure --prefix=${CURDIR}/../
# make -j16
# make install

# cd ${CURDIR}/ffmpeg-4.4.4/
# sed -i 's/compute_30/compute_80/' configure
# sed -i 's/sm_30/sm_80/' configure
# make distclean
# ./configure --prefix=${CURDIR}/../ --enable-shared \
# --enable-nonfree --enable-gpl --enable-version3 \
# --enable-libmp3lame --enable-libvpx --enable-libopus \
# --enable-opencl --enable-libxcb --enable-avresample \
# --enable-opengl --enable-nvenc --enable-vaapi \
# --enable-vdpau --enable-ffplay --enable-ffprobe \
# --enable-libxvid \
# --enable-libx264 --enable-libx265 --enable-openal \
# --enable-openssl --enable-cuda-nvcc --enable-cuvid --extra-cflags=-I/usr/local/cuda/include --extra-ldflags=-L/usr/local/cuda/lib64
# make -j16
# make install

cd ${CURDIR}/resiprocate
rm _build -rf
mkdir _build && cd _build && cmake .. -DCMAKE_INSTALL_PREFIX=${CURDIR}/../ -DCMAKE_PREFIX_PATH=${CURDIR}/../ -DOPENSSL_ROOT_DIR=${CURDIR}/../
make -j16
make install

# cd ${CURDIR}/grpc
# rm build -rf
# mkdir build
# pushd build
# cmake -DgRPC_INSTALL=ON \
#       -DgRPC_BUILD_TESTS=OFF \
#       -DCMAKE_INSTALL_PREFIX=${CURDIR}/../ \
#       -DCMAKE_PREFIX_PATH=${CURDIR}/../ \
#       -DgRPC_PROTOBUF_PROVIDER=package \
#       -DgRPC_ZLIB_PROVIDER=package \
#       -DgRPC_SSL_PROVIDER=package \
#       -DOPENSSL_ROOT_DIR=${CURDIR}/../ \
#       -DOPENSSL_USE_STATIC_LIBS=TRUE \
#       ..
# make -j 16
# make install
# popd