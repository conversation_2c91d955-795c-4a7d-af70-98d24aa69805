#include "OpusAudioFrameEncoder.h"
#include "Frame.h"
#include <json.hpp>
#include <ljcore/jlog.h>

extern int mmi_record_audio;

using namespace panocom;

OpusAudioFrameEncoder::OpusAudioFrameEncoder(const std::string& jsonParams)
{
    FN_BEGIN;
    name_ = "OpusAudioFrameEncoder";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    chn_ = 1;
    // if (j.contains("channel"))
    // {
    //     chn_ = j["channel"];
    // }
    samplerate_ = 16000;
    if (j.contains("samplerate"))
    {
        samplerate_ = j["samplerate"];
    }
    fmt_ = Frame::getOpusFormat(chn_, samplerate_);
    jinfo("OpusAudioFrameEncoder %d %d", chn_, samplerate_);
    start();
    FN_END;
}

OpusAudioFrameEncoder::~OpusAudioFrameEncoder()
{
    FN_BEGIN;
    stop();
    FN_END;
}

void OpusAudioFrameEncoder::start()
{
    if (thread_.isRunning()) return;
    FN_BEGIN;
    int err = 0;
    encoder_ = std::shared_ptr<OpusEncoder>(opus_encoder_create(samplerate_, chn_, OPUS_APPLICATION_VOIP, &err), [](OpusEncoder* encoder){
        opus_encoder_destroy(encoder);
    });
    //opus_encoder_ctl(encoder_.get(), OPUS_SET_INBAND_FEC(1));
    //opus_encoder_ctl(encoder_.get(), OPUS_SET_PACKET_LOSS_PERC(5)); // 5%
    opus_encoder_ctl(encoder_.get(), OPUS_SET_VBR(1));
    if (mmi_record_audio)
    {
        decoder_ = std::shared_ptr<OpusDecoder>(opus_decoder_create(samplerate_, chn_, &err), [](OpusDecoder* decoder){
            opus_decoder_destroy(decoder);
        });
    }
    buffer_.resize(2048);
    thread_.start();
    FN_END;
}

void OpusAudioFrameEncoder::stop()
{
    if (!thread_.isRunning()) return;
    FN_BEGIN;
    std::unique_lock<std::mutex> locker(frame_mutex_);
    thread_.stop(true);
    encoder_.reset();
    initResampler_ = false;
    FN_END;
}

void OpusAudioFrameEncoder::onFrame(const std::shared_ptr<Frame> &frame)
{
    //jinfo("OpusAudioFrameEncoder::onFrame %d %d", frame->getFrameFormat(), frame->getFrameSize());
    std::unique_lock<std::mutex> locker(frame_mutex_);
    if (!thread_.isRunning()) return;
    thread_.loop()->runInLoop([this, frame](){
        auto f = frame;
        int samplerate = 0;
        int chn = 0;
        if (Frame::getSamplerate(f->getFrameFormat(), samplerate, chn))
        {
            if (samplerate_ != samplerate)
            {
                if (!initResampler_ || samplerate != source_samplerate_)
                {
                    source_samplerate_ = samplerate;
                    initResampler_ = true;
#ifdef WEBRTC_RESAMPLE_ANOTHER
                    resampler_.InitializeIfNeeded(samplerate, samplerate_, 1);
#else
                    resampler_.ResetIfNeeded(samplerate, samplerate_, 1);
#endif
                }
                auto frame = Frame::CreateFrame(FRAME_FORMAT_COMMON_AUDIO);
                frame->createFrameBuffer(f->getFrameSize() * samplerate_ / samplerate);
                memset(frame->getFrameBuffer(), 0, frame->getFrameSize());
#ifdef WEBRTC_RESAMPLE_ANOTHER
                resampler_.Resample((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2);
#else
                size_t outlen = 0;
                resampler_.Push((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2, outlen);
#endif
                f = frame;
                if (first_)
                {
                    first_ = false;
                    jinfo("OpusAudioFrameEncoder resample %d -> %d", samplerate, samplerate_);
                }
            }
            // if (chn_ != chn)
            // {
            //     if (chn_ > chn)
            //     {
            //         auto frame = Frame::CreateFrame(FRAME_FORMAT_COMMON_AUDIO);
            //         frame->createFrameBuffer(f->getFrameSize() * 2);
            //         int16_t* in = (int16_t*)f->getFrameBuffer();
            //         int16_t* out = (int16_t*)frame->getFrameBuffer();
            //         for (size_t i = 0; i < f->getFrameSize(); i++)
            //         {
            //             out[2 * i] = in[i];
            //             out[2 * i + 1] = in[i];
            //         }
            //         f = frame;
            //     }
            // }
            int ret = opus_encode(encoder_.get(), (int16_t*)f->getFrameBuffer(), f->getFrameSize() / sizeof(int16_t), buffer_.data(), buffer_.size());
            if (ret > 0)
            {
                if (mmi_record_audio)
                {
                    int ret2 = opus_decode(decoder_.get(), buffer_.data(), ret, (int16_t*)f->getFrameBuffer(), f->getFrameSize() / sizeof(int16_t), 0);
                    if (ret2 > 0)
                    {
                        //jinfo("%d %d", ret, ret2);
                        if (!pfDecode_)
                        {
                            std::string fileName = name_ + std::to_string(samplerate_) + "-" + std::to_string((long)this) + "-decode.pcm";
                            pfDecode_ = fopen(fileName.c_str(), "w+b");
                        }
                        if (pfDecode_)
                        {
                            fwrite(f->getFrameBuffer(), sizeof(int16_t), ret2, pfDecode_);
                        }
                    }
                }
                
                std::shared_ptr<Frame> f = Frame::CreateFrame((FrameFormat)fmt_);
                if (f)
                {
                    f->createFrameBuffer(ret);
                    memcpy(f->getFrameBuffer(), buffer_.data(), ret);
                    deliverFrame(f);
                    printOutputStatus("OpusAudioFrameEncoder");
                    if (mmi_record_audio)
                    {
                        if (!pf_)
                        {
                            std::string fileName = name_ + std::to_string(samplerate_) + "-" + std::to_string((long)this) + ".opus";
                            pf_ = fopen(fileName.c_str(), "w+b");
                        }
                        if (pf_)
                        {
                            fwrite(f->getFrameBuffer(), 1, f->getFrameSize(), pf_);
                        }
                    }
                }
            }
            else
            {
                jerror("opus_encode(%d %d) %d", f->getFrameFormat(), f->getFrameSize(), ret);
            }
        }
        else
        {
            jerror("opus_encode err fmt(%d)", frame->getFrameFormat());
        }
        
    });
    printInputStatus("OpusAudioFrameEncoder");
}