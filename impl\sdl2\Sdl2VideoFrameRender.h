// created by gyj 2024-2-21
#ifdef ENABLE_VIDEO
#ifndef P_Sdl2VideoFrameRender_h
#define P_Sdl2VideoFrameRender_h

#include "VideoFrameRender.h"
#include "VideoLayout.h"
#include <SDL2/SDL.h>
#include <hv/EventLoopThread.h>

#ifdef USE_FFMPEG
extern "C" {
#include <libavutil/imgutils.h>
#include <libavutil/opt.h>
#include <libavcodec/avcodec.h>
#include <libswscale/swscale.h>
}
#endif

namespace panocom
{
    class Sdl2VideoFrameRender : public VideoFrameRender
    {
    public:
        Sdl2VideoFrameRender(const std::string& jsonParams);
        ~Sdl2VideoFrameRender() override;

        void onFrame(const std::shared_ptr<Frame>& frame) override;
        void start() override;
        void stop() override;
    private:
        void render();
        bool readyRender();
    private:
        SDL_Window *win_;
        SDL_Renderer *renderer_;
        struct TextureInfo
        {
            SDL_Texture *texture;
            Region region;
        };
        
        std::vector<TextureInfo> textureInfos_;
        long renderGap_;

        std::string title_;
        int x_;
        int y_;
        int width_;
        int height_;

        hv::EventLoopThread thread_;

#ifdef USE_FFMPEG
        SwsContext* swsCtx_ = nullptr;
#endif
    };
}

#endif
#endif