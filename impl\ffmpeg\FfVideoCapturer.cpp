#include "FfVideoCapturer.h"
#include "Frame.h"
#include <json.hpp>
#include <ljcore/jlog.h>

using namespace panocom;

FfVideoCapturer::FfVideoCapturer(const std::string& jsonParams)
{
    FN_BEGIN;
    avdevice_register_all();
    name_ = "FfVideoCapturer";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    fps_ = 30;
    if (j.contains("fps"))
    {
        fps_ = j["fps"];
    }
#if defined(WIN32) || defined(WIN32)
    deviceType_ = "dshow";
#else
    //deviceType_ = "video4linux2";
    deviceType_ = "v4l2";
#endif

#if defined(WIN32) || defined(WIN32)
    deviceName_ = "video=HD Pro Webcam C920";
#else
    deviceName_ = "/dev/video0";
#endif
    if (j.contains("name"))
    {
        deviceName_ = j["name"];
    }
    width_ = 1280;
    if (j.contains("width"))
    {
        width_ = j["width"];
    }
    height_ = 720;
    if (j.contains("height"))
    {
        height_ = j["height"];
    }
    jinfo("FfVideoCapturer %s-%s %dx%d@%d ", deviceType_.c_str(), deviceName_.c_str(), width_, height_, fps_);
    start();
    FN_END;
}

FfVideoCapturer::~FfVideoCapturer()
{
    FN_BEGIN;
    stop();
    FN_END;
}

void FfVideoCapturer::start()
{
    if (thread_) return;
    FN_BEGIN;
    fmtInput_ = av_find_input_format(deviceType_.c_str());
    if (!fmtInput_)
    {
        jerror("av_find_input_format %s fail", deviceType_.c_str());
        return;
    }
    char optionStr[1024];
    sprintf(optionStr, "%d*%d", width_, height_);
 
    AVDictionary* options = nullptr;
    av_dict_set(&options, "video_size", optionStr, 0);

    av_dict_set_int(&options, "rtbufsize",  2*1024*1024, 0);
    av_dict_set(&options, "start_time_realtime", 0, 0);

    sprintf(optionStr, "%d", fps_);
    av_dict_set(&options, "framerate", optionStr, 0);
    av_dict_set(&options, "input_format", "mjpeg", 0);
    //fmtCtx_ = avformat_alloc_context();

    if (avformat_open_input(&fmtCtx_, deviceName_.c_str(), fmtInput_, &options) != 0)
    {
        jinfo("avformat_open_input %s fail", deviceName_.c_str());
        return;
    }
    if (avformat_find_stream_info(fmtCtx_, nullptr) <  0)
    {
        jinfo("avformat_find_stream_info %s fail", deviceName_.c_str());
        return;
    }
 
    for (unsigned int i = 0; i < fmtCtx_->nb_streams; i++)
    {
        if (fmtCtx_->streams[i]->codecpar->codec_type == AVMEDIA_TYPE_VIDEO)
        {
            jinfo("video %dx%d@%d", fmtCtx_->streams[i]->codecpar->width, fmtCtx_->streams[i]->codecpar->height, fmtCtx_->streams[i]->codecpar->format);
            index_ = i;
            break;
        }
    }

    thread_ = std::make_unique<std::thread>([this]{
        running_ = true;
        while (running_)
        {
            AVPacket *pkt = av_packet_alloc();
            if (av_read_frame(fmtCtx_, pkt) >= 0)
            {
                if (pkt->stream_index == index_)
                {
                    //jinfo("av_read_frame %d %d", pkt->size, index_);
                    std::shared_ptr<Frame> f = std::make_shared<FFAVPacket>(pkt);
                    deliverFrame(f);
                }
            }
        }
    });
    FN_END;
}

void FfVideoCapturer::stop()
{
    running_ = false;
    thread_->join();
    thread_.reset();
    avformat_close_input(&fmtCtx_);
    //avformat_free_context(fmtCtx_);
    fmtCtx_ = NULL;
}