/*
 * @Author: luo <EMAIL>
 * @Date: 2024-04-18 14:07:17
 * @LastEditors: luo <EMAIL>
 * @LastEditTime: 2025-04-24 19:59:04
 * @FilePath: /mediamanager/impl/audiocodec/AACAudioFrameEncoder.h
 * @Description: 音频aac编码模块
 */
#ifndef IMPL_AUDIO_AACAUDIOFRAMEENCODER_H_
#define IMPL_AUDIO_AACAUDIOFRAMEENCODER_H_
#include "AudioFrameEncoder.h"
#include "Frame.h"
#include <ptoolkit/bytebuffer.h>

#include "hv/EventLoopThread.h"
#include <fdk-aac/aacenc_lib.h>
#include <resampler/resampler.h>
#include <resampler/push_resampler.h>
namespace panocom
{

class AACAudioFrameEncoder : public AudioFrameEncoder
{
public:
	AACAudioFrameEncoder(const std::string& jsonParams);
	~AACAudioFrameEncoder() override;
	void onFrame(const std::shared_ptr<Frame> &frame) override;
private:
	bool init();
private:
	FrameFormat fmt_;
	HANDLE_AACENCODER encoder_;
	AACENC_InfoStruct info_;
	int channel_;
	uint32_t samplerate_;
	uint32_t bitrate_;
	uint32_t frame_length_;

	std::shared_ptr<hv::EventLoopThread> loop_thread_;
	hv::EventLoopPtr loop_;

	uint8_t* outbuf_;
	uint32_t outbuf_size_;
	ByteBuffer input_buf_;

#ifdef WEBRTC_RESAMPLE_ANOTHER
	nswebrtc::PushResampler<int16_t> resampler_;
#else
	nswebrtc::Resampler resampler_;
#endif       
	bool initResampler_ = false;
	int source_samplerate_;
    bool first_ = true;	
	uint8_t *encoded_buf_;
};



} // namespace panocom

#endif