// created by gyj 2024-02-28
#ifndef P_RtcRtpVideoFramePipeline_h
#define P_RtcRtpVideoFramePipeline_h

#include "RtpEngine.h"
#include "FramePipeline.h"
#include "SsrcGenerator.h"
#include "WebRTCTaskRunner.h"

#include <api/transport/network_control.h>
#include <call/rtp_transport_controller_send.h>
#include <modules/rtp_rtcp/include/rtp_rtcp.h>
#include <modules/rtp_rtcp/source/rtp_sender_video.h>

#include <rtc_base/random.h>
#include <rtc_base/rate_limiter.h>
#include <rtc_base/time_utils.h>
#include <rtc_base/event.h>

#include <json.hpp>

namespace panocom {

class RtcRtpVideoFrameDestination : public RtpEngine,
                             public webrtc::Transport,
                             public webrtc::RtcpBandwidthObserver,
                             public webrtc::NetworkStateEstimateObserver,
                             public webrtc::TransportFeedbackObserver,
                             public webrtc::RtpPacketSender,
                             public webrtc::TargetTransferRateObserver {
public:
    RtcRtpVideoFrameDestination(const std::string& jsonParams);
    ~RtcRtpVideoFrameDestination();

    void onFrame(const std::shared_ptr<Frame> &f) override;
    void start() override;
    void stop() override;

    // Implement webrtc::Transport
    bool SendRtp(const uint8_t* packet,
        size_t length,
        const webrtc::PacketOptions& options) override;
    bool SendRtcp(const uint8_t* packet, size_t length) override;

    // webrtc::RtcpBandwidthObserver
    void OnReceivedEstimatedBitrate(uint32_t bitrate) override;
    void OnReceivedRtcpReceiverReport(const webrtc::ReportBlockList& report_blocks, int64_t rtt, int64_t now_ms) override;

    // webrtc::NetworkStateEstimateObserver
    void OnRemoteNetworkEstimate(webrtc::NetworkStateEstimate estimate) override;

    // webrtc::TargetTransferRateObserver,
    void OnTargetTransferRate(webrtc::TargetTransferRate msg) override;
    void OnStartRateUpdate(webrtc::DataRate start_rate) override;

    // webrtc::TransportFeedbackObserver
    void OnAddPacket(const webrtc::RtpPacketSendInfo& packet_info) override;
    void OnTransportFeedback(const webrtc::rtcp::TransportFeedback& feedback) override;

    // webrtc::RtpPacketSender
    void EnqueuePackets(std::vector<std::unique_ptr<webrtc::RtpPacketToSend>> packets) override;

private:
    bool init();

    uint32_t convertToRTPTimestamp(struct timeval tv);
    uint32_t presetNextTimestamp();

    bool keyFrameArrived_ = false;
    std::unique_ptr<webrtc::RateLimiter> retransmissionRateLimiter_;
    // std::shared_ptr<webrtc::RtpRtcp> rtpRtcp_;
    std::unique_ptr<webrtc::ModuleRtpRtcpImpl2> rtpRtcp_;
    //std::unique_ptr<webrtc::ModuleRtpRtcpImpl2> m_rtpRtcp;
    std::mutex rtpRtcpMutex_;

    std::shared_ptr<WebRTCTaskRunner> taskRunner_;

    webrtc::Random random_;
    uint32_t ssrc_ = 0;
    SsrcGenerator* const ssrcGenerator_ = nullptr;

    std::mutex transportMutex_;

    webrtc::Clock* clock_ = nullptr;
    int64_t timeStampOffset_ = 0;

    std::unique_ptr<webrtc::RTPSenderVideo> senderVideo_;

    bool fNextTimestampHasBeenPreset_ = false;
    uint32_t fTimestampBase_ = 0;

    std::mutex bitrateMutex_;

    int payloadSize_ = 0;
    uint8_t payloadType_ = 0;
    uint32_t startBitrate_ = 0;
    uint32_t minBitrate_;
    uint32_t maxBitrate_;
    uint32_t avgBitrate_ = 0;
    uint32_t curBitrate_ = 0;

    std::shared_ptr<webrtc::RtpTransportControllerSendInterface> rtpSender_;
    std::shared_ptr<webrtc::RtcEventLog> eventLog_;
    std::shared_ptr<rtc::TaskQueue> taskQueue_;
    std::shared_ptr<webrtc::TaskQueueFactory> taskQueueFactory_;
    std::unique_ptr<webrtc::UlpfecGenerator> ulpfec_generator_;

    webrtc::FecProtectionParams delta_fec_params_;
    webrtc::FecProtectionParams key_fec_params_;

    rtc::Event started_;
    rtc::Event stopped_;

    std::mutex mutex_;

    uint8_t red_payload_type_ = 116;
    uint8_t ulpfec_payload_type_ = 127;

    bool nack_enabled_;
    bool ulpfec_enabled_;
    bool red_enabled_;
    bool ajb_enabled_;
    nlohmann::json rtp_extmap_;
    nlohmann::json remote_rtp_extmap_;
    nlohmann::json remote_rtp_rtcpfb_;

    //双模话机sdp说支持twcc rtcp-fb，但实际却没发反馈包，导致OnAddPacket收不到反馈包, 误判网络拥塞
    uint32_t cntAddPacket_ = 0;
    uint32_t cntTransportFeedback_ = 0;
};
}

#endif /* RTC_ADAPTER_VIDEO_SEND_ADAPTER_ */
