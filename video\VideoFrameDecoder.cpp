#include "VideoFrameDecoder.h"
#ifdef USE_FFMPEG
#include "ffmpeg/FfVideoDecoder.h"
#include "ffmpeg/FfHwVideoDecoder.h"
#include "ffmpeg/FfVideoDecoderAdapter.h"
#endif
#ifdef USE_CUDA
#include "cuda/CuVideoDecoder.h"
#endif

#include <algorithm>
#ifdef RK_PLATFORM
#include "rk/rk_video_decoder.h"
#endif

using namespace panocom;

std::list<std::string> VideoFrameDecoder::decoderTypes_;

bool VideoFrameDecoder::isRegistered = false;

void VideoFrameDecoder::Regist()
{
    if (!isRegistered)
    {
#ifdef USE_FFMPEG
        decoderTypes_.push_back("FfVideoDecoder");
        decoderTypes_.push_back("FfHwVideoDecoder");
        decoderTypes_.push_back("FfVideoDecoderAdapter");
#endif
#ifdef USE_CUDA
        decoderTypes_.push_back("CuVideoDecoder");
#endif
#ifdef RK_PLATFORM
        decoderTypes_.push_back("RKVideoDecoder");
#endif
        isRegistered = true;
    }
}

std::list<std::string> &VideoFrameDecoder::GetSupportDecoderTypes()
{
    Regist();
    return decoderTypes_;
}

bool VideoFrameDecoder::IsSupportDecoderType(const std::string &decoderType)
{
    Regist();
    return std::find(decoderTypes_.begin(), decoderTypes_.end(), decoderType) != decoderTypes_.end();
}

std::shared_ptr<VideoFrameDecoder> VideoFrameDecoder::CreateVideoDecoder(const std::string &decoderType, const std::string &jsonParams)
{
    Regist();
    std::shared_ptr<VideoFrameDecoder> ret;
#ifdef USE_FFMPEG
    if (decoderType == "FfVideoDecoderAdapter")
    {
        ret = std::make_shared<FfVideoDecoderAdapter>(jsonParams);
    }
    else if (decoderType == "FfHwVideoDecoder")
    {
        ret = std::make_shared<FfHwVideoDecoder>(jsonParams);
    }
    else if (decoderType == "FfVideoDecoder")
    {
        ret = std::make_shared<FfVideoDecoder>(jsonParams);
    }
#endif
#ifdef RK_PLATFORM
	if (decoderType == "RKVideoDecoder") 
    {
		ret = std::make_shared<RKVideoDecoder>(jsonParams);
	}
#endif
#ifdef USE_CUDA
    if (decoderType == "CuVideoDecoder")
    {
        ret = std::make_shared<CuVideoDecoder>(jsonParams);
    }
#endif
    return ret;
}

bool VideoFrameDecoder::IsVideoFrameDecoder(const FramePipeline::Ptr& ptr)
{
#ifdef USE_FFMPEG
    if (ptr->name() == "FfVideoDecoderAdapter")
    {
        return true;
    }
    if (ptr->name() == "FfHwVideoDecoder")
    {
        return true;
    }
    if (ptr->name() == "FfVideoDecoder")
    {
        return true;
    }
#endif
#ifdef RK_PLATFORM
	if (ptr->name() == "RKVideoDecoder") 
    {
		return true;
	}
#endif
#ifdef USE_CUDA
    if (ptr->name() == "CuVideoDecoder")
    {
        return true;
    }
#endif
    return false;
}