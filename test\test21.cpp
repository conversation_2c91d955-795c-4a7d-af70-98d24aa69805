﻿#include "MediaManagerInterface.h"
#include <json.hpp>
#include <stdio.h>

using namespace panocom;

int main(int argc, char **argv)
{
	std::string media = "video";
	int inputdev = 0;
	int outputdev = 0;
	if (argc < 4)
	{
		printf("Usage: %s <media:audio/video> <inputdevNo:0/1/2/...> <outputNo:0/1/2/...>\n", argv[0]);
		return 0;
	}
	media = argv[1];
	inputdev = atoi(argv[2]);
	outputdev = atoi(argv[3]);
	MediaManagerInterface mmi;
	std::string id = "12345";

	do
	{
		nlohmann::json param, capturer, render;

		capturer["name"] = (media == "audio") ? "AudioSpec": "V4L2DMAVideoFrameCapturer";
		capturer["dev"] = inputdev;

		render["name"] = (media == "audio") ? "AudioSpec" : "RKVideoRender";
		render["dev"] = outputdev;

		param[media]["capturer"] = capturer;
		param[media]["renderer"] = render;
		param[media]["capturer"]["app"] = "vst";
		
		if (media == "audio")
		{

		}
		else
		{
		}

		mmi.LoopbackTest(id, param.dump());
		printf("press any key to stop, press 'q' to exit\n");
		int c = getchar();
		mmi.StopLoopbackTest(id);
		if (c == 'q')
		{
			break;
		}

		printf("input test param like:\n<media:audio/video> <inputdevNo:0/1/2/...> <outputNo:0/1/2/...>\n");
		char line[256];
		fgets(line, sizeof(line), stdin);
		char buf[256];
		sscanf(line, "%s %d %d", buf, &inputdev, &outputdev);

		media = buf;
	} while (1);
	return 0;

	/**
 * @brief 自环测试接口
 * @param id 自环测试索引，用于结束此次测试
 * @param jsonParam
 * {
		"audio": {
			"capturer": {
				"//"： AudioSpec： 使用libPNPCM；AAlsaAudioFrameCapturer：使用alsa(HDMI)
				"name": "AudioSpec",
				"samplerate": 16000,
				"channel": 1,
				"dev": -1,
				"maxChn": 8
			},
			"renderer": {
				"//"： AudioSpec： 使用libPNPCM；AAlsaAudioFrameRender：使用alsa
				"name": "AudioSpec",
				"samplerate": 16000,
				"channel": 1,
				"dev": -1,
				"maxChn": 8
			}
		},
		"video": {
			"capturer": {
				"name": "V4L2DMAVideoFrameCapturer",
				"width": 1920,
				"height": 1080,
				"fps": 30,
				"format": "bgr3",
				"dev": -1,
			"app": "vst"
			},
			"render": {
				"name": "RKVideoRender",
				"frameCount": 8,
				"dev": -1
			}
		}
 * }
 */

    return 0;
}