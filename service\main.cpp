#include "MediaManagerInterface.h"
#include <json.hpp>
#include <stdio.h>
#include <ljcore/jlog.h>
#include <hv/EventLoop.h>

using namespace panocom;

int main(int argc, char **argv)
{
    nlohmann::json j;
    if (argc < 2)
    {
        printf("mmi [local] [port] [ip]\n");
        return -1;
    }
    int local = atoi(argv[1]);
    j["local"] = local;
    if (!local)
    {
        if (argc < 3)
        {
            printf("mmi [local] [port] [ip]\n");
            return -1;
        }
        j["port"] = atoi(argv[2]);
        j["ip"] = "0.0.0.0";
        if (argc > 3)
        {
            j["ip"] = atoi(argv[3]);
        }
    }
    
    signal(SIGSEGV, [](int) {
        jlog_uninit();
        (void) signal(SIGSEGV, SIG_DFL);  
    }); // 设置退出信号

    signal(SIGINT, [](int) {
        jlog_uninit();
        (void) signal(SIGINT, SIG_DFL);  
    }); // 设置退出信号

    signal(SIGTERM, [](int) {
        (void) signal(SIGTERM, SIG_DFL);  
        jlog_uninit();
    });
    auto loop = std::make_shared<hv::EventLoop>();
    jlog_init(nullptr);

    MediaManagerInterface mmi(j.dump());
    loop->run();
    return 0;
}