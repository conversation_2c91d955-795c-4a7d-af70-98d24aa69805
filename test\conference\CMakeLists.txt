cmake_minimum_required(VERSION 3.10)

project(conference)

set(CMAKE_CXX_STANDARD 14)

include_directories(${CMAKE_CURRENT_SOURCE_DIR} ${CMAKE_PREFIX_PATH}/include)

add_definitions(-Wall -O3 -g -fexceptions -fpermissive)
add_executable(test test.cpp)
target_link_libraries(test PRIVATE MediaPipeline asound ptoolkit jrtp jthread ljcore hv_static ZLToolKit aec rnnoise opus g729 fdk-aac DTLSTool nice glib-2.0 gio-2.0 gobject-2.0 gmodule-2.0 z ffi pcre2-8 srtp2 PNPcm SDL2 ssl crypto pthread dl )

add_executable(test1 test1.cpp)
target_link_libraries(test1 PRIVATE MediaPipeline asound ptoolkit jrtp jthread ljcore hv_static ZLToolKit aec rnnoise opus g729 fdk-aac DTLSTool nice glib-2.0 gio-2.0 gobject-2.0 gmodule-2.0 z ffi pcre2-8 srtp2 PNPcm SDL2 ssl crypto pthread dl )


