#include <future>
#include <fstream>
#include <json.hpp>
#include <jlog.h>

#include "MediaPipeline.h"
#include "AudioFrameMixer.h"

namespace {
const size_t kSampleRateHz = 16000;
}
using namespace panocom;
class TestFileSource : public FramePipeline {
private:
    /* data */
public:
    TestFileSource(const std::string &jsonParams);
    ~TestFileSource();
    void start() { running_ = true; }
private:
    std::future<bool> future_;
    std::ifstream input_;
    bool running_;
    int num_10ms_frames_per_packet_;
};

TestFileSource::TestFileSource(const std::string &jsonParams)
    : num_10ms_frames_per_packet_(1) {
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    if (j.contains("path")) {
        input_.open(j["path"], std::ios::in | std::ios::binary);
        std::string file_name = j["path"];
        printf("open file %s\n", file_name.c_str());
        // running_ = true;
    }
    FrameFormat format = FRAME_FORMAT_PCM_16000_1;
    
    future_ = std::async([this, format]() -> bool {
        uint32_t size = kSampleRateHz / 100 * num_10ms_frames_per_packet_ * 2;
        // uint32_t size = 2048;
        auto last = std::chrono::steady_clock::now();
        input_.seekg(0, std::ios::end);
        const uint32_t file_size = input_.tellg();
        int8_t data[file_size];
        input_.clear();
        input_.seekg(0, std::ios::beg);
        input_.read((char*)data, file_size);
        printf("file_size = %d\n", file_size);
        int pos = 0;
        while (running_) {
            auto now = std::chrono::steady_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(now - last).count();
            if (pos + size < file_size && duration >= 10000) {
                last = now;
                auto frame = std::make_shared<AudioFrame>(format);
                frame->createFrameBuffer((uint8_t*)data + pos, size);
                frame->setSource(this);
                deliverFrame(frame);
                pos += size;
            }
        }
        return true;
    });
}

TestFileSource::~TestFileSource() {
    running_ = false;
    auto status = future_.wait_for(std::chrono::milliseconds(200));
    if (status == std::future_status::timeout) {

    } else {
        future_.get();
    }
    if (input_ && input_.is_open()) {
        input_.close();
    }
}

class TestFileDestination : public FramePipeline {
private:
    /* data */
public:
    TestFileDestination(const std::string &jsonParams);
    ~TestFileDestination();
    virtual void onFrame(const std::shared_ptr<Frame> &f) override;

private:
    std::ofstream output_;
};

TestFileDestination::TestFileDestination(const std::string &jsonParams) {
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    if (j.contains("path")) {
        output_.open(j["path"], std::ios::out | std::ios::binary);
    }
}

void TestFileDestination::onFrame(const std::shared_ptr<Frame> &f) {
    output_.write((char *)f->getFrameBuffer(), f->getFrameBufferSize());
}

TestFileDestination::~TestFileDestination() {
    if (output_ && output_.is_open()) {
        output_.close();
    }
}


int main() {
    jlog_init(nullptr);
    nlohmann::json j;
    j.clear();

    auto mixer = AudioFrameMixer::CreateAudioMixer("DefaultAudioFrameMixer", j.dump());
    j.clear();
    j["path"] = "output.pcm";
    auto file_destination = std::make_shared<TestFileDestination>(j.dump());
    mixer->addAudioDestination(file_destination);

    std::vector<std::string> paths{"audio_short16.pcm", "audio16kHz.pcm", "testfile16kHz.pcm"};
    std::vector<std::shared_ptr<TestFileSource>> file_sources;
    for (auto &path: paths) {
        j.clear();
        j["path"] = path;
        auto file_source = std::make_shared<TestFileSource>(j.dump());
        file_source->addAudioDestination(mixer);
        file_sources.emplace_back(file_source);
        mixer->addSource(file_sources.back());
    }
    for (auto &source: file_sources)
        source->start();
    while (true) {
        usleep(10000);
    }
    return 0; 
}