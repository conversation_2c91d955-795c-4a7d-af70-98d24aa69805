// created by gyj 2024-4-24
#ifndef P_AudioSpecMulticasterSingle_h
#define P_AudioSpecMulticasterSingle_h

#include "FramePipeline.h"
#include <ptoolkit/bytebuffer.h>
#include <hv/EventLoopThread.h>
#include <malloc.h>
#include <stdio.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <arpa/inet.h>
#include <net/if.h>
#include <string.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <pthread.h>
#include <fcntl.h>
#include <stdint.h>
#include <unordered_map>
#include <chrono>
#include <fstream>

#include <PNPcm/HDPcm.h>
#include <resampler/resampler.h>
#include <resampler/push_resampler.h>
#include <ADL/adl_module.h>
#include "CspH3Chn.h"

// #define USEMONITOR 1
#define MAX_CAPTURER 5
namespace panocom
{
    // NOTE: CSP-H3使用
    // FIXME: 大部分是无用代码，从AudioSpecMulticaster拿来，需要删除
    class AudioSpecMulticasterSingle : public FramePipeline
    {
    public:
        AudioSpecMulticasterSingle(const std::string& jsonParams);
        ~AudioSpecMulticasterSingle() override;

        static std::shared_ptr<AudioSpecMulticasterSingle> CreateInstance(const std::string& jsonParams);
        static void ReleaseInstance();

        int updateParam(const std::string& jsonParam) override;

        void onFrame(const std::shared_ptr<Frame>& frame) override;
        void start() override;
        void stop() override;

        void setFrameTimeoutNotify(int ms, void* param, const TimeoutNotifyCallback& cb) override;
        void setFrameTimeoutNotify2(int ms, void* param, const TimeoutNotifyCallback& cb) override;
    private:
        void captureLoop(int index);
        void farendReadLoop();
#if H3_NEW_DRIVER
        void lecRefReadLoop();      // 获取lec参考或者aec处理后在进行防啸叫的数据
        void lecProcessLoop();
        void aecProcessLoop();
#endif
        void farendWriteLoop();
        void downlinkLoop();
        void uplinkLoop();
        void pstnInLoop();
        void pstnOutLoop();
        void pstnInLoop2();
        void playLoop();
        void captureLoop();
        bool getDownlinkData();
        bool setUplinkData(const char* data, int len);
        void writeFarendData(const char* data, int len);
        bool readFarendData(char* data, int len);
        int createSocket(const std::string& src = "");
        void openRecord();
        void closeRecord();
        void writeRecord(FILE* pf, const char* data, int len);

    	void ResetStatistics();
	    void LogStatistics();
    public:
        enum MODE
        {
            MODE_COMMON,
            MODE_MULTICAST,
            MODE_PSTN,
        };
        enum MICTYPE {
            TYPE_COMMON,
            TYPE_GOOSE,
            TYPE_INTERNAL_MIC,
        };
        int mode_ = MODE_COMMON;
        int mic_type_ = TYPE_GOOSE;
        int socketFd_ = -1;
        int pcmFd_ = -1;
        bool inited_ = false;
        std::unique_ptr<std::thread> farendReadThread_;
        std::unique_ptr<std::thread> farendWriteThread_;
        std::unique_ptr<std::thread> downlinkThread_;
        std::unique_ptr<std::thread> uplinkThread_;
        std::unique_ptr<std::thread> pstnInThread_;
        std::unique_ptr<std::thread> pstnOutThread_;
#if H3_NEW_DRIVER
        std::unique_ptr<std::thread> lecRefReadThread_;
        std::mutex lecRefMutex_;
        int lecRefReadRunning_ = 0;
        ByteBuffer lefRefReadBuf_;

        std::unique_ptr<std::thread> lecProcessThread_;
        int lecProcessRunning_ = 0;
        std::unique_ptr<std::thread> aecProcessThread_;
        int aecProcessRunning_ = 0;        
#endif
        // TODO: lockfree queue maybe better
        std::mutex aecMutex_;
        std::mutex lecMutex_;
        std::mutex downlinkMutex_;
        std::mutex uplinkMutex_;
        std::mutex pstnInMutex_;
        std::mutex pstnOutMutex_;
        std::mutex pstnMutex_;
        int farendReadRunning_ = 0;
        int farendWriteRunning_ = 0;
        int downlinkRunning_ = 0;
        int uplinkRunning_ = 0;
        int pstnInRunning_ = 0;
        int pstnOutRunning_ = 0;
        int capturing_ = 0;

        int captureNum = 0;
        int capturelostNum = 0;
        int processNum = 0;

        float gain_radio_;
        uint32_t index_ = 0;
        std::string devName_;
        int dev_;
        std::unique_ptr<std::thread> playthreads_[CH_MAX];
        bool running_[CH_MAX];
        int socketFds_[CH_MAX];
        bool mute_[CH_MAX];
        std::string ip_[CH_MAX];
        std::mutex mutexMuteAddr_;
        std::set<std::string> muteAddr_;
        
		bool startAecFarend_ = false;
        bool startPlay_ = false;
        std::unordered_map<uint64_t, int> devChnMap_;
        int chnIndex_ = 1;
        int samplerate_ = 16000;
        int ptime_;
        int ptimeDataLen_;
        //hv::EventLoopThread thread_;
        std::unique_ptr<std::thread> playthread_;
        std::unique_ptr<std::thread> captureThread_;
        int playRunning_ = 0;
        std::mutex mutexPlay_;
        ByteBuffer playbbuf_;
        hv::EventLoopThread threadPacer_;
        hv::EventLoopThread threadRecorder_;
        ByteBuffer bbuf_;
        std::shared_ptr<void> aec_;
        std::shared_ptr<void> lec_;
        ByteBuffer pstnInBuf_;
        ByteBuffer pstnOutBuf_;
        ByteBuffer pstnBuf_;
        FILE* sendf = nullptr;
        FILE* recvf = nullptr;
        FILE* nearf = nullptr;
        FILE* playf = nullptr;
        FILE* pstnIn = nullptr;
        FILE* pstnOut = nullptr;
        
        int enableAEC_ = 1;
        int enableLEC_ = 1;
        int innerAEC_ = 1;
        int enableAGC_ = 0;
        int enableCNG_ = 0;
        int enableNS_ = 0;
        int anti_howling_ = 0;
        int delay_ = 1;
        int logAEC_ = 0;
        int recordAudio_ = 0;
        long readTick_ = 0;
        long readBytes_ = 0;
        long writeTick_ = 0;
        long writeBytes_ = 0;

        struct sockaddr_in addr_;

        ByteBuffer farendReadBBuf_;
        std::mutex mutexFarendRead_;

        ByteBuffer farendBBuf_;
        std::mutex mutexFarend_;

        std::mutex startstopMutex_;
        struct sockaddr_in localAddr_;

        std::string localIP_;
        int bindPort_;
        std::string multiIP_;
        int multiPort_;
        bool muteNotLP_ = false;

        int playNum = 0;

        std::mutex socketMutex_;
#ifdef WEBRTC_RESAMPLE_ANOTHER
        nswebrtc::PushResampler<int16_t> resampler_;
#else
        nswebrtc::Resampler resampler_;
#endif       
        bool initResampler_ = false;
        int source_samplerate_ = 0;
        bool useHeadset_ = false;

        static std::shared_ptr<AudioSpecMulticasterSingle> inst_;

        std::unique_ptr<std::thread> captureThreads_[MAX_CAPTURER];
        std::mutex captureMutexs_[MAX_CAPTURER];
        int captureRunnings_[MAX_CAPTURER] = { 0 };
        ByteBuffer capturebufs_[MAX_CAPTURER];
        std::mutex captureMutex_;
        ByteBuffer capturebuf_;
        FILE* recordf[MAX_CAPTURER] = { nullptr };
        char ctxADL_[ADL_MEM_SIZE];

        std::chrono::steady_clock::time_point statistics_start_time_;
        uint32_t frames_captured_;
        uint32_t frames_recieved_;
        uint32_t frames_to_send_;
        uint32_t frames_to_playout_;
        uint32_t frames_lec_ref_;

        hv::EventLoopThread check_timeout_thread_;
        std::ofstream ofs_;
        std::ofstream ofs_1;
        std::ofstream ofs_2;

        int ref_count;
        int last_tick_;
        int lec_ref_last_tick_;
#if USEMONITOR
        hv::EventLoopThread monitorThread_;
        int use_original_ = 0;
        int module_disable_ = 1;
#endif

        // 通话时丢弃前xms的音频数据，避免播放出错
        int discard_frames_count_;      // 大包数据
        int discard_frames_count_lp_;      // 小包数据
        int discard_frames_ms_;
    };
}

#endif
