#include "AudioFrameAsFarEnd.h"
#include "Frame.h"
#include "Utils.h"
#include <ljcore/jlog.h>
#include <stdio.h>
#include <json.hpp>

using namespace panocom;

AudioFrameAsFarEnd::AudioFrameAsFarEnd(const std::string& jsonParams)
{
    FN_BEGIN;
    name_ = "AudioFrameAsFarEnd";
    FN_END;
}

AudioFrameAsFarEnd::~AudioFrameAsFarEnd()
{
    FN_BEGIN;
    FN_END;
}

void AudioFrameAsFarEnd::onFrame(const std::shared_ptr<Frame> &frame)
{
    auto f = Frame::CreateFrame(frame->getFormatFarEnd());
    if (f)
    {
        //jinfo("AudioFrameAsFarEnd::onFrame %d %d", f->getFrameFormat(), f->getFrameSize());
        f->createFrameBuffer(frame->getFrameSize());
        memcpy(f->getFrameBuffer(), frame->getFrameBuffer(), frame->getFrameSize());
        deliverFrame(f);
    }
}