// created by gyj 2024-5-27
#ifndef P_DTLSFramePipeline_h
#define P_DTLSFramePipeline_h
#ifdef USE_DTLS
#include "Frame.h"
#include "FramePipeline.h"
#include <srtp2/srtp.h>
#include <atomic>

namespace panocom
{
    class DTLSFramePipeline: public FramePipeline
    {
    public:
        DTLSFramePipeline(const std::string& jsonParams);
        ~DTLSFramePipeline();

        int updateParam(const std::string& jsonParams) override;
    
        void onFrame(const std::shared_ptr<Frame>& frame) override;
        void start();
        void stop();

        int send(char* buf, int size);
        void notify(int result, char* err);
    private:
        static int send(char* buf, int size, void* param);
        static void notify(int result, char* err, void* param);
        static void initDTLSEnv();

        enum TransmissionType
        {
            SENDING,
            RECEIVING
        };
        bool configureSrtpSession(srtp_t *session, const std::string &key, enum TransmissionType type);

    private:
        bool isServer_ = false;
        bool isRtcp_ = false;
        bool active_ = false;
        srtp_t send_session_;
        srtp_t receive_session_;
        void* dtlsCtx_;

        std::atomic<bool> started_;
        std::atomic<bool> closed_;

        static bool dtlsInited_;
        static std::mutex initMutex_;
    };
}
#endif
#endif