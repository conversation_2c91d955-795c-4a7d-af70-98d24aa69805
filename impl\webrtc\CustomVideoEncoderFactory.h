#ifndef IMPL_WEBRTC_CUSTOMVIDEOENCODERFACTORY_H_
#define IMPL_WEBRTC_CUSTOMVIDEOENCODERFACTORY_H_

#include "PassthroughVideoEncoder.h"
#include <api/video_codecs/video_encoder_factory.h>
#include <rtc_base/logging.h>

namespace panocom {
class CustomVideoEncoderFactory : public webrtc::VideoEncoderFactory {
public:
    std::vector<webrtc::SdpVideoFormat> GetSupportedFormats() const override { return { webrtc::SdpVideoFormat("H264"), webrtc::SdpVideoFormat("H265") }; }
    std::unique_ptr<webrtc::VideoEncoder> CreateVideoEncoder(const webrtc::SdpVideoFormat &format) override {
        auto encoder = std::make_unique<PassthroughVideoEncoder>();
        jinfo("CustomVideoEncoderFactory::CreateVideoEncoder");
        RTC_LOG(LS_INFO) << "CustomVideoEncoderFactory::CreateVideoEncoder";
        encoder_ptr_ = encoder.get();
        return encoder;
    }
    webrtc::VideoEncoderFactory::CodecInfo QueryVideoEncoder(const webrtc::SdpVideoFormat &format) const override {
        // Format must be one of the internal formats.
        RTC_LOG(LS_INFO) << "CustomVideoEncoderFactory::QueryVideoEncoder";
        webrtc::VideoEncoderFactory::CodecInfo info;
        info.has_internal_source = true;
        return info;
    }
    PassthroughVideoEncoder *get_encoder() const {
        return encoder_ptr_;
    }
private:
    PassthroughVideoEncoder* encoder_ptr_ = nullptr;
};

} // namespace panocom
#endif