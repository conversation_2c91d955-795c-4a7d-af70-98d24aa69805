cmake_minimum_required(VERSION 3.10)

project(MediaPipeline)

set(CMAKE_CXX_STANDARD 14)

# 添加 git 版本信息
set(COMMIT_HASH "Git_Unkown_commit")
set(COMMIT_TIME "Git_Unkown_time")
set(<PERSON><PERSON><PERSON>_NAME "Git_Unkown_branch")
set(BUILD_TIME "")

string(TIMESTAMP BUILD_TIME "%Y-%m-%dT%H:%M:%S")

find_package(Git QUIET)
if(GIT_FOUND)
  execute_process(
    COMMAND ${GIT_EXECUTABLE} rev-parse --short=7 HEAD
    OUTPUT_VARIABLE COMMIT_HASH
    OUTPUT_STRIP_TRAILING_WHITESPACE
    ERROR_QUIET
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR})
  execute_process(
    COMMAND ${GIT_EXECUTABLE} symbolic-ref --short -q HEAD
    OUTPUT_VARIABLE BRANCH_NAME
    OUTPUT_STRIP_TRAILING_WHITESPACE
    ERROR_QUIET
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR})

  execute_process(
    COMMAND ${GIT_EXECUTABLE} log --format=format:%aI -1
    OUTPUT_VARIABLE COMMIT_TIME
    OUTPUT_STRIP_TRAILING_WHITESPACE
    ERROR_QUIET
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR})
endif ()

message(STATUS "Git version is ${BRANCH_NAME} ${COMMIT_HASH}/${COMMIT_TIME} ${BUILD_TIME}")

add_definitions(-DWIN32_LEAN_AND_MEAN -DWITHOUT_HTTP_CONTENT)

if (ENABLE_VIDEO)
  add_definitions(-DENABLE_VIDEO)
endif ()

include_directories(${CMAKE_CURRENT_SOURCE_DIR}
${CMAKE_CURRENT_SOURCE_DIR}/../
${CMAKE_CURRENT_SOURCE_DIR}/../3rdpart/include
${CMAKE_CURRENT_SOURCE_DIR}/../3rdpart/include/zltoolkit)
link_directories(${CMAKE_CURRENT_SOURCE_DIR}/../3rdpart/lib)

aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR} SRC_LIST)

add_library(${PROJECT_NAME} SHARED ${CMAKE_CURRENT_SOURCE_DIR}/../MediaManagerInterface.cpp ${CMAKE_CURRENT_SOURCE_DIR}/../CodecInfo.cpp  ${SRC_LIST})
target_link_libraries(${PROJECT_NAME} sdptransform ZLToolKit)

install(TARGETS ${PROJECT_NAME} LIBRARY DESTINATION ${CMAKE_INSTALL_PREFIX}/release/lib)

add_subdirectory(test)
