#include <stdio.h>
#include <future>
#include <fstream>
#include <unistd.h>
#include <json.hpp>
#include <ljcore/jlog.h>

#include "VideoFrameCapturer.h"
#include "VideoFrameRender.h"
#include "MediaPipeline.h"

using namespace panocom;

#define WIDTH 1920
#define HEIGHT 1080

class TestFileDestination : public FramePipeline
{
private:
	/* data */
public:
	TestFileDestination(const std::string& jsonParams);
	~TestFileDestination();
	virtual void onFrame(const std::shared_ptr<Frame> &f) override;
private:
	std::ofstream output_;
	uint32_t frame_count_;
};

TestFileDestination::TestFileDestination(const std::string& jsonParams)
{
	nlohmann::json j;
	if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
	if (j.contains("path")) {
		output_.open(j["path"], std::ios::out | std::ios::binary);
	}
	frame_count_ = 0;
}

void TestFileDestination::onFrame(const std::shared_ptr<Frame> &f) {
	if (output_.is_open()) {
		output_.write((char*)f->getFrameBuffer(), f->getFrameBufferSize());
	}
}

TestFileDestination::~TestFileDestination()
{
	if (output_ && output_.is_open()) {
		output_.close();
	}
}

int main() {
    jlog_init(nullptr);
	int count = 1;
    nlohmann::json j;

	nlohmann::json r;
	j.clear();
	j["dev"] = 1;
	j["y"] = 0;
	j["x"] = 0;
	j["width"] = 1920;
	j["height"] = 1080;
	j["format"] = "nv12";
	j["fps"] = 30;
	j["videoLayout"] = nlohmann::json::array();

	r.clear();
	r["id"] = 1;
	r["rect"]["w"] = WIDTH / count;
	r["rect"]["h"] = HEIGHT / count;
	for (int row = 0; row < count; row++) {
		for (int line = 0; line < count; line++) {
			r["rect"]["x"] = (WIDTH / count) * line;
			r["rect"]["y"] = (HEIGHT / count) * row;
			j["videoLayout"].push_back(r);
		}
	}
	auto display = VideoFrameRender::CreateVideoRender("RKVideoRender", j.dump());

	// j.clear();
    // j["codec"] = "h264";
    // j["width"] = WIDTH;
    // j["height"] = HEIGHT;
	// auto decoder = VideoFrameDecoder::CreateVideoDecoder("RKVideoDecoder", j.dump());

	// j.clear();
	// j["path"] = "output.h264";
	// auto file_destination = std::make_shared<TestFileDestination>(j.dump());

	// j.clear();
    // j["codec"] = "H264";
	// j["fps"] = 60;
	// j["format"] = "nv12";
    // j["width"] = 1920;
    // j["height"] = 1080;
	// auto encoder = VideoFrameEncoder::CreateVideoEncoder("RKVideoEncoder", j.dump());

    j.clear();
    j["dev"] = 0;
	// j["app"] = "vst";
    j["format"] = "nv16";

    auto capturer = VideoFrameCapturer::CreateVideoCapturer("V4L2DMAVideoFrameCapturer", j.dump());
	capturer->setGroupId(1);
	
    capturer->addVideoDestination(display);
	// capturer->addVideoDestination(encoder);
    // encoder->addVideoDestination(file_destination);

	getchar();
    // while (true)
    // {
    //     usleep(10000);
    // }
    return 0;
}