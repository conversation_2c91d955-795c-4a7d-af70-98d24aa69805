#ifndef P_VideoFrameEncoder_h
#define P_VideoFrameEncoder_h

#include "../FramePipeline.h"
#include "../CodecInfo.h"

namespace panocom
{
    class VideoFrameEncoder : public FramePipeline
    {
    public:
        static void Regist();
        static std::list<std::string>& GetSupportEncoderTypes();
        static bool IsSupportEncoderType(const std::string &encoderType);
        static std::shared_ptr<VideoFrameEncoder> CreateVideoEncoder(const std::string &encoderType, const std::string& jsonParams = "");

        static std::list<CodecInst>& GetSupportCodecs();
        static bool IsSupportCodec(const CodecInst &codec);
        static bool IsVideoFrameEncoder(const FramePipeline::Ptr& ptr);

        virtual bool updateParams(const std::string& jsonParams) { return false; }

        virtual ~VideoFrameEncoder() = default;
    protected:
        static bool isRegistered;
        static std::list<CodecInst> codecs_;
        static std::list<std::string> encoderTypes_;
    };
}

#endif