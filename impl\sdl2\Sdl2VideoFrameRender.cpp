#ifdef ENABLE_VIDEO
#include "Sdl2VideoFrameRender.h"
#include "Frame.h"
#include "Utils.h"
#include <json.hpp>
#include <ljcore/jlog.h>
#include <stdio.h>

using namespace panocom;

Sdl2VideoFrameRender::Sdl2VideoFrameRender(const std::string& jsonParams) : win_(nullptr), renderer_(nullptr), renderGap_(0)
{
    FN_BEGIN;
    jinfo("Sdl2VideoFrameRender %s", jsonParams.c_str());
    name_ = "Sdl2VideoFrameRender";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    if (j.contains("title"))
    {
        title_ = j["title"];
    }
    x_ = 0;
    if (j.contains("x"))
    {
        x_ = j["x"];
    }
    y_ = 0;
    if (j.contains("y"))
    {
        y_ = j["y"];
    }
    width_ = 1280;
    if (j.contains("width"))
    {
        width_ = j["width"];
    }
    height_ = 720;
    if (j.contains("height"))
    {
        height_ = j["height"];
    }
    int fps = 25;
    if (j.contains("fps"))
    {
        fps = j["fps"];
    }
    renderGap_ = 1000 / fps;

    if (j.contains("videoLayout") && j["videoLayout"].is_array())
    {
        for (int i = 0; i < j["videoLayout"].size(); ++i)
        {
            if (j["videoLayout"][i].contains("id") &&
                j["videoLayout"][i].contains("rect") &&
                j["videoLayout"][i]["rect"].contains("x") &&
                j["videoLayout"][i]["rect"].contains("y") &&
                j["videoLayout"][i]["rect"].contains("w") &&
                j["videoLayout"][i]["rect"].contains("h"))
            {
                TextureInfo info;
                info.texture = nullptr;
                info.region.id = j["videoLayout"][i]["id"];
                info.region.rect.x = j["videoLayout"][i]["rect"]["x"];
                info.region.rect.y = j["videoLayout"][i]["rect"]["y"];
                info.region.rect.w = j["videoLayout"][i]["rect"]["w"];
                info.region.rect.h = j["videoLayout"][i]["rect"]["h"];
                jinfo("video layout id = %d x = %d y= %d w = %d h = %d", info.region.id, info.region.rect.x, info.region.rect.y, info.region.rect.w, info.region.rect.h);
                textureInfos_.push_back(info);
            }
        }
    }
    else
    {
        TextureInfo info;
        info.texture = nullptr;
        info.region.id = 0;
        info.region.rect.x = 0;
        info.region.rect.y = 0;
        info.region.rect.w = width_;
        info.region.rect.h = height_;
        textureInfos_.push_back(info);
    }
    start();
    FN_END;
}

Sdl2VideoFrameRender::~Sdl2VideoFrameRender()
{
    FN_BEGIN;
    stop();
    FN_END;
}

void Sdl2VideoFrameRender::start()
{
    if (thread_.isRunning()) return;
    FN_BEGIN;
    thread_.loop()->setInterval(renderGap_, [this](hv::TimerID id) {
        SDL_Event e;
        while (SDL_PollEvent(&e))
        {
            if (e.type == SDL_QUIT)
            {
                for (int i = 0; i < textureInfos_.size(); ++i)
                {
                    if (textureInfos_[i].texture)
                    {
                        SDL_DestroyTexture(textureInfos_[i].texture);
                        textureInfos_[i].texture = nullptr;
                    }
                }
                if (win_)
                {
                    SDL_DestroyWindow(win_);
                    win_ = nullptr;
                }
                if (renderer_)
                {
                    SDL_DestroyRenderer(renderer_);
                    renderer_ = nullptr;
                }
                hv::killTimer(id);
                return;
            }
        }
        if (renderer_)
        {
            printDestinationStatus("sdl");
            SDL_RenderPresent(renderer_);
        }
    });
    thread_.start();
    FN_END;
}

void Sdl2VideoFrameRender::stop()
{
    if (!thread_.isRunning()) return;
    FN_BEGIN;
    std::unique_lock<std::mutex> locker(frame_mutex_);
    thread_.stop(true);
    FN_END;
}

void Sdl2VideoFrameRender::onFrame(const std::shared_ptr<Frame>& frame)
{
#ifdef USE_FFMPEG
    std::unique_lock<std::mutex> locker(frame_mutex_);
    if (frame->getFrameFormat() != FRAME_FORMAT_FFAVFRAME || !thread_.isRunning()) return;
    thread_.loop()->runInLoop([this, frame](){
        int groupId = frame->getGroupId();
        FFAVFrame* f = (FFAVFrame*)frame.get();
        AVFrame* avframe = (AVFrame*)f->getAVFrame();
        for (int i = 0; i < textureInfos_.size(); ++i)
        {
            if (groupId == textureInfos_[i].region.id)
            {
                if (textureInfos_[i].texture == nullptr)
                {
                    if (win_ == nullptr)
                    {
                        win_ = SDL_CreateWindow(title_.c_str(), x_, y_, width_, height_, SDL_WINDOW_OPENGL);
                        renderer_ = SDL_CreateRenderer(win_, -1, 0);
                    }
                    uint32_t format =  SDL_PIXELFORMAT_NV12;
                    if (avframe->format == AV_PIX_FMT_NV12)
                    {
                        format =  SDL_PIXELFORMAT_NV12;
                    }
                    else if (avframe->format == AV_PIX_FMT_YUVJ420P || avframe->format == AV_PIX_FMT_YUV420P)
                    {
                        format =  SDL_PIXELFORMAT_IYUV;
                    }
                    else
                    {
                        if (!swsCtx_)
                        {
                            swsCtx_ = sws_getContext(avframe->width, avframe->height, (AVPixelFormat)avframe->format, avframe->width, avframe->height, AV_PIX_FMT_YUV420P, SWS_FAST_BILINEAR, NULL, NULL, NULL);
                        }
                        format = SDL_PIXELFORMAT_IYUV;
                    }
                    textureInfos_[i].texture = SDL_CreateTexture(renderer_, format, SDL_TEXTUREACCESS_STREAMING, avframe->width, avframe->height);
                }
                if (avframe->format == AV_PIX_FMT_NV12)
                {
                    SDL_UpdateNVTexture(textureInfos_[i].texture, nullptr, avframe->data[0], avframe->linesize[0], avframe->data[1], avframe->linesize[1]);
                }
                else if (avframe->format == AV_PIX_FMT_YUVJ420P || avframe->format == AV_PIX_FMT_YUV420P)
                {
                    SDL_UpdateYUVTexture(textureInfos_[i].texture, nullptr, avframe->data[0], avframe->linesize[0], avframe->data[1], avframe->linesize[1], avframe->data[2], avframe->linesize[2]);
                }
                else
                {
                    if (swsCtx_)
                    {
                        AVFrame* newFrame = av_frame_alloc();
                        int size = av_image_get_buffer_size(AV_PIX_FMT_YUV420P, avframe->width, avframe->height, 16);
                        uint8_t* buffer = (uint8_t*)av_malloc(size);
                        av_image_fill_arrays(newFrame->data, newFrame->linesize, buffer, AV_PIX_FMT_YUV420P, avframe->width, avframe->height, 16);
                        newFrame->width = avframe->width;
                        newFrame->height = avframe->height;
                        int ret = sws_scale(swsCtx_, avframe->data, avframe->linesize, 0, avframe->height, newFrame->data, newFrame->linesize);
                        avframe = newFrame;
                        SDL_UpdateYUVTexture(textureInfos_[i].texture, nullptr, avframe->data[0], avframe->linesize[0], avframe->data[1], avframe->linesize[1], avframe->data[2], avframe->linesize[2]);
                        av_frame_free(&avframe);
                    }
                    
                }
                SDL_Rect dstRect;
                dstRect.x = textureInfos_[i].region.rect.x;
                dstRect.y = textureInfos_[i].region.rect.y;
                dstRect.w = textureInfos_[i].region.rect.w;
                dstRect.h = textureInfos_[i].region.rect.h;
                SDL_RenderCopy(renderer_, textureInfos_[i].texture, nullptr, &dstRect);
            }
        }
    });
#endif
}
#endif