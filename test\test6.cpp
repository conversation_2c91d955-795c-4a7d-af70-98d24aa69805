#include "MediaPipeline.h"
#include <json.hpp>
#include <stdio.h>
#include <ljcore/jlog.h>
#include <hv/EventLoop.h>

using namespace panocom;

int main()
{
    auto loop = std::make_shared<hv::EventLoop>();
    jlog_init(nullptr);
    nlohmann::json j;
    auto capturer = VideoFrameCapturer::CreateVideoCapturer("V4L2VideoFrameCapturer", j.dump());
    j.clear();
    j["codec"] = "jpeg";
    auto decoder = VideoFrameDecoder::CreateVideoDecoder("CuVideoDecoder", j.dump());
    capturer->addVideoDestination(decoder);
    loop->run();
    return 0;
}