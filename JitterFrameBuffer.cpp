#include "JitterFrameBuffer.h"
#include "Utils.h"

using namespace panocom;

JitterFrameBuffer::JitterFrameBuffer(int initFps, int minDelay, int maxDelay, int detectFpsSec)
 : fps_(initFps), minDelay_(minDelay), maxDelay_(maxDelay), detectFpsSec_(detectFpsSec), frameCount_(0), tick_(0)
{
    int gap = 1000 / fps_;
    cacheFrameCount_ = minDelay_ / gap;
    dropMaxFrameCount_ = maxDelay_ / gap;
}

JitterFrameBuffer::~JitterFrameBuffer()
{

}

void JitterFrameBuffer::pushFrame(const std::shared_ptr<Frame>& f)
{
    frameCount_++;
    dropFrameCount_++;
    if (tick_ == 0)
    {
        tick_ = GetTickCount();
    }
    else
    {
        long gap = GetTickCount() - tick_;
        if (gap >= detectFpsSec_ * 1000)
        {
            fps_ = frameCount_ / detectFpsSec_;
            int gap = 1000 / fps_;
            cacheFrameCount_ = minDelay_ / gap;
            dropMaxFrameCount_ = maxDelay_ / gap;
            frameCount_ = 0;
            tick_ = GetTickCount();
        }
    }
    fb_.push(f);
    if (!readyPop_ && fb_.size() >= cacheFrameCount_)
    {
        readyPop_ = true;
    }
    if (fb_.size() >= dropMaxFrameCount_)
    {
        if (dropFrameCount_ >= fps_)
        {
            dropFrameCount_ = 0;
            fb_.pop();
        }
    }
}

std::shared_ptr<Frame> JitterFrameBuffer::popFrame()
{
    if (!readyPop_)
    {
        return nullptr;
    }
    if (fb_.empty())
    {
        readyPop_ = false;
        return nullptr;
    }
    std::shared_ptr<Frame> f = fb_.front();
    fb_.pop();
    return f;
}