#include "G729AudioFrameEncoder.h"
#include <json.hpp>

namespace panocom
{
namespace
{
const size_t kSampleRateHz = 8000;
} // namespace


G729AudioFrameEncoder::G729AudioFrameEncoder(const std::string &jsonParams)
    : fmt_(FRAME_FORMAT_G729), samplerate_(kSampleRateHz) {
    name_ = "G729AudioFrameEncoder";
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
	// if (j.contains("samplerate"))
	// {
	// 	samplerate_ = j["samplerate"];
	// }
	ctx_ = initBcg729EncoderChannel(0);
	if (!ctx_) printf("Create g729 encoder failed\n");
}
G729AudioFrameEncoder::~G729AudioFrameEncoder() {
	if (ctx_) 
		closeBcg729EncoderChannel(ctx_);
	ctx_ = nullptr;
}
// 16bits, 8k, 单通道
void G729AudioFrameEncoder::onFrame(const std::shared_ptr<Frame> &frame) {
    int samplerate = 0;
    int chn = 0;
    auto f = frame;
    if (Frame::getSamplerate(f->getFrameFormat(), samplerate, chn)) {
        if (samplerate_ != samplerate) {
            if (!initResampler_ || source_samplerate_ != samplerate) {
                source_samplerate_ = samplerate;
                initResampler_ = true;
#ifdef WEBRTC_RESAMPLE_ANOTHER
                resampler_.InitializeIfNeeded(samplerate, samplerate_, 1);
#else
                resampler_.ResetIfNeeded(samplerate, samplerate_, 1);
#endif
            }
            auto frame = Frame::CreateFrame(FRAME_FORMAT_COMMON_AUDIO);
            frame->createFrameBuffer(f->getFrameSize() * samplerate_ / samplerate);
            memset(frame->getFrameBuffer(), 0, frame->getFrameSize());
#ifdef WEBRTC_RESAMPLE_ANOTHER
            resampler_.Resample((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2);
#else
            size_t outlen = 0;
            resampler_.Push((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2, outlen);
#endif
            f = frame;
            if (first_) {
                first_ = false;
                jinfo("G729AudioFrameEncoder resample %d -> %d", samplerate, samplerate_);
            }
        }
    }

    int16_t total = 0;
    uint8_t bitStreamLength = 0;
    int n = f->getFrameBufferSize() / L_FRAME / 2;
    int each_encoded_size = (f->getFrameBufferSize() >> 4) / n; // TODO：话机只处理10ms
    int16_t *speechIn = (int16_t *)f->getFrameBuffer();

    std::shared_ptr<Frame> out_frame = Frame::CreateFrame(fmt_);
    out_frame->createFrameBuffer(f->getFrameBufferSize() >> 4);
    uint8_t *bitStream = (uint8_t *)out_frame->getFrameBuffer();
    if (ctx_) {
        for (int i = 0; i < n; i++) {
            // std::shared_ptr<Frame> out_frame = Frame::CreateFrame(fmt_);
            // out_frame->createFrameBuffer(each_encoded_size);
            // uint8_t *bitStream = (uint8_t *)out_frame->getFrameBuffer();
            bcg729Encoder(ctx_, speechIn + i * L_FRAME, bitStream, &bitStreamLength);
            // deliverFrame(out_frame);
            bitStream += bitStreamLength;
            total += bitStreamLength;
        }
        if (total > 0) {
            deliverFrame(out_frame);
        }
        else
            printf("g729 encode failed\n");
    }
}
} // namespace panocom
