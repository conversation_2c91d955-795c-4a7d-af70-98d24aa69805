#ifndef P_VideoFrameProcesser_h
#define P_VideoFrameProcesser_h

#include "../FramePipeline.h"

namespace panocom
{
    class VideoFrameProcesser : public FramePipeline
    {
    public:
        static void RegistProcessers();
        static std::vector<std::string>& GetSupportProcessers();
        static bool IsSupportProcesser(const std::string &processerName);
        static std::shared_ptr<VideoFrameProcesser> CreateVideoProcesser(const std::string &processerName, const std::string &jsonParams = "");

        virtual ~VideoFrameProcesser() = default;
    protected:
        static bool isRegistered;
        static std::vector<std::string> processers_;
    };
}

#endif