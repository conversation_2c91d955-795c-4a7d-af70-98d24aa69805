// created by gyj 2024-3-28
#ifndef P_OpusAudioFrameDecoder_h
#define P_OpusAudioFrameDecoder_h

#include "AudioFrameDecoder.h"
#include "Frame.h"
#include <opus/opus.h>
#include <hv/EventLoopThread.h>

namespace panocom
{
    class OpusAudioFrameDecoder : public AudioFrameDecoder
    {
    public:
        OpusAudioFrameDecoder(const std::string& jsonParams);
        ~OpusAudioFrameDecoder() override;

        void onFrame(const std::shared_ptr<Frame> &f) override; 
        void start() override;
        void stop() override;
    private:
        std::shared_ptr<OpusDecoder> decoder_;
        std::vector<uint8_t> buffer_;
        hv::EventLoopThread thread_;
        FrameFormat fmt_;
        int samplerate_;
        int chn_;
        FILE* pf_ = nullptr;
    };
}

#endif