#ifndef P_Frame_h
#define P_Frame_h

#include <memory>
#include <string>
#include <unordered_set>

#if defined(WIN32) || defined(WIN64)
#define strcasecmp stricmp
#endif

namespace panocom
{
#define H264_NAL_P		    1 // 
#define H264_NAL_IDR		5 // Coded slice of an IDR picture
#define H264_NAL_SPS		7 // Sequence parameter set
#define H264_NAL_PPS		8 // Picture parameter set
#define H264_NAL_AUD		9 // Access unit delimiter

#define H265_NAL_BLA_W_LP	16
#define H265_NAL_IDR        19
#define H265_NAL_IDR_W_RADL H265_NAL_IDR
#define H265_NAL_IDR_N_LP   20        
#define H265_NAL_RSV_IRAP	23
#define H265_NAL_VPS		32
#define H265_NAL_SPS		33
#define H265_NAL_PPS		34
#define H265_NAL_AUD		35 // Access unit delimiter
#define H265_NAL_SEI        39
#define H265_NAL_SEI_SUFFIX	40
#define H265_NAL_TSA_N      2

    enum FrameFormat
    {
        FRAME_FORMAT_UNKNOWN = 0,
        FRAME_FORMAT_COMMON_VIDEO = 1,
        FRAME_FORMAT_COMMON_AUDIO = 2,

        FRAME_FORMAT_RTP = 30,
        FRAME_FORMAT_RTCP = 31,
        FRAME_FORMAT_RED = 32,
        FRAME_FORMAT_ULPFEC = 33,

        FRAME_FORMAT_FFAVFRAME = 40,
        FRAME_FORMAT_FFAVPACKET = 41,

        FRAME_FORMAT_CU_FRAME = 50,
        FRAME_FORMAT_CU_DECODED_NV12 = 51,
        FRAME_FORMAT_CU_ENCODE_NV12 = 52,
        FRAME_FORMAT_CU_WRAP_NV12 = 53,

		FRAME_FORMAT_RK = 60,

        FRAME_FORMAT_I420 = 100,
        FRAME_FORMAT_NV12,
        FRAME_FORMAT_NV16,

        FRAME_FORMAT_VP8 = 200,
        FRAME_FORMAT_VP9,
        FRAME_FORMAT_H264,
        FRAME_FORMAT_H265,
        FRAME_FORMAT_AV1,

        FRAME_FORMAT_MSDK = 300,

        FRAME_FORMAT_PCM_8000_1 = 800,
        FRAME_FORMAT_PCM_16000_1 = 801,
        FRAME_FORMAT_PCM_16000_2 = 802,
        FRAME_FORMAT_PCM_48000_1 = 803,
        FRAME_FORMAT_PCM_48000_2 = 804,
        FRAME_FORMAT_PCM_96000_1 = 805,
        FRAME_FORMAT_PCM_96000_2 = 806,
        FRAME_FORMAT_PCM_16000_1_LP = 807, //小包
        FRAME_FORMAT_PCM_32000_1 = 808,
        FRAME_FORMAT_PCM_32000_2 = 809,

        FRAME_FORMAT_FAR_PCM_8000_1 = 850,
        FRAME_FORMAT_FAR_PCM_16000_1 = 851,
        FRAME_FORMAT_FAR_PCM_16000_2 = 852,
        FRAME_FORMAT_FAR_PCM_48000_1 = 853,
        FRAME_FORMAT_FAR_PCM_48000_2 = 854,
        FRAME_FORMAT_FAR_PCM_32000_1 = 855,
        FRAME_FORMAT_FAR_PCM_32000_2 = 856,

        FRAME_FORMAT_PCMU = 900,
        FRAME_FORMAT_PCMA,
        FRAME_FORMAT_OPUS_16000_1,
        FRAME_FORMAT_OPUS_48000_2,
        FRAME_FORMAT_G722_16000_1,
        FRAME_FORMAT_G7221_16000_32000_1,
        FRAME_FORMAT_G7221_32000_48000_1,
        FRAME_FORMAT_G7221_16000_24000_1,
        FRAME_FORMAT_G729,

        FRAME_FORMAT_AAC,         // ignore sample rate and channels for decoder, default is 48000_2
        FRAME_FORMAT_AAC_48000_1,
        FRAME_FORMAT_AAC_48000_2, // specify sample rate and channels for encoder

        FRAME_FORMAT_AC3,
        FRAME_FORMAT_NELLYMOSER,

        FRAME_FORMAT_DATA, // Generic data frame. We don't know its detailed structure.

        FRAME_FORMAT_AUDIO_FEEDBACK,
        FRAME_FORMAT_VIDEO_FEEDBACK,
        FRAME_FORMAT_DATA_FEEDBACK,
    };

    const char prefix[] = { 0, 0, 0, 1 };
    const char prefix2[] = { 0, 0, 1 };

    class Frame
    {
    public:
        static std::shared_ptr<Frame> CreateFrame(FrameFormat format, const std::string& jsonParams = "");
        static Frame* CreateFramePtr(FrameFormat format, const std::string& jsonParams = "");
        static void DestroyFramePtr(Frame* f);
        static bool isAudioFrame(const std::shared_ptr<Frame> &frame);
        static bool isVideoFrame(const std::shared_ptr<Frame> &frame);
        static bool isDataFrame(const std::shared_ptr<Frame> &frame);
        static bool isKeyFrame(const std::shared_ptr<Frame> &frame);
        static bool isIDRFrame(const std::shared_ptr<Frame> &frame);

        static bool isVideoFormat(const FrameFormat &format);
        static bool isAudioFormat(const FrameFormat &format);
        static std::string formatName(const FrameFormat &format);

        static bool getSamplerate(const FrameFormat &format, int& samplerate, int& chn);
        bool getSamplerate(int& samplerate, int& chn);

        static FrameFormat getPCMFormat(int chn, int samplerate);
        static FrameFormat getOpusFormat(int chn, int samplerate);
        static FrameFormat getG7221Format(int chn, int samplerate, int bitrate);
        static std::string getCodecName(const FrameFormat &format);
        static bool getCodecConfig(const FrameFormat &format, std::string &name, int &samplerate, int &chn, int &bitrate);
        static bool isPCM(const FrameFormat &format);
        bool isPCM();

        static bool isFarEndFrame(const FrameFormat &format);
        bool isFarEndFrame();

        static FrameFormat getFormatFarEnd(const FrameFormat &format);
        FrameFormat getFormatFarEnd();

        virtual std::shared_ptr<Frame> copy(const std::shared_ptr<Frame>& f);
        virtual bool createFrameBuffer(int bufferSize);
        virtual void writeBytes(uint8_t* frameBuffer, int bufferSize);
        virtual bool createFrameBuffer(uint8_t* frameBuffer, int bufferSize);
        virtual void destroyFrameBuffer();
        virtual uint8_t* getFrameBuffer();
        virtual int getFrameBufferSize();
        virtual void setFrameSize(int size);
        virtual int getFrameSize();
        virtual FrameFormat getFrameFormat() { return format_; }
        virtual void setFrameFormat(FrameFormat fmt) { format_ = fmt; }

        virtual uint8_t* data(int i) { return data_[i]; };
        virtual int linesize(int i) { return linesize_[i]; }

        // video only start
        virtual bool createFrameBuffer(int width, int height, int hStride = 0, int vStride = 0);
        virtual bool createFrameBuffer(uint8_t* frameBuffer, int width, int height, int hStride = 0, int vStride = 0);
        virtual void setWidth(int width) { width_ = width; }
        virtual void setHeight(int height) { height_ = height; }
        virtual void setHStride(int hStride) { hStride_ = hStride; }
        virtual void setVStride(int vStride) { vStride_ = vStride; }
        int width() { return width_; }
        int height() { return height_; }
        int hStride() { return hStride_; }
        int vStride() { return vStride_; }
        // video only end

        virtual void setGroupId(int groupId) { groupId_ = groupId; }
        virtual int getGroupId() { return groupId_; }
		virtual std::string PlatformType() const { return "unknown"; }
        virtual void updateParams(const std::string& jsonParams) {}
        virtual void setSource(void *source) { source_ = source; }
        virtual void *getSource() { return source_; }
        virtual void addToSourceList(void *source) { sources_.emplace(source); }
        virtual bool isInSourceList(void *source) { return sources_.count(source) > 0; }
        virtual void setGain(const int8_t gain) { gain_ = gain; }
        virtual int8_t getGain() { return gain_; }

        virtual int getPayloadType() { return pt_; }
        virtual void setPayloadType(int payloadType) { pt_ = payloadType; }
    protected:
        virtual void assignFrameArgs(int width, int height, int hStride, int vStride);
    
    public:
        Frame(FrameFormat format);
        virtual ~Frame();
    protected:
        FrameFormat format_;
        int pt_;
        uint8_t* frameBuffer_;
        uint8_t* data_[3];
        int linesize_[3];
        int frameBufferSize_;
        int frameSize_;
        bool innerBuffer_;
        int groupId_;   //用于渲染、合屏位置选择

        int width_;
        int height_;
        int hStride_;
        int vStride_;

        void *source_;
        std::unordered_set<void*> sources_;
        int8_t gain_ = 0;
    };

    class H264Frame : public Frame
    {
    public:
        H264Frame(const std::string& jsonParams) : Frame(FRAME_FORMAT_H264) {}
        ~H264Frame() override = default;
    };

    class H265Frame : public Frame
    {
    public:
        H265Frame(const std::string& jsonParams) : Frame(FRAME_FORMAT_H265) {}
        ~H265Frame() override = default;
    };

    class I420Frame : public Frame
    {
    public:
        I420Frame(const std::string& jsonParams)  : Frame(FRAME_FORMAT_I420) {}
        ~I420Frame() override = default;

        bool createFrameBuffer(int width, int height, int hStride, int vStride) override;
        bool createFrameBuffer(uint8_t* frameBuffer, int width, int height, int hStride, int vStride) override;
    };

    class NV12Frame : public Frame
    {
    public:
        NV12Frame(const std::string& jsonParams)  : Frame(FRAME_FORMAT_NV12) {}
        ~NV12Frame() override = default;

        bool createFrameBuffer(int width, int height, int hStride, int vStride) override;
        bool createFrameBuffer(uint8_t* frameBuffer, int width, int height, int hStride, int vStride) override;
    };
#ifdef USE_FFMPEG
    class FFAVFrame : public Frame
    {
    public:
        FFAVFrame(const std::string& jsonParams);
        FFAVFrame(void* avFrame);
        ~FFAVFrame();

        void* getAVFrame() { return avFrame_; }
        void setAVFrame(void* avFrame) { avFrame_ = avFrame; }

        void updateParams(const std::string& jsonParams) override;
    private:
        void* avFrame_;
    };

    class FFAVPacket : public Frame
    {
    public:
        FFAVPacket(const std::string& jsonParams);
        FFAVPacket(void* avPacket);
        ~FFAVPacket();

        void* getAVPacket() { return avPacket_; }
        void setAVPacket(void* avPacket) { avPacket_ = avPacket; }

        void updateParams(const std::string& jsonParams) override;
    private:
        void* avPacket_;
    };
#endif
}
#endif