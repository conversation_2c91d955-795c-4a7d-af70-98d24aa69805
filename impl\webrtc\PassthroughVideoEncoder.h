#ifndef IMPL_WEBRTC_PASSTHROUGHVIDEOENCODER_H_
#define IMPL_WEBRTC_PASSTHROUGHVIDEOENCODER_H_
#include <queue>
#include <rtc_base/logging.h>
#include <api/video_codecs/video_encoder.h>
#include <modules/video_coding/include/video_error_codes.h>
#include <modules/video_coding/include/video_codec_interface.h>
#include <ljcore/jlog.h>
namespace panocom
{
class PassthroughVideoEncoder : public webrtc::VideoEncoder
{
public:
    PassthroughVideoEncoder() = default;
    int InitEncode(const webrtc::VideoCodec *codec_settings, const webrtc::VideoEncoder::Settings &settings) override { 
        RTC_LOG(LS_INFO) << "PassthroughVideoEncoder::InitEncode"; 
        return WEBRTC_VIDEO_CODEC_OK; 
    }
    int32_t Release() override { 
        RTC_LOG(LS_INFO) << "PassthroughVideoEncoder::Release"; 
        return WEBRTC_VIDEO_CODEC_OK; 
    }
    int32_t RegisterEncodeCompleteCallback(webrtc::EncodedImageCallback *callback) override {
        RTC_LOG(LS_INFO) << "PassthroughVideoEncoder::RegisterEncodeCompleteCallback"; 
        callback_ = callback;
        return WEBRTC_VIDEO_CODEC_OK;
    }
    int32_t Encode(const webrtc::VideoFrame &frame, const std::vector<webrtc::VideoFrameType> *frame_types) override {

        RTC_LOG(LS_INFO) << "PassthroughVideoEncoder: Encode.";
        // std::unique_lock<std::mutex> lock(mutex_);
        // if (encoded_frames_.empty()) {
        //     return WEBRTC_VIDEO_CODEC_OK;
        // }
        // auto encoded = encoded_frames_.front();
        // encoded_frames_.pop();
        // lock.unlock();
        // RTC_LOG(LS_INFO) << "PassthroughVideoEncoder: Encode complete.";
        // if (callback_) {
        //     webrtc::CodecSpecificInfo info;
        //     info.codecType = webrtc::kVideoCodecGeneric;
        //     callback_->OnEncodedImage(encoded, &info);
        // }
        return WEBRTC_VIDEO_CODEC_OK;
    }
    void SetRates(const RateControlParameters& parameters) override {}

    void SetEncodedImageCallback(webrtc::EncodedImageCallback *callback) { callback_ = callback; }

    void PushEncodedFrameFrame(const webrtc::EncodedImage &image, const webrtc::CodecSpecificInfo *info) {
        if (callback_) callback_->OnEncodedImage(image, info);
        // std::lock_guard<std::mutex> lock(mutex_);
        // encoded_frames_.push(image);
    }
private:
    webrtc::EncodedImageCallback* callback_ = nullptr;
    std::queue<webrtc::EncodedImage> encoded_frames_;
    std::mutex mutex_;
};

} // namespace panocom


#endif