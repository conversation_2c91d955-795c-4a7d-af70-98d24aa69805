
#include <nvcuvid.h>
#include <json.hpp>
#include <ljcore/jlog.h>
#include "CuFrame.h"
#include "CuVideoDecoder.h"
#include "CuCommon.h"

using namespace panocom;

static float GetChromaHeightFactor(cudaVideoSurfaceFormat eSurfaceFormat)
{
    float factor = 0.5;
    switch (eSurfaceFormat)
    {
    case cudaVideoSurfaceFormat_NV12:
    case cudaVideoSurfaceFormat_P016:
        factor = 0.5;
        break;
    case cudaVideoSurfaceFormat_YUV444:
    case cudaVideoSurfaceFormat_YUV444_16Bit:
        factor = 1.0;
        break;
    }

    return factor;
}

static int GetChromaPlaneCount(cudaVideoSurfaceFormat eSurfaceFormat)
{
    int numPlane = 1;
    switch (eSurfaceFormat)
    {
    case cudaVideoSurfaceFormat_NV12:
    case cudaVideoSurfaceFormat_P016:
        numPlane = 1;
        break;
    case cudaVideoSurfaceFormat_YUV444:
    case cudaVideoSurfaceFormat_YUV444_16Bit:
        numPlane = 2;
        break;
    }

    return numPlane;
}

/* Return value from HandleVideoSequence() are interpreted as   :
*  0: fail, 1: succeeded, > 1: override dpb size of parser (set by CUVIDPARSERPARAMS::ulMaxNumDecodeSurfaces while creating parser)
*/
int CuVideoDecoder::HandleVideoSequence(CUVIDEOFORMAT *pVideoFormat)
{
    int nDecodeSurface = pVideoFormat->min_num_decode_surfaces;

    CUVIDDECODECAPS decodecaps;
    memset(&decodecaps, 0, sizeof(decodecaps));

    decodecaps.eCodecType = pVideoFormat->codec;
    decodecaps.eChromaFormat = pVideoFormat->chroma_format;
    decodecaps.nBitDepthMinus8 = pVideoFormat->bit_depth_luma_minus8;
    cuCtxPushCurrent(*CuCommon::instance().getCudaContext(cudaId_, gpuIndex_));
    CUresult ret = cuvidGetDecoderCaps(&decodecaps);
    if (ret != CUDA_SUCCESS)
    {
        jerror("cuvidGetDecoderCaps %d", ret);
    }
    cuCtxPopCurrent(NULL);

    // eCodec has been set in the constructor (for parser). Here it's set again for potential correction
    chromaFormat_ = pVideoFormat->chroma_format;
    bitDepthMinus8_ = pVideoFormat->bit_depth_luma_minus8;
    BPP_ = bitDepthMinus8_ > 0 ? 2 : 1;
    outputFormat_ = cudaVideoSurfaceFormat_NV12;
    videoFormat_ = *pVideoFormat;

    CUVIDDECODECREATEINFO videoDecodeCreateInfo = { 0 };
    videoDecodeCreateInfo.CodecType = pVideoFormat->codec;
    videoDecodeCreateInfo.ChromaFormat = pVideoFormat->chroma_format;
    videoDecodeCreateInfo.OutputFormat = outputFormat_;
    videoDecodeCreateInfo.bitDepthMinus8 = pVideoFormat->bit_depth_luma_minus8;
    if (pVideoFormat->progressive_sequence)
        videoDecodeCreateInfo.DeinterlaceMode = cudaVideoDeinterlaceMode_Weave;
    else
        videoDecodeCreateInfo.DeinterlaceMode = cudaVideoDeinterlaceMode_Adaptive;
    videoDecodeCreateInfo.ulNumOutputSurfaces = 5;
    // With PreferCUVID, JPEG is still decoded by CUDA while video is decoded by NVDEC hardware
    videoDecodeCreateInfo.ulCreationFlags = cudaVideoCreate_PreferCUVID;
    videoDecodeCreateInfo.ulNumDecodeSurfaces = nDecodeSurface;
    videoDecodeCreateInfo.vidLock = ctxLock_;
    videoDecodeCreateInfo.ulWidth = pVideoFormat->coded_width;
    videoDecodeCreateInfo.ulHeight = pVideoFormat->coded_height;

    jinfo("HandleVideoSequence %d x %d", pVideoFormat->coded_width, pVideoFormat->coded_height);

    if (maxWidth_ < (int)pVideoFormat->coded_width)
        maxWidth_ = pVideoFormat->coded_width;
    if (maxHeight_ < (int)pVideoFormat->coded_height)
        maxHeight_ = pVideoFormat->coded_height;
    videoDecodeCreateInfo.ulMaxWidth = maxWidth_;
    videoDecodeCreateInfo.ulMaxHeight = maxHeight_;

    width_ = pVideoFormat->display_area.right - pVideoFormat->display_area.left;
    hStride_ = pVideoFormat->coded_height;
    lumaHeight_ = pVideoFormat->display_area.bottom - pVideoFormat->display_area.top;
    videoDecodeCreateInfo.ulTargetWidth = pVideoFormat->coded_width;
    videoDecodeCreateInfo.ulTargetHeight = pVideoFormat->coded_height;

    chromaHeight_ = (int)(ceil(lumaHeight_ * GetChromaHeightFactor(outputFormat_)));
    numChromaPlanes_ = GetChromaPlaneCount(outputFormat_);
    displayRect_.b = videoDecodeCreateInfo.display_area.bottom;
    displayRect_.t = videoDecodeCreateInfo.display_area.top;
    displayRect_.l = videoDecodeCreateInfo.display_area.left;
    displayRect_.r = videoDecodeCreateInfo.display_area.right;

    int cudaId = cudaId_;
    int gpuIndex = gpuIndex_;
    decoder_ = std::shared_ptr<CUvideodecoder>(new CUvideodecoder, [cudaId, gpuIndex](CUvideodecoder* dec) {
        if (*dec)
        {
            cuCtxPushCurrent(*CuCommon::instance().getCudaContext(cudaId, gpuIndex));
            cuvidDestroyDecoder(*dec);
            cuCtxPopCurrent(NULL);
        }
        delete dec;
    });

    cuCtxPushCurrent(*CuCommon::instance().getCudaContext(cudaId_, gpuIndex_));
    {
        ret = cuvidCreateDecoder(decoder_.get(), &videoDecodeCreateInfo);
    }
    cuCtxPopCurrent(NULL);

    if (ret != CUDA_SUCCESS)
    {
        jerror("cuvidCreateDecoder %d", ret);
        decoder_ = NULL;
    }
    return nDecodeSurface;
}

/* Return value from HandlePictureDecode() are interpreted as:
*  0: fail, >=1: succeeded
*/
int CuVideoDecoder::HandlePictureDecode(CUVIDPICPARAMS *pPicParams) {
    if (!decoder_)
    {
        return false;
    }
    cuCtxPushCurrent(*CuCommon::instance().getCudaContext(cudaId_, gpuIndex_));
    // TODO： 异步接口
    CUresult ret = cuvidDecodePicture(*decoder_, pPicParams);
    if (ret != CUDA_SUCCESS)
    {
        jerror("cuvidDecodePicture %d", ret);
    }
    else if ((!pPicParams->field_pic_flag) || (pPicParams->second_field))
    {
        CUVIDPARSERDISPINFO dispInfo;
        memset(&dispInfo, 0, sizeof(dispInfo));
        dispInfo.picture_index = pPicParams->CurrPicIdx;
        dispInfo.progressive_frame = !pPicParams->field_pic_flag;
        dispInfo.top_field_first = pPicParams->bottom_field_flag ^ 1;
        HandlePictureDisplay(&dispInfo);
    }
    cuCtxPopCurrent(NULL);
    return 1;
}

/* Return value from HandlePictureDisplay() are interpreted as:
*  0: fail, >=1: succeeded
*/
int CuVideoDecoder::HandlePictureDisplay(CUVIDPARSERDISPINFO *pDispInfo) {
    CUVIDPROCPARAMS videoProcessingParameters = {};
    videoProcessingParameters.progressive_frame = pDispInfo->progressive_frame;
    videoProcessingParameters.second_field = pDispInfo->repeat_first_field + 1;
    videoProcessingParameters.top_field_first = pDispInfo->top_field_first;
    videoProcessingParameters.unpaired_field = pDispInfo->repeat_first_field < 0;
    videoProcessingParameters.output_stream = 0;

    std::shared_ptr<Frame> f;
    f.reset(new CuDecodedNV12Frame(cudaId_, gpuIndex_, videoProcessingParameters, decoder_, pDispInfo->picture_index));
    bool flag = f->createFrameBuffer(width_, lumaHeight_, vStride_, hStride_);
    f->setGroupId(getGroupId());
    if (flag) {
        deliverFrame(f);
        frames_decoded_++;
    } 
    auto now_tick = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now_tick - last_tick_).count();
    if (duration > 10000) {
        jinfo("CuVideoDecoder[%p] duration: %dus, frames_recieved: %d, frames_to_decode: %d, frames_decoded: %d", this, duration, frames_recieved_, frames_to_decode_, frames_decoded_);
        frames_recieved_ = 0;
        frames_to_decode_ = 0;
        frames_decoded_ = 0;
        last_tick_ = now_tick;
    }
    printOutputStatus("CuVideoDecoder" + std::to_string(fmt_));
    return 1;
}

CuVideoDecoder::CuVideoDecoder(const std::string& jsonParams)
{
    FN_BEGIN;
    name_ = "CuVideoDecoder";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    std::string codecName = "h264";
    if (j.contains("codec"))
    {
        codecName = j["codec"];
    }
    cudaId_ = 0;
    if (j.contains("cudaId"))
    {
        cudaId_ = j["cudaId"];
    }
    jinfo("CuVideoDecoder %s", codecName.c_str());

    if (codecName == "h264")
    {
        jinfo("NvVideoDecoder h264");
        fmt_ = cudaVideoCodec_H264;
    }
    else if (codecName == "h265")
    {
        jinfo("NvVideoDecoder h265");
        fmt_ = cudaVideoCodec_HEVC;
    }
    else if (codecName == "jpeg" || codecName == "mjpeg")
    {
        jinfo("NvVideoDecoder jpeg");
        fmt_ = cudaVideoCodec_JPEG;
    }
    else
    {
        jinfo("NvVideoDecoder h264");
        fmt_ = cudaVideoCodec_H264;
    }
    gpuIndex_ = 0;
    if (j.contains("gpuIndex"))
    {
        gpuIndex_ = j["gpuIndex"];
    }
    jinfo("CuVideoDecoder use gpu %d", gpuIndex_);
    start();
    FN_END;
}

CuVideoDecoder::~CuVideoDecoder() 
{
    FN_BEGIN;
    stop();
    FN_END;
}

void CuVideoDecoder::start()
{
    if (thread_.isRunning()) return;
    FN_BEGIN;
    CUVIDPARSERPARAMS videoParserParameters = {};
    videoParserParameters.CodecType = (cudaVideoCodec)fmt_;
    videoParserParameters.ulMaxNumDecodeSurfaces = 1;
    videoParserParameters.ulClockRate = 1000;
    videoParserParameters.ulMaxDisplayDelay = 1;
    videoParserParameters.pUserData = this;
    videoParserParameters.pfnSequenceCallback = HandleVideoSequenceProc;
    videoParserParameters.pfnDecodePicture = HandlePictureDecodeProc;
    videoParserParameters.pfnDisplayPicture = NULL;
    videoParserParameters.pfnGetOperatingPoint = NULL;

    cuvidCtxLockCreate(&ctxLock_, *CuCommon::instance().getCudaContext(cudaId_, gpuIndex_));
    CUVIDPARSERPARAMS _videoParserParameters = videoParserParameters;
    CUresult ret = cuvidCreateVideoParser(&parser_, &_videoParserParameters);
    if (ret == CUDA_SUCCESS)
    {
        thread_.start();
    }
    else
    {
        jerror("cuvidCreateVideoParser ret = %d", ret);
    }
    last_tick_ = std::chrono::steady_clock::now();
    FN_END;
}

void CuVideoDecoder::stop() 
{
    FN_BEGIN;
    if (!thread_.isRunning()) return;
    std::unique_lock<std::mutex> locker(frame_mutex_);
    jinfo("CuVideoDecoder[%p] stop thread_", this);
    thread_.stop(true);
    jinfo("CuVideoDecoder[%p] stopPipeline", this);
    stopPipeline();
    // 解码帧可能还在其他pipeline，必须回收才能释放
    jinfo("CuVideoDecoder[%p] destroy Parser[%p]", this, decoder_.get());
    cuCtxPushCurrent(*CuCommon::instance().getCudaContext(cudaId_, gpuIndex_));
    if (parser_) {
        cuvidDestroyVideoParser(parser_);
    }
    cuCtxPopCurrent(NULL);
    jinfo("CuVideoDecoder[%p] release decoder[%p] begin", this, decoder_.get());
    if (decoder_) {
        decoder_ = NULL;
    }
    jinfo("CuVideoDecoder[%p] release decoder end", this);
    cuvidCtxLockDestroy(ctxLock_);
    FN_END;
}

void CuVideoDecoder::onFrame(const std::shared_ptr<Frame>& frame)
{
    printInputStatus("CuVideoDecoder" + std::to_string(fmt_));
    std::unique_lock<std::mutex> locker(frame_mutex_);
    if (!thread_.isRunning()) return;
    frames_recieved_++;
    thread_.loop()->runInLoop([this, frame]{
        // jinfo("CuVideoDecoder::onFrame %p %d", frame->getFrameBuffer(), frame->getFrameSize());
        // if (fmt_ == cudaVideoCodec_H264)
        // {
        //     if (pf_ == nullptr)
        //     {
        //         pf_ = fopen("decode.h264", "w+b");
        //     }
        //     if (pf_)
        //     {
        //         fwrite(frame->getFrameBuffer(), 1, frame->getFrameSize(), pf_);
        //     }
        // }
        CUVIDSOURCEDATAPACKET packet = { 0 };
        packet.payload = frame->getFrameBuffer();
        packet.payload_size = frame->getFrameSize();
        packet.flags = 0;
        CUresult ret = cuvidParseVideoData(parser_, &packet);
        frames_to_decode_++;
        if (ret != CUDA_SUCCESS)
        {
            jerror("cuvidParseVideoData %d", ret);
        }
    });
}
