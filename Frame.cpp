#include "Frame.h"
#include <json.hpp>
#include <ljcore/jlog.h>

#ifdef USE_FFMPEG
extern "C" {
#include <libavcodec/avcodec.h>
}
#endif

using namespace panocom;

Frame::Frame(FrameFormat format) : format_(format), pt_(-1), frameBuffer_(nullptr), frameBufferSize_(0), width_(0), height_(0), hStride_(0), vStride_(0), source_(nullptr)
{
    for (size_t i = 0; i < 3; i++)
    {
        data_[i] = nullptr;
        linesize_[i] = 0;
    }
}

Frame::~Frame()
{
    destroyFrameBuffer();
    
}

std::shared_ptr<Frame> Frame::copy(const std::shared_ptr<Frame>& f)
{
    std::shared_ptr<Frame> cp = Frame::CreateFrame(f->getFrameFormat());
    if (f->width() && f->height())
    {
        cp->createFrameBuffer(f->width(), f->height(), f->hStride(), f->vStride());
        memcpy(cp->getFrameBuffer(), f->getFrameBuffer(), f->getFrameBufferSize());
    }
    else if (f->getFrameBufferSize())
    {
        cp->createFrameBuffer(f->getFrameBufferSize());
        memcpy(cp->getFrameBuffer(), f->getFrameBuffer(), f->getFrameBufferSize());
    }
    return cp;
}

void Frame::setFrameSize(int size)
{
    frameSize_ = size;
}

int Frame::getFrameSize()
{
    return frameSize_;
}

bool Frame::isFarEndFrame(const FrameFormat &format)
{
    bool ret = false;
    switch (format)
    {
    case FRAME_FORMAT_FAR_PCM_8000_1:
    case FRAME_FORMAT_FAR_PCM_16000_1:
    case FRAME_FORMAT_FAR_PCM_16000_2:
    case FRAME_FORMAT_FAR_PCM_48000_1:
    case FRAME_FORMAT_FAR_PCM_48000_2:
        ret = true;
        break;
    default:
        break;
    }
    return ret;
}

bool Frame::isFarEndFrame()
{
    return isFarEndFrame(format_);
}

FrameFormat Frame::getFormatFarEnd(const FrameFormat &format)
{
    switch (format)
    {
    case FRAME_FORMAT_PCM_8000_1:
        return FRAME_FORMAT_FAR_PCM_8000_1;
    case FRAME_FORMAT_PCM_16000_1:
        return FRAME_FORMAT_FAR_PCM_16000_1;
    case FRAME_FORMAT_PCM_16000_2:
        return FRAME_FORMAT_FAR_PCM_16000_2;
    case FRAME_FORMAT_PCM_48000_1:
        return FRAME_FORMAT_FAR_PCM_48000_1;
    case FRAME_FORMAT_PCM_48000_2:
        return FRAME_FORMAT_FAR_PCM_48000_2;
    case FRAME_FORMAT_PCM_32000_1:
        return FRAME_FORMAT_FAR_PCM_32000_1;
    case FRAME_FORMAT_PCM_32000_2:
        return FRAME_FORMAT_FAR_PCM_32000_2;
    default:
        return FRAME_FORMAT_UNKNOWN;
    }
}

FrameFormat Frame::getFormatFarEnd()
{
    return getFormatFarEnd(format_);
}

void Frame::destroyFrameBuffer()
{
    if (innerBuffer_ && frameBuffer_)
    {
        innerBuffer_ = false;
        delete[] frameBuffer_;
        frameBuffer_ = nullptr;
        frameBufferSize_ = 0;
        frameSize_ = 0;
    }
}

bool Frame::createFrameBuffer(int bufferSize)
{
    destroyFrameBuffer();
    innerBuffer_ = true;
    frameBuffer_ = new uint8_t[bufferSize];
    frameBufferSize_ = bufferSize;
    frameSize_ = bufferSize;
    data_[0] = frameBuffer_;
    linesize_[0] = frameBufferSize_;
    return true;
}

void Frame::writeBytes(uint8_t* frameBuffer, int bufferSize)
{
    destroyFrameBuffer();
    innerBuffer_ = true;
    frameBuffer_ = new uint8_t[bufferSize];
    frameBufferSize_ = bufferSize;
    frameSize_ = bufferSize;
    data_[0] = frameBuffer_;
    linesize_[0] = frameBufferSize_;
    memcpy(frameBuffer_, frameBuffer, bufferSize);
}

bool Frame::createFrameBuffer(uint8_t *frameBuffer, int bufferSize)
{
    innerBuffer_ = false;
    frameBuffer_ = frameBuffer;
    frameBufferSize_ = bufferSize;
    frameSize_ = bufferSize;
    data_[0] = frameBuffer_;
    linesize_[0] = frameBufferSize_;
    return true;
}

bool Frame::createFrameBuffer(int width, int height, int hStride, int vStride)
{
    jerror("not implement");
    abort();
    return true;
}

bool Frame::createFrameBuffer(uint8_t* frameBuffer, int width, int height, int hStride, int vStride)
{
    jerror("not implement");
    abort();
    return true;
}

uint8_t *Frame::getFrameBuffer()
{
    return frameBuffer_;
}

int Frame::getFrameBufferSize()
{
    return frameBufferSize_;
}

void Frame::assignFrameArgs(int width, int height, int hStride, int vStride)
{
    if (hStride == 0) hStride = width;
    if (vStride == 0) vStride = height;
    width_ = (width + 1) & ~1;
    height_ = (height + 1) & ~1;
    hStride_ = (hStride + 1) & ~1;
    vStride_ = (vStride + 1) & ~1;
    linesize_[0] = hStride_;
    linesize_[1] = vStride_;
}

std::shared_ptr<Frame> Frame::CreateFrame(FrameFormat format, const std::string &jsonParams)
{
    switch (format)
    {
#ifdef ENABLE_VIDEO
#ifdef USE_FFMPEG
    case FRAME_FORMAT_FFAVFRAME:
        return std::shared_ptr<Frame>(new FFAVFrame(jsonParams));
        break;
#endif
    case FRAME_FORMAT_I420:
        return std::shared_ptr<Frame>(new I420Frame(jsonParams));
        break;
    case FRAME_FORMAT_NV12:
        return std::shared_ptr<Frame>(new NV12Frame(jsonParams));
        break;
    case FRAME_FORMAT_H264:
        return std::shared_ptr<Frame>(new H264Frame(jsonParams));
        break;
    case FRAME_FORMAT_H265:
        return std::shared_ptr<Frame>(new H265Frame(jsonParams));
        break;
#endif
    default:
        return std::shared_ptr<Frame>(new Frame(format));
        break;
    }
    return nullptr;
}

Frame* Frame::CreateFramePtr(FrameFormat format, const std::string &jsonParams)
{
    switch (format)
    {
#ifdef ENABLE_VIDEO
#ifdef USE_FFMPEG
    case FRAME_FORMAT_FFAVFRAME:
        return new FFAVFrame(jsonParams);
        break;
#endif
    case FRAME_FORMAT_I420:
        return new I420Frame(jsonParams);
        break;
    case FRAME_FORMAT_NV12:
        return new NV12Frame(jsonParams);
        break;
    case FRAME_FORMAT_H264:
        return new H264Frame(jsonParams);
        break;
    case FRAME_FORMAT_H265:
        return new H265Frame(jsonParams);
        break;
#endif
    default:
        return new Frame(format);
        break;
    }
    return nullptr;
}

void Frame::DestroyFramePtr(Frame* f)
{
    delete f;
}

bool Frame::isAudioFrame(const std::shared_ptr<Frame> &frame)
{
    return 
    frame->getFrameFormat() == FRAME_FORMAT_COMMON_AUDIO || 
    frame->getFrameFormat() == FRAME_FORMAT_PCM_8000_1 ||
    frame->getFrameFormat() == FRAME_FORMAT_PCM_16000_1 || 
    frame->getFrameFormat() == FRAME_FORMAT_PCM_16000_2 || 
    frame->getFrameFormat() == FRAME_FORMAT_PCM_48000_1 || 
    frame->getFrameFormat() == FRAME_FORMAT_PCM_48000_2 ||
    frame->getFrameFormat() == FRAME_FORMAT_PCM_32000_1 || 
    frame->getFrameFormat() == FRAME_FORMAT_PCM_32000_2 || 
    frame->getFrameFormat() == FRAME_FORMAT_PCM_96000_1 || 
    frame->getFrameFormat() == FRAME_FORMAT_PCM_96000_2 ||

    frame->getFrameFormat() == FRAME_FORMAT_FAR_PCM_8000_1 || 
    frame->getFrameFormat() == FRAME_FORMAT_FAR_PCM_16000_1 || 
    frame->getFrameFormat() == FRAME_FORMAT_FAR_PCM_16000_2 || 
    frame->getFrameFormat() == FRAME_FORMAT_FAR_PCM_48000_1 || 
    frame->getFrameFormat() == FRAME_FORMAT_FAR_PCM_48000_2 ||
    frame->getFrameFormat() == FRAME_FORMAT_FAR_PCM_32000_1 || 
    frame->getFrameFormat() == FRAME_FORMAT_FAR_PCM_32000_2 ||
    frame->getFrameFormat() == FRAME_FORMAT_PCMU || 
    frame->getFrameFormat() == FRAME_FORMAT_PCMA || 
    frame->getFrameFormat() == FRAME_FORMAT_OPUS_16000_1 || 
    frame->getFrameFormat() == FRAME_FORMAT_OPUS_48000_2 || 
    frame->getFrameFormat() == FRAME_FORMAT_G722_16000_1 || 
    frame->getFrameFormat() == FRAME_FORMAT_G7221_16000_32000_1 || 
    frame->getFrameFormat() == FRAME_FORMAT_G7221_32000_48000_1 || 
    frame->getFrameFormat() == FRAME_FORMAT_G7221_16000_24000_1 || 
	frame->getFrameFormat() == FRAME_FORMAT_G729 ||
    frame->getFrameFormat() == FRAME_FORMAT_AAC || 
    frame->getFrameFormat() == FRAME_FORMAT_AAC_48000_1 || 
    frame->getFrameFormat() == FRAME_FORMAT_AAC_48000_2 || 
    frame->getFrameFormat() == FRAME_FORMAT_AC3 || 
    frame->getFrameFormat() == FRAME_FORMAT_NELLYMOSER;
}

bool Frame::isVideoFrame(const std::shared_ptr<Frame> &frame)
{
    return 
	frame->getFrameFormat() == FRAME_FORMAT_RK || 
    frame->getFrameFormat() == FRAME_FORMAT_COMMON_VIDEO || 
    frame->getFrameFormat() == FRAME_FORMAT_CU_FRAME || 
    frame->getFrameFormat() == FRAME_FORMAT_CU_DECODED_NV12 || 
    frame->getFrameFormat() == FRAME_FORMAT_CU_ENCODE_NV12 || 
    frame->getFrameFormat() == FRAME_FORMAT_CU_WRAP_NV12 ||
    frame->getFrameFormat() == FRAME_FORMAT_I420 || 
    frame->getFrameFormat() == FRAME_FORMAT_MSDK || 
    frame->getFrameFormat() == FRAME_FORMAT_VP8 || 
    frame->getFrameFormat() == FRAME_FORMAT_VP9 || 
    frame->getFrameFormat() == FRAME_FORMAT_H264 || 
    frame->getFrameFormat() == FRAME_FORMAT_H265 || 
    frame->getFrameFormat() == FRAME_FORMAT_AV1 || 
    frame->getFrameFormat() == FRAME_FORMAT_FFAVFRAME || 
    frame->getFrameFormat() == FRAME_FORMAT_FFAVPACKET || 
    frame->getFrameFormat() == FRAME_FORMAT_NV12 || 
    frame->getFrameFormat() == FRAME_FORMAT_NV12;
}

bool Frame::isDataFrame(const std::shared_ptr<Frame> &frame)
{
    return frame->getFrameFormat() == FRAME_FORMAT_DATA || frame->getFrameFormat() == FRAME_FORMAT_RTP || frame->getFrameFormat() == FRAME_FORMAT_RTCP;
}

static char prefix3[3] = { 0, 0, 1 };
static char prefix4[4] = { 0, 0, 0, 1 };

bool Frame::isKeyFrame(const std::shared_ptr<Frame> &frame)
{
    if (frame->getFrameFormat() == FRAME_FORMAT_H264)
    {
        uint8_t type = 0;
        if (memcmp(frame->getFrameBuffer(), prefix3, 3) == 0)
        {
            type = frame->getFrameBuffer()[3] & 0x1F;
        }
        else if (memcmp(frame->getFrameBuffer(), prefix4, 4) == 0)
        {
            type = frame->getFrameBuffer()[4] & 0x1F;
        }
        switch (type)
        {
        case H264_NAL_IDR:
        case H264_NAL_SPS:
        case H264_NAL_PPS:
            return true;
        default:
            return false;
        }
    }
    else if (frame->getFrameFormat() == FRAME_FORMAT_H265)
    {
        uint8_t type = 0;
        if (memcmp(frame->getFrameBuffer(), prefix3, 3) == 0)
        {
            type = (frame->getFrameBuffer()[3]>> 1) & 0x3F;
        }
        else if (memcmp(frame->getFrameBuffer(), prefix4, 4) == 0)
        {
            type = (frame->getFrameBuffer()[4]>> 1) & 0x3F;
        }
        switch (type)
        {
        case H265_NAL_IDR_W_RADL:
        case H265_NAL_IDR_N_LP:
        case H265_NAL_RSV_IRAP:
        case H265_NAL_VPS:
        case H265_NAL_SPS:
        case H265_NAL_PPS:
            return true;
        default:
            return false;
        }
    }
#ifdef USE_FFMPEG
    else if(frame->getFrameFormat() == FRAME_FORMAT_FFAVPACKET){
        auto p = std::dynamic_pointer_cast<FFAVPacket>(frame);
        AVPacket*pkt = (AVPacket*)p->getAVPacket();
        if (pkt &&pkt->flags & AV_PKT_FLAG_KEY) {
            return true;
        }
    }
#endif
    return false;
}

bool Frame::isIDRFrame(const std::shared_ptr<Frame> &frame) {
    if (frame->getFrameFormat() == FRAME_FORMAT_H264)
    {
        uint8_t type = 0;
        if (memcmp(frame->getFrameBuffer(), prefix3, 3) == 0)
        {
            type = frame->getFrameBuffer()[3] & 0x1F;
        }
        else if (memcmp(frame->getFrameBuffer(), prefix4, 4) == 0)
        {
            type = frame->getFrameBuffer()[4] & 0x1F;
        }
        switch (type)
        {
        case H264_NAL_IDR:
            return true;
        default:
            return false;
        }
    }
    else if (frame->getFrameFormat() == FRAME_FORMAT_H265)
    {
        uint8_t type = 0;
        if (memcmp(frame->getFrameBuffer(), prefix3, 3) == 0)
        {
            type = (frame->getFrameBuffer()[3]>> 1) & 0x3F;
        }
        else if (memcmp(frame->getFrameBuffer(), prefix4, 4) == 0)
        {
            type = (frame->getFrameBuffer()[4]>> 1) & 0x3F;
        }
        switch (type)
        {
        case H265_NAL_IDR_W_RADL:
        case H265_NAL_IDR_N_LP:
            return true;
        default:
            return false;
        }
    }
    return false;
}

bool Frame::isVideoFormat(const FrameFormat &format)
{
    return 
	format == FRAME_FORMAT_RK || 
    format == FRAME_FORMAT_COMMON_VIDEO || 
    format == FRAME_FORMAT_CU_FRAME || 
    format == FRAME_FORMAT_CU_DECODED_NV12 || 
    format == FRAME_FORMAT_CU_ENCODE_NV12 || 
    format == FRAME_FORMAT_CU_WRAP_NV12 ||
    format == FRAME_FORMAT_I420 || 
    format == FRAME_FORMAT_MSDK || 
    format == FRAME_FORMAT_VP8 || 
    format == FRAME_FORMAT_VP9 || 
    format == FRAME_FORMAT_H264 || 
    format == FRAME_FORMAT_H265 || 
    format == FRAME_FORMAT_AV1 || 
    format == FRAME_FORMAT_FFAVFRAME || 
    format == FRAME_FORMAT_FFAVPACKET || 
    format == FRAME_FORMAT_NV12 || 
    format == FRAME_FORMAT_NV12;
}

bool Frame::isAudioFormat(const FrameFormat &format)
{
    return 
    format == FRAME_FORMAT_COMMON_AUDIO || 
    format == FRAME_FORMAT_PCM_8000_1 ||
    format == FRAME_FORMAT_PCM_16000_1 || 
    format == FRAME_FORMAT_PCM_16000_2 || 
    format == FRAME_FORMAT_PCM_48000_1 || 
    format == FRAME_FORMAT_PCM_48000_2 ||
    format == FRAME_FORMAT_PCM_32000_1 || 
    format == FRAME_FORMAT_PCM_32000_2 || 
    format == FRAME_FORMAT_PCM_96000_1 || 
    format == FRAME_FORMAT_PCM_96000_2 ||
    format == FRAME_FORMAT_FAR_PCM_8000_1 || 
    format == FRAME_FORMAT_FAR_PCM_16000_1 || 
    format == FRAME_FORMAT_FAR_PCM_16000_2 || 
    format == FRAME_FORMAT_FAR_PCM_48000_1 || 
    format == FRAME_FORMAT_FAR_PCM_48000_2 ||
    format == FRAME_FORMAT_FAR_PCM_32000_1 || 
    format == FRAME_FORMAT_FAR_PCM_32000_2 || 
    format == FRAME_FORMAT_PCMU || 
    format == FRAME_FORMAT_PCMA || 
    format == FRAME_FORMAT_OPUS_16000_1 || 
    format == FRAME_FORMAT_OPUS_48000_2 || 
    format == FRAME_FORMAT_G722_16000_1 || 
    format == FRAME_FORMAT_G7221_16000_32000_1 ||
    format == FRAME_FORMAT_G7221_32000_48000_1 ||
    format == FRAME_FORMAT_G7221_16000_24000_1 ||
	format == FRAME_FORMAT_G729 ||
    format == FRAME_FORMAT_AAC || 
    format == FRAME_FORMAT_AAC_48000_1 || 
    format == FRAME_FORMAT_AAC_48000_2 || 
    format == FRAME_FORMAT_AC3 || 
    format == FRAME_FORMAT_NELLYMOSER;
}

std::string Frame::formatName(const FrameFormat &format)
{
    switch (format)
    {
    case FRAME_FORMAT_PCM_8000_1:
    case FRAME_FORMAT_PCM_16000_1:
    case FRAME_FORMAT_PCM_16000_2:
    case FRAME_FORMAT_PCM_48000_1:
    case FRAME_FORMAT_PCM_48000_2:
    case FRAME_FORMAT_PCM_96000_1:
    case FRAME_FORMAT_PCM_96000_2:
    case FRAME_FORMAT_PCM_32000_1:
    case FRAME_FORMAT_PCM_32000_2:
        return "pcm";
    case FRAME_FORMAT_PCMU:
        return "pcmu";
    case FRAME_FORMAT_PCMA:
        return "pcma";
    case FRAME_FORMAT_OPUS_16000_1:
    case FRAME_FORMAT_OPUS_48000_2:
        return "opus";
    case FRAME_FORMAT_G722_16000_1:
        return "g722";
    case FRAME_FORMAT_G7221_16000_32000_1:
    case FRAME_FORMAT_G7221_16000_24000_1:
        return "g722.1";
    case FRAME_FORMAT_G7221_32000_48000_1:
        return "g722.1c";
    case FRAME_FORMAT_G729:
        return "g729";
    case FRAME_FORMAT_AAC:
    case FRAME_FORMAT_AAC_48000_2:
        return "aac";
    case FRAME_FORMAT_AAC_48000_1:
        return "MP4A-LATM";
    case FRAME_FORMAT_H264:
        return "h264";
    case FRAME_FORMAT_H265:
        return "h265";
    default:
        break;
    }
    return "";
}

bool Frame::getSamplerate(const FrameFormat &format, int &samplerate, int &chn)
{
    bool ret = true;
    switch (format)
    {
    case FRAME_FORMAT_PCM_8000_1:
    case FRAME_FORMAT_FAR_PCM_8000_1:
        samplerate = 8000;
        chn = 1;
        break;
    case FRAME_FORMAT_PCM_16000_1:
    case FRAME_FORMAT_FAR_PCM_16000_1:
        samplerate = 16000;
        chn = 1;
        break;
    case FRAME_FORMAT_PCM_16000_2:
    case FRAME_FORMAT_FAR_PCM_16000_2:
        samplerate = 16000;
        chn = 2;
        break;
    case FRAME_FORMAT_PCM_48000_1:
    case FRAME_FORMAT_FAR_PCM_48000_1:
        samplerate = 48000;
        chn = 1;
        break;
    case FRAME_FORMAT_PCM_48000_2:
    case FRAME_FORMAT_FAR_PCM_48000_2:
        samplerate = 48000;
        chn = 2;
        break;
    case FRAME_FORMAT_PCM_96000_1:
        samplerate = 96000;
        chn = 1;
        break;
    case FRAME_FORMAT_PCM_96000_2:
        samplerate = 96000;
        chn = 2;
        break;
    case FRAME_FORMAT_PCM_32000_1:
    case FRAME_FORMAT_FAR_PCM_32000_1:
        samplerate = 32000;
        chn = 1;
        break;
    case FRAME_FORMAT_PCM_32000_2:
    case FRAME_FORMAT_FAR_PCM_32000_2:
        samplerate = 32000;
        chn = 2;
        break;
    default:
        ret = false;
        break;
    }
    return ret;
}

bool Frame::getSamplerate(int &samplerate, int &chn)
{
    return getSamplerate(format_, samplerate, chn);
}

FrameFormat Frame::getPCMFormat(int chn, int samplerate)
{
    switch (samplerate)
    {
    case 8000:
        {
            switch (chn)
            {
            case 1:
                return FRAME_FORMAT_PCM_8000_1;
                break;
            default:
                break;
            }
        }
        break;
    case 16000:
        {
            switch (chn)
            {
            case 1:
                return FRAME_FORMAT_PCM_16000_1;
                break;
            case 2:
                return FRAME_FORMAT_PCM_16000_2;
                break;
            default:
                break;
            }
        }
        break;
    case 48000:
        {
            switch (chn)
            {
            case 1:
                return FRAME_FORMAT_PCM_48000_1;
                break;
            case 2:
                return FRAME_FORMAT_PCM_48000_2;
                break;
            default:
                break;
            }
        }
        break;
    case 96000:
        {
            switch (chn)
            {
            case 1:
                return FRAME_FORMAT_PCM_96000_1;
            case 2:
                return FRAME_FORMAT_PCM_96000_2;
            default:
                break;
            }
        }
        break;
    case 32000:
        {
            switch (chn)
            {
            case 1:
                return FRAME_FORMAT_PCM_32000_1;
                break;
            case 2:
                return FRAME_FORMAT_PCM_32000_2;
                break;
            default:
                break;
            }
        }
        break;
    default:
        return FRAME_FORMAT_PCM_16000_1;
        break;
    }
    return FRAME_FORMAT_PCM_16000_1;
}


FrameFormat Frame::getOpusFormat(int chn, int samplerate)
{
    switch (samplerate)
    {
    case 16000:
        {
            switch (chn)
            {
            case 1:
                return FRAME_FORMAT_OPUS_16000_1;
                break;
            default:
                break;
            }
        }
        break;
    case 48000:
        {
            switch (chn)
            {
            case 1:
            case 2:
                return FRAME_FORMAT_OPUS_48000_2;
                break;
            default:
                break;
            }
        }
        break;
    default:
        return FRAME_FORMAT_OPUS_16000_1;
        break;
    }
    return FRAME_FORMAT_OPUS_48000_2;
}

FrameFormat Frame::getG7221Format(int chn, int samplerate, int bitrate) {
    switch (samplerate) {
        case 16000: {
            switch (bitrate) {
                case 32000: return FRAME_FORMAT_G7221_16000_32000_1;
                case 24000: return FRAME_FORMAT_G7221_16000_24000_1;
                default: break;
            }
        } break;
        case 32000: {
            switch (bitrate) {
                case 48000: return FRAME_FORMAT_G7221_32000_48000_1;
                default: break;
            }
        } break;
        default: break;
    }
    return FRAME_FORMAT_G7221_16000_32000_1;
}

std::string Frame::getCodecName(const FrameFormat &format) {
    std::string name = "";
    switch (format) {
        case FRAME_FORMAT_PCMA: name = "pcma"; break;
        case FRAME_FORMAT_PCMU: name = "pcmu"; break;
        case FRAME_FORMAT_OPUS_16000_1:
        case FRAME_FORMAT_OPUS_48000_2: name = "opus"; break;
        case FRAME_FORMAT_G722_16000_1: name = "g722"; break;
        case FRAME_FORMAT_G729: name = "g729"; break;
        case FRAME_FORMAT_G7221_16000_32000_1:
        case FRAME_FORMAT_G7221_32000_48000_1:
        case FRAME_FORMAT_G7221_16000_24000_1: name = "g7221"; break;
        case FRAME_FORMAT_AAC_48000_1: name = "MP4A-LATM"; break;
        default: break;
    }
    return name;
}

bool Frame::getCodecConfig(const FrameFormat &format, std::string &name, int &samplerate, int &chn, int &bitrate) {
    switch (format) {
        case FRAME_FORMAT_PCMA: name = "pcma"; samplerate = 8000; chn = 1; break;
        case FRAME_FORMAT_PCMU: name = "pcmu"; samplerate = 8000; chn = 1; break;
        case FRAME_FORMAT_OPUS_16000_1: name = "opus"; samplerate = 16000; chn = 1; break;
        case FRAME_FORMAT_OPUS_48000_2: name = "opus"; samplerate = 48000; chn = 2; break;
        case FRAME_FORMAT_G722_16000_1: name = "g722"; samplerate = 16000; chn = 1; bitrate = 64000; break;
        case FRAME_FORMAT_G729: name = "g729"; samplerate = 8000; chn = 1; break;
        case FRAME_FORMAT_G7221_16000_32000_1: name = "g7221"; samplerate = 16000; chn = 1; bitrate = 32000; break;
        case FRAME_FORMAT_G7221_32000_48000_1: name = "g7221"; samplerate = 32000; chn = 1; bitrate = 48000; break;
        case FRAME_FORMAT_G7221_16000_24000_1: name = "g7221"; samplerate = 16000; chn = 1; bitrate = 24000; break;
        case FRAME_FORMAT_AAC_48000_1: name = "MP4A-LATM"; samplerate = 48000; chn = 1; bitrate = 64000; break;
        default: return false;
    }
    return true;
}

bool Frame::isPCM(const FrameFormat &format)
{
    bool ret = true;
    switch (format)
    {
    case FRAME_FORMAT_PCM_8000_1:
    case FRAME_FORMAT_FAR_PCM_8000_1:
    case FRAME_FORMAT_PCM_16000_1:
    case FRAME_FORMAT_FAR_PCM_16000_1:
    case FRAME_FORMAT_PCM_16000_2:
    case FRAME_FORMAT_FAR_PCM_16000_2:
    case FRAME_FORMAT_PCM_48000_1:
    case FRAME_FORMAT_FAR_PCM_48000_1:
    case FRAME_FORMAT_PCM_48000_2:
    case FRAME_FORMAT_FAR_PCM_48000_2:
    case FRAME_FORMAT_PCM_96000_1:
    case FRAME_FORMAT_PCM_96000_2:
    case FRAME_FORMAT_PCM_32000_1:
    case FRAME_FORMAT_FAR_PCM_32000_1:
    case FRAME_FORMAT_PCM_32000_2:
    case FRAME_FORMAT_FAR_PCM_32000_2:
        break;
    default:
        ret = false;
    }
    return ret;
}

bool Frame::isPCM()
{
    return isPCM(format_);
}

#ifdef ENABLE_VIDEO
bool I420Frame::createFrameBuffer(int width, int height, int hStride, int vStride)
{
    assignFrameArgs(width, height, hStride, vStride);
    int bufferSize = hStride_ * vStride_ * 3 / 2;
    Frame::createFrameBuffer(bufferSize);
    data_[0] = frameBuffer_;
    data_[1] = frameBuffer_ + hStride_ * vStride_;
    data_[2] = frameBuffer_ + hStride_ * vStride_ / 4;
    return true;
}

bool I420Frame::createFrameBuffer(uint8_t* frameBuffer, int width, int height, int hStride, int vStride)
{
    assignFrameArgs(width, height, hStride, vStride);
    int bufferSize = hStride_ * vStride_ * 3 / 2;
    Frame::createFrameBuffer(frameBuffer, bufferSize);
    data_[0] = frameBuffer_;
    data_[1] = frameBuffer_ + hStride_ * vStride_;
    data_[2] = frameBuffer_ + hStride_ * vStride_ / 4;
    return true;
}

bool NV12Frame::createFrameBuffer(int width, int height, int hStride, int vStride)
{
    assignFrameArgs(width, height, hStride, vStride);
    int bufferSize = hStride_ * vStride_ * 3 / 2;
    Frame::createFrameBuffer(bufferSize);
    data_[0] = frameBuffer_;
    data_[1] = frameBuffer_ + hStride_ * vStride_;
    return true;
}

bool NV12Frame::createFrameBuffer(uint8_t* frameBuffer, int width, int height, int hStride, int vStride)
{
    assignFrameArgs(width, height, hStride, vStride);
    int bufferSize = hStride_ * vStride_ * 3 / 2;
    Frame::createFrameBuffer(frameBuffer, bufferSize);
    data_[0] = frameBuffer_;
    data_[1] = frameBuffer_ + hStride_ * vStride_;
    return true;
}
#ifdef USE_FFMPEG
FFAVFrame::~FFAVFrame()
{
    AVFrame* avFrame = (AVFrame*)avFrame_;
    av_frame_free(&avFrame);
}

FFAVFrame::FFAVFrame(const std::string& jsonParams): Frame(FRAME_FORMAT_FFAVFRAME)
{
    avFrame_ = nullptr;
    nlohmann::json j;
    if (jsonParams != "")
    {
        j = nlohmann::json::parse(jsonParams);
        if (j.contains("avframe") && j["avframe"].is_number())
        {
            long ptr = j["avframe"];
            avFrame_ = (void*)ptr;
        }
    }
}

FFAVFrame::FFAVFrame(void* avFrame): Frame(FRAME_FORMAT_FFAVFRAME), avFrame_(avFrame)
{

}

void FFAVFrame::updateParams(const std::string& jsonParams)
{
    avFrame_ = nullptr;
    nlohmann::json j;
    if (jsonParams != "")
    {
        j = nlohmann::json::parse(jsonParams);
        if (j.contains("avframe") && j["avframe"].is_number())
        {
            long ptr = j["avframe"];
            avFrame_ = (void*)ptr;
        }
    }
}

FFAVPacket::~FFAVPacket()
{
    AVPacket* avPacket = (AVPacket*)avPacket_;
    av_packet_free(&avPacket);
}

FFAVPacket::FFAVPacket(const std::string& jsonParams): Frame(FRAME_FORMAT_FFAVPACKET)
{
    avPacket_ = nullptr;
    nlohmann::json j;
    if (jsonParams != "")
    {
        j = nlohmann::json::parse(jsonParams);
        if (j.contains("avpacket") && j["avpacket"].is_number())
        {
            long ptr = j["avpacket"];
            avPacket_ = (void*)ptr;
        }
    }
}

FFAVPacket::FFAVPacket(void* avPacket): Frame(FRAME_FORMAT_FFAVPACKET), avPacket_(avPacket)
{

}

void FFAVPacket::updateParams(const std::string& jsonParams)
{
    avPacket_ = nullptr;
    nlohmann::json j;
    if (jsonParams != "")
    {
        j = nlohmann::json::parse(jsonParams);
        if (j.contains("avpacket") && j["avpacket"].is_number())
        {
            long ptr = j["avpacket"];
            avPacket_ = (void*)ptr;
        }
    }
}
#endif

#endif