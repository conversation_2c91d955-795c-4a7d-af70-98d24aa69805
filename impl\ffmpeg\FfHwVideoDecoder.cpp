#include "FfHwVideoDecoder.h"
#include "Frame.h"
#include <json.hpp>
#include <ljcore/jlog.h>
#include <stdexcept>
using namespace panocom;

enum AVPixelFormat FfHwVideoDecoder::hw_pix_fmt = AV_PIX_FMT_NONE;

int FfHwVideoDecoder::hwDecoderInit(AVCodecContext *ctx, const enum AVHWDeviceType type)
{

    int err = 0;
    if ((err = av_hwdevice_ctx_create(&hw_device_ctx, type,"/dev/dri/renderD128", NULL, 0)) < 0) {
        jerror("hwDecoderInit Failed to create specified HW device. ctx:%p type:%d",ctx,type);
        return err;
    }
    ctx->hw_device_ctx = av_buffer_ref(hw_device_ctx);

    return err;
}


enum AVPixelFormat FfHwVideoDecoder::getHwformat(AVCodecContext *ctx,const enum AVPixelFormat *pix_fmts)
{
    const enum AVPixelFormat *p;
    for (p = pix_fmts; *p != -1; p++) {
        if (*p == hw_pix_fmt)
            return *p;
    }
    jerror("getHwformat Failed to get HW surface format.");
    return AV_PIX_FMT_NONE;
}


FfHwVideoDecoder::FfHwVideoDecoder(const std::string& jsonParams):initSuccess(false)
{
	FN_BEGIN;
	name_ = "FfHwVideoDecoder";
	nlohmann::json j;
	if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
	fps_ = 30;
	if (j.contains("fps"))
	{
		fps_ = j["fps"];
	}
	codecName_ = "h264";
	if (j.contains("codec"))
	{
		codecName_ = j["codec"];//h264、hevc可选
        if("libx264" == codecName_)
            codecName_ = "h264";
        else if("h265" == codecName_)
            codecName_ = "hevc";
	}
	kbps_ = 2000;
	if (j.contains("kbps"))
	{
		kbps_ = j["kbps"];
	}
	gop_ = 30;
	if (j.contains("gop"))
	{
		gop_ = j["gop"];
	}
	width_ = 1280;
	if (j.contains("width"))
	{
		width_ = j["width"];
	}
	height_ = 720;
	if (j.contains("height"))
	{
		height_ = j["height"];
	}

    extra_hw_frames_ = 0;
    if (j.contains("hw_frames"))
    {
        int hw_frames = j["hw_frames"];
        if(hw_frames > 2*gop_)
            hw_frames = 2*gop_;
        extra_hw_frames_ = hw_frames -20;
        if(extra_hw_frames_<0)
            extra_hw_frames_ = 0;
    }

	jinfo("FfHwVideoDecoder codecName_:%s %dx%d@%d gop=%d kbps=%d  extra_hw_frames_:%d", codecName_.c_str(), width_, height_, fps_, gop_, kbps_,extra_hw_frames_);
	start();
	FN_END;
}

FfHwVideoDecoder::~FfHwVideoDecoder()
{
	FN_BEGIN;
	stop();
	FN_END;
}

void FfHwVideoDecoder::start()
{
	if (thread_.isRunning()) return;
	FN_BEGIN;
	avcodec_ = avcodec_find_decoder_by_name(codecName_.c_str());//
	if (!avcodec_)
	{
		jerror("avcodec_find_decoder_by_name(%s) failed  ",codecName_.c_str());
		return;
	}
    jinfo("avcodec_find_decoder_by_name(%s)  success  avcodec_->id:%d ",codecName_.c_str(),avcodec_->id);

    /**查找硬件解码器类型*/
    const char* hwTypeName ="vaapi";
    enum AVHWDeviceType type;
    type = av_hwdevice_find_type_by_name(hwTypeName);
    if (type == AV_HWDEVICE_TYPE_NONE) {
        jerror("Device type %s is not supported.\n",hwTypeName);
        jerror("Available device types:");
        while((type = av_hwdevice_iterate_types(type)) != AV_HWDEVICE_TYPE_NONE)
            jerror(" %s", av_hwdevice_get_type_name(type));
        return;
    }

    /**查找硬件解码配置、硬件格式 hw_pix_fmt，
     * vaapi一般对应 AV_PIX_FMT_VAVAPI_VLD
     * */
    int i;
    for (i = 0;; i++) {
        const AVCodecHWConfig *config = avcodec_get_hw_config(avcodec_, i);
        if (!config) {
            fprintf(stderr, "Decoder %s does not support device type %s.\n",
                    avcodec_->name, av_hwdevice_get_type_name(type));
            return;
        }
        if (config->methods & AV_CODEC_HW_CONFIG_METHOD_HW_DEVICE_CTX &&
            config->device_type == type) {
            hw_pix_fmt = config->pix_fmt;
            break;
        }
    }

	parser_ = av_parser_init(avcodec_->id);
    /**分配解码上下文**/
	ctx_ = avcodec_alloc_context3(avcodec_);
	if (ctx_)
	{
		/* put sample parameters */
		if (codecName_ == "libx264")
			ctx_->bit_rate = kbps_;
		else
			ctx_->bit_rate = kbps_ * 1000;
		/* resolution must be a multiple of two */
		ctx_->width = width_;
		ctx_->height = height_;
		/* frames per second */
		ctx_->time_base.num = 1;
		ctx_->time_base.den = fps_;
		ctx_->framerate.num = fps_;
		ctx_->framerate.den = 1;
		ctx_->gop_size = gop_;
		ctx_->max_b_frames = 0;
//        ctx_->pix_fmt = AV_PIX_FMT_NV12;
        ctx_->get_format  = getHwformat;
        if (hwDecoderInit(ctx_, type) < 0)
        {
            jerror("hwDecoderInit(ctx_, type) < 0");
            return;
        }
        /**初始化硬件解码器**/
        ctx_->extra_hw_frames = extra_hw_frames_;

		int ret = avcodec_open2(ctx_, avcodec_, NULL);//初始化硬件解码器
		if (ret < 0)
		{
			jerror("avcodec_open2(ctx_, avcodec_, NULL) ret < 0");
		}
		else
		{
			thread_.start();
		}
	}
	else
	{
		jerror("!ctx_");
        return;
	}
    initSuccess = true;
	FN_END;
}

void FfHwVideoDecoder::stop()
{
	if (!thread_.isRunning()) return;
	std::unique_lock<std::mutex> locker(frame_mutex_);
	thread_.stop(true);
	if (ctx_)
	{
		avcodec_close(ctx_);
		avcodec_free_context(&ctx_);
	}
    av_buffer_unref(&hw_device_ctx);
}

void FfHwVideoDecoder::onFrame(const std::shared_ptr<Frame>& frame)
{
	printInputStatus("ffhwdecoder");
	std::unique_lock<std::mutex> locker(frame_mutex_);
	if (!thread_.isRunning()) return;
	thread_.loop()->runInLoop([this, frame]() {
		AVPacket* pkt = NULL;
		bool needFreePacket = true;
		if (frame->getFrameFormat() == FRAME_FORMAT_FFAVPACKET)
		{
			auto p = std::dynamic_pointer_cast<FFAVPacket>(frame);
			pkt = (AVPacket*)p->getAVPacket();
			needFreePacket = false;
		}
		else
		{
			pkt = av_packet_alloc();
			av_init_packet(pkt);
			if (parser_)
			{
				int ret = 0;
				uint8_t* buf = frame->getFrameBuffer();
				int len = frame->getFrameSize();
				//jinfo("FfHwVideoDecoder::onFrame %d", len);
				do {
					ret = av_parser_parse2(parser_, ctx_, &pkt->data, &pkt->size, buf, len, AV_NOPTS_VALUE, AV_NOPTS_VALUE, 0);
					if (ret > 0 && len - ret > 0)
					{
						buf += ret;
						len -= ret;
					}
					else
					{
						break;
					}
				} while (1);
				if (pkt->size == 0)
				{
					av_packet_free(&pkt);
					return;
				}
			}
			else
			{
				pkt->data = frame->getFrameBuffer();
				pkt->size = frame->getFrameSize();
			}
		}

		int ret = avcodec_send_packet(ctx_, pkt);
		if (ret < 0)
		{
            jerror("avcodec_send_packet(ctx_, pkt) ret(%d) < 0",ret);
		}
		while (ret >= 0)//TODO:应该先获取帧后send packet???
		{
			AVFrame* frame = av_frame_alloc();
			if (!frame)
			{
                jerror(" av_frame_alloc() frame is nullptr ");
			}
			ret = avcodec_receive_frame(ctx_, frame);
			if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF)
			{
				av_frame_free(&frame);
				break;
			}
			else if (ret < 0)
			{
				av_frame_free(&frame);
                jerror("avcodec_receive_frame ret<0  Error while decoding");
			}
			else
			{
				std::shared_ptr<Frame> f = std::make_shared<FFAVFrame>(frame);
				f->setGroupId(getGroupId());
				deliverFrame(f);
				printOutputStatus("ffhwdecoder");
			}
		}
		if (needFreePacket)
		{
			av_packet_free(&pkt);
		}
		});
}