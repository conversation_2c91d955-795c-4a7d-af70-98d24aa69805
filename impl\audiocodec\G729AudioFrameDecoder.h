#ifndef IMPL_AUDIO_G729AUDIOFRAMEDECODER_H_
#define IMPL_AUDIO_G729AUDIOFRAMEDECODER_H_
#include "AudioFrameDecoder.h"
#include "Frame.h"
#ifdef __cplusplus
extern "C" {
#endif
#include "g729/codecParameters.h"
#include "g729/utils.h"
#include "g729/decoder.h"
#ifdef __cplusplus
}
#endif
namespace panocom
{
class G729AudioFrameDecoder : public AudioFrameDecoder
{
public:
	G729AudioFrameDecoder(const std::string& jsonParams);
	~G729AudioFrameDecoder();
	void onFrame(const std::shared_ptr<Frame> &f) override;
private:
	FrameFormat fmt_;
	bcg729DecoderChannelContextStruct *ctx_;
};



} // namespace panocom

#endif