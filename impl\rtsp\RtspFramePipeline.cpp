#include "RtspFramePipeline.h"
#include "RtpHeader.h"
#include "RtpEngine.h"
#include "CodecInfo.h"
#include <json.hpp>
#include <ljcore/jlog.h>

using namespace panocom;

int RtspFramePipeline::rtsp_client_send(void* param, const char* uri, const void* req, size_t bytes)
{
    return ((RtspFramePipeline*)param)->_rtsp_client_send(uri, req, bytes);
}

int RtspFramePipeline::rtpport(void* param, int media, const char* source, unsigned short rtp[2], char* ip, int len)
{
	return ((RtspFramePipeline*)param)->_rtpport(media, source, rtp, ip, len);
}

void RtspFramePipeline::onrtp(void* param, uint8_t channel, const void* data, uint16_t bytes)
{
    return ((RtspFramePipeline*)param)->_onrtp(channel, data, bytes);
}

int RtspFramePipeline::onannounce(void* param)
{
    return ((RtspFramePipeline*)param)->_onannounce();
}

int RtspFramePipeline::ondescribe(void* param, const char* sdp, int len)
{
    return ((RtspFramePipeline*)param)->_ondescribe(sdp, len);
}

int RtspFramePipeline::onsetup(void* param, int timeout, int64_t duration)
{
    return ((RtspFramePipeline*)param)->_onsetup(timeout, duration);
}

int RtspFramePipeline::onteardown(void* param)
{
	return ((RtspFramePipeline*)param)->_onteardown();
}

int RtspFramePipeline::onplay(void* param, int media, const uint64_t *nptbegin, const uint64_t *nptend, const double *scale, const struct rtsp_rtp_info_t* rtpinfo, int count)
{
	return ((RtspFramePipeline*)param)->_onplay(media, nptbegin, nptend, scale, rtpinfo, count);
}

int RtspFramePipeline::onpause(void* param)
{
	return ((RtspFramePipeline*)param)->_onpause();
}

int RtspFramePipeline::_rtsp_client_send(const char* uri, const void* req, size_t bytes)
{
    return SockSender::send((const char *)req, bytes);
}

int RtspFramePipeline::_rtpport(int media, const char* source, unsigned short rtp[2], char* ip, int len)
{
	return 0;
}

void RtspFramePipeline::_onrtp(uint8_t channel, const void* data, uint16_t bytes)
{
    const char* encoding = rtsp_client_get_media_encoding(rtsp_, channel);
    int rate = rtsp_client_get_media_rate(rtsp_, channel);
    
    if (channel < fmts_.size() && fmts_[channel] != FRAME_FORMAT_UNKNOWN)
    {
        auto f = Frame::CreateFrame(fmts_[channel]);
        if (f)
        {
            f->createFrameBuffer(bytes);
            RTPHeader* head = (RTPHeader*)(data);
            memcpy(f->getFrameBuffer(), (char*)data + head->getHeaderLength(), bytes - head->getHeaderLength());
            deliverFrame(f);
        }
    }
}

int RtspFramePipeline::_onannounce()
{
    return rtsp_client_describe(rtsp_);
}

int RtspFramePipeline::_ondescribe(const char* sdp, int len)
{
    return rtsp_client_setup(rtsp_, sdp, len);
}

int RtspFramePipeline::_onsetup(int timeout, int64_t duration)
{
    int i;
	uint64_t npt = 0;
	char ip[65];
	u_short rtspport;
	rtsp_client_play(rtsp_, &npt, NULL);
    rtp_.clear();
	for (i = 0; i < rtsp_client_media_count(rtsp_); i++)
	{
		int payload;
		const char* encoding;
        int rate;
		const struct rtsp_header_transport_t* transport;
		transport = rtsp_client_get_media_transport(rtsp_, i);
		encoding = rtsp_client_get_media_encoding(rtsp_, i);
		payload = rtsp_client_get_media_payload(rtsp_, i);
        rate = rtsp_client_get_media_rate(rtsp_, i);
        std::list<CodecInst> insts;
        CodecInst inst;
        if (getCodecInst(encoding, insts))
        {
            bool isFound = false;
            for (CodecInst& i: insts)
            {
                if (i.rate == rate)
                {
                    inst = i;
                    isFound = true;
                    break;
                }
            }
            if (!isFound)
            {
                inst = insts.front();
            }
            fmts_.push_back(inst.fmt);
        }
        else
        {
            fmts_.push_back(FRAME_FORMAT_UNKNOWN);
        }
		if (RTSP_TRANSPORT_RTP_UDP == transport->transport)
		{
            nlohmann::json j;
            j["payloadType"] = payload;
            j["hz"] = rate;
            j["bindPort"] = transport->rtp.u.client_port1;
            j["dst"] = nlohmann::json::array();
            nlohmann::json dst;
            dst["ip"] = rtspUrl_._host;
            dst["port"] = transport->rtp.u.server_port1;
            j["dst"].push_back(dst);
            auto rtp = RtpEngine::CreateRTPEngine(rtpEngineName_, j.dump());
            switch (fmts_[i])
            {
            case FRAME_FORMAT_H264:
            case FRAME_FORMAT_H265:
                rtp->addVideoDestination(FramePipeline::shared_from_this());
                break;
            default:
                rtp->addAudioDestination(FramePipeline::shared_from_this());
                break;
            }
            
            rtp_.push_back(rtp);
		}
		else if (RTSP_TRANSPORT_RTP_TCP == transport->transport)
		{
            
		}
		else
		{
			assert(0); // TODO
		}
	}

	return 0;
}

int RtspFramePipeline::_onteardown()
{
    return 0;
}

int RtspFramePipeline::_onplay(int media, const uint64_t *nptbegin, const uint64_t *nptend, const double *scale, const struct rtsp_rtp_info_t* rtpinfo, int count)
{
    return 0;
}

int RtspFramePipeline::_onpause()
{
    return 0;
}

RtspFramePipeline::RtspFramePipeline(const std::string& jsonParams)
{
    FN_BEGIN;
    name_ = "RtspFramePipeline";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    if (!j.contains("url"))
    {
        return;
    }
    std::string url = j["url"];
    std::string account;
    std::string passwd;
    if (j.contains("account"))
    {
        account = j["account"];
    }
    if (j.contains("passwd"))
    {
        passwd = j["passwd"];
    }
    rtpEngineName_ = "JRtpEngine";
    if (j.contains("rtp"))
    {
        rtpEngineName_ = j["rtp"];
    }

    mediakit::RtspUrl rtspUrl;
    rtspUrl.parse(url);

    struct rtsp_client_handler_t handler;
	handler.send = rtsp_client_send;
	handler.rtpport = rtpport;
    handler.onannounce = onannounce;
	handler.ondescribe = ondescribe;
	handler.onsetup = onsetup;
	handler.onplay = onplay;
	handler.onpause = onpause;
	handler.onteardown = onteardown;
	handler.onrtp = onrtp;

	rtsp_ = rtsp_client_create(url.c_str(), account.c_str(), passwd.c_str(), &handler, this);
    
    startConnect(rtspUrl._host, rtspUrl._port);
    FN_END;
}

RtspFramePipeline::~RtspFramePipeline()
{
    FN_BEGIN;
    FN_END;
}

void RtspFramePipeline::onFrame(const std::shared_ptr<Frame> &f)
{
    deliverFrame(f);
}

void RtspFramePipeline::onConnect(const SockException &ex)
{
    rtsp_client_options(rtsp_, NULL);
}

void RtspFramePipeline::onRecv(const toolkit::Buffer::Ptr &buf)
{
    rtsp_client_input(rtsp_, buf->data(), buf->size());
}

void RtspFramePipeline::onError(const toolkit::SockException &ex)
{

}