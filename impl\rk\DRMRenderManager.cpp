#include "DRMDisplayManager.h"

#include <fcntl.h>
#include <sys/ioctl.h>
#include <unistd.h>

#include <json.hpp>
#include <hv/hlog.h>
namespace {
const std::string DEFAUTL_PATH = "/dev/dri/card0";
const int MAX_DEV_NUM = 2;
}

namespace panocom {
// TODO: 加锁保护fds_
DRMDisplayManager::DRMDisplayManager(/* args */) {}

DRMDisplayManager::~DRMDisplayManager() {
	for (auto& e: fds_) {
		if (e.second >= 0) drmClose(e.second);
		e.second = -1;
	}
	fds_.clear();
}

DRMDisplayManager &DRMDisplayManager::instance() {
    static DRMDisplayManager obj;
    return obj;
}

int DRMDisplayManager::GetDrmDev(int index) {
    if (index >= MAX_DEV_NUM) return -1;
    std::string path = DEFAUTL_PATH;
    path.back() += index;

    int fd = -1;
    if (fds_.count(path) != 0)
        fd = fds_[path];
    else {
        fd = open_driver(path.c_str());
        if (fd >= 0)
            fds_[path] = fd;
    }
    return fd;
}

std::shared_ptr<DRMDisplay> DRMDisplayManager::CreateDisplay(const std::string &jsonParams) {
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    std::string path = DEFAUTL_PATH;
    int card_id = 0;
    if (j.contains("path"))
        path = j["path"];
    else if (j.contains("CardId")) {
        card_id = j["CardId"];
        path.back() += card_id;
    }
    int fd = -1;
    if (fds_.count(path) != 0)
        fd = fds_[path];
    else {
        fd = open_driver(path.c_str());
        if (fd >= 0)
            fds_[path] = fd;
    }
	if (fd < 0) LOGD("failed to open drm device %s", path.c_str());
    return (fd >= 0 ? std::make_shared<DRMDisplay>(fd, jsonParams) : nullptr);
}

std::shared_ptr<RKVideoFrameRender> DRMDisplayManager::CreateRender(const std::string &jsonParams) {
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    std::string path = DEFAUTL_PATH;
    int card_id = 0;
    if (j.contains("path"))
        path = j["path"];
    else if (j.contains("CardId")) {
        card_id = j["CardId"];
        path.back() += card_id;
    }
    int fd = -1;
    if (fds_.count(path) != 0)
        fd = fds_[path];
    else {
        fd = open_driver(path.c_str());
        if (fd >= 0)
            fds_[path] = fd;
    }
	if (fd < 0) jerror("failed to open drm device %s", path.c_str());
	return (fd >= 0 ? std::make_shared<RKVideoFrameRender>(fd, jsonParams) : nullptr);
}

int DRMDisplayManager::open_drivers(const char *base_path, int base_id) {
    int fd = -1;
    char dev_path[32];
    LOGD("path = %s", base_path);

    for (int i = 0; i < 16; i++) {
        sprintf(dev_path, "%s%d", base_path, base_id + i);
        if (access(dev_path, F_OK) != 0)
            continue;

        fd = open_driver(dev_path);
        if (fd >= 0)
            break;
    }
    return fd;
}

int DRMDisplayManager::open_driver(const char *dev_path) {
    assert(dev_path);

    int fd = open(dev_path, O_RDWR | O_CLOEXEC);
    if (fd < 0) {
        LOGE("failed to open %s", dev_path);
        return -1;
    }

    if (!strncmp(dev_path, "/dev/dri/card", 13)) {
        if (!is_authenticated(fd, dev_path)) {
            close(fd);
            return -1;
        }
    }

    return fd;
}

bool DRMDisplayManager::is_authenticated(int fd, const char *msg) {
    drm_client_t client;
    memset(&client, 0, sizeof(drm_client_t));
    if (ioctl(fd, DRM_IOCTL_GET_CLIENT, &client) == -1) {
        LOGE("failed to get drm client");
        return false;
    }

    if (!client.auth) {
        LOGE("%s is not authenticated", msg);
        return false;
    }
    return true;
}

bool DRMDisplayManager::GetConnectorStatus(int dev, int card) {
    std::string path = DEFAUTL_PATH;
    int fd = GetDrmDev(card);
    return RKVideoFrameRender::GetConnectorStatus(fd, dev);
}
} // namespace panocom
