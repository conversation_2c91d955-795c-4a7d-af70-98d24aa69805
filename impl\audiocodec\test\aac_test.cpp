#include <fstream>
#include <future>
#include <unistd.h>

#include <jlog.h>
#include <json.hpp>

#include "AudioFrameDecoder.h"
#include "AudioFrameEncoder.h"

namespace {
const size_t kSampleRateHz = 16000;
}
using namespace panocom;
class TestFileSource : public FramePipeline {
private:
    /* data */
public:
    TestFileSource(const std::string &jsonParams);
    ~TestFileSource();

private:
    std::future<bool> future_;
    std::ifstream input_;
    bool running_;
    int num_10ms_frames_per_packet_;
};

TestFileSource::TestFileSource(const std::string &jsonParams)
    : num_10ms_frames_per_packet_(1) {
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    if (j.contains("path")) {
        input_.open(j["path"], std::ios::in | std::ios::binary);
        running_ = true;
    }
    FrameFormat format = FRAME_FORMAT_PCM_16000_1;
    future_ = std::async([this, format]() -> bool {
        uint32_t size = kSampleRateHz / 100 * num_10ms_frames_per_packet_ * 2;
        // uint32_t size = 2048;
        
        while (running_) {
            if (!input_.eof()) {
                printf("size = %d\n", size);
                auto frame = Frame::CreateFrame(format);
                frame->createFrameBuffer(size);
                input_.read((char *)frame->getFrameBuffer(), size);
                deliverFrame(frame);
            }
            // else {
            // 	input_.clear();
            // 	input_.seekg(0, std::ios::beg);
            // }
        }
        return true;
    });
}

TestFileSource::~TestFileSource() {
    running_ = false;
    auto status = future_.wait_for(std::chrono::milliseconds(200));
    if (status == std::future_status::timeout) {

    } else {
        future_.get();
    }
    if (input_ && input_.is_open()) {
        input_.close();
    }
}

class TestFileDestination : public FramePipeline {
private:
    /* data */
public:
    TestFileDestination(const std::string &jsonParams);
    ~TestFileDestination();
    virtual void onFrame(const std::shared_ptr<Frame> &f) override;

private:
    std::ofstream output_;
};

TestFileDestination::TestFileDestination(const std::string &jsonParams) {
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    if (j.contains("path")) {
        output_.open(j["path"], std::ios::out | std::ios::binary);
    }
}

void TestFileDestination::onFrame(const std::shared_ptr<Frame> &f) {
    output_.write((char *)f->getFrameBuffer(), f->getFrameBufferSize());
}

TestFileDestination::~TestFileDestination() {
    if (output_ && output_.is_open()) {
        output_.close();
    }
}

int main() {
    jlog_init(nullptr);
    nlohmann::json j;
    j.clear();
    j["codec"] = "aac";
    auto encoder = AudioFrameEncoder::CreateAudioEncoder("native", j.dump());
    j.clear();
    j["codec"] = "aac";
    auto decoder = AudioFrameDecoder::CreateAudioDecoder("native", j.dump());

    j.clear();
    j["path"] = "audio_short16.pcm";
    auto file_source = std::make_shared<TestFileSource>(j.dump());

    j.clear();
    j["path"] = "output.pcm";
    auto file_destination = std::make_shared<TestFileDestination>(j.dump());

    decoder->addAudioDestination(file_destination);
    encoder->addAudioDestination(decoder);
    file_source->addAudioDestination(encoder);
    while (true) {
        usleep(1000);
    }
    return 0;
}