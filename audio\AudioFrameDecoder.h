
#ifndef P_AudioFrameDecoder_h
#define P_AudioFrameDecoder_h

#include "../FramePipeline.h"

namespace panocom
{
    class AudioFrameDecoder : public FramePipeline
    {
    public:
        static void Regist();
        static std::list<std::string>& GetSupportDecoderTypes();
        static bool IsSupportDecoderType(const std::string &decoderType);
        static std::shared_ptr<AudioFrameDecoder> CreateAudioDecoder(const std::string &decoderType, const std::string& jsonParams = "");
        static std::list<std::string>& GetSupportCodecs();
        static bool IsSupportCodec(const std::string &codec);
        static bool IsAudioFrameDecoder(const FramePipeline::Ptr& ptr);

        virtual ~AudioFrameDecoder() = default;
    protected:
        static bool isRegistered;
        static std::list<std::string> codecs_;
        static std::list<std::string> decoderTypes_;
    };
}

#endif

