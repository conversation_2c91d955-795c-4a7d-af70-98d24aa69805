#include "FfVideoDecoderAdapter.h"
#include "FfHwVideoDecoder.h"
#include "FfVideoDecoder.h"
#include "Frame.h"
#include <json.hpp>
#include <ljcore/jlog.h>

using namespace panocom;

FfVideoDecoderAdapter::FfVideoDecoderAdapter(const std::string& jsonParams)
{
	FN_BEGIN;
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    std::string decoderType= "hardware";
	if (j.contains("decoderType"))
	{
        decoderType = j["decoderType"];
	}
    jinfo("FfVideoDecoderAdapter decoderType:%sjsonParams:%s ",decoderType.c_str(),jsonParams.c_str());
    if("hardware" == decoderType){
        std::shared_ptr<FfHwVideoDecoder> hwDecoder = std::make_shared<FfHwVideoDecoder>(jsonParams);
        if(hwDecoder->isInitSuccess()){
            decoder_ = hwDecoder;
        }else{
            jerror("FfVideoDecoderAdapter create FfHwVideoDecoder failed,then create FfVideoDecoder");
            decoder_ = std::make_shared<FfVideoDecoder>(jsonParams);
        }
    }else{
        decoder_ = std::make_shared<FfVideoDecoder>(jsonParams);
    }
	FN_END;
}

FfVideoDecoderAdapter::~FfVideoDecoderAdapter()
{
	FN_BEGIN;
    if(decoder_){
        decoder_->reset();
        decoder_ = nullptr;
    }
	FN_END;
}

void FfVideoDecoderAdapter::onFeedbackFrame(const std::shared_ptr<Frame> &f){
    if(decoder_)
        decoder_->onFeedbackFrame(f);
}


FramePipeline::Ptr FfVideoDecoderAdapter::addAudioDestination(const FramePipeline::Ptr& dest)
{
    if(decoder_)
        return decoder_->addAudioDestination(dest);
    return FramePipeline::addAudioDestination(dest);
}

FramePipeline::Ptr FfVideoDecoderAdapter::addVideoDestination(const FramePipeline::Ptr& dest)
{
    if(decoder_)
        return decoder_->addVideoDestination(dest);
    return FramePipeline::addVideoDestination(dest);
}

FramePipeline::Ptr FfVideoDecoderAdapter::addDataDestination(const FramePipeline::Ptr& dest)
{
    if(decoder_)
        return decoder_->addDataDestination(dest);
    return FramePipeline::addDataDestination(dest);
}

FramePipeline::Ptr FfVideoDecoderAdapter::removeAudioDestination(const FramePipeline::Ptr& dest)
{
    if(decoder_)
        return decoder_->removeAudioDestination(dest);
    return FramePipeline::removeAudioDestination(dest);
}

FramePipeline::Ptr FfVideoDecoderAdapter::removeVideoDestination(const FramePipeline::Ptr& dest)
{
    if(decoder_)
        return decoder_->removeVideoDestination(dest);
    return FramePipeline::removeDataDestination(dest);
}

FramePipeline::Ptr FfVideoDecoderAdapter::removeDataDestination(const FramePipeline::Ptr& dest)
{
    if(decoder_)
        return decoder_->removeDataDestination(dest);
    return FramePipeline::removeDataDestination(dest);
}

std::list<FramePipeline::WPtr> FfVideoDecoderAdapter::getAudioDestinations()
{
    if(decoder_)
        return decoder_->getAudioDestinations();
    std::list<FramePipeline::WPtr> audio_dests_;
    return FramePipeline::getAudioDestinations();
}

std::list<FramePipeline::WPtr> FfVideoDecoderAdapter::getVideoDestinations()
{
    if(decoder_)
        return decoder_->getVideoDestinations();
    return FramePipeline::getVideoDestinations();
}

std::list<FramePipeline::WPtr> FfVideoDecoderAdapter::getDataDestinations()
{
    if(decoder_)
        return decoder_->getDataDestinations();
    return FramePipeline::getDataDestinations();
}

bool FfVideoDecoderAdapter::isAudioDestination(const std::shared_ptr<FramePipeline> &dst)
{
    if(decoder_)
        return decoder_->isAudioDestination(dst);
    return false;
}

bool FfVideoDecoderAdapter::isVideoDestination(const std::shared_ptr<FramePipeline> &dst)
{
    if(decoder_)
        return decoder_->isVideoDestination(dst);
    return false;
}

bool FfVideoDecoderAdapter::isDataDestination(const std::shared_ptr<FramePipeline> &dst)
{
    if(decoder_)
        return decoder_->isDataDestination(dst);
    return false;
}


void FfVideoDecoderAdapter::clearPipeline()
{
    if(decoder_)
        decoder_->clearPipeline();
}

void FfVideoDecoderAdapter::stopPipeline()
{
    if(decoder_)
        decoder_->stopPipeline();
}



void FfVideoDecoderAdapter::setGroupId(int groupId) {
    if (decoder_)
        decoder_->setGroupId(groupId);
    FramePipeline::setGroupId(groupId);
}

int FfVideoDecoderAdapter::getGroupId() {
    if (decoder_)
        decoder_->getGroupId();
    return FramePipeline::getGroupId();
}

void FfVideoDecoderAdapter::printOutputStatus(const std::string& name){
    if (decoder_)
        decoder_->printOutputStatus(name);
}



void FfVideoDecoderAdapter::start()
{
    if (decoder_)
        decoder_->start();
}

void FfVideoDecoderAdapter::pause()
{
    if (decoder_)
        decoder_->pause();
}

void FfVideoDecoderAdapter::stop()
{
    if (decoder_)
        decoder_->stop();
}

FramePipeline::WorkState FfVideoDecoderAdapter::workState()
{
    if (decoder_)
        return decoder_->workState();
    return WorkState::STOPPED;
}

void FfVideoDecoderAdapter::addAudioSource(const FramePipeline::Ptr &source)
{
    if (decoder_)
        decoder_->addAudioSource(source);
}

void FfVideoDecoderAdapter::removeAudioSource(const FramePipeline::Ptr &source)
{
    if (decoder_)
        decoder_->removeAudioSource(source);
}

void FfVideoDecoderAdapter::addVideoSource(const FramePipeline::Ptr &source)
{
    if (decoder_)
        decoder_->addVideoSource(source);
}

void FfVideoDecoderAdapter::removeVideoSource(const FramePipeline::Ptr &source)
{
    if (decoder_)
        decoder_->removeVideoSource(source);
}

void FfVideoDecoderAdapter::addDataSource(const FramePipeline::Ptr &source)
{
    if (decoder_)
        decoder_->addDataSource(source);
}

void FfVideoDecoderAdapter::removeDataSource(const FramePipeline::Ptr &source)
{
    if (decoder_)
        decoder_->removeDataSource(source);
}

std::list<FramePipeline::WPtr> FfVideoDecoderAdapter::getAudioSources()
{
    if (decoder_)
        decoder_->getAudioSources();
    std::list<FramePipeline::WPtr> audio_srcs_;
    return audio_srcs_;
}

std::list<FramePipeline::WPtr> FfVideoDecoderAdapter::getVideoSources()
{
    if (decoder_)
        decoder_->getVideoSources();
    return FramePipeline::getVideoSources();
}

std::list<FramePipeline::WPtr> FfVideoDecoderAdapter::getDataSources()
{
    if (decoder_)
        decoder_->getDataSources();
    return FramePipeline::getDataSources();
}

void FfVideoDecoderAdapter::checkFrameTimeout()
{
    if (decoder_)
        decoder_->checkFrameTimeout();
}

void FfVideoDecoderAdapter::resetConnectState(bool state) {
    if (decoder_)
        decoder_->resetConnectState(state);
}

void FfVideoDecoderAdapter::onFrameTransfer(const std::shared_ptr<Frame> &f)
{
    if (decoder_)
        decoder_->onFrameTransfer(f);
}

void FfVideoDecoderAdapter::printInputStatus(const std::string& name)
{
    if (decoder_)
        decoder_->printInputStatus(name);
}

void FfVideoDecoderAdapter::printDestinationStatus(const std::string& name)
{
    if (decoder_)
        decoder_->printDestinationStatus(name);
}

