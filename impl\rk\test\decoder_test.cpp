#include "MediaFramePipeline.h"
#include <json.hpp>
#include <unistd.h>
#include <ljcore/jlog.h>

#include <future>
#include <fstream>
// #include "osal/mpp_common.h"

// #include "rk_frame_buffer.h"
#include "VideoFrameEncoder.h"
#include "VideoFrameDecoder.h"

using namespace panocom;

#define WIDTH 1920
#define HEIGHT 1080
#define BUFFER_SIZE 4096
class TestFileSource : public FramePipeline
{
private:
	/* data */
public:
	TestFileSource(const std::string& jsonParams);
	~TestFileSource();
private:
	std::future<bool> future_;
	std::ifstream input_;
	bool running_;
	// RKBufferManager buufer_manager_;
};

// TestFileSource::TestFileSource(const std::string& jsonParams)
// {
// 	nlohmann::json j;
// 	if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
// 	if (j.contains("path")) {
// 		input_.open(j["path"], std::ios::in | std::ios::binary);
// 		running_ = true;
// 	}
// 	future_ = std::async([this]() -> bool {
// 		uint32_t width = WIDTH, height = HEIGHT;
// 		uint32_t size = width * height * 3 / 2;
// 		uint32_t hor_stride = MPP_ALIGN(WIDTH, 16);
// 		uint32_t ver_stride = MPP_ALIGN(HEIGHT, 16);
// 		uint32_t buf_size = hor_stride * ver_stride * 3 / 2;
// 		while (running_)
// 		{
// 			if (!input_.eof()) {
// 				auto frame = Frame::CreateFrame(FRAME_FORMAT_NV12);
// 				frame->createFrameBuffer(buf_size);
// 				char *base_y = (char*)frame->getFrameBuffer();
// 				char *base_u = base_y + hor_stride * ver_stride;
// 				for (int row = 0; row < HEIGHT; row++) {
// 					input_.read(base_y + row * hor_stride, width);
// 				}
// 				for (int row = 0; row < height / 2; row++) {
// 					input_.read(base_u + row * hor_stride, width);
// 				}
// 				// input_.read((char*)frame->getFrameBuffer(), size);
// 				deliverFrame(frame);
				
// 			}
// 			std::this_thread::sleep_for(std::chrono::milliseconds(10));
// 		}
// 		return true;
// 	});
// }

TestFileSource::TestFileSource(const std::string& jsonParams)
{
	nlohmann::json j;
	if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
	if (j.contains("path")) {
		input_.open(j["path"], std::ios::in | std::ios::binary);
		running_ = true;
	}
	int interval = 5;
	FrameFormat format = FRAME_FORMAT_H264;
	if (j.contains("codec")) {
		if (j["codec"] == "h264") format = FRAME_FORMAT_H264;
		else if (j["codec"] == "h265") {
			format = FRAME_FORMAT_H265; 
			interval = 8;
		}
	}
	future_ = std::async([this, format, interval]() -> bool {
		uint32_t size = BUFFER_SIZE;
		while (running_)
		{
			if (!input_.eof()) {
				auto frame = Frame::CreateFrame(format);
				frame->createFrameBuffer(BUFFER_SIZE);
				input_.read((char*)frame->getFrameBuffer(), size);
				deliverFrame(frame);
			} else {
				input_.clear();
				input_.seekg(0, std::ios::beg);
			}
			std::this_thread::sleep_for(std::chrono::milliseconds(interval));
		}
		return true;
	});
}

TestFileSource::~TestFileSource()
{
	running_ = false;
	auto status = future_.wait_for(std::chrono::milliseconds(200));
	if (status == std::future_status::timeout) {
		
	} else {
		future_.get();
	}
	if (input_ && input_.is_open()) {
		input_.close();
	}
}

class TestFileDestination : public FramePipeline
{
private:
	/* data */
public:
	TestFileDestination(const std::string& jsonParams);
	~TestFileDestination();
	virtual void onFrame(const std::shared_ptr<Frame> &f) override;
private:
	std::ofstream output_;
	uint32_t frame_count_;
};

TestFileDestination::TestFileDestination(const std::string& jsonParams)
{
	nlohmann::json j;
	if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
	if (j.contains("path")) {
		output_.open(j["path"], std::ios::out | std::ios::binary);
	}
	frame_count_ = 0;
}

void TestFileDestination::onFrame(const std::shared_ptr<Frame> &f) {
	if (output_.is_open()) {
		output_.write((char*)f->getFrameBuffer(), f->getFrameBufferSize());
	}

	// auto frame = RKVideoFrameBuffer::ConvertFrom(f);
	// if (output_.is_open() && frame->RawData()) {
	// 	int32_t width = frame->width();
	// 	int32_t height = frame->height();
	// 	int32_t hor_stride = frame->hor_stride();
	// 	int32_t ver_stride = frame->ver_stride();
	// 	char *base = (char*)frame->RawData();
	// 	char *base_y = base, *base_uv = base + hor_stride * ver_stride;
	// 	for (int i = 0; i < height; i++, base_y += hor_stride)
	// 		output_.write(base_y, width);
	// 	for (int i = 0; i < height / 2; i++, base_uv += hor_stride)
	// 		output_.write(base_uv, width);
	// }
}

TestFileDestination::~TestFileDestination()
{
	if (output_ && output_.is_open()) {
		output_.close();
	}
}

int main() {
    jlog_init(nullptr);
    nlohmann::json j;

	j.clear();
	j["path"] = "output.h264";
	auto file_destination = std::make_shared<TestFileDestination>(j.dump());

	j.clear();
    j["codec"] = "h264";
	j["fps"] = 25;
	j["format"] = "NV12";
    j["width"] = WIDTH;
    j["height"] = HEIGHT;
	auto encoder = VideoFrameEncoder::CreateVideoEncoder("RKVideoEncoder", j.dump());


	j.clear();
    j["codec"] = "h264";
    j["width"] = WIDTH;
    j["height"] = HEIGHT;
	auto decoder = VideoFrameDecoder::CreateVideoDecoder("RKVideoDecoder", j.dump());

	j.clear();
	j["path"] = "input.h264";
	auto file_source = std::make_shared<TestFileSource>(j.dump());

	encoder->addVideoDestination(file_destination);
	decoder->addVideoDestination(encoder);
	file_source->addVideoDestination(decoder);
	
    while (true)
    {
        usleep(1000);
    }
	return 0;
}