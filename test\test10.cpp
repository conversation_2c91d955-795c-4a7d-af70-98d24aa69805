#include <nvcuvid.h>
#include <Network/Socket.h>
#include <driver_types.h>
#include <cuda_runtime_api.h>
#include <hv/EventLoop.h>
#include "impl/cuda/CuCommon.h"

using namespace panocom;
using namespace toolkit;
using namespace hv;

#define LEN 32

int main(int argc, char **argv)
{
    std::shared_ptr<CUcontext> ctx;
    char buf[LEN] = { 0 };
    if (argc > 1)
    {
        bool waiting = true;
        Socket::Ptr socket = Socket::createSocket();
        socket->bindUdpSock(6666);
        socket->setOnRead([&ctx, &waiting, &buf](const Buffer::Ptr &buffer, struct sockaddr *addr, int addr_len) {
            if (!ctx)
            {
                ctx = CuCommon::instance().createCudaContext();
            }
            cudaIpcMemHandle_t t;
            if (buffer->size() == sizeof(t))
            {
                t = *(cudaIpcMemHandle_t*)buffer->data();
                void* ptr;
                cudaError_t ret = cudaIpcOpenMemHandle(&ptr, *(cudaIpcMemHandle_t *)&t, cudaIpcMemLazyEnablePeerAccess);
                if (ret != cudaSuccess)
                {
                    printf("cudaIpcOpenMemHandle fail\n");
                }
                else
                {
                    CUresult r = cuMemcpyDtoH(buf, (CUdeviceptr)ptr, LEN);
                    if (r != CUDA_SUCCESS)
                    {
                        printf("cuMemcpyDtoH fail\n");
                    }
                    else
                    {
                        printf("cp sharedmemory to host\n");
                    }
                }
            }
            waiting = false;
        });
        while (waiting)
        {
            usleep(1000);
        }
        
    }
    else
    {
        for (size_t i = 0; i < LEN-1; i++)
        {
            buf[i] = 'A' + i;
        }

        if (!ctx)
        {
            ctx = CuCommon::instance().createCudaContext();
        }
        
        void* ptr;
        CUresult ret = cuMemAlloc((CUdeviceptr *)&ptr, LEN);
        if (ret != CUDA_SUCCESS)
        {
            printf("cuMemAlloc fail\n");
        }
        else
        {
            CUresult r = cuMemcpyHtoD((CUdeviceptr)ptr, buf, LEN);
            if (r != CUDA_SUCCESS)
            {
                printf("cuMemcpyHtoD fail\n");
            }
            cudaIpcMemHandle_t t;
            cudaError_t ret = cudaIpcGetMemHandle((cudaIpcMemHandle_t *)&t, ptr);
            if (ret != cudaSuccess)
            {
                printf("cudaIpcGetMemHandle fail\n");
            }
            Socket::Ptr socket = Socket::createSocket();
            socket->bindUdpSock(8888);
            EventLoop loop;
            loop.setInterval(1000, [t, socket](TimerID id){
                struct sockaddr_storage addr = SockUtil::make_sockaddr("127.0.0.1", 6666);
                socket->send((char*)&t, sizeof(t), (struct sockaddr *)&addr);
            });
            loop.run();
        }
        
    }
    printf("buf = %s\n", buf);
    return 0;
}