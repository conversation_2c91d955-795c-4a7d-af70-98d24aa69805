// created by gyj 2024-2-19
#ifndef P_JRtpEngine_h
#define P_JRtpEngine_h
#include <chrono>
#include <unordered_set>
#include <unordered_map>
#include <future>

#include "RtpEngine.h"
#include <ptoolkit/bytebuffer.h>
#include <jrtplib3/rtpsession.h>
#include <jrtplib3/rtpipv4address.h>
#include <hv/EventLoopThread.h>
#ifdef USE_LIBNICE
#include <nice/agent.h>
#include <glib.h>
#endif

#ifdef USE_AUDIO_SIMULATION
#include <random>
#endif
#include "CodecInfo.h"

using namespace jrtplib;

namespace panocom
{
    class Frame;
    class JRtpEngine : public RtpEngine
    {
    public:
        JRtpEngine(const std::string& jsonParams);
        ~JRtpEngine() override;

        void onData(const char* buf, int len, bool rtp);
        int updateParam(const std::string& jsonParams) override;

        void onFrame(const std::shared_ptr<Frame> &f) override;
        void onFeedbackFrame(const std::shared_ptr<Frame> &f) override;
        void start() override;
        void stop() override;
        
#ifdef USE_LIBNICE
        void startNICE();
        void stopNICE();
        static void cb_new_selected_pair(NiceAgent *agent, guint _stream_id, guint component_id, gchar *lfoundation, gchar *rfoundation, gpointer data);
        static void cb_candidate_gathering_done(NiceAgent *agent, guint stream_id, gpointer data);
        static void cb_component_state_changed(NiceAgent *agent, guint stream_id, guint component_id, guint state, gpointer data);
        static void cb_nice_recv(NiceAgent *agent, guint stream_id, guint component_id, guint len, gchar *buf, gpointer data);
        
#endif
    private:
        void sendH264(const std::shared_ptr<Frame> &f);
        void sendH265(const std::shared_ptr<Frame> &f);

        uint32_t convertToRTPTimestampInc(struct timeval tv);
        uint32_t nextTimestampInc();

        int32_t sendFIR(uint32_t ssrc, uint32_t media_ssrc);
        int32_t sendPLI(uint32_t ssrc, uint32_t media_ssrc);

        void insertRecv(uint16_t seq, uint32_t packetSize, uint32_t timestamp);
        void insertSend(uint32_t packetSize, uint32_t timestamp);
#ifdef USE_AUDIO_SIMULATION
        bool should_drop_packet();
        void simulate_network_conditions();
#endif
        void notifyCallback();  // 调用回调
    private:
        std::unique_ptr<hv::EventLoopThread> thread_;
        std::unique_ptr<hv::EventLoopThread> cb_thread_;
        std::shared_ptr<RTPSession> sess_;
        std::vector<uint8_t> payload_;
        uint8_t payloadType_;
        uint8_t local_payload_type_;
        uint8_t remote_payload_type_;
        bool fNextTimestampHasBeenPreset = false;
        uint32_t fTimestampBase;
        int fmt_;
        std::string bindIp_;
        int bindPort_;
        int payloadSize_;
        int timestampInc_;
        bool defaultMark_;
        int hz_;
        bool dtls_;
        bool stun_;
        std::string id_;
        // TODO: 发送和接收分开
        std::vector<RTPIPv4Address> dsts_;
        std::vector<std::string> ips_;
        std::unordered_set<std::string> ip_addrs_;
        ByteBuffer bbuf_;
        std::string lastUpdate_;
        std::shared_ptr<RtpEngine> rtpSourceProxy_;
        std::shared_ptr<RtpEngine> rtpDestinationProxy_;
        std::shared_ptr<FramePipeline> rtpProxy_;
        std::shared_ptr<FramePipeline> rtcpProxy_;
        bool dtlsReady_[2] = { false };
        bool outputRawRtp = false;
#ifdef USE_LIBNICE
        std::unique_ptr<std::thread> gthread_;
        GMainContext* context_;
        GMainLoop* loop_;

        std::atomic<bool> started_;
        std::atomic<bool> closed_;

        NiceAgent* agent_;
        std::string stun_addr_;
        uint32_t stun_port_;
        uint32_t stream_id_ = 1;
        int controlling_;

        bool ready_[2] = {false, false};
#endif
        bool first_ = true;
        uint32_t fir_index_ = 1;
        std::chrono::steady_clock::time_point last_time_point_;
        
        std::mutex stopMutex_;
        bool stopped_ = true;
        bool seqValid_ = false;
        uint32_t seq_ = 0;
        const static int kMaxSeqCheck = 1024;
        // TODO: 多目标地址
        struct RtpStatistics {
            uint64_t packetCount_ = 0;
            int64_t bpsTick_ = 0;
            std::list<uint32_t> bpsEverySecond_;
            std::list<uint32_t> packetRateEverySecond_;
            uint64_t bps_ = 0; // 取5秒的平均值
            uint32_t fps_ = 0;
            uint64_t packetRate_ = 0;
            double delay = 0.0;
            // 时间戳，用于计算发送抖动和接收抖动
            uint32_t last_rtp_ts = 0;
            uint32_t last_local_ts = 0;
            float jitter = 0.f;
        };
        enum {
            RTP_SEND,
            RTP_RECV,
        };
        RtpStatistics rtpStatistics[2];
        std::unordered_set<std::string> servers_to_ping_;
        // for lost packet
        uint16_t avSeq_ = 0;
        uint16_t avStartSeq_ = 0;
        std::set<uint16_t> avSeqSet_;
        uint64_t disorderCount_ = 0;
        uint64_t lostCount_ = 0;
#ifdef USE_AUDIO_SIMULATION
        // TEST: 测试丢包
        std::random_device rd_;
        std::mt19937 gen_;
        std::uniform_real_distribution<> uniform_dist_;
#endif
        int ptime_ = 20;
        int setted_pt_ = -1;
        CodecInst codec_inst_;

        // payloadtype映射，用于适配不同终端的payload type
        std::unordered_map<int, int> pt_map_;
        // 启用pt映射
        bool enable_pt_map_ = false;

        // FIXME: 临时方案：解决h323和sip同时使用时，payloadtype冲突的问题
        bool is_h323_ = false;

        // 时钟频率
        int recv_clock_rate_;
        int send_clock_rate_;
        // 发送rtp包的线程
        hv::EventLoopThread rtp_send_thread_;
        // rtp发送抖动需要的增量
        uint32_t prev_timestamp_inc_ = 0;

        uint32_t remote_ssrc_;
    };
}

#endif