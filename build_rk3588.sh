#!/bin/bash
###
 # @Author: luo <EMAIL>
 # @Date: 2024-04-22 09:12:44
 # @LastEditors: luo <EMAIL>
 # @LastEditTime: 2024-04-22 10:29:19
 # @FilePath: /mediamanager/build_rk3588.sh
 # @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
### 
CURDIR=$(pwd)
CROSS=aarch64-rockchip1031-linux-gnu-
rm build -rf
mkdir build/MediaManager -p
mkdir build/MediaManagerTest -p
cd ${CURDIR}/build/MediaManager
cmake ${CURDIR} -DCMAKE_INSTALL_PREFIX=$CURDIR -DCMAKE_C_COMPILER=${CROSS}gcc -DCMAKE_CXX_COMPILER=${CROSS}g++ -DENABLE_RK=true -DENABLE_VIDEO=true && make -j8 && make install
# cd ${CURDIR}/build/MediaManagerTest
# cmake ${CURDIR}/test -DCMAKE_INSTALL_PREFIX=$CURDIR -DCMAKE_C_COMPILER=${CROSS}gcc -DCMAKE_CXX_COMPILER=${CROSS}g++ && make -j4 && make install
