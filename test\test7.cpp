#include "MediaPipeline.h"
#include <json.hpp>
#include <stdio.h>
#include <ljcore/jlog.h>
#include <hv/EventLoop.h>

using namespace panocom;

int main()
{
    auto loop = std::make_shared<hv::EventLoop>();
    jlog_init(nullptr);
    nlohmann::json j;
    auto decodedDeviceToHost = FramePipeline::CreateFramePipeline("CuFrameDeviceToHostNV12");
    auto capturer = VideoFrameCapturer::CreateVideoCapturer("V4L2VideoFrameCapturer", j.dump());
    j.clear();
    j["codec"] = "jpeg";
    auto decoder = VideoFrameDecoder::CreateVideoDecoder("CuVideoDecoder", j.dump());
    j.clear();
    j["path"] = "mjpeg.yuv";
    auto fileDecoded = FileFramePipeline::CreateFileDestination("file", j.dump());
    decoder->addVideoDestination(fileDecoded);
    j.clear();
    j["codec"] = "h264";
    auto encoder = VideoFrameEncoder::CreateVideoEncoder("CuVideoEncoder", j.dump());
    j.clear();
    j["path"] = "encode.h264";
    auto fileEncoded = FileFramePipeline::CreateFileDestination("file", j.dump());
    decoder->addVideoDestination(decodedDeviceToHost);
    decodedDeviceToHost->addVideoDestination(fileDecoded);
    capturer->addVideoDestination(decoder);
    decoder->addVideoDestination(encoder);
    encoder->addVideoDestination(fileEncoded);

    loop->run();
    return 0;
}