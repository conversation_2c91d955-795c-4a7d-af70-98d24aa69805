#ifndef P_CuFrameDeviceToHost_h
#define P_CuFrameDeviceToHost_h

#include "FramePipeline.h"
#include <cuda.h>

namespace panocom
{
    class CuFrameDeviceToHost : public FramePipeline
    {
    public:
        CuFrameDeviceToHost(const std::string& jsonParams);
        ~CuFrameDeviceToHost() override = default;

        void onFrame(const std::shared_ptr<Frame> &f) override; 
    private:
        int cudaId_ = 0;
        int gpuIndex_ = 0;
    };

    class CuFrameDeviceToHostNV12 : public FramePipeline
    {
    public:
        CuFrameDeviceToHostNV12(const std::string& jsonParams);
        ~CuFrameDeviceToHostNV12() override = default;

        void onFrame(const std::shared_ptr<Frame> &f) override;
    private:
        int cudaId_ = 0;
        int gpuIndex_ = 0;
    };
}

#endif