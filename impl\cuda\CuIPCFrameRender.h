// created by gyj 2024-3-15
#ifndef P_CuIPCFrameRender_h
#define P_CuIPCFrameRender_h

#include "VideoFrameRender.h"
#include "CuCommon.h"
#include <unordered_map>
#include <cuda.h>
#include <nvcuvid.h>
#include <driver_types.h>
#include <hv/EventLoopThread.h>

namespace panocom
{
    class CuIPCFrameRender : public VideoFrameRender
    {
    public:
        CuIPCFrameRender(const std::string& jsonParams);
        ~CuIPCFrameRender();

        void onFrame(const std::shared_ptr<Frame> &frame) override;
        void start() override;
        void stop() override;

        void setFrameTimeoutNotify(int ms, void* param, const TimeoutNotifyCallback& cb) override;
        void clearCache() override;
    private:
        uint32_t convertToRTPTimestamp(struct timeval tv);
        uint32_t presetNextTimestamp();
    private:
        FrameShm* findFrameToFill();

    private:
        SharedMemory shm_;
        FrameShm* frame_;
        int frameCount_;    //上一半用于写，下一半用于读（回收）
        hv::EventLoopThread thread_;
        std::unordered_map<IpcMemHandle, std::shared_ptr<Frame>, HandleHash> ipcFrames_;
        uint32_t seq_;
        uint32_t timestampBase_;
        int cudaId_ = 0;
        int gpuIndex_ = 0;
        int dev_ = 0;
        std::mutex mutex_;

        hv::EventLoopThread check_timeout_thread_;

        int frames_recieved_ = 0;
        int frames_to_render_ = 0;
        std::chrono::steady_clock::time_point last_tick_;
    };
}

#endif