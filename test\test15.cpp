#include "MediaPipeline.h"
#include "Frame.h"
#include <json.hpp>
#include <stdio.h>
#include <ljcore/jlog.h>
#include <string>
#include <hv/EventLoop.h>

using namespace panocom;

int main(int argc, char *argv[])
{
    auto loop = std::make_shared<hv::EventLoop>();

    jlog_init(nullptr);

    nlohmann::json j;
    nlohmann::json dst;
    j["bindPort"] = atoi(argv[1]);
    j["fmt"] = FRAME_FORMAT_OPUS_16000_1;
    dst["ip"] = argv[2];
    dst["port"] = atoi(argv[3]);
    j["dst"].push_back(dst);
    auto rtp = RtpEngine::CreateRTPEngine("jrtp", j.dump());

    j.clear();
    j["codec"] = "opus";
    auto encoder = AudioFrameEncoder::CreateAudioEncoder("native", j.dump());
    auto decoder = AudioFrameDecoder::CreateAudioDecoder("native", j.dump());

    j.clear();
    auto asfarend = AudioFrameProcesser::CreateAudioProcesser("AudioFrameAsFarEnd");
    auto aec = AudioFrameProcesser::CreateAudioProcesser("RtcAudioFrameAec");
    auto ns = AudioFrameProcesser::CreateAudioProcesser("RnnAudioFrameNS");
    auto pacer = AudioFrameProcesser::CreateAudioProcesser("AudioFramePacer");

    j["src"]["channel"] = 1;
    j["src"]["samplerate"] = 16000;
    j["dst"]["channel"] = 1;
    j["dst"]["samplerate"] = 48000;
    auto resample16kTo48k = AudioFrameProcesser::CreateAudioProcesser("AudioFrameResampler", j.dump());

    j["src"]["channel"] = 1;
    j["src"]["samplerate"] = 48000;
    j["dst"]["channel"] = 1;
    j["dst"]["samplerate"] = 16000;
    auto resample48kTo16k = AudioFrameProcesser::CreateAudioProcesser("AudioFrameResampler", j.dump());

    j.clear();
    j["dev"] = atoi(argv[2]);
    auto capturer = AudioFrameCapturer::CreateAudioCapturer("Sdl2AudioFrameCapturer", j.dump());
    auto render = AudioFrameRender::CreateAudioRender("Sdl2AudioFrameRender");

    j.clear();
    j["path"] = std::string(argv[1]) + "-s.pcm";
    auto fileCapture = FileFramePipeline::CreateFileDestination("file", j.dump());

    j["path"] = std::string(argv[1]) + "-r.pcm";
    auto fileRender = FileFramePipeline::CreateFileDestination("file", j.dump());

    rtp->addAudioDestination(decoder);
    decoder->addAudioDestination(fileRender);
    decoder->addAudioDestination(render);
    decoder->addAudioDestination(asfarend);
    asfarend->addAudioDestination(aec);

    capturer->addAudioDestination(pacer)
        ->addAudioDestination(aec)
        ->addAudioDestination(resample16kTo48k)
        ->addAudioDestination(ns)
        ->addAudioDestination(resample48kTo16k)
        ->addAudioDestination(encoder)
        ->addAudioDestination(rtp);
    resample48kTo16k->addAudioDestination(fileCapture);
    
    loop->run();
    return 0;
}