#include "FrameSelector.h"
#include <json.hpp>
#include <ljcore/jlog.h>

#include "Frame.h"
#ifdef ENABLE_VIDEO
#endif
namespace panocom {
std::shared_ptr<FrameSelector> FrameSelector::Create(const std::string &jsonParam) {
    nlohmann::json j;
    if (!jsonParam.empty())
        j = nlohmann::json::parse(jsonParam);
    return std::make_shared<FrameSelector>(jsonParam);
}

bool FrameSelector::IsFrameSelector(const FramePipeline::Ptr& ptr) {
    if (ptr->name() == "FrameSelector")
    {
        return true;
    }
    return false;
}

void FrameSelector::registerInput(FramePipeline::Ptr input) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (input)
        inputs_.push_back(input);
}

void FrameSelector::unregisterInput(FramePipeline::Ptr input) {
    std::lock_guard<std::mutex> lock(mutex_);
    for (auto it = inputs_.begin(); it != inputs_.end(); ++it) {
        if (it->lock() == input) {
            inputs_.erase(it);
            return;
        }
    }
}

FramePipeline::Ptr FrameSelector::getSelectedInput() {
    return selected_.lock();
}

void FrameSelector::setSelectedInput(FramePipeline::Ptr input) {
    jinfo("FrameSelector::setSelectedInput: %s", input.get() ? input->name().c_str() : "nullptr");
    std::lock_guard<std::mutex> lock(mutex_);
    for (auto it = inputs_.begin(); it != inputs_.end(); ++it) {
        if (it->lock() == input) {
            selected_ = *it;
            return;
        }
    }
    if (!selected_.lock()) {
        // 如果没有找到，则选择第一个输入作为默认值
        if (inputs_.size() > 0) {
            selected_ = *inputs_.begin();
        }
    }
    // priority_ = selected_;
}

void FrameSelector::setPriorityInput(FramePipeline::Ptr input) {
   jinfo("FrameSelector::setPriorityInput: %s", input.get() ? input->name().c_str() : "nullptr");
    std::lock_guard<std::mutex> lock(mutex_);
    for (auto it = inputs_.begin(); it != inputs_.end(); ++it) {
        if (it->lock() == input) {
            priority_ = *it;
            return;
        }
    }
}

FrameSelector::FrameSelector(const std::string &jsonParam) : strategy_(SelectStrategy::Priority), isTimeout_(true)
{
    name_ = "FrameSelector";
    lastFrameTime_ = std::chrono::steady_clock::now();
    if (strategy_ != SelectStrategy::Manual) {
        timer_.start();
        timer_.loop()->setInterval(30, [this](hv::TimerID id) {
            if (!isTimeout_ && isTimeout()) {
                jinfo("FrameSelector: timeout");
                selectNextInput();      
                isTimeout_ = true;          
            }
        });
    }
}

FrameSelector::~FrameSelector()
{
    jinfo("FrameSelector::~FrameSelector");
    {
        std::lock_guard<std::mutex> lock(mutex_);
        // clear all subscribers
        selected_.reset();
        if (inputs_.size() > 0)
            inputs_.clear();
    }
    jinfo("FrameSelector::~FrameSelector: %s", name().c_str());
}

void FrameSelector::onFrame(const std::shared_ptr<Frame> &frame)
{
    if (strategy_ == SelectStrategy::Priority) {
        if (priority_.lock() && frame->isInSourceList((void*)priority_.lock().get()) /*(void *)priority_.lock().get() == frame->getSource()*/) {
            if (isTimeout_) {
                selected_ = priority_;
                isTimeout_ = false;
            }
            lastFrameTime_ = std::chrono::steady_clock::now();
            deliverFrame(frame);
            return;
        }
    }
    if (!selected_.lock()) {
        return;
    }
    if (frame->getSource() == (void*)selected_.lock().get())
    {
        deliverFrame(frame);
    }
}

void FrameSelector::selectNextInput() {
    std::unique_lock<std::mutex> lock(mutex_);
    auto it = inputs_.begin();
    for (; it != inputs_.end(); ++it) {
        if (selected_.lock() == it->lock()) { // 找到当前选中的输入，选择下一个输入
            break;
        }
    }
    if (it == inputs_.end()) { // 没有找到当前选中的输入，则不进行处理
        return;
    }
    ++it; // 选择下一个输入
    if (it == inputs_.end()) {
        it = inputs_.begin();
    }
    selected_ = *it;
    jinfo("select next input: %s, priority input: %s", selected_.lock()->name().c_str(), priority_.lock()->name().c_str());
}

bool FrameSelector::isTimeout() {
    return (std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::steady_clock::now() - lastFrameTime_).count() > timeout_);
}
}