#include "UlpFecEncoder.h"
#include "RtpHeader.h"
#include <json.hpp>
#include <webrtc/system_wrappers/include/clock.h>
#include <webrtc/modules/rtp_rtcp/source/rtp_packet.h>

using namespace panocom;

UlpFecEncoder::UlpFecEncoder(const std::string& jsonParams)
{
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    red_payload_type_ = 127;
    if (j.contains("redPayloadType"))
    {
        red_payload_type_ = j["redPayloadType"];
    }
    ulpfec_payload_type_ = 125;
    if (j.contains("ulpfecPayloadType"))
    {
        ulpfec_payload_type_ = j["ulpfecPayloadType"];
    }
    payload_type_ = 96;
    if (j.contains("payloadType"))
    {
        payload_type_ = j["payloadType"];
    }
    ulpfec_generator_ = std::make_unique<UlpfecGenerator>(red_payload_type_, ulpfec_payload_type_, Clock::GetRealTimeClock());
}

UlpFecEncoder::~UlpFecEncoder()
{

}

void UlpFecEncoder::onFrame(const std::shared_ptr<Frame> &f)
{
    if (f->getFrameFormat() == FRAME_FORMAT_RTP)
    {
        RtpPacketToSend rtp_packet(nullptr);
        rtp_packet.Parse(f->getFrameBuffer(), f->getFrameSize());
        ulpfec_generator_->AddPacketAndGenerateFec(rtp_packet);
        auto fec_packets = ulpfec_generator_->GetFecPackets();

        for (auto& packet : fec_packets)
        {
            auto f = Frame::CreateFrame(FRAME_FORMAT_ULPFEC);
            f->createFrameBuffer(packet->size());
            memcpy(f->getFrameBuffer(), packet->data(), packet->size());
            deliverFrame(f);
        }
    }
}