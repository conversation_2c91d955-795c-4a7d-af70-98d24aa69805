// created by gyj 2024-2-20
#ifndef P_V4L2VideoFrameCapturer_h
#define P_V4L2VideoFrameCapturer_h

#include "VideoFrameCapturer.h"
#include "FrameBufferManager.h"
#include "V4l2Capture.h"
#include <thread>
#include <mutex>
#include <condition_variable>
#include <memory>
#include <linux/videodev2.h>
#include <sys/times.h>


namespace panocom
{
    class V4L2VideoFrameCapturer : public VideoFrameCapturer
    {
    public:
        V4L2VideoFrameCapturer(const std::string& jsonParams);
        ~V4L2VideoFrameCapturer() override;
        void start() override;
        void stop() override;
    private:
        std::unique_ptr<std::thread> thread_;
        bool running_ = false;
        std::unique_ptr<V4l2Capture> videoCapture_;

        int dev_;
        std::string format_;
        int fmt_;
        int width_;
        int height_;
        int fps_ = 0;

        std::unique_ptr<FrameBufferManager> frameBufferManager_;
    };
}

#endif