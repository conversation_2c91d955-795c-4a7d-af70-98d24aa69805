#include "rk_video_encoder.h"
#include <json.hpp>
#include "Frame.h"
#include <rga/im2d.hpp>
#include <rga/rga.h>
#include "drm_utils.h"
namespace panocom {

namespace {
std::unordered_map<std::string, RgaSURF_FORMAT> tab_s_rgapixel {
	{"nv12", RK_FORMAT_YCbCr_420_SP},
	{"nv16", RK_FORMAT_YCbCr_422_SP},
    // {"nv24", RK_FORMAT_YCbCr_444_SP},    // 不支持nv24的rga转换
    {"bgr3", RK_FORMAT_BGR_888}
};
}

MppBufferGroup RKVideoEncoder::m_buf_grp = NULL;
MppBufferGroup RKVideoEncoder::m_frm_buf_grp = NULL;
static std::once_flag flag;
static std::once_flag frm_flag;

RKVideoEncoder::RKVideoEncoder(const std::string &jsonParams)
    : fps_(30)
    , gop_(30)
    , width_(1920)
    , height_(1080)
    , bitrate_(2048)
    , max_bitrate_(-1)
    , min_bitrate_(-1)
    , codec_type_(MPP_VIDEO_CodingAVC)
    , frm_fmt_(MPP_FMT_YUV420SP)
    , max_queue_size_(20)
    , profile_idc_(66)
    , level_idc_(42)
    , cabac_en_(1) {
    name_ = "RKVideoEncoder";
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    if (j.contains("fps")) fps_ = j["fps"];
	input_fps_ = fps_;
	if (j.contains("codec")) {
		codec_name_ = j["codec"];
		if (strcasecmp(codec_name_.c_str(), "H264") == 0) codec_type_ = MPP_VIDEO_CodingAVC;
		else if (strcasecmp(codec_name_.c_str(), "H265") == 0) codec_type_ = MPP_VIDEO_CodingHEVC;		
	}
	if (j.contains("format")) {
		std::string format = j["format"];
		if (strcasecmp(format.c_str(), "I420") == 0 || strcasecmp(format.c_str(), "YUV420P") == 0) frm_fmt_ = MPP_FMT_YUV420P;
		else if (strcasecmp(format.c_str(), "NV12") == 0 || strcasecmp(format.c_str(), "YUV420SP") == 0) frm_fmt_ = MPP_FMT_YUV420SP;
		else if (strcasecmp(format.c_str(), "NV21") == 0) frm_fmt_ = MPP_FMT_YUV420SP_VU;
		else if (strcasecmp(format.c_str(), "NV16") == 0 || strcasecmp(format.c_str(), "YUV422SP") == 0) frm_fmt_ = MPP_FMT_YUV422SP;
		else if (strcasecmp(format.c_str(), "BGR888") == 0 || strcasecmp(format.c_str(), "BGR3") == 0) frm_fmt_ = MPP_FMT_BGR888;
	}
	rc_mode_ = MPP_ENC_RC_MODE_VBR;
	if (j.contains("rc")) {
		std::string rc_mode = j["rc"];
		if (strcasecmp(rc_mode.c_str(), "CBR") == 0) rc_mode_ = MPP_ENC_RC_MODE_CBR;
		else if (strcasecmp(rc_mode.c_str(), "VBR") == 0) rc_mode_ = MPP_ENC_RC_MODE_VBR;
		else if (strcasecmp(rc_mode.c_str(), "AVBR") == 0) rc_mode_ = MPP_ENC_RC_MODE_AVBR;
		else if (strcasecmp(rc_mode.c_str(), "FIXQP") == 0) rc_mode_ = MPP_ENC_RC_MODE_FIXQP;
	}

	// TODO: CBR需要固定码率，VBR需要设置最小和最大码率
	if (j.contains("bitrate")) bitrate_ = j["bitrate"];
	if (j.contains("maxBitrate")) max_bitrate_ = j["maxBitrate"];
	if (j.contains("minBitrate")) min_bitrate_ = j["minBitrate"];
	if (j.contains("gop")) gop_ = j["gop"];
	if (j.contains("width")) width_ = j["width"];
	if (j.contains("height")) height_ = j["height"];
	if (j.contains("porfileidc")) {
		std::string profile_idc = j["porfileidc"];
		if (strcasecmp(profile_idc.c_str(), "Baseline") == 0) profile_idc_ = 66;
		else if (strcasecmp(profile_idc.c_str(), "Main") == 0) profile_idc_ = 77;
		else if (strcasecmp(profile_idc.c_str(), "High") == 0) profile_idc_ = 100;	
	}
	if (j.contains("enableCabac")) cabac_en_ = j["enableCabac"];
	hor_stride_ = MPP_ALIGN(width_, 16);
	ver_stride_ = MPP_ALIGN(height_, 16);
	frameBufferManager_ = std::make_shared<RKBufferManager>(6);
	auto ret = InitEncode();
	if (MPP_OK == ret) {
		ResetStatistics();
		loop_thread_ = std::make_shared<hv::EventLoopThread>();
		loop_ = loop_thread_->loop();
		loop_thread_->start();

        notifycallbacks_thread_.loop()->setInterval(1000, [this](hv::TimerID id) {
            if (notifycallbacks_.find("VideoEncoderStatus") != notifycallbacks_.end() && notifycallbacks_["VideoEncoderStatus"].cb2) {
                nlohmann::json notify;
                notify["encoder"]["type"] = "VideoEncoderStatus";
                notify["encoder"]["codecType"] = "H264";
                notify["encoder"]["width"] = width_;
                notify["encoder"]["height"] = height_;
                notifycallbacks_["VideoEncoderStatus"].cb2(
                    notifycallbacks_["VideoEncoderStatus"].id, "VideoEncoderStatus", notify.dump(), notifycallbacks_["VideoEncoderStatus"].param);
            }
        });
        notifycallbacks_thread_.start();
    } else {
		jerror("Failed to initEncode ret = %d", ret);
	}
	fmt_ = FRAME_FORMAT_H264;
	switch (codec_type_) {
		case MPP_VIDEO_CodingAVC: {
			fmt_ = FRAME_FORMAT_H264;
		} break;
		case MPP_VIDEO_CodingHEVC: {
			fmt_ = FRAME_FORMAT_H265;
		} break;
		default: break;
	}
}

RKVideoEncoder::~RKVideoEncoder()
{
	// {
	// 	std::unique_lock<std::mutex> lock(mutex_);
	// 	running_ = false;
	// 	cond_.notify_all();
	// 	auto status = encode_future_.wait_for(std::chrono::milliseconds(200));
	// 	if (std::future_status::timeout == status) {
	// 		//TODO: 释放超时处理
	// 	} else if (std::future_status::ready == status) {
	// 		encode_future_.get();
	// 	}
	// }
	jinfo("RKVideoEncoder Release!");
	if (loop_thread_) {
		loop_thread_->stop(true);
		loop_thread_->join();
	}
    if (notifycallbacks_thread_.isRunning()) {
        notifycallbacks_thread_.stop(true);
        notifycallbacks_thread_.join();
    }
	Release();
	if (ofs_.is_open()) ofs_.close();
	jinfo("RKVideoEncoder Release success!");
}

void RKVideoEncoder::onFrame(const std::shared_ptr<Frame>& frame) {
    /* Configure the current thread to use only RGA3_core0 or RGA3_core1. */
    imconfig(IM_CONFIG_SCHEDULER_CORE, IM_SCHEDULER_RGA3_CORE0 | IM_SCHEDULER_RGA3_CORE1); // IM_SCHEDULER_RGA2_CORE0 只支持32位地址，rk3588先写死用rga3
	if (frame->getFrameFormat() == FRAME_FORMAT_RK) {
		auto wrap_frame = std::dynamic_pointer_cast<WrapRKMppFrame>(frame);
		auto temp_frame = std::make_shared<WrapRKMppFrame>();
		if (wrap_frame->frame_) {
			auto buffer = mpp_frame_get_buffer(wrap_frame->frame_);
			if (!buffer) return;
			int src_fd = mpp_buffer_get_fd(buffer);
			auto ret = frameBufferManager_->getFreeFrame(width_, height_, &temp_frame->frame_);
			if (ret) {
				jerror("RKVideoEncoder mpp_frame_init failed ret = %d", ret);
				if (temp_frame->frame_) {
					mpp_frame_deinit(&temp_frame->frame_);
					temp_frame->frame_ = nullptr;
				}
				return;
			}
			if (temp_frame->frame_) {
				auto dst_buffer = mpp_frame_get_buffer(temp_frame->frame_);
				if (!dst_buffer) return;
				int dst_fd = mpp_buffer_get_fd(dst_buffer);
                bool res = DrmUtils::rga_copy(
                    dst_fd, width_, height_, RK_FORMAT_YCbCr_420_SP, src_fd, wrap_frame->width(), wrap_frame->height(), wrap_frame->hStride(),
                    wrap_frame->vStride(), tab_s_rgapixel[wrap_frame->format_str_]);
            }
		}
        if (loop_ && temp_frame && temp_frame->frame_) {
			// if (frame_queue_.size() > 30) {
			// 	frame_queue_.pop();
			// }
            // frame_queue_.emplace(f);
            loop_->runInLoop([this, temp_frame] {
                // if (frame_queue_.empty())
                //     return;
                // auto frame = frame_queue_.front();
                // frame_queue_.pop();
                MppFrame rk_frame = temp_frame->InnerFrame();
                Encode(rk_frame);
            });
        }
    }
}

int RKVideoEncoder::updateParam(const std::string & jsonParams)
{
	int change = 0;
    int32_t bitrate;

	nlohmann::json j;
	if (jsonParams != "")
	{
		j = nlohmann::json::parse(jsonParams);
	}
	if (j.contains("vidcodec-bitrate"))
	{
		bitrate = j["vidcodec-bitrate"];
		change |= (1 << BITRATE_CHANGE);
	}

	if (change)
	{
		if (loop_)
		{
			loop_->runInLoop([this, change, bitrate] {
                int64_t now = GetTickCount();
                change_bitrate_ = bitrate;
                int64_t timePass = now - lastChangeBitrateTick_;
                if (timePass >= 2000)
                {
                    lastChangeBitrateTick_ = now;
                    ChangeEncConfig(change);
                }
                else if (!pending_change_bitrate_)
                {
                    pending_change_bitrate_ = true;
                    lastChangeBitrateTick_ = now;
                    loop_->setTimeout(2000 - timePass, [this, change](hv::TimerID) {
                        ChangeEncConfig(change);
                        lastChangeBitrateTick_ = GetTickCount();
                        pending_change_bitrate_ = false;
                    });
                }
			});
		}
		else
		{
			return -1;
		}
	}
	return 0;
}

void RKVideoEncoder::onFeedbackFrame(const std::shared_ptr<Frame>& f)
{
    if (f->getFrameFormat() == FRAME_FORMAT_VIDEO_FEEDBACK)
    {
        std::string jsonStr((char*)f->getFrameBuffer(), f->getFrameBufferSize());
        updateParam(jsonStr);
    }
}

MPP_RET RKVideoEncoder::InitEncode() {
	MPP_RET ret = MPP_OK;
    try {
        std::call_once(flag, [this, &ret]() {
            MPP_RET ret = mpp_buffer_group_get_internal(&m_buf_grp, MPP_BUFFER_TYPE_DRM);
            if (MPP_OK != ret) {
                throw std::exception();
            }
        });
    } catch (...) {
		return ret;
    }
    // try {
    //     std::call_once(flag, [this, &ret]() {
	// 		MppBufferType type = (MppBufferType)(MPP_BUFFER_TYPE_DMA_HEAP | MPP_BUFFER_FLAGS_DMA32);
    //         MPP_RET ret = mpp_buffer_group_get_internal(&m_buf_grp, type);
    //         if (MPP_OK != ret) {
    //             throw std::exception();
    //         }
	// 		uint32_t w_stride = MPP_ALIGN(1920, 16);
	// 		uint32_t h_stride = MPP_ALIGN(1080, 16);
	// 		int buf_size = w_stride * h_stride * 3 / 2;

	// 		ret = mpp_buffer_group_limit_config(m_buf_grp, buf_size, 4);
	// 		if (MPP_OK != ret) {
	// 			printf("limit buffer group failed ret %d\n", 4);
	// 			throw std::exception();
	// 		}
    //     });
    // } catch (...) {
	// 	return ret;
    // }
    uint32_t frame_size = 0;
	if (frm_fmt_ == MPP_FMT_YUV420SP || frm_fmt_ == MPP_FMT_YUV420P) {
		frame_size = hor_stride_ * ver_stride_ * 3 / 2;
	} else if (frm_fmt_ == MPP_FMT_YUV422SP) {
		frame_size = hor_stride_ * ver_stride_ * 2;
	} else if (frm_fmt_ == MPP_FMT_BGR888) {
		frame_size = width_ * height_ * 3;
	}
	else if (frm_fmt_ <= MPP_FMT_YUV422_UYVY) {
		hor_stride_ *= 2;
		frame_size = hor_stride_ * ver_stride_;
	} else {
		frame_size = hor_stride_ * ver_stride_ * 4;
	}
	
    hor_stride_ = MPP_ALIGN(width_, 16);
    ver_stride_ = MPP_ALIGN(height_, 16);
    frame_size = MPP_ALIGN(hor_stride_, 64) * MPP_ALIGN(ver_stride_, 64) * 3 / 2 / 4;
	
	uint32_t mdinfo_size = ((codec_type_ == MPP_VIDEO_CodingAVC) ? (MPP_ALIGN(hor_stride_, 64) >> 6) * (MPP_ALIGN(ver_stride_, 64) >> 6) * 32 : (MPP_ALIGN(hor_stride_, 64) >> 6) * (MPP_ALIGN(ver_stride_, 16) >> 4) * 8);
	for (int i = 0; i < 3 && m_packets.size() < 3; ++i) {
		MppBuffer buf;
		MppPacket packet;
		ret = mpp_buffer_get(m_buf_grp, &buf, frame_size);
		if (MPP_OK != ret) {
			return ret;
		}
		mpp_packet_init_with_buffer(&packet, buf);
		// mpp_buffer_put(buf);
		m_packets.push_back(packet);
	}
	ret = mpp_buffer_get(m_buf_grp, &m_md_info, mdinfo_size);
	if (MPP_OK != ret) {
		Release();
		return ret;
	}
	ret = mpp_create(&mpp_ctx_, &mpp_api_);
	if (MPP_OK != ret) {
		Release();
		return ret;
	}
	MppPollType timeout = MPP_POLL_BLOCK;
	ret = mpp_api_->control(mpp_ctx_, MPP_SET_OUTPUT_TIMEOUT, &timeout);
	if (MPP_OK != ret) {
		Release();
		return ret;
	}
	ret = mpp_init(mpp_ctx_, MPP_CTX_ENC, codec_type_);
	if (MPP_OK != ret) {
		Release();
		return ret;
	}

	ret = mpp_enc_cfg_init(&m_cfg);
	if (ret) {
		jerror("mpp_enc_cfg_init failed ret %d", ret);
		return ret;
	}	
	ret = MppSetup2();
	if (MPP_OK != ret) {
		Release();
		return ret;
	}
	jinfo("Init RKVideoEncoder %dx%d fps %d successfully!", width_, height_, fps_);
    return ret;

}

void RKVideoEncoder::Release() {
	// if (frm_buf_) {
	// 	mpp_buffer_put(frm_buf_);
	// 	frm_buf_ = nullptr;
	// }
    for (auto &packet: m_packets) {
		auto buffer = mpp_packet_get_buffer(packet);
		if (buffer) mpp_buffer_put(buffer);
        mpp_packet_deinit(&packet);
    }
    if (m_md_info) {
        mpp_buffer_put(m_md_info);
    }
	if (mpp_ctx_) {
		if (mpp_api_) {
			mpp_api_->reset(mpp_ctx_);
			mpp_api_ = nullptr;
		}
		mpp_destroy(mpp_ctx_);
		mpp_ctx_ = nullptr;
	}
	while (!frame_queue_.empty()) {
		frame_queue_.pop();
	}
	if (m_cfg) {
		mpp_enc_cfg_deinit(m_cfg);
		m_cfg = nullptr;
		jinfo("MPP Encoder: MPPConfig: cfg deinit done!");
	}
}

MPP_RET RKVideoEncoder::MppSetup() {
	MPP_RET ret;
    MppEncPrepCfg prep_cfg { 0 };
    prep_cfg.change = MPP_ENC_PREP_CFG_CHANGE_INPUT | MPP_ENC_PREP_CFG_CHANGE_ROTATION | MPP_ENC_PREP_CFG_CHANGE_FORMAT;
    prep_cfg.width = width_;
    prep_cfg.height = height_;
    prep_cfg.hor_stride = hor_stride_;
    prep_cfg.ver_stride = ver_stride_;
    prep_cfg.format = frm_fmt_;
    prep_cfg.rotation = MPP_ENC_ROT_0;
    ret = mpp_api_->control(mpp_ctx_, MPP_ENC_SET_PREP_CFG, &prep_cfg);
    if (MPP_OK != ret) {
		jerror("MPP_ENC_SET_PREP_CFG failed ret = %d\n", ret);
        return ret;
    }
    MppEncRcCfg rc_cfg { 0 };
	// TODO: CBR or VBR
	rc_cfg.change = MPP_ENC_RC_CFG_CHANGE_ALL;
	rc_cfg.rc_mode = rc_mode_;
	rc_cfg.quality = MPP_ENC_RC_QUALITY_MEDIUM;
	int32_t bps = bitrate_ * 1024;
    if (rc_cfg.rc_mode == MPP_ENC_RC_MODE_CBR) {
        /* constant bitrate has very small bps range of 1/16 bps */
        rc_cfg.bps_target   = bps;
        rc_cfg.bps_max      = bps * 17 / 16;
        rc_cfg.bps_min      = bps * 15 / 16;
    } else if (rc_cfg.rc_mode ==  rc_mode_) {
        if (rc_cfg.quality == MPP_ENC_RC_QUALITY_CQP) {
            /* constant QP does not have bps */
            rc_cfg.bps_target   = -1;
            rc_cfg.bps_max      = -1;
            rc_cfg.bps_min      = -1;
        } else {
            /* variable bitrate has large bps range */
            rc_cfg.bps_target   = bps;
            rc_cfg.bps_max      = bps * 17 / 16;
            rc_cfg.bps_min      = bps * 1 / 16;
        }
    }
    /* fix input / output frame rate */
    rc_cfg.fps_in_flex      = 0;
    rc_cfg.fps_in_num       = fps_;
    rc_cfg.fps_in_denorm    = 1;
    rc_cfg.fps_out_flex     = 0;
    rc_cfg.fps_out_num      = fps_;
    rc_cfg.fps_out_denorm   = 1;

    rc_cfg.gop              = gop_;
    rc_cfg.skip_cnt         = 0;
	
	ret = mpp_api_->control(mpp_ctx_, MPP_ENC_SET_RC_CFG, &rc_cfg);
	if (MPP_OK != ret) {
		jerror("MPP_ENC_SET_RC_CFG failed ret = %d\n", ret);
		return ret;
	}

    MppEncCodecCfg codec_cfg;
    codec_cfg.coding = codec_type_;
    switch (codec_cfg.coding) {
        case MPP_VIDEO_CodingAVC: {
            codec_cfg.h264.change = MPP_ENC_H264_CFG_CHANGE_PROFILE | MPP_ENC_H264_CFG_CHANGE_ENTROPY | MPP_ENC_H264_CFG_CHANGE_TRANS_8x8;

            /*
             * H.264 profile_idc parameter
             * 66  - Baseline profile
             * 77  - Main profile
             * 100 - High profile
             */
            codec_cfg.h264.profile = 100;
            /*
             * H.264 level_idc parameter
             * 10 / 11 / 12 / 13    - qcif@15fps / cif@7.5fps / cif@15fps / cif@30fps
             * 20 / 21 / 22         - cif@30fps / half-D1@@25fps / D1@12.5fps
             * 30 / 31 / 32         - D1@25fps / 720p@30fps / 720p@60fps
             * 40 / 41 / 42         - 1080p@30fps / 1080p@30fps / 1080p@60fps
             * 50 / 51 / 52         - 4K@30fps
             */
            codec_cfg.h264.level = 40;
            codec_cfg.h264.entropy_coding_mode = 1;
            codec_cfg.h264.cabac_init_idc = 0;
            codec_cfg.h264.transform8x8_mode = 1;
			codec_cfg.h264.qp_max = 40;
			codec_cfg.h264.qp_max_i = 40;
			codec_cfg.h264.qp_min = 10;
			codec_cfg.h264.qp_min_i = 10;
			codec_cfg.h264.qp_delta_ip = 2;
			codec_cfg.h264.profile = 66;
        } break;
        case MPP_VIDEO_CodingMJPEG: {
            codec_cfg.jpeg.change = MPP_ENC_JPEG_CFG_CHANGE_QP;
            codec_cfg.jpeg.quant = 10;
        } break;
        case MPP_VIDEO_CodingVP8: {
        } break;
        case MPP_VIDEO_CodingHEVC: {
            codec_cfg.h265.change = MPP_ENC_H265_CFG_INTRA_QP_CHANGE;
            codec_cfg.h265.intra_qp = 26;
        } break;
        default: {
			// TODO: error log
        } break;
    }

	ret = mpp_api_->control(mpp_ctx_, MPP_ENC_SET_CODEC_CFG, &codec_cfg);
	if (MPP_OK != ret) {
		jerror("MPP_ENC_SET_CODEC_CFG failed ret = %d", ret);
		return ret;
	}
	auto sei_mode = MPP_ENC_SEI_MODE_ONE_FRAME;
	ret = mpp_api_->control(mpp_ctx_, MPP_ENC_SET_SEI_CFG, &sei_mode);
	if (MPP_OK != ret) {
		jerror("MPP_ENC_SET_SEI_CFG failed ret = %d", ret);
		return ret;
	}

	if (codec_type_ == MPP_VIDEO_CodingAVC || codec_type_ == MPP_VIDEO_CodingHEVC) {
		MppEncHeaderMode header_mode = MPP_ENC_HEADER_MODE_EACH_IDR;
        ret = mpp_api_->control(mpp_ctx_, MPP_ENC_SET_HEADER_MODE, &header_mode);
        if (MPP_OK != ret) {
            jerror("mpi control enc set header mode failed ret %d", ret);
            return ret;
        }
	}
	return ret;
}

MPP_RET RKVideoEncoder::MppSetup2() {
    MPP_RET ret;
	int res = 0;
    res |= mpp_enc_cfg_set_s32(m_cfg, "prep:width", width_);
    res |= mpp_enc_cfg_set_s32(m_cfg, "prep:height", height_);
    res |= mpp_enc_cfg_set_s32(m_cfg, "prep:hor_stride", hor_stride_);
    res |= mpp_enc_cfg_set_s32(m_cfg, "prep:ver_stride", ver_stride_);
    res |= mpp_enc_cfg_set_s32(m_cfg, "prep:format", frm_fmt_);

    res |= mpp_enc_cfg_set_s32(m_cfg, "rc:mode", rc_mode_);

    /* fix input / output frame rate */
    res |= mpp_enc_cfg_set_s32(m_cfg, "rc:fps_in_num", input_fps_);
	res |= mpp_enc_cfg_set_s32(m_cfg, "rc:fps_in_denorm", 1);
	res |= mpp_enc_cfg_set_s32(m_cfg, "rc:fps_out_flex", 0);
    res |= mpp_enc_cfg_set_s32(m_cfg, "rc:fps_out_num", fps_);
    res |= mpp_enc_cfg_set_s32(m_cfg, "rc:fps_out_denorm", 1);
    res |= mpp_enc_cfg_set_s32(m_cfg, "rc:gop", gop_);

    /* drop frame or not when bitrate overflow */
    res |= mpp_enc_cfg_set_u32(m_cfg, "rc:drop_mode", MPP_ENC_RC_DROP_FRM_DISABLED);
    res |= mpp_enc_cfg_set_u32(m_cfg, "rc:drop_thd", 20);        /* 20% of max bps */
    res |= mpp_enc_cfg_set_u32(m_cfg, "rc:drop_gap", 1);        
     /* Do not continuous drop frame */

    /* setup bitrate for different rc_mode */
	int32_t bps = bitrate_ * 1024;
	int32_t min_bps = min_bitrate_ * 1024;
	int32_t max_bps = max_bitrate_ * 1024;

    res |= mpp_enc_cfg_set_s32(m_cfg, "rc:qp_init", -1);
    res |= mpp_enc_cfg_set_s32(m_cfg, "rc:qp_max", 40);
    res |= mpp_enc_cfg_set_s32(m_cfg, "rc:qp_min", 10);
    res |= mpp_enc_cfg_set_s32(m_cfg, "rc:qp_max_i", 40);
    res |= mpp_enc_cfg_set_s32(m_cfg, "rc:qp_min_i", 10);
    res |= mpp_enc_cfg_set_s32(m_cfg, "rc:qp_ip", 2);

	// // 呼吸效应
    // res |= mpp_enc_cfg_set_s32(m_cfg, "rc:debreath_en", 1);
    // res |= mpp_enc_cfg_set_s32(m_cfg, "rc:debreath_strength", 16);

	switch (rc_mode_)
	{
	case MPP_ENC_RC_MODE_CBR: {
    	res |= mpp_enc_cfg_set_s32(m_cfg, "rc:bps_target", bps);
		res |= mpp_enc_cfg_set_s32(m_cfg, "rc:bps_max", bps * 17 / 16);
		res |= mpp_enc_cfg_set_s32(m_cfg, "rc:bps_min", bps * 15 / 16);		
		break;
	}
	case MPP_ENC_RC_MODE_AVBR:
		res |= mpp_enc_cfg_set_s32(m_cfg, "rc:qp_max", 40);
		res |= mpp_enc_cfg_set_s32(m_cfg, "rc:qp_min", 20);
		res |= mpp_enc_cfg_set_s32(m_cfg, "rc:qp_max_i", 40);
		res |= mpp_enc_cfg_set_s32(m_cfg, "rc:qp_min_i", 20);
		res |= mpp_enc_cfg_set_s32(m_cfg, "rc:bps_max", max_bps > 0 ? max_bps : bps * 17 / 16);
		res |= mpp_enc_cfg_set_s32(m_cfg, "rc:bps_min", min_bps > 0 ? min_bps : bps * 1 / 16);	
	case MPP_ENC_RC_MODE_VBR: {
    	res |= mpp_enc_cfg_set_s32(m_cfg, "rc:bps_target", bps);
		res |= mpp_enc_cfg_set_s32(m_cfg, "rc:bps_max", max_bps > 0 ? max_bps : bps * 17 / 16);
		res |= mpp_enc_cfg_set_s32(m_cfg, "rc:bps_min", min_bps > 0 ? min_bps : bps * 1 / 16);		
		break;
	}
	default:
		break;
	}

    /* setup codec  */
    mpp_enc_cfg_set_s32(m_cfg, "codec:type", codec_type_);
    switch (codec_type_) {
    case MPP_VIDEO_CodingAVC : {
        /*
         * H.264 profile_idc parameter
         * 66  - Baseline profile
         * 77  - Main profile
         * 100 - High profile
         */
        res |= mpp_enc_cfg_set_s32(m_cfg, "h264:profile", profile_idc_);
        /*
         * H.264 level_idc parameter
         * 10 / 11 / 12 / 13    - qcif@15fps / cif@7.5fps / cif@15fps / cif@30fps
         * 20 / 21 / 22         - cif@30fps / half-D1@@25fps / D1@12.5fps
         * 30 / 31 / 32         - D1@25fps / 720p@30fps / 720p@60fps
         * 40 / 41 / 42         - 1080p@30fps / 1080p@30fps / 1080p@60fps
         * 50 / 51 / 52         - 4K@30fps
         */
        res |= mpp_enc_cfg_set_s32(m_cfg, "h264:level", level_idc_);
        res |= mpp_enc_cfg_set_s32(m_cfg, "h264:cabac_en", cabac_en_);
        res |= mpp_enc_cfg_set_s32(m_cfg, "h264:cabac_idc", 0);
        res |= mpp_enc_cfg_set_s32(m_cfg, "h264:trans8x8", 1);
    } break;
    case MPP_VIDEO_CodingHEVC :
		// mpp_enc_cfg_set_s32 (m_cfg, "h265:qp_max", 28);
		// mpp_enc_cfg_set_s32 (m_cfg, "h265:qp_min", 4);
		// mpp_enc_cfg_set_s32 (m_cfg, "h265:qp_step", 8);
		// res |= mpp_enc_cfg_set_s32(m_cfg, "prep:hor_stride", width_);
		// res |= mpp_enc_cfg_set_s32(m_cfg, "prep:ver_stride", height_);
		break;
    case MPP_VIDEO_CodingMJPEG :
    case MPP_VIDEO_CodingVP8 : {
    } break;
    default : {
        jerror("unsupport encoder coding type %d", codec_type_);
    } break;
    }

    res |= mpp_enc_cfg_set_s32(m_cfg, "split:mode", MPP_ENC_SPLIT_NONE);
    res |= mpp_enc_cfg_set_s32(m_cfg, "split:arg", 0);
    res |= mpp_enc_cfg_set_s32(m_cfg, "split:out", 0);
	if (res) {
		jerror("MppSetup2 mpp_enc_cfg_set_s32 failed ret %d!!!!!!!!!!!!!!!!!!", ret);
	}
    ret = mpp_api_->control(mpp_ctx_, MPP_ENC_SET_CFG, m_cfg);
    if (ret) {
        jerror("mpi control enc set m_cfg failed ret %d", ret);
        return ret;
    }

    /* optional */
    MppEncSeiMode sei_mode = MPP_ENC_SEI_MODE_ONE_FRAME;
    ret = mpp_api_->control(mpp_ctx_, MPP_ENC_SET_SEI_CFG, &sei_mode);
    if (ret) {
        jerror("mpi control enc set sei m_cfg failed ret %d", ret);
        return ret;
    }

    if (codec_type_ == MPP_VIDEO_CodingAVC || codec_type_ == MPP_VIDEO_CodingHEVC) {
        MppEncHeaderMode header_mode = MPP_ENC_HEADER_MODE_EACH_IDR;
        ret = mpp_api_->control(mpp_ctx_, MPP_ENC_SET_HEADER_MODE, &header_mode);
        if (ret) {
            jerror("mpi control enc set header mode failed ret %d", ret);
            return ret;
        }
    }
	return ret;
}

void RKVideoEncoder::Encode(MppFrame &f) {
	MPP_RET ret = MPP_OK;
	MppMeta meta = NULL;
	meta = mpp_frame_get_meta(f);
	/* NOTE: It is important to clear output packet length!! */
	if (m_packets.empty()) {
		return;
	}
	MppPacket packet = m_packets.front();
	m_packets.pop_front();
	mpp_packet_set_length(packet, 0);
	mpp_meta_set_packet(meta, KEY_OUTPUT_PACKET, packet);
	mpp_meta_set_buffer(meta, KEY_MOTION_INFO, m_md_info);
	frames_recieved_++;
	{
		int change = 0;
		const auto hor_stride = mpp_frame_get_hor_stride(f);
		const auto ver_stride = mpp_frame_get_ver_stride(f);
		if (hor_stride != hor_stride_ || ver_stride != ver_stride_) {
			hor_stride_ = hor_stride;
			ver_stride_ = ver_stride;
			ChangeEncConfig(change | (1 << STRIDE_CHANGE));
		}
	}
	// {
	// 	auto buffer = mpp_frame_get_buffer(f);
	// 	auto size = mpp_buffer_get_size(buffer);
	// 	auto buf_ptr = mpp_buffer_get_ptr(buffer);
	// 	if (!ofs_.is_open()) ofs_.open("capture.yuv", std::ios::binary | std::ios::out);
	// 	ofs_.write(buf_ptr, size);
	// }
	ret = mpp_api_->encode_put_frame(mpp_ctx_, f);
	if (MPP_OK != ret) {
		jerror("encode get packet failed");
		m_packets.push_back(packet);
		return;
	}

    //encode_get_packet可能会修改packet为NULL，调用encode_get_packet前要先记下来，后面才能push_back
    MppPacket origPacket = packet;
	ret = mpp_api_->encode_get_packet(mpp_ctx_, &packet);
	if (MPP_OK != ret) {
		jerror("encode get packet failed");
		m_packets.push_back(origPacket);
		return;
	}

	// mpp_assert(packet);
	if (packet) {
		uint8_t* ptr = (uint8_t*)mpp_packet_get_pos(packet);
		size_t len = mpp_packet_get_length(packet);
        bool isKeyFrame = false;
        if (ptr && len > 0) { //输入帧率大于编码帧率时，会返回ptr!=null、len==0的packet
            frames_encoded_++;
            LogStatistics();
            if (ptr[4] == 0x67 || ptr[4] == 0x40) {
                isKeyFrame = true;
                m_requestKeyFrame = false;
            }

            std::shared_ptr<Frame> frame = nullptr;
            frame = Frame::CreateFrame(fmt_);
            frame->createFrameBuffer(ptr, len);
            // if (!ofs_.is_open()) ofs_.open("capture.h264", std::ios::binary | std::ios::out);
            // ofs_.write(ptr, len);
            deliverFrame(frame);
        } else if (!ptr) {
            jerror("%d: packet ptr is nullptr!!!!!!", __LINE__);
        }
    } else {
		jerror("%d: packet is nullptr!!!!!!", __LINE__);
	}
	m_packets.push_back(origPacket);
}
// TODO: DRY
void RKVideoEncoder::ResetStatistics() {
	statistics_start_time_ = std::chrono::steady_clock::now();
	frames_recieved_ = 0;
	frames_encoded_ = 0;
}

void RKVideoEncoder::LogStatistics() {
	auto current_time = std::chrono::steady_clock::now();
	auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - statistics_start_time_).count();
	if (duration > 10000) {
		jinfo("RKVideoEncoder[%p] Duartion: %dms. Frame received: %d. Frame encoded: %d.", this, duration, frames_recieved_, frames_encoded_);
		int fps = round((float)frames_recieved_ * 1000 / duration);
		if (mpp_ctx_ && fps >= fps_ && std::abs(input_fps_ - fps) >= 3) {
			mpp_enc_cfg_set_s32(m_cfg, "rc:fps_in_num", fps);
			mpp_enc_cfg_set_s32(m_cfg, "rc:fps_in_denorm", 1);
			jinfo("MPP_ENC_SET_CFG set input fps: %d", fps);
			auto ret = mpp_api_->control(mpp_ctx_, MPP_ENC_SET_CFG, m_cfg);
    		if (ret) {
        		jerror("mpi control enc set m_cfg failed ret %d", ret);
			} else input_fps_ = fps;
		}
		ResetStatistics();
	}
}

MPP_RET RKVideoEncoder::ChangeEncConfig(uint32_t change) {
	MPP_RET ret = MPP_OK;
	int res = 0;
	jinfo("ChangeEncConfig: %d, %dkbps", change, change_bitrate_);
	if (change) {
		if (change & (1 << STRIDE_CHANGE)) {
			res |= mpp_enc_cfg_set_s32(m_cfg, "prep:hor_stride", hor_stride_);
			res |= mpp_enc_cfg_set_s32(m_cfg, "prep:ver_stride", ver_stride_);
		}

		if (change & (1 << BITRATE_CHANGE)) {

			int32_t bps, min_bps, max_bps;
			if (change_bitrate_ == bitrate_)
			{//还原码率
				bps = bitrate_ * 1024;
				min_bps = min_bitrate_ * 1024;
				max_bps = max_bitrate_ * 1024;
			}
			else
			{
				bps = change_bitrate_ * 1024;
				min_bps = (change_bitrate_ < min_bitrate_) ? -1 : min_bitrate_;
				max_bps = -1;
			}

			switch (rc_mode_)
			{
			case MPP_ENC_RC_MODE_CBR: {
				res |= mpp_enc_cfg_set_s32(m_cfg, "rc:bps_target", bps);
				res |= mpp_enc_cfg_set_s32(m_cfg, "rc:bps_max", bps * 17 / 16);
				res |= mpp_enc_cfg_set_s32(m_cfg, "rc:bps_min", bps * 15 / 16);
				break;
			}
			case MPP_ENC_RC_MODE_AVBR:
				res |= mpp_enc_cfg_set_s32(m_cfg, "rc:qp_max", 40);
				res |= mpp_enc_cfg_set_s32(m_cfg, "rc:qp_min", 20);
				res |= mpp_enc_cfg_set_s32(m_cfg, "rc:qp_max_i", 40);
				res |= mpp_enc_cfg_set_s32(m_cfg, "rc:qp_min_i", 20);
			case MPP_ENC_RC_MODE_VBR: {
				res |= mpp_enc_cfg_set_s32(m_cfg, "rc:bps_target", bps);
				res |= mpp_enc_cfg_set_s32(m_cfg, "rc:bps_max", max_bps > 0 ? max_bps : bps * 17 / 16);
				res |= mpp_enc_cfg_set_s32(m_cfg, "rc:bps_min", min_bps > 0 ? min_bps : bps * 1 / 16);
				break;
			}
			default:
				break;
			}
		}

		if (res) {
			jerror("ChangeEncConfig mpp_enc_cfg_set_s32 failed ret %d!!!!!!!!!!!!!!!!!!", ret);
		}
		ret = mpp_api_->control(mpp_ctx_, MPP_ENC_SET_CFG, m_cfg);
		if (ret) {
			jerror("mpi control enc set m_cfg failed ret %d", ret);
			return ret;
		}
	}
	return ret;
}
} // namespace panocom