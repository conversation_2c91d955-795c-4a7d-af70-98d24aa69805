#include <fstream>
#include <future>
#include <unistd.h>

#include <ljcore/jlog.h>
#include <json.hpp>
#include <hv/EventLoop.h>
#include "VideoFrameCapturer.h"
#include "VideoFrameRender.h"
#include "VideoFrameEncoder.h"
#include "./file/FileFramePipeline.h"
#include "ffmpeg/FrameSender.h"
#include "VideoFrameDecoder.h"
#include "./impl/file/VideoFileFrameSource.h"

int mmi_print_state = 1;
using namespace panocom;



/***
 * 摄像头输出mjpeg后解码、后渲染、编码
 *  画面颜色不对
 *
 * @param argc
 * @param argv
 * @return
 */
int main(int argc,char** argv) {
    jlog_init(nullptr);

    jinfo("v4l2_test main() start  444  ");

    // 打印命令行参数以确保正确传递
    for (int i = 0; i < argc; ++i) {
        jinfo("v4l2_test main() argv i:%d   %s",i, argv[i] );
    }

    std::string captureName ="V4L2VideoFrameCapturer";
    std::string src2FilePath ="/home/<USER>/videos/V4L2VideoFrameCapturer.mjpeg";
    std::string encodeDstPath ="/home/<USER>/videos/V4L2VideoFrameCapturer.h264";
    if (argc >= 2 && strcmp(argv[1] ,"ff") == 0 ) {
        captureName = "FfVideoCapturer";
        src2FilePath ="/home/<USER>/videos/FfVideoCapturer.mjpeg";
        encodeDstPath ="/home/<USER>/videos/FfVideoCapturer.h264";
    }else {
        captureName = "V4L2VideoFrameCapturer";
        src2FilePath ="/home/<USER>/videos/V4L2VideoFrameCapturer.mjpeg";
        encodeDstPath ="/home/<USER>/videos/V4L2VideoFrameCapturer.h264";
    }
    jinfo("v4l2_test main()   captureName:%s",captureName.c_str());
    jinfo("v4l2_test main()   src2FilePath:%s",src2FilePath.c_str());
    jinfo("v4l2_test main()   encodeDstPath:%s",encodeDstPath.c_str());

    int maxChn = 25;
    int shmStartId = 10086;
    FrameSender::Inst()->init(maxChn,shmStartId,"J8");
    auto loop = std::make_shared<hv::EventLoop>();

    nlohmann::json j;
    int id = 0;

    j.clear();
    std::shared_ptr<FramePipeline> source = nullptr;



    j["dev"] = 0; //摄像头设备id
    j["format"] = "mjpeg";//摄像头输出格式  mjpeg、yuyv可选  查询摄像头支持输出的格式： v4l2-ctl --list-formats-ext
    source = VideoFrameCapturer::CreateVideoCapturer(captureName, j.dump()); //"V4L2VideoFrameCapturer"、"FfVideoCapturer"
    source->setGroupId(id);

    j.clear();
    j["codec"] = "mjpeg";
//    j["codec"] = "mjpeg_cuvid";//cuda硬件才行
    auto decoder = VideoFrameDecoder::CreateVideoDecoder("FfVideoDecoder", j.dump());//解码很慢，而且丢帧
    decoder->setGroupId(id);


    j.clear();
    j["codec"] = "libx264";
    j["kbps"] = 2000;
    auto encoder = VideoFrameEncoder::CreateVideoEncoder("FfVideoEncoder", j.dump());
    encoder->setGroupId(id);

    j.clear();
    auto render = VideoFrameRender::CreateVideoRender("FfIPCFrameRender", j.dump());
    render->setGroupId(id);

    j.clear();
    j["path"] = encodeDstPath;
    auto dstFile = std::make_shared<FileFramePipeline>(j.dump());
    dstFile->setGroupId(id);
    source->addVideoDestination(decoder);
    decoder->addVideoDestination(render);
    decoder->addVideoDestination(encoder);
    encoder->addVideoDestination(dstFile);

    jinfo("v4l2_test main() start  end  ");

    loop->run();

    FrameSender::Free();
    jinfo("v4l2_test main() end  ");
    return 0;
}


/****
 * 編碼前些幀數據yuv到文件
 * @param argc
 * @param argv
 * @return
int main(int argc,char** argv) {
    jlog_init(nullptr);

    jinfo("v4l2_test main() start  444  ");

    // 打印命令行参数以确保正确传递
    for (int i = 0; i < argc; ++i) {
        jinfo("v4l2_test main() argv i:%d   %s",i, argv[i] );
    }

    std::string captureName ="V4L2VideoFrameCapturer";
    std::string src2FilePath ="/home/<USER>/videos/V4L2VideoFrameCapturer.mjpeg";
    std::string encodeDstPath ="/home/<USER>/videos/V4L2VideoFrameCapturer.h264";
    if (argc >= 2 && strcmp(argv[1] ,"ff") == 0 ) {
        captureName = "FfVideoCapturer";
        src2FilePath ="/home/<USER>/videos/FfVideoCapturer.mjpeg";
        encodeDstPath ="/home/<USER>/videos/FfVideoCapturer.h264";
    }else {
        captureName = "V4L2VideoFrameCapturer";
        src2FilePath ="/home/<USER>/videos/V4L2VideoFrameCapturer.mjpeg";
        encodeDstPath ="/home/<USER>/videos/V4L2VideoFrameCapturer.h264";
    }
    jinfo("v4l2_test main()   captureName:%s",captureName.c_str());
    jinfo("v4l2_test main()   src2FilePath:%s",src2FilePath.c_str());
    jinfo("v4l2_test main()   encodeDstPath:%s",encodeDstPath.c_str());

    int maxChn = 25;
    int shmStartId = 10086;
    FrameSender::Inst()->init(maxChn,shmStartId,"J8");
    auto loop = std::make_shared<hv::EventLoop>();

    nlohmann::json j;
    int id = 0;

    j.clear();
    std::shared_ptr<FramePipeline> source = nullptr;



    j["dev"] = 0; //摄像头设备id
    j["format"] = "mjpeg";//摄像头输出格式  mjpeg、yuyv可选  查询摄像头支持输出的格式： v4l2-ctl --list-formats-ext
    source = VideoFrameCapturer::CreateVideoCapturer(captureName, j.dump()); //"V4L2VideoFrameCapturer"、"FfVideoCapturer"
    source->setGroupId(id);


    j.clear();
//    j["path"] = src2FilePath;
//    auto src2File = std::make_shared<FileFramePipeline>(j.dump());
//    src2File->setGroupId(id);

    j.clear();
    j["path"] = "/home/<USER>/videos/before.yuv";
    auto beforeFile = std::make_shared<FileFramePipeline>(j.dump());
    beforeFile->setGroupId(id);


    j.clear();
    j["path"] = "/home/<USER>/videos/after.yuv";
    auto afterFile = std::make_shared<FileFramePipeline>(j.dump());
    afterFile->setGroupId(id);

    j.clear();
    j["codec"] = "mjpeg";
//    j["codec"] = "mjpeg_cuvid";//cuda硬件才行
    auto decoder = VideoFrameDecoder::CreateVideoDecoder("FfVideoDecoder", j.dump());//解码很慢，而且丢帧
    decoder->setGroupId(id);


    j.clear();
    j["codec"] = "libx264";
    j["kbps"] = 2000;
    auto encoder = VideoFrameEncoder::CreateVideoEncoder("FfVideoEncoder", j.dump());
    encoder->setGroupId(id);

    j.clear();
    auto render = VideoFrameRender::CreateVideoRender("FfIPCFrameRender", j.dump());
    render->setGroupId(id);

    j.clear();
    j["path"] = encodeDstPath;
    auto dstFile = std::make_shared<FileFramePipeline>(j.dump());
    dstFile->setGroupId(id);
//    source->addVideoDestination(src2File);
    source->addVideoDestination(decoder);
//    decoder->addVideoDestination(render);
    decoder->addVideoDestination(beforeFile);
    decoder->addVideoDestination(encoder);

    encoder->addVideoDestination(afterFile);

//    encoder->addVideoDestination(dstFile);

    jinfo("v4l2_test main() start  end  ");

    loop->run();

    FrameSender::Free();
    jinfo("v4l2_test main() end  ");
    return 0;
}**/



/***
 * 摄像头输出yuyv、后渲染、编码。
 * 渲染无显示、编码显示帧为空
 * @param argc
 * @param argv
 * @return
 */
/***
int main(int argc,char** argv) {
    jlog_init(nullptr);

    jinfo("v4l2_test main() start  yuyv start  ");

    // 打印命令行参数以确保正确传递
    for (int i = 0; i < argc; ++i) {
        jinfo("v4l2_test main() argv i:%d   %s",i, argv[i] );
    }

    std::string captureName ="V4L2VideoFrameCapturer";
    std::string src2FilePath ="/home/<USER>/videos/V4L2VideoFrameCapturer.yuyv";
    std::string encodeDstPath ="/home/<USER>/videos/V4L2VideoFrameCapturer.h264";
    if (argc >= 2 && strcmp(argv[1] ,"ff") == 0 ) {
        captureName = "FfVideoCapturer";
        src2FilePath ="/home/<USER>/videos/FfVideoCapturer.yuyv";
        encodeDstPath ="/home/<USER>/videos/FfVideoCapturer.h264";
    }else {
        captureName = "V4L2VideoFrameCapturer";
        src2FilePath ="/home/<USER>/videos/V4L2VideoFrameCapturer.yuyv";
        encodeDstPath ="/home/<USER>/videos/V4L2VideoFrameCapturer.h264";
    }
    jinfo("v4l2_test main()   captureName:%s",captureName.c_str());
    jinfo("v4l2_test main()   src2FilePath:%s",src2FilePath.c_str());
    jinfo("v4l2_test main()   encodeDstPath:%s",encodeDstPath.c_str());

    int maxChn = 25;
    int shmStartId = 10086;
    FrameSender::Inst()->init(maxChn,shmStartId,"J8");
    auto loop = std::make_shared<hv::EventLoop>();

    nlohmann::json j;
    int id = 0;

    j.clear();
    std::shared_ptr<FramePipeline> source = nullptr;



    j["dev"] = 0; //摄像头设备id
    j["format"] = "yuyv";//摄像头输出格式  mjpeg、yuyv可选  查询摄像头支持输出的格式： v4l2-ctl --list-formats-ext
    source = VideoFrameCapturer::CreateVideoCapturer(captureName, j.dump()); //"V4L2VideoFrameCapturer"、"FfVideoCapturer"
    source->setGroupId(id);


    j.clear();
//    j["path"] = src2FilePath;
//    auto src2File = std::make_shared<FileFramePipeline>(j.dump());
//    src2File->setGroupId(id);

    j.clear();
    j["codec"] = "libx264";
    j["kbps"] = 2000;
    auto encoder = VideoFrameEncoder::CreateVideoEncoder("FfVideoEncoder", j.dump());
    encoder->setGroupId(id);

    j.clear();
    auto render = VideoFrameRender::CreateVideoRender("FfIPCFrameRender", j.dump());
    render->setGroupId(id);

    j.clear();
    j["path"] = encodeDstPath;
    auto dstFile = std::make_shared<FileFramePipeline>(j.dump());
    dstFile->setGroupId(id);
//    source->addVideoDestination(src2File);
//    source->addVideoDestination(decoder);
    source->addVideoDestination(render);
    source->addVideoDestination(encoder);
    encoder->addVideoDestination(dstFile);

    jinfo("v4l2_test main() start  end  ");

    loop->run();

    FrameSender::Free();
    jinfo("v4l2_test main() end  ");
    return 0;
}**/

/**
int main(int argc,char** argv) {
    jlog_init(nullptr);

    int maxChn = 25;
    int shmStartId = 10086;
    FrameSender::Inst()->init(maxChn,shmStartId,"J8");
    jinfo("v4l2_test main() start   22222 ");
    auto loop = std::make_shared<hv::EventLoop>();

    nlohmann::json j;
    int id = 0;

    j.clear();
    std::shared_ptr<FramePipeline> source = nullptr;

    jinfo("v4l2_test main() start  CreateVideoCapturer  ");

    j["dev"] = 0; //摄像头设备id
    j["format"] = "mjpeg";//摄像头输出格式  mjpeg、yuyv可选  查询摄像头支持输出的格式： v4l2-ctl --list-formats-ext
    source = VideoFrameCapturer::CreateVideoCapturer("V4L2VideoFrameCapturer", j.dump());
//    source = VideoFrameCapturer::CreateVideoCapturer("FfVideoCapturer", j.dump());
    source->setGroupId(id);

    j.clear();
//    j["path"] = "/home/<USER>/videos/capture_ffvideocapturer.mjpeg";
    j["path"] = "/home/<USER>/videos/capture_v4l2videoframecapturer.mjpeg";
    auto dstFile = std::make_shared<FileFramePipeline>(j.dump());
    dstFile->setGroupId(id);
    source->addVideoDestination(dstFile);
    jinfo("v4l2_test main() start  end  ");
    loop->run();
    FrameSender::Free();
    jinfo("v4l2_test main() end  ");
    return 0;
}**/



//int main(int argc,char** argv) {
//    jlog_init(nullptr);
//
//    int maxChn = 25;
//    int shmStartId = 10086;
//    FrameSender::Inst()->init(maxChn,shmStartId,"J8");
//    jinfo("v4l2_test main() start   111111111111 ");
//    auto loop = std::make_shared<hv::EventLoop>();
//
//    nlohmann::json j;
//    int id = 0;
//
//    j.clear();
//    std::shared_ptr<FramePipeline> source = nullptr;
//
//    jinfo("v4l2_test main() start  CreateVideoCapturer  ");
//
////    j["dev"] = 0; //摄像头设备id
////    j["format"] = "mjpeg";//摄像头输出格式  mjpeg、yuyv可选  查询摄像头支持输出的格式： v4l2-ctl --list-formats-ext
////    source = VideoFrameCapturer::CreateVideoCapturer("V4L2VideoFrameCapturer", j.dump());
////    source = VideoFrameCapturer::CreateVideoCapturer("FfVideoCapturer", j.dump());
////    source->setGroupId(id);
//
//
//    j.clear();
////    j["path"] = "/home/<USER>/videos/movice.mjpeg";//yuvj420p
////    j["path"] = "/home/<USER>/videos/capture_v4l2videoframecapturer.mjpeg";//yuvj422p
//    j["path"] = "/home/<USER>/videos/capture_v4l2videoframecapturer.mjpeg";//yuvj422p
////    j["path"] = "/home/<USER>/videos/v4l2_capture_test.mjpeg";//yuvj422p
//    j["speed"] = 0.1;
//
//    int gop = 30;
//    source = FileFramePipeline::CreateFileSource("VideoFileFrameSource", j.dump());
//    source->setGroupId(id);
//    std::shared_ptr<panocom::VideoFileFrameSource> videoFileFrameSourcePtr = std::dynamic_pointer_cast<panocom::VideoFileFrameSource>(source);
//    if(videoFileFrameSourcePtr){
//        if(videoFileFrameSourcePtr->getFps()>0){
//            j["fps"] = videoFileFrameSourcePtr->getFps();
//        }
//        if(videoFileFrameSourcePtr->getWidth()>0){
//            j["width"] = videoFileFrameSourcePtr->getWidth();
//        }
//        if(videoFileFrameSourcePtr->getHeight()>0){
//            j["height"] = videoFileFrameSourcePtr->getHeight();
//        }
//        if(!videoFileFrameSourcePtr->getCodec().empty()){
//            j["codec"] = videoFileFrameSourcePtr->getCodec(); //codec可选：mjpeg h264 hevc
//        }
//        if(!videoFileFrameSourcePtr->getCodec().empty()){
//            j["gop"] = gop = videoFileFrameSourcePtr->getGop(); //codec可选：mjpeg h264 hevc
//        }
//    }
//
////    j.clear();
//    j["codec"] = "mjpeg";
////    j["codec"] = "mjpeg_cuvid";//cuda硬件才行
//    auto decoder = VideoFrameDecoder::CreateVideoDecoder("FfVideoDecoder", j.dump());//解码很慢，而且丢帧
//    decoder->setGroupId(id);
//
//
//    j.clear();
//    j["codec"] = "libx264";
//    j["kbps"] = 2000;
//    auto encoder = VideoFrameEncoder::CreateVideoEncoder("FfVideoEncoder", j.dump());
//    encoder->setGroupId(id);
//
//
//    j.clear();
//    auto render = VideoFrameRender::CreateVideoRender("FfIPCFrameRender", j.dump());
//    render->setGroupId(id);
//
//    j.clear();
//    j["path"] = "/home/<USER>/videos/v4l2_capture_test.h264";
//    auto dstFile = std::make_shared<FileFramePipeline>(j.dump());
//    dstFile->setGroupId(id);
//
//    source->addVideoDestination(decoder);
//    decoder->addVideoDestination(render);
//    decoder->addVideoDestination(encoder);
//    encoder->addVideoDestination(dstFile);
//
//
//    source->start();
//
//    jinfo("v4l2_test main() start  end  ");
//
//    loop->run();
//
//    FrameSender::Free();
//    jinfo("v4l2_test main() end  ");
//    return 0;
//}