#ifndef P_QCuRender_h
#define P_QCuRender_h

#include <QObject>
#include <QtWidgets/QOpenGLWidget>
#include <QOpenGLFunctions>
#include <QOpenGLTexture>
#include <QOpenGLBuffer>
#include <QOpenGLShaderProgram>
#include <QTimer>
#include "OpenGLRender.h"

namespace panocom
{
    class QCuRender : public QObject, protected QOpenGLFunctions
    {
        Q_OBJECT

    public:
        QCuRender(QObject *window, int maxWidth = 1920, int maxHeight = 1088, int dev = 0 , int cudaIndex = 0, int gpuIndex = 0);
        ~QCuRender();
        void initializeGL();
        int createVideoFrameRender(int videoId, int areaId, int x, int y, int z, int w, int h, int cudaId, int gpuIdex);
        int destroyVideoFrameRender(int areaId);
        int destroyVideoFrameRenderByVideoId(int videoId);
        int moveVideoFrameRender(int areaId, int x, int y, int z, int w, int h);

        std::shared_ptr<OpenGLRender> render() { return m_render; }
    public slots:
        void doRender();
        void display();

    private:
        void initializeShader();
        bool m_init = false;
        bool m_updateResource = false;
        int m_maxWidth;
        int m_maxHeight;
        QOpenGLTexture m_texture;
        QOpenGLBuffer m_vbo;
        QOpenGLBuffer m_pbo;
        QOpenGLShaderProgram m_program;
        QObject *m_window = nullptr;

        std::shared_ptr<OpenGLRender> m_render;

        std::unordered_map<int, int> area_video_ids_;
    
        long m_tick;
        int m_index = 0;
    };
}

#endif
