cmake_minimum_required(VERSION 3.4.1)
project(RKTest)

link_directories(${CMAKE_PREFIX_PATH}/lib)
# link_directories(${CUDA_TOOLKIT_ROOT_DIR}/targets/x86_64-linux/lib)

link_libraries(MediaPipeline 
				ljcore pthread rockchip_mpp hv_static 
				jrtp jthread ZLToolKit drm rga DTLSTool
				opus g729 fdk-aac PNPcm asound ssl crypto
				glib-2.0 gio-2.0 gmodule-2.0 gobject-2.0
				nice pcre2-8 srtp2 aec rnnoise ffi z ptoolkit)

add_executable(decoder_test decoder_test.cpp)
		
add_executable(audio_capturer_render_test audio_capturer_render_test.cpp)

add_executable(alsa_test alsa_test.cpp)

add_executable(display_test display_test.cpp)
# add_executable(drm_test drm_test.cpp)
add_executable(v4l2_test v4l2_test.cpp)

add_executable(mixer_test mixer_test.cpp)

add_executable(amixer_test amixer_test.cpp)