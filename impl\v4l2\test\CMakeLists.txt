cmake_minimum_required(VERSION 3.4.1)
project(V4L2_Test)

#link_libraries(MediaPipeline
#				ljcore pthread rockchip_mpp hv jrtp
#				jthread yuv ZLToolKit drm rga sdptransform
#				opus g729 fdk-aac PNPcm asound)


# 共有
#ljcore pthread (hv)hv_static jrtp drm

#共有移除 drm jrtp
# ptoolkit     DTLSTool ssl crypto
#		glib-2.0 gio-2.0 gmodule-2.0 gobject-2.0
#		nice pcre2-8 srtp2 aec rnnoise ffi z
#link_libraries(MediaPipeline
#		ljcore pthread  hv_static jrtp
#		avcodec swscale avutil avdevice avfilter avformat
#		 jthread ZLToolKit
#		opus g729 fdk-aac PNPcm asound )

link_libraries(MediaPipeline
		ljcore pthread  hv_static
		avcodec swscale avutil avdevice avfilter avformat  SDL2
		jrtp jthread ZLToolKit drm  DTLSTool
		opus g729 fdk-aac PNPcm asound ssl crypto
		glib-2.0 gio-2.0 gmodule-2.0 gobject-2.0
		nice pcre2-8 srtp2 aec rnnoise ffi z ptoolkit)

add_executable(v4l2_test v4l2_test.cpp)


#link_libraries(MediaPipeline
#		ljcore pthread  hv_static
#		avcodec swscale avutil avdevice avfilter avformat  SDL2
#		jrtp jthread ZLToolKit drm  DTLSTool
#		opus g729 fdk-aac PNPcm asound ssl crypto
#		glib-2.0 gio-2.0 gmodule-2.0 gobject-2.0
#		nice pcre2-8 srtp2 aec rnnoise ffi z ptoolkit)

