#ifndef IMPL_AUDIOMIXER_RKAUDIOFRAMEMIXER_H_
#define IMPL_AUDIOMIXER_RKAUDIOFRAMEMIXER_H_
#include <queue>
#include <unordered_map>
#include <vector>
#include <fstream>

#include "AudioFrameMixer.h"
#include "Frame.h"

#include <hv/EventLoopThread.h>
#include <hv/EventLoopThreadPool.h>
#include <resampler/resampler.h>
#include <resampler/push_resampler.h>
#include <ptoolkit/bytebuffer.h>

namespace panocom
{
class DefaultAudioFrameMixer : public AudioFrameMixer
{
private:
    /* data */
public:
    DefaultAudioFrameMixer(const std::string& jsonParams);
    ~DefaultAudioFrameMixer();
    void onFrame(const std::shared_ptr<Frame> &frame) override;

    void addAudioSource(const FramePipeline::Ptr &source) override;
    void removeAudioSource(const FramePipeline::Ptr &source) override;

    void addSource(const std::shared_ptr<FramePipeline> &source);
    void removeSource(const std::shared_ptr<FramePipeline> &source);
private:
	hv::EventLoopThread loop_thread_;
    hv::EventLoopThreadPool receive_thread_;
    // TODO: 使用bytebuf存储数据
    std::mutex mutex_;
    // std::unordered_map<FramePipeline*, std::queue<std::shared_ptr<Frame>>> audio_source_lists_;
    std::unordered_map<void*, ByteBuffer> audio_sources_;

    int sample_rate_;
    int number_of_channels_;
    FrameFormat fmt_;
    int ptime_;
    size_t samples_per_channel_;
    size_t max_buffer_size_;
#ifdef WEBRTC_RESAMPLE_ANOTHER
    nswebrtc::PushResampler<int16_t> resampler_;
#else
    nswebrtc::Resampler resampler_;
#endif
    bool initResampler_ = false;
    int source_samplerate_;

    std::ofstream ofs_;
};

class DefaultAudioFrameMixerManager
{
public:
    /* data */
    static DefaultAudioFrameMixerManager &instance();
    std::shared_ptr<DefaultAudioFrameMixer> CreateMixer(const std::string& jsonParams);
    std::shared_ptr<DefaultAudioFrameMixer> CreateLocalMixer(const std::string &jsonParms);
    std::shared_ptr<DefaultAudioFrameMixer> CreateRemoteMixer(const std::string &jsonParms);
private:
    DefaultAudioFrameMixerManager(/* args */) = default;
    ~DefaultAudioFrameMixerManager();

private:
    std::unordered_map<int, std::shared_ptr<DefaultAudioFrameMixer>> mixers_;
    std::unordered_map<std::string, std::shared_ptr<DefaultAudioFrameMixer>> local_mixers_;
    std::unordered_map<int, std::shared_ptr<DefaultAudioFrameMixer>> remote_mixers_;
};

} // namespace panocom


#endif