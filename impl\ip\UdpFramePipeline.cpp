#include "UdpFramePipeline.h"
#include "Frame.h"
#include <json.hpp>
#include <ljcore/jlog.h>

using namespace panocom;
using namespace toolkit;

namespace
{
const uint8_t s_in6_addr_maped[]
    = { 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00 };
std::string makeSockId(sockaddr *addr, int) {
    std::string ret;
    switch (addr->sa_family) {
        case AF_INET : {
            ret.resize(18);
            ret[0] = ((struct sockaddr_in *) addr)->sin_port >> 8;
            ret[1] = ((struct sockaddr_in *) addr)->sin_port & 0xFF;
            //ipv4地址统一转换为ipv6方式处理
            memcpy(&ret[2], &s_in6_addr_maped, 12);
            memcpy(&ret[14], &(((struct sockaddr_in *) addr)->sin_addr), 4);
            return ret;
        }
        case AF_INET6 : {
            ret.resize(18);
            ret[0] = ((struct sockaddr_in6 *) addr)->sin6_port >> 8;
            ret[1] = ((struct sockaddr_in6 *) addr)->sin6_port & 0xFF;
            memcpy(&ret[2], &(((struct sockaddr_in6 *)addr)->sin6_addr), 16);
            return ret;
        }
        default: assert(0); return "";
    }
}    
} // namespace


UdpFramePipeline::UdpFramePipeline(const std::string& jsonParams)
#ifdef USE_SIMULATION
    : rd_()
    , gen_(rd_())
    , uniform_dist_(0.0, 1.0)
#endif
{
    FN_BEGIN;
    name_ = "UdpFramePipeline";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    std::string ip = "0.0.0.0";
    if (j.contains("bindIp"))
    {
        ip = j["bindIp"];
    }
    int port = 10000;
    if (j.contains("bindPort"))
    {
        port = j["bindPort"];
    }
    fmt_ = FRAME_FORMAT_RTP;
    if (j.contains("fmt"))
    {
        fmt_ = (FrameFormat)j["fmt"];
    }
    if (j.contains("dst") && j["dst"].is_array())
    {
        for (int i = 0; i < j["dst"].size(); ++i)
        {
            std::string ip = j["dst"][i]["ip"];
            int port = j["dst"][i]["port"];

            jinfo("UdpFramePipeline %s rtp %s:%d", fmt_ == FRAME_FORMAT_RTP ? "RTP" : "RTCP", ip.c_str(), port);

            struct sockaddr_storage addr = toolkit::SockUtil::make_sockaddr(ip.c_str(), port);
            dsts_.push_back(addr);
            sock_ids_.emplace(makeSockId((struct sockaddr*)&addr, sizeof(addr)));
        }
    }
    running_ = true;
    sock_ = Socket::createSocket(nullptr, true);
    sock_->bindUdpSock(port, ip, false);
    sock_->setOnRead([this](const Buffer::Ptr &buf, struct sockaddr *addr, int addr_len) {
        if (!running_ || sock_ids_.count(makeSockId(addr, addr_len)) == 0)
            return;
        std::shared_ptr<Frame> f = Frame::CreateFrame(fmt_);
        f->createFrameBuffer(buf->size());
        memcpy(f->getFrameBuffer(), buf->data(), buf->size());
        deliverFrame(f);
#ifdef USE_SIMULATION
        // auto data = buf->data();
        // uint16_t seq = data[2];
        // seq = (seq << 8) + data[3];
        // if (first_) {
        //     seq_ = seq;
        //     first_ = false;
        // } else if (seq_ + 1 != seq) {
        //     jinfo("unexpected %d, expected %d", seq, seq_ + 1);
        // } else seq_ = seq;
#endif
    });
    FN_END;
}

UdpFramePipeline::~UdpFramePipeline()
{
    FN_BEGIN;
    {
        std::lock_guard<std::mutex> lock(sock_mutex_);
        running_ = false;
        sock_.reset();
        sock_ids_.clear();
    }
    stop();
    FN_END;
}

void UdpFramePipeline::start()
{
    FN_BEGIN;
    FN_END;
}

void UdpFramePipeline::stop()
{
    FN_BEGIN;
    // sock_->getPoller()->sync([this] {
    //     sock_->closeSock();
    // });
    FN_END;
}

void UdpFramePipeline::onFrame(const std::shared_ptr<Frame>& frame)
{
#ifdef USE_SIMULATION
    if (fmt_== FRAME_FORMAT_RTP && frame->getFrameFormat() == fmt_) {
        auto data = frame->getFrameBuffer();
        uint16_t seq = data[2];
        seq = (seq << 8) + data[3];
        // if (first_) {
        //     seq_ = seq;
        //     first_ = false;
        // } else if ((uint16_t)(seq_ + 1) != seq) {
        //     jinfo("unexpected %d, expected %d", seq, (uint16_t)(seq_ + 1));
        // } else seq_ = seq;
        if (should_drop_packet()) {
            jinfo("drop packet seq %d", seq);
            return;
        }
    }
#endif   
    {
        std::lock_guard<std::mutex> lock(sock_mutex_);
        if (frame->getFrameFormat() == fmt_ && running_ && sock_) {
            for (auto it = dsts_.begin(); it != dsts_.end(); it++) {
                struct sockaddr_storage addr = *it;
                int ret = sock_->send(frame->getFrameBuffer(), frame->getFrameSize(), (struct sockaddr *)&addr, sizeof(addr));
                if (ret != frame->getFrameSize()) {
                    jerror("sock(%p) send fail ret = %d", this, ret);
                }
            }
        }
    }
}

#ifdef USE_SIMULATION
bool UdpFramePipeline::should_drop_packet() {
    return uniform_dist_(gen_) < 0.05;
}

void UdpFramePipeline::simulate_network_conditions() {
    static int BASE_DELAY = 100;    // ms
    std::uniform_int_distribution<> int_dist(-50, 50);
    int actual_delay = BASE_DELAY + int_dist(gen_);
    std::this_thread::sleep_for(std::chrono::milliseconds(actual_delay));
}
#endif