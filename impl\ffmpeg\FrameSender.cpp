﻿#include "FrameSender.h"
#include <ljcore/jlog.h>
#ifdef WIN32
#else
UnixDomainSocketProc* UnixDomainSocketProc::m_obj = nullptr;
UnixDomainSocketProc * UnixDomainSocketProc::Inst()
{
	if (m_obj == nullptr)
		m_obj = new UnixDomainSocketProc();
	return m_obj;
}
void UnixDomainSocketProc::Free()
{
	if (m_obj)
		delete m_obj;
}
int UnixDomainSocketProc::GetSendSocket(uint8_t channel)
{
	std::unique_lock<std::mutex> lock(m_socketMutex);
	if (m_isInit && channel >= 0 && channel < m_maxChn)
		return m_clientSocket[channel];
	else
		return 0;
}

void UnixDomainSocketProc::init(uint8_t maxChn, char* appName)
{
	std::unique_lock<std::mutex> lock(m_socketMutex);
	m_serverSocket.resize(maxChn);
	m_clientSocket.resize(maxChn);
	int sock = socket(AF_UNIX, SOCK_STREAM, 0);
	if (sock == -1) {
        jerror("域间套接字创建失败 通道0");
		return;
	}
	struct sockaddr_un addr = { 0 };
	memset(&addr, 0, sizeof(sockaddr_un));
	addr.sun_family = AF_UNIX;
	//char socket_path[100] = { 0 };
	//snprintf(socket_path, sizeof(socket_path) - 1, "J8Ctrler%d", i);
	strncpy(addr.sun_path + 1, appName, sizeof(addr.sun_path) - 1);
	// 绑定socket
	if (::bind(sock, (struct sockaddr*)&addr, sizeof(addr)) == -1) {
        jerror("域间套接字绑定失败 通道0");
		::close(sock);
		return;
	}
	// 监听连接
	if (::listen(sock, 1) == -1) {
        jerror("域间套接字监听失败 通道0");
		::close(sock);
		return;
	}
	m_serverSocket[0] = sock;
	for (int i = 0; i < maxChn; ++i)
	{
		// 接受连接
		m_clientSocket[i] = accept(sock, NULL, NULL);
		if (m_clientSocket[i] == -1) {
			::close(sock);
            jerror("域间套接字接受连接失败 i = %d",i);
			return;
		}
		if (fcntl(m_clientSocket[i], F_SETFL, O_NONBLOCK) == -1)
		{
			::close(sock);
			::close(m_clientSocket[i]);
            jerror("域间套接字设置非阻塞失败 i = %d",i);
			return;
		}
        jinfo("域间套接字建立连接成功 i = %d",i);
	}
	m_isInit = true;
	m_maxChn = maxChn;
}

UnixDomainSocketProc::~UnixDomainSocketProc()
{
	std::unique_lock<std::mutex> lock(m_socketMutex);
	for (const auto& fd : m_serverSocket) {
		if(fd > 0)
			::close(fd);
	}
	for (const auto& fd : m_clientSocket) {
		if (fd > 0)
			::close(fd);
	}
}
#endif
FrameSender* FrameSender::m_obj = nullptr;
FrameSender * FrameSender::Inst()
{
	if (m_obj == nullptr)
		m_obj = new FrameSender();
	return m_obj;
}

void FrameSender::Free()
{
	if (m_obj)
		delete m_obj;
	m_obj = nullptr;
}

//界面和后端需要传入相同的id与appName
//不同软件不能传入相同的appName与shmStartId
void FrameSender::init(uint8_t maxChn, uint32_t shmStartId ,char* appName)
{
    jinfo("FrameSender::init() maxChn:%d shmStartId:%d appName:%s",maxChn,shmStartId,appName);
#ifdef WIN32
#else
	if (m_socketProc){
		m_socketProc->init(maxChn, appName);
	}
#endif
	if (m_shmProc) {
		m_shmProc->init(maxChn, shmStartId);
	}
}
//同一通道只支持单线程调用
bool FrameSender::sendFrame(const AVFrame * frame, uint8_t chn)
{
	if (frame && chn >= 0){
		//硬解句柄存储在data[3]，软解yuv分量分别在data[0~2]
		if (!frame->data[3]) {
			return writeVideoToShm(frame, chn);
		}
		else {
#ifdef WIN32
#else
			return sendDmaBuf(frame, chn);
#endif
			//todo:windows下dxva解码句柄零拷贝传输
			return false;
		}
        //释放内存
        av_frame_free(&frame);
	}
	else
		return false;
}

bool FrameSender::writeVideoToShm(const AVFrame * frame, uint8_t chn)
{
    YUVShm* yuv = m_shmProc->GetWriteShm(chn);
    if (yuv == NULL)
    {
        return false;
    }

    int lineSize = frame->height * frame->linesize[0] +
                       frame->height * frame->linesize[1] / 2 +
                       frame->height * frame->linesize[2] / 2;
    if (lineSize > sizeof(yuv->data))
    {
        return false;
    }
    if (frame->format == AVPixelFormat::AV_PIX_FMT_YUVJ422P ||
        frame->format == AVPixelFormat::AV_PIX_FMT_YUV422P)
    {
        uint8_t* pData0 = (uint8_t*)yuv->data;
        uint8_t* pData1 = pData0 + frame->height * frame->linesize[0];
        uint8_t* pData2 = pData1 + frame->height * frame->linesize[1];
        memcpy(pData0, frame->data[0], frame->height * frame->linesize[0]);
        memcpy(pData1, frame->data[1], frame->height * frame->linesize[1]);
        memcpy(pData2, frame->data[2], frame->height * frame->linesize[2]);
    }
    else if (frame->format == AVPixelFormat::AV_PIX_FMT_YUVJ420P ||
             frame->format == AVPixelFormat::AV_PIX_FMT_YUV420P)
    {
        uint8_t* pData0 = (uint8_t*)yuv->data;
        uint8_t* pData1 = pData0 + frame->height * frame->linesize[0];
        uint8_t* pData2 = pData1 + frame->height * frame->linesize[1] / 2;
        memcpy(pData0, frame->data[0], frame->height * frame->linesize[0]);
        memcpy(pData1, frame->data[1], frame->height * frame->linesize[1] / 2);
        memcpy(pData2, frame->data[2], frame->height * frame->linesize[2] / 2);
    }
    else if (frame->format == AVPixelFormat::AV_PIX_FMT_NV12)
    {
        uint8_t* pData0 = (uint8_t*)yuv->data;
        uint8_t* pData1 = pData0 + frame->height * frame->linesize[0];
        memcpy(pData0, frame->data[0], frame->height * frame->linesize[0]);
        memcpy(pData1, frame->data[1], frame->height * frame->linesize[1] / 2);
    }
    else if (frame->format == AVPixelFormat::AV_PIX_FMT_YUYV422)
    {
        uint8_t* pData0 = (uint8_t*)yuv->data;
        memcpy(pData0, frame->data[0], frame->height * frame->linesize[0]);
    }

    yuv->linesize[0] = frame->linesize[0];
    yuv->linesize[1] = frame->linesize[1];
    yuv->linesize[2] = frame->linesize[2];
    yuv->width = frame->width;
    yuv->height = frame->height;
    yuv->pts = frame->pts;
    yuv->is_valid = 1;
    yuv->pixfmt = (uint32_t)frame->format;
    return true;
}
#ifdef WIN32
#else
bool FrameSender::sendDmaBuf(const AVFrame * frame, uint8_t chn)
{
	auto clientSocket = m_socketProc->GetSendSocket(chn);
	if (clientSocket <= 0){
        jerror("通信套接字不存在 chn = %d",chn);
		return false;
	}
	VADRMPRIMESurfaceDescriptor prime_desc;
	memset(&prime_desc, 0, sizeof(prime_desc));
	if (exportVAAPIFrameToDMABuf(frame, prime_desc)){
		return sendFrameInfo(clientSocket, prime_desc, frame);
	}
	else {
        jerror("导出dma失败 chn = %d",chn);
		return false;
	}
}

bool FrameSender::exportVAAPIFrameToDMABuf(const AVFrame * frame, VADRMPRIMESurfaceDescriptor & prime_desc)
{
	if (!frame || !frame->data[3]) {
        jerror("无效的帧数据 frame = %ld", (long)frame->data[3]);
		return false;
	}

	if (frame->width <= 0 || frame->height <= 0) {
        jerror("Error: Invalid frame size %dx%d ", frame->width, frame->height);
		return false;
	}
	// 检查 VA-API 显示是否有效
	VADisplay va_display = 0;
	if (frame->hw_frames_ctx)
	{
		AVHWFramesContext* hw_frames_ctx = (AVHWFramesContext*)(frame->hw_frames_ctx->data);
		AVHWDeviceContext* device_ctx = (AVHWDeviceContext*)(hw_frames_ctx->device_ctx);
		AVVAAPIDeviceContext* vaapi_ctx = (AVVAAPIDeviceContext*)(device_ctx->hwctx);
		va_display = vaapi_ctx->display;
		if (!va_display) {
            jerror("VA-API 显示未初始化 va_display= %ld",  (long)va_display);
			return false;
		}
	}
	else
	{
        jerror("frame->hw_frames_ctx is null,VA-API 显示未初始化 va_display= %ld",  (long)va_display);
		return false;
	}

	// 获取 VA 表面 ID
	VASurfaceID surface_id = (VASurfaceID)(uintptr_t)frame->data[3];

	//由于vaapi是异步解码，获取va表面不代表解码已经完成，需要等待同步后发送给界面，否则画面会撕裂
	VAStatus va_status = VA_STATUS_SUCCESS;
	va_status = vaSyncSurface(va_display, surface_id);
	if (va_status != VA_STATUS_SUCCESS) {
        jerror("VA-API 同步失败 surface_id= %ld",  (long)surface_id);
	}

	va_status = VA_STATUS_SUCCESS;
	VASurfaceAttrib attribs[1];
	attribs[0].type = VASurfaceAttribPixelFormat;
	attribs[0].flags = VA_SURFACE_ATTRIB_SETTABLE;
	attribs[0].value.type = VAGenericValueTypeInteger;
	attribs[0].value.value.i = VA_FOURCC_NV12;  // 明确指定 NV12 格式

	va_status = vaExportSurfaceHandle(va_display,
		surface_id,
		VA_SURFACE_ATTRIB_MEM_TYPE_DRM_PRIME_2,
		VA_EXPORT_SURFACE_READ_ONLY | VA_EXPORT_SURFACE_SEPARATE_LAYERS,
		&prime_desc);

	if (va_status != VA_STATUS_SUCCESS) {
		std::string error_msg = std::string("vaExportSurfaceHandle 导出VA表面到DMA-BUF失败:")
			+ std::to_string(va_status);
        jerror("error_msg: %ls", error_msg);
		return false;
	}

	// 检查导出的表面描述符
	if (prime_desc.num_objects < 1) {
        jerror("效的DRM PRIME描述符：没有对象  surface_id= %ld",  (long)surface_id);
		return false;
	}

	if (prime_desc.num_layers < 1) {
        jerror("无效的DRM PRIME描述符：没有层  surface_id= %ld",  (long)surface_id);
		return false;
	}

	// 获取第一个平面的 DMA-BUF 文件描述符
	auto dma_buf_fd_ = prime_desc.objects[0].fd;
	if (dma_buf_fd_ <= 0) {
        jerror("无效的DRM PRIME描述符  dma_buf_fd_= %ld",  (long)dma_buf_fd_);
		return false;
	}
//     jinfo("成功导出vaSurface surface_id= %ld",  (long)surface_id);
	return true;
}

bool FrameSender::sendFrameInfo(int socket, const VADRMPRIMESurfaceDescriptor & prime_desc, const AVFrame * frame)
{
	struct msghdr msg = { 0 };
	struct iovec iov[4];
	struct cmsghdr *cmsg;
	char buf[CMSG_SPACE(sizeof(int))];
	unsigned long offset = (long)prime_desc.layers[1].offset[0];
	unsigned long data[4] = { frame->width, frame->height, offset,frame->pts };
	auto dma_buf_fd = prime_desc.objects[0].fd;
	// 准备发送消息
	msg.msg_control = buf;
	msg.msg_controllen = sizeof(buf);
	cmsg = CMSG_FIRSTHDR(&msg);
	cmsg->cmsg_level = SOL_SOCKET;
	cmsg->cmsg_type = SCM_RIGHTS;
	cmsg->cmsg_len = CMSG_LEN(sizeof(int));
	*(int*)CMSG_DATA(cmsg) = dma_buf_fd;

	// 发送帧尺寸
	iov[0].iov_base = data;
	iov[0].iov_len = sizeof(data);
	msg.msg_iov = iov;
	msg.msg_iovlen = 1;

	sendmsg(socket, &msg, 0);
	::close(dma_buf_fd);
	return true;
}
#endif
FrameSender::FrameSender()
{
#ifdef WIN32
#else
	m_socketProc = UnixDomainSocketProc::Inst();
#endif
	m_shmProc = ShmVideoProc::Inst();
}

FrameSender::~FrameSender()
{
#ifdef WIN32
#else
	if (m_socketProc) {
		m_socketProc->Free();
		m_socketProc = nullptr;
	}
#endif
	if (m_shmProc) {
		m_shmProc->Free();
		m_shmProc = nullptr;
	}
    jinfo("FrameSender::~FrameSender()");
}
