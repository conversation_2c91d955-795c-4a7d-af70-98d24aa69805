#ifndef IMPL_AUDIO_G7221AUDIOFRAMEENCODER_H_
#define IMPL_AUDIO_G7221AUDIOFRAMEENCODER_H_
#include <future>
#include <mutex>
#include <queue>
#include <condition_variable>
#include "AudioFrameEncoder.h"
#include "Frame.h"
#include <resampler/resampler.h>
#include <resampler/push_resampler.h>
#ifdef __cplusplus
extern "C" {
#endif
#include <g722_1.h>
#ifdef __cplusplus
}
#endif
// #include <fstream>
namespace panocom
{

class G7221AudioFrameEncoder : public AudioFrameEncoder
{
public:
	G7221AudioFrameEncoder(const std::string& jsonParams);
	~G7221AudioFrameEncoder() override;
	void onFrame(const std::shared_ptr<Frame> &f) override;
private:
	FrameFormat fmt_;
	int bit_rate_;	// bps
	int samplerate_;
	g722_1_encode_state_t *encode_state_;
#ifdef WEBRTC_RESAMPLE_ANOTHER
	nswebrtc::PushResampler<int16_t> resampler_;
#else
	nswebrtc::Resampler resampler_;
#endif       
	bool initResampler_ = false;
	int source_samplerate_;
    bool first_ = true;	
	uint8_t *encoded_buf_;

	bool running_;
    std::queue<std::shared_ptr<Frame>> frame_queue_;
    std::mutex mutex_;
	std::future<bool> encode_future_;
	std::condition_variable queue_available_;

	// std::ofstream ofs_;
};



} // namespace panocom

#endif