// created by guoyj 2024-7-3
#ifndef P_UlpFecDecoder_h
#define P_UlpFecDecoder_h

#include "UlpFec.h"
#include "FramePipeline.h"
#include "Frame.h"

using namespace webrtc;

namespace panocom
{
    class UlpFecDecoder: public FramePipeline, public RecoveredPacketReceiver
    {
    public:
        UlpFecDecoder(const std::string& jsonParams);
        ~UlpFecDecoder() override;

        void onFrame(const std::shared_ptr<Frame> &f) override;
        void OnRecoveredPacket(const uint8_t* packet, size_t length) override;
    private:
        std::unique_ptr<UlpfecReceiver> receiver_;
        uint32_t ssrc_ = 12345;
        uint8_t ulpfec_payload_type_ = 125;
    };
}

#endif