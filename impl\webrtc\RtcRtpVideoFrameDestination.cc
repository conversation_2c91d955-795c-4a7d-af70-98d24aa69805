#include "RtcRtpVideoFrameDestination.h"
#include "Frame.h"
#include "Utils.h"
#include "thread/StaticTaskQueueFactory.h"
#include "thread/ProcessThreadProxy.h"

#include <api/video/video_codec_type.h>
#include <api/video_codecs/video_codec.h>
#include <modules/include/module_common_types.h>
#include <modules/pacing/packet_router.h>
#include <modules/rtp_rtcp/source/rtcp_packet/transport_feedback.h>
#include <modules/rtp_rtcp/source/rtp_video_header.h>
#include <modules/rtp_rtcp/source/ulpfec_generator.h>
#include <rtc_base/logging.h>
#include <system_wrappers/include/field_trial.h>
#include <sys/time.h>

#include <json.hpp>
#include <ljcore/jlog.h>

#include <api/array_view.h>
#include <modules/rtp_rtcp/source/rtcp_packet/common_header.h>
#include <modules/rtp_rtcp/source/rtcp_packet/nack.h>
#include <modules/rtp_rtcp/source/rtcp_packet/rtpfb.h>

using namespace panocom;

namespace {
bool ParseCompoundPacket(rtc::ArrayView<const uint8_t> packet, int64_t time_ms) {
    webrtc::rtcp::CommonHeader rtcp_block;
    for (const uint8_t *next_block = packet.begin(); next_block != packet.end(); next_block = rtcp_block.NextPacket()) {
        ptrdiff_t remaining_blocks_size = packet.end() - next_block;
        RTC_DCHECK_GT(remaining_blocks_size, 0);
        if (!rtcp_block.Parse(next_block, remaining_blocks_size)) {
            if (next_block == packet.begin()) {
                // Failed to parse 1st header, nothing was extracted from this packet.
                jwarn("[%ld] Incoming invalid RTCP packet", time_ms);
                return false;
            }
            break;
        }
        switch (rtcp_block.type()) {
            case webrtc::rtcp::Rtpfb::kPacketType:
                switch (rtcp_block.fmt()) {
                    case webrtc::rtcp::Nack::kFeedbackMessageType: {
                        webrtc::rtcp::Nack nack;
                        if (!nack.Parse(rtcp_block)) {
                            break;
                        }
                        int i = 0;
                        for (uint16_t packet_id : nack.packet_ids()) {
                            jinfo("[%ld] Rtcp NACK Packet id: %d %d", time_ms, packet_id, i++);
                        }
                    } break;
                    // case rtcp::Tmmbr::kFeedbackMessageType: break;
                    // case rtcp::Tmmbn::kFeedbackMessageType: break;
                    // case rtcp::RapidResyncRequest::kFeedbackMessageType: break;
                    // case rtcp::TransportFeedback::kFeedbackMessageType: break;
                    default: break;
                }
                break;
            default: break;
        }
    }
    return true;
}
} // namespace

static std::unique_ptr<webrtc::FieldTrialBasedConfig> g_fieldTrial= []()
{
    auto config = std::make_unique<webrtc::FieldTrialBasedConfig>();
    /**/
    webrtc::field_trial::InitFieldTrialsFromString(
        "WebRTC-KeyframeInterval/"
        "max_wait_for_keyframe_ms:500,"
        "max_wait_for_frame_ms:1500/"
        "WebRTC-TaskQueuePacer/Enabled/");
    return config;
}();

static int getNextNaluPosition(uint8_t *buffer, int buffer_size, bool &is_aud_or_sei, int &sc_len)
{
    if (buffer_size < 3)
    {
        return -1;
    }
    is_aud_or_sei = false;
    uint8_t *head = buffer;
    uint8_t *end = buffer + buffer_size - 3;
    while (head < end)
    {
        if (head[0])
        {
            head++;
            continue;
        }
        if (head[1])
        {
            head += 2;
            continue;
        }
        if (head[2])
        {
            if (head[2] == 0x01)
            {
                if (((head[3] & 0x1F) == 9) || ((head[3] & 0x1F) == 6))
                {
                    is_aud_or_sei = true;
                }
                sc_len = 3;
                return static_cast<int>(head - buffer);
            }
            head += 3;
            continue;
        }
        if (head[3] != 0x01)
        {
            head++;
            continue;
        }
        if (head + 1 == end)
        {
            break;
        }
        if (((head[4] & 0x1F) == 9) || ((head[4] & 0x1F) == 6))
        {
            is_aud_or_sei = true;
        }
        sc_len = 4;
        return static_cast<int>(head - buffer);
    }
    return -1;
}

#define MAX_NALS_PER_FRAME 128
static int dropAUDandSEI(uint8_t *framePayload, int frameLength)
{
    uint8_t *origin_pkt_data = framePayload;
    int origin_pkt_length = frameLength;
    uint8_t *head = origin_pkt_data;

    std::vector<int> nal_offset;
    std::vector<bool> nal_type_is_aud_or_sei;
    std::vector<int> nal_size;
    bool is_aud_or_sei = false, has_aud_or_sei = false;

    int sc_positions_length = 0;
    int sc_position = 0;
    int sc_len = 4;
    while (sc_positions_length < MAX_NALS_PER_FRAME)
    {
        int nalu_position = getNextNaluPosition(origin_pkt_data + sc_position, origin_pkt_length - sc_position, is_aud_or_sei, sc_len);
        if (nalu_position < 0)
        {
            break;
        }
        sc_position += nalu_position;
        nal_offset.push_back(sc_position); // include start code.
        sc_position += sc_len;
        sc_positions_length++;
        if (is_aud_or_sei)
        {
            has_aud_or_sei = true;
            nal_type_is_aud_or_sei.push_back(true);
        }
        else
        {
            nal_type_is_aud_or_sei.push_back(false);
        }
    }
    if (sc_positions_length == 0 || !has_aud_or_sei)
        return frameLength;
    // Calculate size of each NALs
    for (unsigned int count = 0; count < nal_offset.size(); count++)
    {
        if (count + 1 == nal_offset.size())
        {
            nal_size.push_back(origin_pkt_length - nal_offset[count]);
        }
        else
        {
            nal_size.push_back(nal_offset[count + 1] - nal_offset[count]);
        }
    }
    // remove in place the AUD NALs
    int new_size = 0;
    for (unsigned int i = 0; i < nal_offset.size(); i++)
    {
        if (!nal_type_is_aud_or_sei[i])
        {
            memmove(head + new_size, head + nal_offset[i], nal_size[i]);
            new_size += nal_size[i];
        }
    }
    return new_size;
}

static bool hasRtcpFbType(const nlohmann::json& rtcpfb, const char* fbtype)
{
    if (rtcpfb.is_array())
    {
        for (auto fb = rtcpfb.begin(); fb != rtcpfb.end(); fb++)
        {
            if ((*fb)["type"] == fbtype)
            {
                return true;
                break;
            }
        }
    }
    return false;
}

// PacedSender without pacing
class NonPacedSender : public webrtc::RtpPacketSender
{
public:
    NonPacedSender(webrtc::PacketRouter *sender)
        : m_packetRouter(sender) {}
    virtual ~NonPacedSender() {}

    // Implements webrtc::RtpPacketSender
    virtual void EnqueuePackets(std::vector<std::unique_ptr<webrtc::RtpPacketToSend>> packets) override
    {
        webrtc::PacedPacketInfo pacing_info;
        for (auto &packet : packets)
        {
            m_packetRouter->SendPacket(std::move(packet), pacing_info);
            EnqueuePackets(m_packetRouter->FetchFec());
        }
    }

private:
    webrtc::PacketRouter *m_packetRouter;
};

RtcRtpVideoFrameDestination::RtcRtpVideoFrameDestination(const std::string &jsonParams)
    : keyFrameArrived_(false)
    , random_(rtc::TimeMicros())
    , ssrc_(0)
    , ssrcGenerator_(SsrcGenerator::GetSsrcGenerator())
    , clock_(nullptr)
    , timeStampOffset_(0)
    , started_(/*manual_reset=*/false, /*initially_signaled=*/false)
    , stopped_(/*manual_reset=*/false, /*initially_signaled=*/false)
    , nack_enabled_(true)
    , ulpfec_enabled_(false)
    , red_enabled_(false)
    , ajb_enabled_(false) {
    FN_BEGIN;
    name_ = "RtcRtpVideoFrameDestination";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    payloadSize_ = 1200;
    if (j.contains("maxPayloadSize"))
    {
        payloadSize_ = j["maxPayloadSize"];
    }
    payloadType_ = 96;
    if (j.contains("payloadType"))
    {
        payloadType_ = j["payloadType"];
    }
    if (j.contains("remote_payload_type")) {
        payloadType_ = j["remote_payload_type"];
    }
    red_payload_type_ = 116;
    if (j.contains("remote_red_payload_type"))
    {
        red_payload_type_ = j["remote_red_payload_type"];
    }
    ulpfec_payload_type_ = 127;
    if (j.contains("remote_ulpfec_payload_type"))
    {
        ulpfec_payload_type_ = j["remote_ulpfec_payload_type"];
    }
    startBitrate_ = 3000000;
    if (j.contains("startBitrate"))
    {
        int bitrate = j["startBitrate"];
        startBitrate_ = bitrate * 1024;
    }
    minBitrate_ = 2000000;
    if (j.contains("startBitrate"))
    {
        int bitrate = j["startBitrate"];
        minBitrate_ = bitrate * 1024;
    }
    maxBitrate_ = 4000000;
    if (j.contains("maxBitrate"))
    {
        int bitrate = j["maxBitrate"];
        maxBitrate_= bitrate * 1024;
    }
    avgBitrate_ = 4000000;
    if (j.contains("avgBitrate"))
    {
        int bitrate = j["avgBitrate"];
        avgBitrate_ = bitrate * 1024;
    }
    curBitrate_ = startBitrate_;
    if (j.contains("NACK")) {
        nack_enabled_ = j["NACK"];
    }
    if (j.contains("ULPFEC")) {
        ulpfec_enabled_ = j["ULPFEC"];
    }
    if (j.contains("AJB")) {
        ajb_enabled_ = j["AJB"];
    }
    if (j.contains("local_media") && j["local_media"].contains("ext")) {
        rtp_extmap_ = j["local_media"]["ext"];
    }
    if (j.contains("remote_media"))
    {
        if (j["remote_media"].contains("ext")) {
            remote_rtp_extmap_ = j["remote_media"]["ext"];
        }
        if (j["remote_media"].contains("rtcpFb")) {
            remote_rtp_rtcpfb_ = j["remote_media"]["rtcpFb"];
        }
    }
    if (ajb_enabled_)
    {
        if (minBitrate_ > 64 * 1024)
        { //webrtc带宽预测，不限最低带宽
            minBitrate_ = 64 * 1024;
        }
    }

    eventLog_ = std::make_shared<webrtc::RtcEventLogNull>();
    taskQueueFactory_ = createStaticTaskQueueFactory(TaskQueueType::kEncoding);
    start();
    FN_END;
}

RtcRtpVideoFrameDestination::~RtcRtpVideoFrameDestination()
{
    FN_BEGIN;
    stop();
    FN_END;
}

void RtcRtpVideoFrameDestination::start()
{
    if (taskQueue_) return;
    FN_BEGIN;
    started_.Reset();
    {
        std::unique_lock<std::mutex> locker(mutex_);
        taskQueue_ = std::make_shared<rtc::TaskQueue>(taskQueueFactory_->CreateTaskQueue("CallTaskQueue", webrtc::TaskQueueFactory::Priority::NORMAL));
        taskQueue_->PostTask([this]() {
            ssrc_ = ssrcGenerator_->CreateSsrc();
            ssrcGenerator_->RegisterSsrc(ssrc_);
            init();
            started_.Set();
        });
    }
    started_.Wait(rtc::Event::kForever);
    FN_END;
}

void RtcRtpVideoFrameDestination::stop()
{
    FN_BEGIN;
    std::unique_lock<std::mutex> locker(mutex_);
    if (taskQueue_)
    {
        stopped_.Reset();
        taskQueue_->PostTask([this]() {
            senderVideo_.reset();
            rtpRtcp_->SetSendingStatus(false);
            rtpRtcp_->SetSendingMediaStatus(false);
            ssrcGenerator_->ReturnSsrc(ssrc_);
            taskRunner_->DeRegisterModule(rtpRtcp_.get());
            taskRunner_->Stop();
            rtpSender_->OnNetworkAvailability(false);
            rtpSender_->packet_router()->RemoveSendRtpModule(rtpRtcp_.get());
            rtpSender_.reset();
            rtpRtcp_.reset();
            stopped_.Set();
            taskRunner_.reset();
            retransmissionRateLimiter_.reset();
        });
        stopped_.Wait(rtc::Event::kForever);
        taskQueue_.reset();
    }
    FN_END;
}

bool RtcRtpVideoFrameDestination::init()
{
    // taskQueue()->PostTask([this]() {
    taskRunner_.reset(new WebRTCTaskRunner("TaskRunner"));
    taskRunner_->Start();
    clock_ = webrtc::Clock::GetRealTimeClock();
    retransmissionRateLimiter_.reset(new webrtc::RateLimiter(webrtc::Clock::GetRealTimeClock(), 1000));

    std::unique_ptr<webrtc::ProcessThread> pacerThreadProxy = 
            std::make_unique<ProcessThreadProxy>(nullptr);

    webrtc::BitrateConstraints bitrateConstraints;
    bitrateConstraints.start_bitrate_bps = startBitrate_;
    bitrateConstraints.min_bitrate_bps = minBitrate_;
    bitrateConstraints.max_bitrate_bps = maxBitrate_;

    rtpSender_ = std::make_shared<webrtc::RtpTransportControllerSend>(
        webrtc::Clock::GetRealTimeClock(), eventLog_.get(),
        nullptr/*network_state_predicator_factory*/,
        nullptr/*network_controller_factory*/, bitrateConstraints,
        std::move(pacerThreadProxy)/*pacer_thread*/, taskQueueFactory_.get(), g_fieldTrial.get());

    rtpSender_->RegisterTargetTransferRateObserver(this);
    rtpSender_->OnNetworkAvailability(true);

    webrtc::RtpRtcpInterface::Configuration configuration;
    configuration.clock = clock_;
    configuration.audio = false;
    configuration.receiver_only = false;
    configuration.outgoing_transport = this;
    configuration.retransmission_rate_limiter = retransmissionRateLimiter_.get();
    configuration.local_media_ssrc = ssrc_;

    configuration.bandwidth_callback = this;
    configuration.network_state_estimate_observer = this;
    configuration.transport_feedback_callback = this;
    configuration.paced_sender = rtpSender_->packet_sender();    
    // configuration.paced_sender = nullptr;    // 停止平滑控制
    
    if (ulpfec_enabled_) {
        // TODO: Set FEC.
        ulpfec_generator_ = std::make_unique<webrtc::UlpfecGenerator>(
            red_payload_type_, ulpfec_payload_type_, clock_);
        configuration.fec_generator = ulpfec_generator_.get();
        // TODO: 合理配置保护参数
        delta_fec_params_.fec_rate = 10;
        delta_fec_params_.max_fec_frames = 1;
        key_fec_params_.fec_rate = 20;
        key_fec_params_.max_fec_frames = 1;
    }

    rtpRtcp_ = webrtc::ModuleRtpRtcpImpl2::Create(configuration);
    // m_rtpRtcp = webrtc::ModuleRtpRtcpImpl2::Create(configuration);
    rtpRtcp_->SetSendingStatus(true);
    rtpRtcp_->SetSendingMediaStatus(true);
    rtpRtcp_->SetRTCPStatus(webrtc::RtcpMode::kReducedSize);
    // Set NACK.
    rtpRtcp_->SetStorePacketsStatus(nack_enabled_, 600);
    rtpRtcp_->SetMaxRtpPacketSize(payloadSize_);

    if (ulpfec_generator_) {
        rtpRtcp_->SetFecProtectionParams(delta_fec_params_, key_fec_params_);
    }

    rtpSender_->packet_router()->AddSendRtpModule(rtpRtcp_.get(), true);

    // 本地sdp里面的extmap:id 传给rtp打包扩展头id
    if (ajb_enabled_ && rtp_extmap_.is_array())
    {
        for (auto iter = rtp_extmap_.begin(); iter != rtp_extmap_.end(); iter++)
        {
            const std::string& extension = (*iter)["uri"];
            int id = (*iter)["value"];
            if (webrtc::RtpExtension::IsSupportedForVideo(extension))
            {
                if (extension == webrtc::RtpExtension::kAbsSendTimeUri && !hasRtcpFbType(remote_rtp_rtcpfb_, "goog-remb"))
                {
                    continue;
                }
                if (extension == webrtc::RtpExtension::kTransportSequenceNumberUri && !hasRtcpFbType(remote_rtp_rtcpfb_, "transport-cc"))
                {
                    continue;
                }
                if (remote_rtp_extmap_.is_array())
                {
                    for (auto remoteIt = remote_rtp_extmap_.begin(); remoteIt != remote_rtp_extmap_.end(); remoteIt++)
                    {
                        if (extension == (*remoteIt)["uri"])
                        {
                            //假如对方后提供的extmap:id不一样，本端也要改为一样
                            id = (*remoteIt)["value"];
                            break;
                        }
                    }
                }
                rtpRtcp_->RegisterRtpHeaderExtension(extension, id);
            }
        }
    }

    webrtc::RTPSenderVideo::Config video_config;
    video_config.clock = configuration.clock;
    video_config.rtp_sender = rtpRtcp_->RtpSender();
    video_config.field_trials = g_fieldTrial.get();
    if (ulpfec_generator_) {
      video_config.fec_type = ulpfec_generator_->GetFecType();
      video_config.fec_overhead_bytes = ulpfec_generator_->MaxPacketOverhead();
    }
    senderVideo_ = std::make_unique<webrtc::RTPSenderVideo>(video_config);
    taskRunner_->RegisterModule(rtpRtcp_.get());

    return true;
}

void RtcRtpVideoFrameDestination::onFrame(const std::shared_ptr<Frame> &f)
{
    std::unique_lock<std::mutex> locker(mutex_);
    if (taskQueue_)
    {
        taskQueue_->PostTask([this, f](){
            using namespace webrtc;

            uint8_t *frameBuffer = f->getFrameBuffer();
            int frameSize = f->getFrameSize();

            if (f->getFrameFormat() == FRAME_FORMAT_H264 || f->getFrameFormat() == FRAME_FORMAT_H265)
            {
                // FIXME: temporarily filter out AUD because chrome M59 could NOT handle it correctly.
                // FIXME: temporarily filter out SEI because safari could NOT handle it correctly.
                if (f->getFrameFormat() == FRAME_FORMAT_H264)
                {
                    frameSize = dropAUDandSEI(frameBuffer, frameSize);
                }
                // Recalculate timestamp for stream substitution
                uint32_t timeStamp = presetNextTimestamp(); // frame.timeStamp + m_timeStampOffset; //kMsToRtpTimestamp * m_clock->TimeInMilliseconds();
                webrtc::RTPVideoHeader h;
                //memset(&h, 0, sizeof(webrtc::RTPVideoHeader));

                h.frame_type = Frame::isKeyFrame(f) ? VideoFrameType::kVideoFrameKey : VideoFrameType::kVideoFrameDelta;
                h.width = f->width();
                h.height = f->height();
                if (f->getFrameFormat() == FRAME_FORMAT_H264)
                {
                    h.codec = webrtc::VideoCodecType::kVideoCodecH264;
                    h.video_type_header.emplace<RTPVideoHeaderH264>();
                }
                else
                {
                    h.codec = webrtc::VideoCodecType::kVideoCodecH265;
                    h.video_type_header.emplace<RTPVideoHeaderH265>();
                }
                //jinfo("RtcRtpVideoFrameDestination::onFrame(%d) %d", f->getFrameFormat(), f->getFrameSize());
                senderVideo_->SendVideo(payloadType_, h.codec, timeStamp, timeStamp, rtc::ArrayView<const uint8_t>(frameBuffer, frameSize), h,
                                        rtpRtcp_->ExpectedRetransmissionTimeMs(), 0);

                rtpRtcp_->OnSendingRtpFrame(timeStamp, -1, payloadType_, false);
            }
            else if (f->getFrameFormat() == FRAME_FORMAT_RTCP)
            {
                std::unique_lock<std::mutex> lock(rtpRtcpMutex_);
                if (rtpRtcp_)
                {
                    //jinfo("RtcRtpVideoFrameDestination::onFrame rtcp %d", f->getFrameSize());
                    rtpRtcp_->IncomingRtcpPacket(reinterpret_cast<const uint8_t *>(f->getFrameBuffer()), f->getFrameSize());
                    // ParseCompoundPacket(rtc::MakeArrayView(reinterpret_cast<const uint8_t *>(f->getFrameBuffer()), f->getFrameSize()), clock_->TimeInMilliseconds());
                }
            }
        });
    }
}

bool RtcRtpVideoFrameDestination::SendRtp(const uint8_t *data, size_t length, const webrtc::PacketOptions &options)
{
    if (options.packet_id != -1)
    {
        rtc::SentPacket sent_packet;
        sent_packet.packet_id = options.packet_id;
        sent_packet.send_time_ms = clock_->TimeInMilliseconds();
        sent_packet.info.included_in_feedback = options.included_in_feedback;
        sent_packet.info.included_in_allocation = options.included_in_allocation;
        sent_packet.info.packet_size_bytes = length;
        sent_packet.info.packet_type = rtc::PacketType::kData;
        rtpSender_->OnSentPacket(sent_packet);
    }
    std::shared_ptr<Frame> f = Frame::CreateFrame(FRAME_FORMAT_RTP);
    if (f)
    {
        //jinfo(" RtcRtpVideoFrameDestination::SendRtp %d", length);
        f->createFrameBuffer(length);
        memcpy(f->getFrameBuffer(), data, length);
        deliverFrame(f);
        return true;
    }
    return false;
}

bool RtcRtpVideoFrameDestination::SendRtcp(const uint8_t *data, size_t len)
{
    std::shared_ptr<Frame> f = Frame::CreateFrame(FRAME_FORMAT_RTCP);
    if (f)
    {
        //jinfo("RtcRtpVideoFrameDestination::SendRtcp %d", len);
        f->createFrameBuffer(len);
        memcpy(f->getFrameBuffer(), data, len);
        deliverFrame(f);
        return true;
    }
    return false;
}

void RtcRtpVideoFrameDestination::OnReceivedEstimatedBitrate(uint32_t bitrate)
{
    //jinfo("OnReceivedEstimatedBitrate bps: %d", bitrate);
    // 如果接收端启用了webrtc反馈的带宽，要传给rtpSender_,最终决策得到OnTargetTransferRate
    rtpSender_->GetBandwidthObserver()->OnReceivedEstimatedBitrate(bitrate);
}

void RtcRtpVideoFrameDestination::OnReceivedRtcpReceiverReport(const webrtc::ReportBlockList &report_blocks, int64_t rtt, int64_t now_ms)
{

    uint32_t sender_ssrc = 0;
    if (report_blocks.size() > 0)
    {
        sender_ssrc = report_blocks.begin()->sender_ssrc;
    }
    // if (!sender_ssrc) RTC_LOG(LS_INFO) << "OnReceivedRtcpReceiverReport ssrc = " << sender_ssrc << " rtt = " << rtt << " now_ms = " << now_ms;
    if (sender_ssrc > 0)
    {
        rtpSender_->GetBandwidthObserver()->OnReceivedRtcpReceiverReport(report_blocks, rtt, now_ms);
    }
}

void RtcRtpVideoFrameDestination::OnRemoteNetworkEstimate(webrtc::NetworkStateEstimate estimate)
{
    rtpSender_->network_state_estimate_observer()->OnRemoteNetworkEstimate(estimate);
}

void RtcRtpVideoFrameDestination::OnStartRateUpdate(webrtc::DataRate start_rate)
{
    jinfo("OnStartRateUpdate %d", start_rate.bps<uint32_t>());
}

void RtcRtpVideoFrameDestination::OnTargetTransferRate(webrtc::TargetTransferRate msg)
{
    // 目标码率
    uint32_t target_bps = msg.target_rate.bps();
    //保守码率
    uint32_t stable_target_bps = msg.stable_target_rate.bps();
    jinfo("OnTargetTransferRate bps: %d, %d", target_bps, stable_target_bps);

    if (!ajb_enabled_)
    {
        return;
    }
    if (cntAddPacket_ > 0 && cntTransportFeedback_ <= 0)
    {
        jinfo("OnTargetTransferRate, remote unsupport transport-cc");
        return;
    }
    // TODO: 根据带宽更新feedback消息的间隔，令发送rtcp只占5%带宽, 但是feedback是在RtcRtpVideoFrameSource模块发送
    // For controlling the rate of feedback messages.
    //receive_side_cc_.OnBitrateChanged(target_bitrate_bps);

    // 通过码率分配器分配给每路流后通过OnBitrateUpdated回调，目前只有1个视频码流占用这份带宽，没必要用码率分配器了

    // 目标码率（带宽）减去平均额外开销（冗余包/rtcp...）得到有效负载码率
    // 再减去打包开销(rtp封装...)得到目标编码码率
    // 根据目标编码码率重新计算overhead...
    int32_t target_encoder_bps = stable_target_bps;// target_bps * 0.9;// *0.85; // 本来是控制rtcp占5%带宽，但jrtp重复发了rtcp

    if (target_bps >= avgBitrate_)
    {
        target_encoder_bps = avgBitrate_;
    }
    else if (target_encoder_bps <= 0)
    {
        return;
    }
    // 估计码率与当前设置的编码码率 进行比较并更新设置...
    int32_t diff = abs((int32_t)curBitrate_ - target_encoder_bps);
    if ((diff >= 64 * 1024 || diff >= curBitrate_ / 10)
        || (target_encoder_bps == avgBitrate_ && curBitrate_ != avgBitrate_))
    {
        curBitrate_ = target_encoder_bps;

        std::string key = "vidcodec-bitrate";
        auto it = notifycallbacks_.find(key);
        if (it != notifycallbacks_.end() && it->second.cb)
        {
            nlohmann::json j;
            j[key] = curBitrate_ / 1024;
            std::string jsonStr = j.dump();

            it->second.cb(key, jsonStr, it->second.param);
        }
    }
}

void RtcRtpVideoFrameDestination::OnAddPacket(const webrtc::RtpPacketSendInfo &packet_info)
{
    ++cntAddPacket_;
    rtpSender_->transport_feedback_observer()->OnAddPacket(packet_info);
}

void RtcRtpVideoFrameDestination::OnTransportFeedback(const webrtc::rtcp::TransportFeedback &feedback)
{
    ++cntTransportFeedback_;
    uint32_t sender_ssrc = feedback.sender_ssrc();
    if (!sender_ssrc)
        RTC_LOG(LS_INFO) << "OnTransportFeedback ssrc = " << sender_ssrc;

    // 传给rtpSender_,用于码率预估
    rtpSender_->transport_feedback_observer()->OnTransportFeedback(feedback);
}

void RtcRtpVideoFrameDestination::EnqueuePackets(std::vector<std::unique_ptr<webrtc::RtpPacketToSend>> packets)
{
    rtpSender_->packet_sender()->EnqueuePackets(std::move(packets));
}

uint32_t RtcRtpVideoFrameDestination::convertToRTPTimestamp(struct timeval tv)
{
    // Begin by converting from "struct timeval" units to RTP timestamp units:
    u_int32_t timestampIncrement = (90000 * tv.tv_sec);
    timestampIncrement += (u_int32_t)(90000 * (tv.tv_usec / 1000000.0) + 0.5); // note: rounding

    // Then add this to our 'timestamp base':
    if (fNextTimestampHasBeenPreset_)
    {
        // Make the returned timestamp the same as the current "fTimestampBase",
        // so that timestamps begin with the value that was previously preset:
        fTimestampBase_ -= timestampIncrement;
        fNextTimestampHasBeenPreset_ = false;
    }

    u_int32_t const rtpTimestamp = fTimestampBase_ + timestampIncrement;
#ifdef DEBUG_TIMESTAMPS
    fprintf(stderr, "fTimestampBase: 0x%08x, tv: %lu.%06ld\n\t=> RTP timestamp: 0x%08x\n",
            fTimestampBase, tv.tv_sec, tv.tv_usec, rtpTimestamp);
    fflush(stderr);
#endif

    return rtpTimestamp;
}

uint32_t RtcRtpVideoFrameDestination::presetNextTimestamp()
{
    struct timeval timeNow;
    gettimeofday(&timeNow, NULL);

    u_int32_t tsNow = convertToRTPTimestamp(timeNow);
    // fTimestampBase = tsNow;
    // fNextTimestampHasBeenPreset = true;
    return tsNow;
}
