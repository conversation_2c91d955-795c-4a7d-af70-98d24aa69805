#include "FfVideoEncoder.h"
#include "Frame.h"
#include "Utils.h"
#include <json.hpp>
#include <ljcore/jlog.h>
#include "FfUtils.h"
using namespace panocom;

FfVideoEncoder::FfVideoEncoder(const std::string& jsonParams):pts_(0)
{
    FN_BEGIN;
    name_ = "FfVideoEncoder";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    fps_ = 30;
    if (j.contains("fps"))
    {
        fps_ = j["fps"];
    }
    codecName_ = "libx264";
    if (j.contains("codec"))
    {
        codecName_ = j["codec"];
    }
    if (codecName_ == "h264")
    {
        codecName_ = "libx264";
    }
    else if (codecName_ == "h265" || codecName_ == "hevc")
    {
        codecName_ = "libx265";
    }
    else
    {
        codecName_ = "libx264";
    }
    kbps_ = 2000;
    if (j.contains("kbps"))
    {
        kbps_ = j["kbps"];
    }
    gop_ = 30;
    if (j.contains("gop"))
    {
        gop_ = j["gop"];
    }
    width_ = 1280;
    if (j.contains("width"))
    {
        width_ = j["width"];
    }
    height_ = 720;
    if (j.contains("height"))
    {
        height_ = j["height"];
    }

    jinfo("FfVideoEncoder %s %dx%d@%d gop=%d kbps=%d jsonParams:%s ", codecName_.c_str(), width_, height_, fps_, gop_, kbps_, jsonParams.c_str());

    start();
    
    FN_END;
}

FfVideoEncoder::~FfVideoEncoder()
{
    FN_BEGIN;
    stop();
    FN_END;
}

void FfVideoEncoder::start()
{
    if (thread_.isRunning()) return;
    FN_BEGIN;
    avcodec_ = avcodec_find_encoder_by_name(codecName_.c_str());
    if (!avcodec_)
    {
        return;
    }
    ctx_ = avcodec_alloc_context3(avcodec_);
    if (ctx_)
    {
        /* put sample parameters */
        if (codecName_ == "libx264")
        {
            fmt_ = AV_PIX_FMT_YUV420P;// AV_PIX_FMT_YUVJ422P被弃用，ffplay无法播放
            ctx_->bit_rate = kbps_;
        }
        else
        {
            fmt_ = AV_PIX_FMT_NV12;
            ctx_->bit_rate = kbps_ * 1000;
        }
        /* resolution must be a multiple of two */
        ctx_->width = width_;
        ctx_->height = height_;
        /* frames per second */
        ctx_->time_base.num = 1;
        ctx_->time_base.den = fps_;
        ctx_->gop_size = gop_;
        ctx_->global_quality = 32;
        ctx_->max_b_frames = 0;
        ctx_->pix_fmt = (AVPixelFormat)fmt_;
        jinfo("FfVideoEncoder::start() pix_fmt:%d %s",ctx_->pix_fmt,FfUtils::ff_get_pix_fmt_name(ctx_->pix_fmt));
        int ret = avcodec_open2(ctx_, avcodec_, NULL);
        if (ret < 0)
        {
            jerror("FfVideoEncoder::start() avcodec_open2 ret < 0  %s",FfUtils::ff_err2str(ret));
        }
        else
        {
            jinfo("FfVideoEncoder %dx%d@%d %s", width_, height_, fps_, codecName_.c_str());
            thread_.start();
        }
    }
    else
    {
        
    }
    inputFrameCount_ =0;
    outputFrameCount_ =0;
    FN_END;
}

void FfVideoEncoder::stop()
{
    if (!thread_.isRunning()) return;
    FN_BEGIN;
    std::unique_lock<std::mutex> locker(frame_mutex_);
    thread_.stop(true);
    if (ctx_)
    {
        avcodec_close(ctx_);
        avcodec_free_context(&ctx_);
    }
    FN_END;
}

void FfVideoEncoder::onFrame(const std::shared_ptr<Frame>& frame)
{
    if (frame->getFrameFormat() != FRAME_FORMAT_FFAVFRAME || !thread_.isRunning()) {
        jerror("FfVideoEncoder::onFrame frame:%dx%d frameFormat:%d encoder:%dx%d fmt_:%d isRunning:%d", frame->width(), frame->height(), frame->getFrameFormat(), width_, height_,  fmt_,thread_.isRunning());
        return;
    }
    inputFrameCount_ ++;
//    jinfo("FfVideoEncoder input inputFrameCount_:%d outputFrameCount_:%d", inputFrameCount_,outputFrameCount_);
    printInputStatus("ffencoder");
    std::unique_lock<std::mutex> locker(frame_mutex_);
    thread_.loop()->runInLoop([this, frame](){
        auto ff = frame;
        FFAVFrame* f = (FFAVFrame*)frame.get();
        AVFrame* avframe = (AVFrame*)f->getAVFrame();
        if (fmt_ != avframe->format)
        {
            if (!swsCtx_)
            {
                swsCtx_ = sws_getContext(avframe->width, avframe->height, (AVPixelFormat)avframe->format, width_, height_,  (AVPixelFormat)fmt_, SWS_FAST_BILINEAR, NULL, NULL, NULL);
                jinfo("sws_getContext %dx%d %d %dx%d %d swsCtx_:%p", avframe->width, avframe->height, avframe->format, width_, height_,  fmt_,swsCtx_);
                if(avframe->format == AV_PIX_FMT_YUVJ422P){
                    sws_setColorspaceDetails(swsCtx_,
                                             sws_getCoefficients(SWS_CS_ITU601),1,//src colorspace,full range
                                             sws_getCoefficients(SWS_CS_ITU601),0,//dst colorspace,limited
                                             0,1<<16,1<<16);
                }
            }

            if (swsCtx_)
            {
                AVFrame* newFrame = av_frame_alloc();
                int size = av_image_get_buffer_size((AVPixelFormat)fmt_, width_, height_, 16);
                uint8_t* buffer = (uint8_t*)av_malloc(size);
                av_image_fill_arrays(newFrame->data, newFrame->linesize, buffer, (AVPixelFormat)fmt_, width_, height_, 16);
                newFrame->width = width_;
                newFrame->height = height_;
                newFrame->pts = avframe->pts;
                newFrame->format = fmt_;
                sws_scale(swsCtx_, avframe->data, avframe->linesize, 0, ctx_->height, newFrame->data, newFrame->linesize);
                //jinfo("FfVideoEncoder::onFrame sws_scale %dx%d %d %d %d fmt_:%d format:%d pts1:%llu  pts2:%llu ",newFrame->width, newFrame->height, newFrame->linesize[0], newFrame->linesize[1], newFrame->linesize[2],fmt_,newFrame->format,(unsigned long long)avframe->pts,(unsigned long long)newFrame->pts);
                ff = std::make_shared<FFAVFrame>(newFrame);
                avframe = newFrame;
            }
        }

        //pts需>=0且递增
        if(avframe->pts <= pts_){
            pts_ ++;
            avframe->pts = pts_;
        }else{
            pts_ = avframe->pts;
        }


        int ret = avcodec_send_frame(ctx_, avframe);
        if (ret < 0)
        {
            jerror("avcodec_send_frame error: ret < 0 %d  %s", ret,FfUtils::ff_err2str(ret));
        }
        while (ret >= 0)
        {
            AVPacket *pkt = av_packet_alloc();
            ret = avcodec_receive_packet(ctx_, pkt);
            if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF)
            {
                //LOG(ERROR) << "EAGAIN";
                av_packet_free(&pkt);
                break;
            }
            else if (ret < 0)
            {
                jerror("avcodec_receive_packet  error:ret<0  %d  %s", ret,FfUtils::ff_err2str(ret));
                av_packet_free(&pkt);
            }
            else
            {
                //if (pf_ == nullptr)
                //{
                //    pf_ = fopen("encode.h264", "wb");
                //}
                //if (pf_)
                //{
                //    fwrite(pkt->data, 1, pkt->size, pf_);
                //}
                //LOG(ERROR) << "got a h264 frame";
                //jinfo("got a h264 frame %s", hexStr(pkt->data, 100).c_str());
                FrameFormat format = FRAME_FORMAT_H264;
                if (codecName_ == "libx265")
                {
                    format = FRAME_FORMAT_H265;
                }
                std::shared_ptr<Frame> f = Frame::CreateFrame(format);
                f->setGroupId(getGroupId());
                f->createFrameBuffer(pkt->size);
                memcpy(f->getFrameBuffer(), pkt->data, pkt->size);

                outputFrameCount_++;
//                jinfo("FfVideoEncoder output inputFrameCount_:%d outputFrameCount_:%d", inputFrameCount_,outputFrameCount_);
                deliverFrame(f);
                av_packet_free(&pkt);
                printOutputStatus("ffencoder");
            }
        }
    });
}