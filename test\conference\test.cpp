#include "MediaManagerInterface.h"
#include <ljcore/jlog.h>
#include <json.hpp>
#include <thread>
#include <chrono>

using namespace panocom;
int main(const int argc, const char *argv[]) {
    jinfo("=============Conference Test=============");
    MediaManagerInterface mmi;
    auto devices = mmi.ListAudioDeviceInfos();
    for (const auto &device: devices) {
        jinfo("[%d] Name: %s <%d, %d> Desc: %s", device.index, device.name.c_str(), device.can_capture, device.can_playback, device.desc.c_str());
    }
    nlohmann::json j;
    if (argc <= 2)
        j["audio"]["dev"] = 0;
    else 
        j["audio"]["dev"] = atoi(argv[2]);
    if (argc <= 1)
        j["audio"]["path"] = "test.pcm";
    else 
        j["audio"]["path"] = argv[1];
    j["audio"]["loop"] = true;
    jinfo("StartPlayFile: %d", mmi.StartPlayFile(j.dump()));
    std::this_thread::sleep_for(std::chrono::seconds(30));
    jinfo("StopPlayFile: %d", mmi.StopPlayFile("{}"));
    return 0;
}