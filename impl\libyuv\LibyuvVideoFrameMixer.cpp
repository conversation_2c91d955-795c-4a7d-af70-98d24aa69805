#include "LibyuvVideoFrameMixer.h"
#include "Frame.h"
#include "Utils.h"
#include <json.hpp>
#include <ljcore/jlog.h>
#include <libyuv.h>
#include <stdio.h>

using namespace panocom;

LibyuvVideoFrameMixer::LibyuvVideoFrameMixer(const std::string& jsonParams)
{
    FN_BEGIN;
    jinfo("LibyuvVideoFrameMixer %s", jsonParams.c_str());
    name_ = "LibyuvVideoFrameMixer";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    width_ = 1280;
    if (j.contains("width"))
    {
        width_ = j["width"];
    }
    height_ = 720;
    if (j.contains("height"))
    {
        height_ = j["height"];
    }
    int fps = 25;
    if (j.contains("fps"))
    {
        fps = j["fps"];
    }
    outputFrameGap_ = 1000 / fps;
    outputFrameTick_ = GetTickCount();

    int frameSize = av_image_get_buffer_size(AV_PIX_FMT_NV12, width_, height_, 1);
    frameBufferManager_ = std::make_unique<FrameBufferManager>(5, frameSize, FRAME_FORMAT_FFAVFRAME);

    if (j.contains("videoLayout") && j["videoLayout"].is_array())
    {
        for (int i = 0; i < j["videoLayout"].size(); ++i)
        {
            if (j["videoLayout"][i].contains("id") &&
                j["videoLayout"][i].contains("rect") &&
                j["videoLayout"][i]["rect"].contains("x") &&
                j["videoLayout"][i]["rect"].contains("y") &&
                j["videoLayout"][i]["rect"].contains("w") &&
                j["videoLayout"][i]["rect"].contains("h"))
            {
                Region r;
                r.id = j["videoLayout"][i]["id"];
                r.rect.x = j["videoLayout"][i]["rect"]["x"];
                r.rect.y = j["videoLayout"][i]["rect"]["y"];
                r.rect.w = j["videoLayout"][i]["rect"]["w"];
                r.rect.h = j["videoLayout"][i]["rect"]["h"];
                jinfo("video layout id = %d x = %d y= %d w = %d h = %d", r.id, r.rect.x, r.rect.y, r.rect.w, r.rect.h);
                videoLayout_.push_back(r);
            }
        }
    }
    start();
    
    FN_END;
}

LibyuvVideoFrameMixer::~LibyuvVideoFrameMixer()
{
    FN_BEGIN;
    stop();
    FN_END;
}

void LibyuvVideoFrameMixer::start()
{
    if (thread_.isRunning()) return;
    FN_BEGIN;
    thread_.loop()->setInterval(outputFrameGap_, [this](hv::TimerID id) {
        if (outputFrame_)
        {
            deliverFrame(outputFrame_);
            generateOutputFrame();
            printOutputStatus("libyuv");
        }
    });

    thread_.start();
    FN_END;
}

void LibyuvVideoFrameMixer::stop()
{
    if (!thread_.isRunning()) return;
    FN_BEGIN;
    std::unique_lock<std::mutex> locker(frame_mutex_);
    thread_.stop(true);
    FN_END;
}

void LibyuvVideoFrameMixer::onFrame(const std::shared_ptr<Frame>& frame)
{
    if (frame->getFrameFormat() != FRAME_FORMAT_FFAVFRAME || !thread_.isRunning()) return;
    printInputStatus("libyuv");
    std::unique_lock<std::mutex> locker(frame_mutex_);
    if (!thread_.isRunning()) return;
    thread_.loop()->runInLoop([this, frame](){
        FFAVFrame* f = (FFAVFrame*)frame.get();
        AVFrame* avframe = (AVFrame*)f->getAVFrame();
        int groupId = frame->getGroupId();
        for (int i = 0; i < videoLayout_.size(); ++i)
        {
            if (videoLayout_[i].id == groupId)
            {
                if (outputFrame_ == nullptr)
                {
                    generateOutputFrame();
                }
                if (outputFrame_)
                {
                    FFAVFrame* temp = (FFAVFrame*)outputFrame_.get();
                    AVFrame* outputAvframe = (AVFrame*)temp->getAVFrame();

                    int ret = libyuv::NV12Scale(
                        avframe->data[0], avframe->linesize[0],
                        avframe->data[1], avframe->linesize[1],
                        avframe->width, avframe->height,
                        outputAvframe->data[0] + videoLayout_[i].rect.y * outputAvframe->linesize[0] + videoLayout_[i].rect.x, outputAvframe->linesize[0],
                        outputAvframe->data[1] + (videoLayout_[i].rect.y * outputAvframe->linesize[0] + videoLayout_[i].rect.x * 2) / 2, outputAvframe->linesize[1],
                        videoLayout_[i].rect.w, videoLayout_[i].rect.h, libyuv::kFilterBox);
                }
            }
        }
    });
}

void LibyuvVideoFrameMixer::generateOutputFrame()
{
    outputFrame_ = frameBufferManager_->getFrame();
    if (outputFrame_)
    {
        int frameSize = av_image_get_buffer_size(AV_PIX_FMT_NV12, width_, height_, 1);
        AVFrame* avframe = av_frame_alloc();
        nlohmann::json j;
        j["avframe"] = (long)avframe;
        outputFrame_->updateParams(j.dump());
        outputFrame_->setGroupId(getGroupId());

        avframe->format = AV_PIX_FMT_NV12;
        avframe->width = width_;
        avframe->height = height_;
        int ret = av_image_fill_arrays(avframe->data, avframe->linesize, outputFrame_->getFrameBuffer(), AV_PIX_FMT_NV12, width_, height_, 1);
    }
}