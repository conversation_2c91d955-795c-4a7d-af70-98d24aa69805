// created by guoyj 2024-7-2
#ifndef P_UlpFecEncoder_h
#define P_UlpFecEncoder_h

#include "UlpFec.h"

#include "Frame.h"
#include "FramePipeline.h"

using namespace webrtc;

namespace panocom
{
    class UlpFecEncoder: public FramePipeline
    {
    public:
        UlpFecEncoder(const std::string& jsonParams);
        ~UlpFecEncoder() override;

        void onFrame(const std::shared_ptr<Frame> &f) override;

    private:
        std::unique_ptr<RtpPacket> buildRedPayload(const uint8_t* data, int len);
    
    private:
        UlpfecGenerator ulpfec_generator_;
        uint8_t red_payload_type_ = 127;
        uint8_t ulpfec_payload_type_ = 125;
        uint8_t payload_type_ = 96;
    };
}

#endif