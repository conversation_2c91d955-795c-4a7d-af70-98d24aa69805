{"name": "template", "audio": {"capturer": {"name": "AlsaAudioFrameCapturer", "samplerate": 48000, "channel": 2, "dev": -1}, "render": {"name": "AlsaAudioFrameRender", "samplerate": 48000, "channel": 2, "dev": -1}, "encoder": {"name": "native", "codec": [{"name": "opus", "samplerate": 16000, "channel": 1, "fec": 1, "rc": "vbr"}, {"name": "g711", "samplerate": 8000, "channel": 1}]}, "decoder": {"name": "native", "codec": [{"name": "opus", "samplerate": 16000, "channel": 1, "fec": 1, "rc": "vbr"}, {"name": "g711", "samplerate": 8000, "channel": 1}]}, "uplink": {"processer": [{"name": "AudioFramePacer", "ptime": 20}], "file-src": {"name": "PCMFileFrameSource"}, "rtp": "JRtpEngine"}, "downlink": {"processer": [], "file-src": {"name": "PCMFileFrameSource"}, "rtp": "JRtpEngine"}}}