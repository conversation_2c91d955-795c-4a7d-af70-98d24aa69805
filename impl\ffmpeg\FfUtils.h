//
// Created by li-weibing on 2025/5/9.
//

#ifndef MEDIAPIPELINE_FFUTILS_H
#define MEDIAPIPELINE_FFUTILS_H
extern "C" {
#include <libavutil/error.h>
#include <libavutil/opt.h>
#include <libavutil/imgutils.h>
#include <libavcodec/avcodec.h>
#include <libswscale/swscale.h>
}
class FfUtils {
public:
    static const char * ff_err2str(int errRet);
    static const char * ff_get_pix_fmt_name(AVPixelFormat pix_fmt);
};


#endif //MEDIAPIPELINE_FFUTILS_H
