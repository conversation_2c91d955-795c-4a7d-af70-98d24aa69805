#include <Network/sockutil.h>
#include "MediaManagerInterfaceImp.h"
#include "MediaManagerInterface.h"
#include "CodecInfo.h"
#include <sdptransform/sdptransform.hpp>

#include "jlog.h"

using namespace panocom;
using namespace toolkit;

#define CHECK_AUDIO_SESSION_EXIST \
if (sessions_[AUDIO].find(id) == sessions_[AUDIO].end()) \
{ \
    jerror("Please CreateSession fist"); \
    return MediaManagerInterface::MM_SESSION_NOT_FOUND; \
}

#define CHECK_VIDEO_SESSION_EXIST \
if (sessions_[VIDEO].find(id) == sessions_[VIDEO].end()) \
{ \
    jerror("Please CreateSession fist"); \
    return MediaManagerInterface::MM_SESSION_NOT_FOUND; \
} 

static std::string sdpAudioTemplate = R"(v=0
o=- 0 0 IN IP4 127.0.0.1
s=mmi
t=0 0
m=audio 0 RTP/AVP 8 111
c=IN IP4 127.0.0.1
a=rtpmap:8 PCMA/8000
a=rtpmap:111 opus/48000
a=ptime:20
a=sendrecv
)";

static std::string sdpVideoTemplate = R"(v=0
o=- 0 0 IN IP4 127.0.0.1
s=mmi
t=0 0
m=audio 0 RTP/AVP 8 111
c=IN IP4 127.0.0.1
a=rtpmap:8 PCMA/8000
a=rtpmap:111 opus/48000
a=ptime:20
a=sendrecv
m=video 0 RTP/AVP 96 98
c=IN IP4 *********
a=rtpmap:96 H264/90000
a=rtpmap:98 H265/90000
a=sendrecv
)";

std::shared_ptr<uint16_t> MediaManagerInterfaceImp::getPort()
{
    std::unique_lock<std::mutex> locker(mutex_);
    if (ports_.empty())
    {
        return nullptr;
    }
    std::shared_ptr<uint16_t> ret = std::shared_ptr<uint16_t>(new uint16_t, [this](uint16_t* port){
        std::unique_lock<std::mutex> locker(mutex_);
        ports_.insert(*port);
        delete port;
    });
    *ret = *ports_.begin();
    ports_.erase(*ret);
    return ret;
}

std::string MediaManagerInterfaceImp::getIP(const std::string& ifdev)
{
    std::string ip;
    std::vector<std::map<std::string, std::string>> devs = SockUtil::getInterfaceList();
    if (devs.empty())
    {
        jerror("GetLocalSDP getInterfaceList fail");
        return ip;
    }
    for (auto it = devs.begin(); it != devs.end(); it++)
    {
        if ((*it)["name"] == ifdev)
        {
            ip = (*it)["ip"];
            break;
        }
    }
    if (ip == "")
    {
        for (auto it = devs.begin(); it != devs.end(); it++)
        {
            if ((*it)["ip"] != "127.0.0.1" && (*it)["ip"] != "0.0.0.0" )
            {
                ip = (*it)["ip"];
                break;
            }
        }
    }
    return ip;
}

CodecInst MediaManagerInterfaceImp::codecInstFromJson(const nlohmann::json& rtp, int ptime)
{
    jinfo("codecInstFromJson %s", rtp.dump().c_str());
    CodecInst inst;
    inst.rate = 8000;
    inst.chn = 1;
    inst.profile = 0;
    inst.pt = 0;
    if (rtp.contains("codec"))
    {
        std::string codec = rtp["codec"];
        memcpy(inst.name, codec.data(), codec.size() + 1);
    }
    if (rtp.contains("rate"))
    {
        inst.rate = rtp["rate"];
    }
    if (rtp.contains("payload"))
    {
        inst.pt = rtp["payload"];
    }
    if (rtp.contains("encoding"))
    {
        if (rtp["encoding"].is_number())
        {
            inst.chn = rtp["encoding"];
        }
        else if (rtp["encoding"].is_string())
        {
            std::string encoding = rtp["encoding"];
            inst.chn = atoi(encoding.c_str());
        }
    }
    std::list<CodecInst> insts;
    if (getCodecInst(inst.name, insts))
    {
        for (CodecInst& i : insts)
        {
            if (i.rate == inst.rate && i.chn == inst.chn)
            {
                inst.ptime = i.ptime;
                inst.fmt = i.fmt;
                break;
            }
        }
    }
    if (ptime)
    {
        inst.ptime = ptime;
    }
    return inst;
}

MediaManagerInterfaceImp::MediaManagerInterfaceImp(const std::string& jsonParams)
{
    sessions_.resize(2);
    codecs_.resize(2);

    nlohmann::json j = sdptransform::parse(sdpAudioTemplate);
    templates_.push_back(j);
    j = sdptransform::parse(sdpVideoTemplate);
    templates_.push_back(j);

    int startPort = 10000;
    int endPort = 20000;
    j.clear();
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    if (j.contains("startPort"))
    {
        startPort = j["startPort"];
    }
    if (j.contains("endport"))
    {
        int port = j["endport"];
        if (port > startPort)
        {
            endPort = port;
        }
    }
    for (uint16_t i = startPort; i < endPort; i++)
    {
        ports_.insert(i);
    }
    std::string config = "pipeline.json";
    if (j.contains("config"))
    {
        config = j["config"];
    }
    LoadConfig(config);
}

MediaManagerInterfaceImp::~MediaManagerInterfaceImp()
{
    
}

void MediaManagerInterfaceImp::LoadConfig(const std::string& path)
{
    FILE* pf = fopen(path.c_str(), "r");
    if (pf)
    {
        fseek(pf, 0, SEEK_END);
        int len = ftell(pf);
        fseek(pf, 0, SEEK_SET);
        std::vector<uint8_t> buf;
        buf.resize(len + 1);
        memset(buf.data(), 0, len + 1);
        int ret = fread(buf.data(), 1, len, pf);
        if (ret > 0)
        {
            config_ = nlohmann::json::parse(buf.data());
        }
        fclose(pf);
        jinfo("LoadConfig %s", config_.dump().c_str());
    }
}

int MediaManagerInterfaceImp::CreateSession(const std::string& id, const std::string& jsonParams)
{
    if (sessions_[AUDIO].find(id) != sessions_[AUDIO].end())
    {
        return MediaManagerInterface::MM_SESSION_EXIST;
    }
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    sessions_[AUDIO][id].local = MediaEndpoint();
    if (j.contains("local"))
    {
        if (j["local"].contains("audio"))
        {
            if (j["local"]["audio"].contains("id"))
            {
                sessions_[AUDIO][id].local.id = j["local"]["audio"]["id"];
            }
            if (j["local"]["audio"].contains("dev"))
            {
                sessions_[AUDIO][id].local.dev = j["local"]["audio"]["dev"];
            }
            sessions_[AUDIO][id].local.params = j["local"]["audio"];
        }
        if (j["local"].contains("video"))
        {
            if (j["local"]["video"].contains("id"))
            {
                sessions_[VIDEO][id].local.id = j["local"]["video"]["id"];
            }
            if (j["local"]["video"].contains("dev"))
            {
                sessions_[VIDEO][id].local.dev = j["local"]["video"]["dev"];
            }
            sessions_[VIDEO][id].local.params = j["local"]["video"];
        }
    }
    if (j.contains("remote"))
    {
        if (j["remote"].contains("audio"))
        {
            if (j["remote"]["audio"].contains("id"))
            {
                sessions_[AUDIO][id].remote.id = j["remote"]["audio"]["id"];
            }
            if (j["remote"]["audio"].contains("dev"))
            {
                sessions_[AUDIO][id].remote.dev = j["remote"]["audio"]["dev"];
            }
            sessions_[AUDIO][id].remote.params = j["remote"]["audio"];
        }
        if (j["remote"].contains("video"))
        {
            if (j["remote"]["video"].contains("id"))
            {
                sessions_[VIDEO][id].remote.id = j["remote"]["video"]["id"];
            }
            if (j["remote"]["video"].contains("dev"))
            {
                sessions_[VIDEO][id].remote.dev = j["remote"]["video"]["dev"];
            }
            sessions_[VIDEO][id].remote.params = j["remote"]["video"];
        }
    }
    if (sessions_[VIDEO][id].local.dev < 0)
    {
        sessions_[VIDEO][id].local.dev = 0;
    }
    if (sessions_[VIDEO][id].remote.dev < 0)
    {
        sessions_[VIDEO][id].remote.dev = sessions_[VIDEO][id].local.dev + 1;
    }
    jinfo("CreateSession %s localdev = %d remotedev = %d", id.c_str(), sessions_[VIDEO][id].local.dev, sessions_[VIDEO][id].remote.dev);
    return MediaManagerInterface::MM_SUCCESS; 
}

int MediaManagerInterfaceImp::StartSession(const std::string& id)
{
    return MediaManagerInterface::MM_SUCCESS; 
}

int MediaManagerInterfaceImp::PauseSession(const std::string& id)
{
    return MediaManagerInterface::MM_SUCCESS; 
}

int MediaManagerInterfaceImp::StopSession(const std::string& id)
{
    return MediaManagerInterface::MM_SUCCESS; 
}

int MediaManagerInterfaceImp::ReleaseSession(const std::string& id)
{
    CHECK_AUDIO_SESSION_EXIST;
    for (auto it = sessions_.begin(); it != sessions_.end(); it++)
    {
        for (auto it2 = (*it)[id].local.pipelines.begin(); it2 != (*it)[id].local.pipelines.end(); it2++)
        {
            (*it2)->stop();
        }
        for (auto it2 = (*it)[id].remote.pipelines.begin(); it2 != (*it)[id].remote.pipelines.end(); it2++)
        {
            (*it2)->stop();
        }
        it->erase(id);
    }
    return MediaManagerInterface::MM_SUCCESS; 
}

int MediaManagerInterfaceImp::GetLocalSDP(const std::string& id, std::string& sdp, const std::string& jsonParams)
{
    CHECK_AUDIO_SESSION_EXIST;
    if (codecs_[AUDIO].empty())
    {
        codecs_[AUDIO] = GetSupportAudioCodecs();
    }
    if (codecs_[VIDEO].empty())
    {
        codecs_[VIDEO] = GetSupportVideoCodecs();
    }
    printf("GetLocalSDP %d %d\n", codecs_[AUDIO].size(), codecs_[VIDEO].size());
    return getLocalSDP(id, sdp, jsonParams, codecs_[AUDIO], codecs_[VIDEO]);
}

int MediaManagerInterfaceImp::SetRemoteSDP(const std::string& id, const std::string& remotesdp, std::string& localSdp, const std::string& jsonParams, bool startSession)
{
    CHECK_AUDIO_SESSION_EXIST;
    int ret = MediaManagerInterface::MM_SUCCESS;
    auto sdp = sdptransform::parse(remotesdp);
    if (sessions_[AUDIO][id].local.ip != "")
    {
        jinfo("onAnswer\n%s", sdp.dump().c_str());
        // 主叫应答，优先采用对方的编码
        for (size_t i = 0; i < sdp["media"].size(); i++)
        {
            if (sdp["media"][i]["type"] == "audio")
            {
                int ptime = 0;
                if (sdp["media"][i].contains("ptime"))
                {
                    ptime = sdp["media"][i]["ptime"];
                }
                for (size_t j = 0; j < sdp["media"][i]["rtp"].size(); j++)
                {
                    CodecInst inst = codecInstFromJson(sdp["media"][i]["rtp"][j]);
                    if (isSupportCodec(inst, AUDIO))
                    {
                        std::string ip = sdp["origin"]["address"];
                        if (sdp["media"][i].contains("connection") && sdp["media"][i]["connection"].contains("ip"))
                        {
                            ip = sdp["media"][i]["connection"]["ip"];
                        }
                        std::string dir = "sendrecv";
                        if (sdp["media"][i].contains("direction"))
                        {
                            dir = sdp["media"][i]["direction"];
                        }
                        makeRemoteSession(id, inst, ip, sdp["media"][i]["port"], dir, AUDIO);
                        break;
                    }
                }
            }
            else if (sdp["media"][i]["type"] == "video")
            {
                if (sessions_[VIDEO].find(id) != sessions_[VIDEO].end())
                {
                    for (size_t j = 0; j < sdp["media"][i]["rtp"].size(); j++)
                    {
                        CodecInst inst = codecInstFromJson(sdp["media"][i]["rtp"][j]);
                        if (isSupportCodec(inst, VIDEO))
                        {
                            std::string ip = sdp["origin"]["address"];
                            if (sdp["media"][i].contains("connection") && sdp["media"][i]["connection"].contains("ip"))
                            {
                                ip = sdp["media"][i]["connection"]["ip"];
                            }
                            makeRemoteSession(id, inst, ip, sdp["media"][i]["port"], sdp["media"][i]["direction"], VIDEO);
                            break;
                        }
                    }
                }
                else
                {
                    jerror("audio call answer video sdp, sth wrong?");
                }
            }
        }
    }
    else
    {
        jinfo("onOffer\n %s", remotesdp.c_str());
        // 被叫
        // 从自己的优选编码表里去除对端的编码表中不支持的
        if (codecs_[AUDIO].empty())
        {
            GetSupportAudioCodecs();
        }
        if (codecs_[VIDEO].empty())
        {
            GetSupportVideoCodecs();
        }
        std::list<CodecInst> audioCodecs = codecs_[AUDIO];
        std::list<CodecInst> videoCodecs = codecs_[VIDEO];
        nlohmann::json audio;
        nlohmann::json video;
        for (size_t i = 0; i < sdp["media"].size(); i++)
        {
            if (sdp["media"][i]["type"] == "audio")
            {
                audio = sdp["media"][i]["rtp"];
            }
            else if (sdp["media"][i]["type"] == "video")
            {
                video = sdp["media"][i]["rtp"];
            }
        }
        for (auto it = audioCodecs.begin(); it != audioCodecs.end(); )
        {
            bool isFound = false;
            for (size_t i = 0; i < audio.size(); i++)
            {
                CodecInst inst = codecInstFromJson(audio[i]);
                //jinfo("audio codec %s", codec.c_str());
                if (inst == *it)
                {
                    isFound = true;
                    break;
                }
            }
            if (!isFound)
            {
                audioCodecs.erase(it++);
            }
            else
            {
                it++;
            }
        }
        for (auto it = videoCodecs.begin(); it != videoCodecs.end(); )
        {
            bool isFound = false;
            for (size_t i = 0; i < video.size(); i++)
            {
                CodecInst inst = codecInstFromJson(video[i]);
                //jinfo("video codec %s", codec.c_str());
                if (inst == *it)
                {
                    isFound = true;
                    break;
                }
            }
            if (!isFound)
            {
                videoCodecs.erase(it++);
            }
            else
            {
                it++;
            }
        }
        ret = getLocalSDP(id, localSdp, jsonParams, audioCodecs, videoCodecs);
    }
    return ret;
}

std::string MediaManagerInterfaceImp::GetDefaultIP()
{
    return getIP("");
}

int MediaManagerInterfaceImp::StartUplinkAudioFile(const std::string& id, const std::string& path, const std::string& jsonParams)
{
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::StopUplinkAudioFile(const std::string& id)
{
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::StartUplinkVideoFile(const std::string& id, const std::string& path, const std::string& jsonParams)
{
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::StopUplinkVideoFile(const std::string& id)
{
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::StartDownlinkAudioFile(const std::string& id, const std::string& path, const std::string& jsonParams)
{
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::StopDownlinkAudioFile(const std::string& id)
{
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::StartDownlinkVideoFile(const std::string& id, const std::string& path, const std::string& jsonParams)
{
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::StopDownlinkVideoFile(const std::string& id)
{
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::GetLocalSDP(const std::string& id, const std::string& jsonParams, const SDPNotifyCallback& cb)
{
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::SetRemoteSDP(const std::string& id, const std::string& sdp, const std::string& jsonParams, const SDPNotifyCallback& cb)
{
    return MediaManagerInterface::MM_SUCCESS;
}

bool MediaManagerInterfaceImp::isSupportCodec(const CodecInst& codec, CodecType type)
{
    for (auto findcodec: codecs_[type])
    {
        jinfo("findcodec %s %d %d", findcodec.name, findcodec.chn, findcodec.rate);
    }
    
    bool ret = std::find(codecs_[type].begin(), codecs_[type].end(), codec) != codecs_[type].end();
    jinfo("isSupportCodec(%d) %s %d %d", ret, codec.name, codec.chn, codec.rate);
    return ret;
}

int MediaManagerInterfaceImp::AddToMediaPool(const std::string poolId, const std::string& id)
{
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::RemoveFromMediaPool(const std::string poolId, const std::string& id)
{
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::MediaCtrl(const std::string& id, const std::string& jsonParams)
{
    return MediaManagerInterface::MM_SUCCESS;
}

int MediaManagerInterfaceImp::SetMediaLayout(const std::list<Region>& layout)
{
    return MediaManagerInterface::MM_SUCCESS;
}

std::list<CodecInst> MediaManagerInterfaceImp::GetSupportAudioCodecs()
{
    return GetSupportCodecs(AUDIO);
}

std::list<CodecInst> MediaManagerInterfaceImp::GetSupportVideoCodecs()
{
    return GetSupportCodecs(VIDEO);
}

std::list<CodecInst> MediaManagerInterfaceImp::GetSupportCodecs(CodecType type)
{
    std::list<CodecInst> codecs;
    if (type == AUDIO)
    {
        printf("GetSupportCodecs audio\n");
        getCodecInst("opus", codecs);
        getCodecInst("PCMA", codecs);
        getCodecInst("PCMU", codecs);
    }
#ifdef ENABLE_VIDEO
    else if (type == VIDEO)
    {
        printf("GetSupportCodecs video\n");
        getCodecInst("H264", codecs);
        getCodecInst("H265", codecs);
    }
#endif
    for (auto inst : codecs)
    {
        printf("GetSupportCodecs %s\n", inst.name);
    }
    return codecs;
}

void MediaManagerInterfaceImp::SetAudioCodecPriority(const std::list<CodecInst>& codecs)
{
    codecs_[AUDIO] = codecs;
}

void MediaManagerInterfaceImp::SetVideoCodecPriority(const std::list<CodecInst>& codecs)
{
    codecs_[VIDEO] = codecs;
}

int MediaManagerInterfaceImp::getLocalSDP(const std::string& id, std::string& sdp, const std::string& jsonParams, const std::list<CodecInst>& audioCodecs, const std::list<CodecInst>& videoCodecs)
{
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    bool useVideo = false;
    if (j.contains("video"))
    {
        useVideo = j["video"];
    }
    if (!config_.contains("video"))
    {
        useVideo = false;
    }
    bool useDTLS = false;
    std::string fingerprint;
    if (j.contains("DTLS"))
    {
        useDTLS = j["DTLS"];
        if (j.contains("fingerprint"))
        {
            fingerprint = j["fingerprint"];
        }
        else
        {
            useDTLS = false;
        }
    }
    std::string ip;
    if (j.contains("ip"))
    {
        ip = j["ip"];
    }
    std::string dir = "sendrecv";
    if (j.contains("dir"))
    {
        dir = j["dir"];
    }
    std::string ifdev;
    if (ip == "")
    {
        if (j.contains("if")) 
            ifdev = j["if"];
        ip = getIP(ifdev);
    }
    if (ip == "")
    {
        return MediaManagerInterface::MM_FETCH_IP_FAIL;
    }
    jinfo("use ip %s", ip.c_str());
    nlohmann::json sdpJson = templates_[useVideo ? VIDEO : AUDIO];
    sdpJson["origin"]["address"] = ip;
    if (useDTLS)
    {
        sdpJson["fingerprint"]["type"] = "sha-1";
        sdpJson["fingerprint"]["hash"] = fingerprint;
    }
    for (size_t i = 0; i < sdpJson["media"].size(); i++)
    {
        sdpJson["media"][i]["connection"]["ip"] = ip;
        if (sdpJson["media"][i]["type"] == "audio")
        {
            makeLocalSession(id, sdpJson["media"][i], ip, dir, audioCodecs, AUDIO);
        }
        else if (sdpJson["media"][i]["type"] == "video")
        {
            makeLocalSession(id, sdpJson["media"][i], ip, dir, videoCodecs, VIDEO);
        }
    }
    sdp = sdptransform::write(sdpJson);
    jinfo("getLocalSDP id=[%s] sdp=\n%s", id.c_str(), sdp.c_str());
    return MediaManagerInterface::MM_SUCCESS; 
}

std::list<CodecInst> MediaManagerInterfaceImp::findMatchCodec(const std::list<CodecInst>& codecs, CodecType type)
{
    std::list<CodecInst> ret;
    for (const CodecInst& inst : codecs)
    {
        for (const CodecInst& inst2 : codecs_[type])
        {
            if (inst == inst2)
            {
                ret.push_back(inst);
                break;
            }
        }
    }
    return ret;
}

void MediaManagerInterfaceImp::makeLocalSession(const std::string& id, nlohmann::json& media, const std::string& ip, const std::string& dir, const std::list<CodecInst>& codecs, CodecType type)
{
    std::shared_ptr<uint16_t> rtpPort = getPort();
    std::shared_ptr<uint16_t> rtcpPort = getPort();
    if (!rtpPort)
    {
        rtpPort = std::make_shared<uint16_t>();
        *rtpPort = 0;
    }
    if (!rtcpPort)
    {
        rtcpPort = std::make_shared<uint16_t>();
        *rtcpPort = 0;
    }
    jinfo("makeLocalSession id=[%s] port=[%d] dir=[%s] codecs=[%d]", id.c_str(), *rtpPort, dir.c_str(), codecs.size());
    media["port"] = *rtpPort;
    sessions_[type][id].local.ip = ip;
    sessions_[type][id].local.rtpPort = rtpPort;
    sessions_[type][id].local.rtcpPort = rtcpPort;
    media["rtp"].clear();
    std::string payloads;
    for (const CodecInst& inst : codecs)
    {
        nlohmann::json codec;
        codec["codec"] = inst.name;
        codec["payload"] = inst.pt;
        codec["rate"] = inst.rate;
        if (inst.chn > 1)
        {
            codec["encoding"] = inst.chn;
        }
        media["rtp"].push_back(codec);
        payloads += std::to_string(inst.pt);
        payloads += " ";
    }
    if (payloads[payloads.size() - 1] == ' ')
    {
        payloads = payloads.substr(0, payloads.size() - 1);
    }
    media["payloads"] = payloads;
    if (*rtpPort == 0)
    {
        media["direction"] = "inactive";
    }
    else
    {
        media["direction"] = dir;
    }
}

void MediaManagerInterfaceImp::makeRemoteSession(const std::string &id, const CodecInst &codec, const std::string& ip, int port, const std::string &dir, CodecType type)
{
    jinfo("makeRemoteSession id=[%s] port=[%d] dir=[%s] codec[%s-%d-%d]", id.c_str(), port, dir.c_str(), codec.name, codec.rate, codec.chn);
    sessions_[type][id].local.codec = codec;
    sessions_[type][id].remote.codec = codec;
    sessions_[type][id].remote.ip = ip;
    sessions_[type][id].remote.rtpPort = std::make_shared<uint16_t>();
    *sessions_[type][id].remote.rtpPort = port;
    sessions_[type][id].remote.rtcpPort = std::make_shared<uint16_t>();
    *sessions_[type][id].remote.rtcpPort = port + 1;
    if (dir == "sendrecv")
    {
        sessions_[type][id].dir = MediaSession::SENDRECV;
    }
    else if (dir == "sendonly")
    {
        sessions_[type][id].dir = MediaSession::RECVONLY;
    }
    else if (dir == "recvonly")
    {
        sessions_[type][id].dir = MediaSession::SENDONLY;
    }
    else if (dir == "inactive")
    {
        sessions_[type][id].dir = MediaSession::INACTIVE;
    }
}