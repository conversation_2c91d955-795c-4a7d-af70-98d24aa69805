// created by gyj 2024-3-15
#ifndef P_CuIPCFrameCapturer_h
#define P_CuIPCFrameCapturer_h

#include "VideoFrameCapturer.h"
#include "CuCommon.h"
#include <set>
#include <cuda.h>
#include <driver_types.h>
#include <hv/EventLoopThread.h>

namespace panocom
{
    class CuIPCFrameCapturer : public VideoFrameCapturer
    {
    public:
        CuIPCFrameCapturer(const std::string& jsonParams);
        ~CuIPCFrameCapturer() override;

        void start() override;
        void stop() override;
    private:
        void syncSeq();
        Frame* getWaitFreeFrame();
        bool isFrameWaitFree(uint32_t seq);
        void freeFrames();
        void freeShmFrames();
        void findShmFrame();

    private:
        SharedMemory shm_;
        FrameShm* frame_;
        int frameCount_;    //上一半用于写，下一半用于读（回收）
        hv::EventLoopThread thread_;
        std::unordered_map<cudaIpcMemHandle_t, std::shared_ptr<Frame>, HandleHash> ipcFrames_;
        uint32_t seq_;
        uint32_t timestampBase_;
        std::set<Frame*> waitFree_;
        std::mutex mutex_;
        int cudaId_ = 0;
        int gpuIndex_ = 0;
        int dev_ = 0;
    };
}

#endif