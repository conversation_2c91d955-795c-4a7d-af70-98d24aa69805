// created by gyj 2024-2-20
#ifndef P_FfVideoEncoder_h
#define P_FfVideoEncoder_h

#include <hv/EventLoopThread.h>
#include "VideoFrameEncoder.h"

extern "C" {
#include <libavutil/opt.h>
#include <libavutil/imgutils.h>
#include <libavcodec/avcodec.h>
#include <libswscale/swscale.h>
}

namespace panocom
{
    class FfVideoEncoder : public VideoFrameEncoder
    {
    public:
        FfVideoEncoder(const std::string& jsonParams);
        virtual ~FfVideoEncoder();

        void onFrame(const std::shared_ptr<Frame>& frame) override;
        void start() override;
        void stop() override;
    private:
        AVCodec *avcodec_ = nullptr;
        AVCodecContext *ctx_ = nullptr;
        SwsContext *swsCtx_ = nullptr;
        std::string codecName_;
        int fps_;
        int kbps_;
        int gop_;
        int width_;
        int height_;
        hv::EventLoopThread thread_;
        int fmt_;
        uint64_t pts_;
        // FILE* pf_ = nullptr;
    };
} // namespace panocom


#endif