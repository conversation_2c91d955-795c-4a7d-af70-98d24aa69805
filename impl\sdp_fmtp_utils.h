#ifndef SDP_FMTP_UTILS_H_
#define SDP_FMTP_UTILS_H_
#include <string>

namespace panocom {
class SdpFmtpUtils
{
public:
    // FIXME: c++17 has std::optional. Use it when we move to c++17.
    // Parse positive number from SDP fmtp line. Return -1 if not found. fmtp is a json string, key is the key to parse.
    static int ParseSdpFmtpLine(const std::string& fmtp, const std::string& key);
    // Parse max frame rate from SDP fmtp line. Return -1 if not found.
    static int PsrseSdpForMaxFrameRate(const std::string& fmtp);
    // Parse max frame size from SDP fmtp line. Return -1 if not found.
    static int PsrseSdpForMaxFrameSize(const std::string& fmtp);
};
} // namespace xrtc
#endif