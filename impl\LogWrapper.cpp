﻿#ifdef WIN32
//#include "StdAfx.h"
#include <Windows.h>
#else
#include <dlfcn.h>
#endif
#include "LogWrapper.h"
#include <stdio.h>
#include <stdarg.h>
#include <memory.h>
//#include<QString>
//#include <QDebug>
#ifdef USE_MLOG

#if defined(_MSC_VER) && (_MSC_VER >= 1600)
#pragma execution_character_set("utf-8")
#endif

#define LOG_TEXT_HEAD_SIZE 128       //日志默认加的内容要余留大小

typedef void (*FN_Init)();
typedef void (*FN_UnInit)();
typedef HANDLE (*FN_Open)(const char *log);
typedef bool (*FN_Close)(HANDLE h);
typedef bool (*FN_MLogWrite)(int type, HANDLE h, const char *log, unsigned short logLen);
typedef bool (*FN_MLogWriteHex)(int type, HANDLE hj, const char *prefix,
                                const char *data, unsigned short dataLen);

static HMODULE s_hModule = NULL;
static FN_Init s_init = NULL;
static FN_UnInit s_uninit = NULL;
static FN_Open s_open = NULL;
static FN_Close s_close = NULL;
static FN_MLogWrite s_write = NULL;
static FN_MLogWriteHex s_writeHex = NULL;

namespace MediaManagerSP
{

LogWrapper::LogWrapper(void)
{
	m_hLog = NULL;
	Load();
}

LogWrapper::~LogWrapper(void)
{
	Free();
}

void LogWrapper::Load()
{
	if (s_hModule != NULL)
		return;
#ifdef WIN32
	char lpszPath[1024];
	Utility::GetCurrentDir(lpszPath, 1024);
    strcat_s(lpszPath, "mlog.dll");
    //printf("Load MLog.dll Path:%s\n", lpszPath);
	s_hModule = ::LoadLibraryA(lpszPath);
	if (!s_hModule)
		return;
	s_init = (FN_Init)::GetProcAddress(s_hModule, "MLogInit");
	s_uninit = (FN_UnInit)::GetProcAddress(s_hModule, "MLogUnInit");
	s_open = (FN_Open)::GetProcAddress(s_hModule, "MLogOpen");
	s_close = (FN_Close)::GetProcAddress(s_hModule, "MLogClose");
	s_write = (FN_MLogWrite)::GetProcAddress(s_hModule, "MLogWrite");
	s_writeHex = (FN_MLogWriteHex)::GetProcAddress(s_hModule, "MLogWriteHex");
#else
	s_hModule = dlopen("libmlog.so", RTLD_NOW);
	if (s_hModule)
	{
		printf("dlopen libmlog.so successed\n");
		s_init = (FN_Init)dlsym(s_hModule, "MLogInit");
		s_uninit = (FN_UnInit)dlsym(s_hModule, "MLogUnInit");
		s_open = (FN_Open)dlsym(s_hModule, "MLogOpen");
		s_close = (FN_Close)dlsym(s_hModule, "MLogClose");
		s_write = (FN_MLogWrite)dlsym(s_hModule, "MLogWrite");
		s_writeHex = (FN_MLogWriteHex)dlsym(s_hModule, "MLogWriteHex");
	}
	else
		printf("dlopen libmlog.so failed\n");
#endif
	if (s_init != NULL)
		s_init();
}

void LogWrapper::Free()
{
	if (s_uninit != NULL)
	{
		s_uninit();
		s_uninit = NULL;
	}
	if (s_hModule == NULL)
		return;
#ifdef WIN32
	::FreeLibrary(s_hModule);
#else
	dlclose(s_hModule);
#endif
	s_hModule = NULL;
	s_init = NULL;
	s_uninit = NULL;
	s_open = NULL;
	s_close = NULL;
	s_write = NULL;
	s_writeHex = NULL;
}

void LogWrapper::Open(const char *name)
{
	if (s_open != NULL)
		m_hLog = s_open(name);
}

void LogWrapper::Close()
{
	if (s_close != NULL)
		s_close(m_hLog);
}

bool LogWrapper::Write(int type,  const char *format, ... )
{
	va_list args;
	va_start(args, format);
	memset(m_buf, 0, MAX_BUF_SIZE);
	//vsprintf_s(m_buf, MAX_BUF_SIZE-2, format, args);
    vsprintf(m_buf, format, args);
    m_buf[MAX_BUF_SIZE - 1] = '\0';
	va_end(args);

	if(s_write != NULL)
		return	s_write(type, m_hLog, m_buf, strlen(m_buf));
	return false;
}

bool LogWrapper::Write(int type, const std::string &msg)
{
    if (s_write != NULL)
        return	s_write(type, m_hLog, msg.data(), msg.size());

    return false;
}

bool LogWrapper::WriteString(int type, const std::string &strText)
{
    if(s_write != NULL)
        return	s_write(type, m_hLog, strText.c_str(), strText.size());
    return false;
}

bool LogWrapper::WriteHex(int type,  const char *data, unsigned short dataLen,
                          const char *format, ... )
{
	va_list args;
	memset(m_buf, 0, MAX_BUF_SIZE);
	va_start(args, format);
	//vsprintf_s(m_buf, MAX_BUF_SIZE-2, format, args);
    vsprintf(m_buf, format, args);
	m_buf[MAX_BUF_SIZE - 1] = '\0';
	va_end(args);

	if(s_writeHex != NULL)
		return s_writeHex(type, m_hLog, m_buf, data, dataLen);
	return false;
}

void LogWrapper::write(int level, const std::string &msg)
{
    Write(level, msg);
}

void LogWrapper::writeFatal(const std::string &msg)
{
    WriteString(LOG_LEVEL_FATAL, msg);
}

void LogWrapper::writeWarning(const std::string &msg)
{
    WriteString(LOG_LEVEL_ERROR, msg);
}

void LogWrapper::writeNormal(const std::string &msg)
{
    WriteString(LOG_LEVEL_INFO, msg);
}

void LogWrapper::writeDebug(const std::string &msg)
{
    WriteString(LOG_LEVEL_DEBUG, msg);
}

void LogWrapper::writeInfo(const std::string &msg)
{
    WriteString(LOG_LEVEL_INFO, msg);
}
}

#endif