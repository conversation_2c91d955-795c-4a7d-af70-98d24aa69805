#include "StreamMonitor.h"
#include "Frame.h"
#include "json.hpp"

namespace panocom {
StreamMonitor::StreamMonitor(const std::string &jsonParams) : is_master_(false) {
    name_ = "StreamMonitor";
    connected_ = true;
    connected_timeout_ = true;
}

StreamMonitor::~StreamMonitor() {
    connected_ = false;
    connected_timeout_ = false;
    if (check_timeout_thread_.isRunning()) {
        check_timeout_thread_.stop(true);
        jinfo("StreamMonitor check_timeout_thread stop success!");
    }
}

void StreamMonitor::setFrameTimeoutNotify(int ms, void *param, const TimeoutNotifyCallback &cb) {
	if (ms <= 0) {
		jinfo("StreamMonitor[%p] setFrameTimeoutNotify timeout must > 0", this);
		return;
	}
	jinfo("StreamMonitor[%p] setFrameTimeoutNotify %d", this, ms);
    FramePipeline::setFrameTimeoutNotify(ms, param, cb);
    if (!check_timeout_thread_.isRunning()) {
		connected_ = true;
		connected_timeout_ = true;
        check_timeout_thread_.loop()->setInterval(10, [this](hv::TimerID id) {
            checkFrameTimeout();
        });
        check_timeout_thread_.start(true);
    }
}

void StreamMonitor::setFrameTimeoutNotify2(int ms, void* param, const TimeoutNotifyCallback& cb) {
	if (ms <= 0) {
		jinfo("StreamMonitor[%p] setFrameTimeoutNotify2 timeout must > 0", this);
		return;
	}
	jinfo("StreamMonitor[%p] setFrameTimeoutNotify2 %d", this, ms);
	// if (is_master_) {
	// 	FramePipeline::setFrameTimeoutNotify2(ms, param, cb);
	// }
	// else {
	// 	FramePipeline::setFrameTimeoutNotify2(0, param, cb);
	// }
	FramePipeline::setFrameTimeoutNotify2(ms, param, cb);
	if (!check_timeout_thread_.isRunning()) {
		connected_ = true;
		connected_timeout_ = true;
		check_timeout_thread_.loop()->setInterval(10, [this](hv::TimerID id) {
			checkFrameTimeout();
		});
		check_timeout_thread_.start(true);
	}
}

int StreamMonitor::updateParam(const std::string& jsonParams) {
    jinfo("StreamMonitor updateParam: %s", jsonParams.c_str());
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
	bool is_master = is_master_;
    if (j.contains("is-master")) is_master_ = j["is-master"];
	int timeoutMS = 3000;
	if (j.contains("timeoutMS")) timeoutMS = j["timeoutMS"];
	// if (is_master_ != is_master) {
	// 	if (is_master_) {
	// 		FramePipeline::resetFrameTimeoutNotify2(timeoutMS);
	// 	} else {
	// 		FramePipeline::resetFrameTimeoutNotify2(0);
	// 	}
	// }
    return 0;
}
}