#include "AlsaAudioFrameCapturer.h"

#include <iostream>

#include <json.hpp>
#include <ljcore/jlog.h>

namespace panocom {
namespace {
const std::string default_alsa_player { "hw:2,0" };
std::vector<int> cards { 2, 3 }; // TODO:
}
AlsaAudioFrameCapturer::AlsaAudioFrameCapturer(const std::string &jsonParams)
    : card_(0)
    , device_(0)
    , sample_rate_(48000)
    , channel_(2)
    , ptime_(20)
    , frames_(256)
    , running_(false)
    , pcm_handle_(nullptr)
    , use_hw_(true)
    , internal_gain_control_(false)
    , gain_(0)
    , init_done_(false) {
    name_ = "AlsaAudioFrameCapturer";
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    if (j.contains("dev")) {
        dev_ = j["dev"];
        if (dev_ < 0) dev_ = 0;
        if (dev_ < cards.size()) card_ = cards[dev_];
    } else {
        if (j.contains("card"))
            card_ = j["card"];
        if (j.contains("device"))
            device_ = j["device"];
    }
    if (j.contains("channel")) {
        channel_ = j["channel"];
    }
    if (j.contains("samplerate")) {
        sample_rate_ = j["samplerate"];
    }
    if (j.contains("ptime"))
        ptime_ = j["ptime"];
    if (card_ < 0 && device_ < 0) alsa_recorder_ = default_alsa_player;
    else {
        alsa_recorder_ = "hw:";
        alsa_recorder_ += std::to_string(card_) + "," + std::to_string(device_);
    }   
    capture_device.index = dev_;
    if (use_hw_ && AlsaUtils::GetAlsaHWPcmDevice(SND_PCM_STREAM_CAPTURE, capture_device)) {
        jinfo("Capture Device: %s", capture_device.hw_string.c_str());
        alsa_recorder_ = capture_device.hw_string;
    }
    fmt_ = Frame::getPCMFormat(1, sample_rate_);    // TODO: 单通道
    // frames_ = sample_rate_ * ptime_ / 1000;
    running_ = true;
    notifycallbacksThread_.start();
    capture_future_ = std::async([this]() -> bool {
        char *buffer = nullptr;
        char *muted_buf = nullptr;
        while (running_) {
            if (!pcm_handle_) {
                if (!Init()) {
                    Release();
                    jerror("ERROR: Failed to init AlsaAudioFrameCapturer.");
                    std::this_thread::sleep_for(std::chrono::seconds(3));
                    continue;
                } else {
                    if (buffer) delete[] buffer;
                    if (muted_buf) delete[] muted_buf;
                    buffer = new char[frames_ * 2 * channel_];
                    muted_buf = new char[frames_ * 2 * channel_];   // 静音缓冲区
                    memset(muted_buf, 0, frames_ * 2 * channel_);
                }
            }
            int32_t rc = snd_pcm_readi(pcm_handle_, buffer, frames_);
            if (rc == -EPIPE) {
                jwarn("Overrun occurred.");
                snd_pcm_prepare(pcm_handle_);
            } else if (rc < 0) {
                jerror("Error from read: %s", snd_strerror(rc));
                Release();
                std::this_thread::sleep_for(std::chrono::seconds(3));
                continue;
            } else if (rc == 0) {
                // std::cerr << "No data read (rc == 0) " << std::endl;
                usleep(1000);
                continue;
            } else if (rc < frames_) {
                jerror("Short read. read %d frames.", rc);
                usleep(2000);
            } else {
                // TODO: 后续处理固定单通道数据
                auto frame = Frame::CreateFrame((FrameFormat)fmt_);
                frame->createFrameBuffer(rc * /*channel_ **/ 2);
                if (muted_ && muted_buf) {
                    memcpy(frame->getFrameBuffer(), muted_buf, rc * channel_ * 2);
                } else {
                    if (channel_ == 2)
                        stereo_to_mono((int16_t *)buffer, (int16_t *)frame->getFrameBuffer(), rc /* * channel_*/);
                    else memcpy(frame->getFrameBuffer(), buffer, rc * channel_ * 2);
                    if (internal_gain_control_ && gain_ != 0) {
                        adjust_gain((int16_t *)frame->getFrameBuffer(), rc, (int16_t *)frame->getFrameBuffer(), rc, gain_);
                    }
                }
                AudioFrameCapturer::deliverFrame(frame);

                if (notifycallbacks_.find("AudioCapturerStatus") != notifycallbacks_.end() && notifycallbacks_["AudioCapturerStatus"].cb) {
                    volume_ = quantize_volume(caculateRMS((int16_t *)frame->getFrameBuffer(), rc));
                    if (notifycallbacksThread_.isRunning()) {
                        notifycallbacksThread_.loop()->runInLoop([this](){
                            nlohmann::json notify;
                            notify["deviceId"] = dev_;
                            notify["samplerate"] = sample_rate_;
                            notify["channel"] = channel_;
                            notify["fmt"] = fmt_;
                            notify["ptime"] = ptime_;
                            notify["volume"] = volume_;
                            if (notifycallbacks_.find("AudioCapturerStatus") != notifycallbacks_.end() && notifycallbacks_["AudioCapturerStatus"].cb) {
                                notifycallbacks_["AudioCapturerStatus"].cb("AudioCapturerStatus", notify.dump(), notifycallbacks_["AudioCapturerStatus"].param);
                            }
                        });
                    }  
                }
                // if (!ofs_.is_open()) ofs_.open("capture.pcm", std::ios::binary | std::ios::out);
                //   ofs_.write(frame->getFrameBuffer(), frame->getFrameBufferSize());
            }
        }
        Release();
        delete[] buffer;
        delete[] muted_buf;
        return true;
    });
}

AlsaAudioFrameCapturer::~AlsaAudioFrameCapturer()
{
    running_ = false;
    if (capture_future_.valid()) {
        auto status = capture_future_.wait_for(std::chrono::milliseconds(500));
        if (status == std::future_status::timeout) {
            jerror("audio capture stop timeout");
        } else if (status == std::future_status::ready) {
            bool res = capture_future_.get();
            jinfo("audio capture stop status: %d", res);
        }
    }
    if (notifycallbacksThread_.isRunning()) {
        notifycallbacksThread_.stop(true);
        notifycallbacksThread_.join();
    }
    // if (ofs_.is_open()) ofs_.close();
}

bool AlsaAudioFrameCapturer::Init() {
    jinfo("Init AlsaAudioFrameCapturer %s", alsa_recorder_.c_str());
    int32_t rc = snd_pcm_open(&pcm_handle_, alsa_recorder_.c_str(), SND_PCM_STREAM_CAPTURE, 0);
    if (rc < 0) {
        jerror("ERROR: Can't open %s PCM device: %s", alsa_recorder_.c_str(), snd_strerror(rc));;
        return false;
    }
    snd_pcm_hw_params_t *hw_params;
    
    snd_pcm_hw_params_alloca(&hw_params);
    snd_pcm_hw_params_any(pcm_handle_, hw_params);
    snd_pcm_hw_params_set_access(pcm_handle_, hw_params, SND_PCM_ACCESS_RW_INTERLEAVED);
    if (rc < 0) {
        jerror("ERROR: Can't set interleaved mode: %s", snd_strerror(rc));
        return false;
    }

    bool is_channel_supported = false;
    bool is_rate_supported = false;
    // acquire supported channel and rate
    capture_device.channels.clear();
    for (const uint32_t &channel : {1, 2}) {
        if (snd_pcm_hw_params_test_channels(pcm_handle_, hw_params, channel) == 0) {
            jinfo("%s Supported channel number is %d", capture_device.hw_string.c_str(), channel);
            capture_device.channels.push_back(channel);
            if (channel == channel_) {
                is_channel_supported = true;
            }
        }
    }
    capture_device.sample_rates.clear();
    for (const uint32_t &rate : { 16000, 24000, 32000, 44100, 48000, 96000 }) {
        if (snd_pcm_hw_params_test_rate(pcm_handle_, hw_params, rate, 0) == 0) {
            jinfo("%s Supported rate is %d", capture_device.hw_string.c_str(), rate);
            capture_device.sample_rates.push_back(rate);
            if (rate == sample_rate_) {
                is_rate_supported = true;
            }
        }
    }
    capture_device.formats.clear();
    for (const std::string &format : {"S16_LE", "S24_3LE"}) {
        if (snd_pcm_hw_params_test_format(pcm_handle_, hw_params,
                                          snd_pcm_format_t(snd_pcm_format_value(format.c_str()))) == 0) {
            jinfo("%s Supported format is %s", capture_device.hw_string.c_str(), format.c_str());
            capture_device.formats.push_back(format);
        }
    }
    // Use the first supported rate and format if the specified rate or format are not supported
    if (!is_rate_supported && !capture_device.sample_rates.empty()) {
        sample_rate_ = capture_device.sample_rates[0];
        jinfo("%s Use default sample rate is %d", capture_device.hw_string.c_str(), sample_rate_);
    }
    if (!is_channel_supported && !capture_device.channels.empty()) {
        channel_ = capture_device.channels[0];
        jinfo("%s Use default channel is %d", capture_device.hw_string.c_str(), channel_);
    }

    rc = snd_pcm_hw_params_set_format(pcm_handle_, hw_params, SND_PCM_FORMAT_S16_LE);
    if (rc < 0) {
        jerror("ERROR: Can't set format: %s", snd_strerror(rc));
        return false;
    }
    rc = snd_pcm_hw_params_set_channels(pcm_handle_, hw_params, channel_);
    if (rc < 0) {
        jerror("ERROR: Can't set channels number: %s", snd_strerror(rc));
        return false;
    }
    rc = snd_pcm_hw_params_set_rate_near(pcm_handle_, hw_params, &sample_rate_, nullptr);
    if (rc < 0) {
        jerror("ERROR: Can't set rate: %s", snd_strerror(rc));
        return false;
    } 
    jinfo("actually sample rate: %d", sample_rate_);
    frames_ = sample_rate_ * ptime_ / 1000;
    int dir = 0;
    if (use_hw_) {
        rc = snd_pcm_hw_params_set_period_size(pcm_handle_, hw_params, frames_, dir);
        if (rc < 0) {
            jerror("ERROR: Can't set period size: %s", snd_strerror(rc));
            return false;
        }
    } else {
        rc = snd_pcm_hw_params_set_period_size_near(pcm_handle_, hw_params, &frames_, &dir);
        if (rc < 0) {
            jerror("ERROR: Can't set period size: %s", snd_strerror(rc));
            return false;
        }
    }
    // rc = snd_pcm_hw_params_set_period_time_near(pcm_handle_, hw_params, &ptime_, 0);
    // if (rc < 0) {
    //     jerror("ERROR: Can't set period size: %s", snd_strerror(rc));
    //     return false;
    // }
    rc = snd_pcm_hw_params(pcm_handle_, hw_params);
    if (rc < 0) {
        jerror("ERROR: Can't set parameters: %s", snd_strerror(rc));
        return false;
    }
    // rc = snd_pcm_hw_params_get_period_size(hw_params, &frames_, nullptr);
    // if (rc < 0) {
    //     std::cerr << "ERROR: Can't get period size: " << snd_strerror(rc) << std::endl;
    //     // return false;
    // }
    jinfo("Expect read %d frames.", frames_);
    jinfo("Init alsa capturer success.");
    init_done_ = true;
    return true;
}

bool AlsaAudioFrameCapturer::Release() {
    if (pcm_handle_) {
        // snd_pcm_drain(pcm_handle_);
        snd_pcm_close(pcm_handle_);
        pcm_handle_ = nullptr;
    }
    return true;
}

int AlsaAudioFrameCapturer::updateParam(const std::string& jsonParams) {
    jinfo("###AlsaAudioFrameCapturer::updateParam %s", jsonParams.c_str());
    int ret = 0;
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    if (j.contains("gain")) {
        gain_ = j["gain"];
    }
    return ret;
}
} // namespace panocom
