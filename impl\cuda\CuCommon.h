// created by gyj 2024-3-6
#ifndef P_CuCommon_h
#define P_CuCommon_h

#include <cuda.h>
#include <unordered_map>
#include <vector>
#include <mutex>
#include <memory>
#include <string.h>
#include <nvEncodeAPI.h>
#include <driver_types.h>

namespace panocom
{
    class CuCommon
    {
    public:
        static CuCommon &instance();
        void init();
        void free();

        std::shared_ptr<CUcontext> createCudaContext(int gpuIndex = 0);
        std::shared_ptr<CUcontext> getCudaContext(uint32_t id, int gpuIndex = 0);

        /**
         *  @brief This a static function to get chroma offsets for YUV planar formats.
         */
        static void GetChromaSubPlaneOffsets(const NV_ENC_BUFFER_FORMAT bufferFormat, const uint32_t pitch,
                                             const uint32_t height, std::vector<uint32_t> &chromaOffsets);
        /**
         *  @brief This a static function to get the chroma plane pitch for YUV planar formats.
         */
        static uint32_t GetChromaPitch(const NV_ENC_BUFFER_FORMAT bufferFormat, const uint32_t lumaPitch);

        /**
         *  @brief This a static function to get the number of chroma planes for YUV planar formats.
         */
        static uint32_t GetNumChromaPlanes(const NV_ENC_BUFFER_FORMAT bufferFormat);

        /**
         *  @brief This a static function to get the chroma plane width in bytes for YUV planar formats.
         */
        static uint32_t GetChromaWidthInBytes(const NV_ENC_BUFFER_FORMAT bufferFormat, const uint32_t lumaWidth);

        /**
         *  @brief This a static function to get the chroma planes height in bytes for YUV planar formats.
         */
        static uint32_t GetChromaHeight(const NV_ENC_BUFFER_FORMAT bufferFormat, const uint32_t lumaHeight);

        /**
         *  @brief This a static function to get the width in bytes for the frame.
         *  For YUV planar format this is the width in bytes of the luma plane.
         */
        static uint32_t GetWidthInBytes(const NV_ENC_BUFFER_FORMAT bufferFormat, const uint32_t width);

        static uint32_t GetFrameSize(const NV_ENC_BUFFER_FORMAT bufferFormat, const uint32_t width, const uint32_t height);

    private:
        CuCommon() {}
        ~CuCommon() {}
    private:
        bool inited_ = false;
        std::mutex mutex_;
        std::mutex mutexCtx_;
        std::unordered_map<uint32_t, std::unordered_map<uint32_t, std::shared_ptr<CUcontext>>> ctxs_;
    };

    typedef struct FrameShmSt
    {
        uint32_t sync;
        uint32_t seq;
        uint32_t timestamp;
        uint32_t status;
        uint32_t width;
        uint32_t height;
        uint32_t hStride;
        uint32_t vStride;
        cudaIpcMemHandle_t memHandle;
    } FrameShm;

    struct IpcMemHandle
    {
        cudaIpcMemHandle_t handle;
        bool operator==(const IpcMemHandle & p) const 
        {
            return memcmp(&p.handle, &handle, sizeof(cudaIpcMemHandle_t)) == 0;
        }
        IpcMemHandle(const cudaIpcMemHandle_t & t)
        {
            handle = t;
        }
    };
    

    struct HandleHash
    {
        size_t operator()(const IpcMemHandle &var) const 
        {
            std::hash<std::string> hash_str;
            std::string str((char*)&var.handle, sizeof(cudaIpcMemHandle_t));
            return hash_str(str); 
        }
    };

    class SharedMemory
    {
    public:
        SharedMemory();
        ~SharedMemory();
        int sharedMemoryCreate(const char *name, size_t sz);
        int sharedMemoryOpen(const char *name, size_t sz);
        void sharedMemoryClose();
        void* data() { return addr_; }
        size_t size() { return size_; }
    private:
        void *addr_;
        size_t size_;
        int shmFd_;
    };
}

#endif