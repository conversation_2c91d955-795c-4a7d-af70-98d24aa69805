#ifdef ENABLE_VIDEO
#ifdef USE_CUDA
#include "Nv12FileFrameSource.h"
#include "Frame.h"
#include <json.hpp>
#include <nvcuvid.h>
#include <ljcore/jlog.h>

using namespace panocom;

Nv12FileFrameSource::Nv12FileFrameSource(const std::string& jsonParams) : FileFramePipeline(jsonParams)
{
    FN_BEGIN;
    name_ = "Nv12FileFrameSource";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    path_ = "test.nv12";
    if (j.contains("path"))
    {
        path_ = j["path"];
    }
    width_ = 1280;
    if (j.contains("width"))
    {
        width_ = j["width"];
    }
    height_ = 720;
    if (j.contains("height"))
    {
        height_ = j["height"];
    }
    hStride_ = width_;
    if (j.contains("hStride"))
    {
        hStride_ = j["hStride"];
    }
    vStride_ = height_;
    if (j.contains("vStride"))
    {
        vStride_ = j["vStride"];
    }
    fps_ = 25;
    if (j.contains("fps"))
    {
        fps_ = j["fps"];
    }
    frameType_ = FRAME_FORMAT_CU_FRAME;
    if (j.contains("frameType"))
    {
        frameType_ = j["frameType"];
    }
    start();
    FN_END;
}

Nv12FileFrameSource::~Nv12FileFrameSource()
{
    FN_BEGIN;
    stop();
    FN_END;
}

void Nv12FileFrameSource::start()
{
    if (thread_.isRunning()) return;
    FN_BEGIN;
    int gap = 1000 / fps_;
    pf_ = fopen(path_.c_str(), "rb");
    jinfo("fopen %s", path_.c_str());
    thread_.loop()->setInterval(gap, [this](hv::TimerID id) {
        int frameSize = hStride_ * vStride_;
        switch (frameType_)
        {
        case FRAME_FORMAT_CU_FRAME:
            {
                if (pf_)
                {
                    int ret = fread(buffer_.data(), 1, buffer_.size(), pf_);
                    if (ret == buffer_.size())
                    {
                        if (!ctx_)
                        {
                            ctx_ = CuCommon::instance().createCudaContext();
                        }
                        std::shared_ptr<Frame> f = Frame::CreateFrame(FRAME_FORMAT_CU_FRAME);
                        f->createFrameBuffer(width_, height_, hStride_, vStride_);
                        buffer_.resize(f->getFrameSize());
                        // Copy luma plane
                        CUDA_MEMCPY2D m = { 0 };
                        m.srcMemoryType = CU_MEMORYTYPE_HOST;
                        m.srcDevice = (CUdeviceptr)(m.srcHost = buffer_.data());
                        m.srcPitch = hStride_;
                        m.dstMemoryType = CU_MEMORYTYPE_DEVICE;
                        m.dstDevice = (CUdeviceptr)f->getFrameBuffer();
                        m.dstPitch = hStride_;
                        m.WidthInBytes = width_;
                        m.Height = height_;
                        cuMemcpy2DAsync(&m, 0);

                        m.srcDevice = (CUdeviceptr)(m.srcHost = buffer_.data() + m.srcPitch * ((height_ + 1) & ~1));
                        m.dstDevice = (CUdeviceptr)(f->getFrameBuffer() + m.dstPitch * height_);
                        m.Height = height_ / 2;
                        cuMemcpy2DAsync(&m, 0);

                        cuStreamSynchronize(0);

                        deliverFrame(f);
                    }
                    else
                    {
                        jerror("read %d", ret);
                    }
                }
                else
                {
                    jerror("pf_ == null");
                }
            }
            break;
        default:
            break;
        }
    });

    thread_.start();
    FN_END;
}

void Nv12FileFrameSource::stop()
{
    FN_BEGIN;
    thread_.stop(true);
    if (pf_)
    {
        fclose(pf_);
        pf_ = nullptr;
    }
    FN_END;
}

#endif
#endif