#include "alsa_utils.h"
#include <iostream>
#include <ljcore/jlog.h>
namespace panocom {
void AlsaUtils::printCardInfo(snd_ctl_card_info_t *info) {
    const char* id = snd_ctl_card_info_get_id(info);
    if (id != nullptr && strlen(id) > 0) {
        printf("ID: %s\n", id);
    } else {
        std::cerr << "Error getting card ID." << std::endl;
    }
}
bool AlsaUtils::isDeviceAvailable(int cardId, const char *deviceName) {
    snd_ctl_t* handle = nullptr;
    // Open the control interface for the specified card.
    int err = snd_ctl_open(&handle, "hw:CARD=0", 0);
    if (err < 0) {
        std::cerr << "Failed to open ALSA device: " << snd_strerror(err) << std::endl;
        return false; // Return an appropriate value to indicate failure.
    }
    return true;
}
int AlsaUtils::openAlsaHandle(const std::string &name, snd_pcm_stream_t streamType, snd_pcm_t **handle) {
    int err = snd_pcm_open(handle, name.c_str(), streamType, 0);
    if (err < 0) {
        fprintf(stderr, "Cannot open audio device %s (%s)\n",
                name.c_str(),
                snd_strerror(err));
        return -1; // Return an appropriate value to indicate failure.
    }
    return err;
}
void AlsaUtils::listDevices(snd_pcm_stream_t type) {
    int card = 0;
    while (true) {
        std::string deviceName = "hw:" + std::to_string(card);
        if (!isDeviceAvailable(card, deviceName.c_str())) {
            break; // No more devices found.
        }
        snd_pcm_t *handle;
        int err = openAlsaHandle(deviceName, type, &handle);
        if (err < 0) {
            continue; // Skip this device and move to the next one.
        }
        std::cout << "Device: " << deviceName << std::endl;
        snd_pcm_close(handle); // Close the handle after use.
    }
}
void AlsaUtils::listAllDevices() {
    int err = 0;
    void **hints;
    char *name, *decs, *io;
    if ((err = snd_device_name_hint(-1, "pcm", &hints)) < 0) {
        jerror("snd_device_name_hint failed: %s", snd_strerror(err));
        return; // Return an appropriate value to indicate failure.
    }
    void **n = hints;
    while (*n != NULL) {
        name = snd_device_name_get_hint(*n, "NAME");
        decs = snd_device_name_get_hint(*n, "DESC");
        io   = snd_device_name_get_hint(*n, "IOID");

        std::string deviceName(name ? name : "");
        std::string direction(io ? io : "Unknown");
        // 过滤掉非硬件设备（比如 plug:、default 等）
        // if (deviceName.find("hw:") != 0 && deviceName.find("plughw:") != 0) {
        //     free(name);
        //     free(decs);
        //     free(io);
        //     n++;
        //     continue;
        // }
        jinfo("Name: %s, IO: %s\nDescription: %s", name, direction.c_str(), decs);
        free(name);
        free(decs);
        free(io);
        n++;
    }
    err = snd_device_name_free_hint(hints);
}

void AlsaUtils::listPcmDevices(snd_pcm_stream_t stream_type) {
    int card = -1;

    if (stream_type == SND_PCM_STREAM_PLAYBACK) {
        jinfo("== Playback Devices (aplay -l) == ");
    } else {
        jinfo("== Capture Devices (arecord -l)== ");
    }

    while (snd_card_next(&card) >= 0 && card >= 0) {
        snd_ctl_t *ctl;
        char card_name[32];
        snprintf(card_name, sizeof(card_name), "hw:%d", card);
        if (snd_ctl_open(&ctl, card_name, 0) < 0) {
            continue;
        }
        int dev = -1;
        while (snd_ctl_pcm_next_device(ctl, &dev) >= 0 && dev >= 0)
        {
            snd_pcm_info_t *pcm_info;
            snd_pcm_info_alloca(&pcm_info);
            snd_pcm_info_set_device(pcm_info, dev);
            snd_pcm_info_set_subdevice(pcm_info, 0);
            snd_pcm_info_set_stream(pcm_info, stream_type);
            if (snd_ctl_pcm_info(ctl, pcm_info) >= 0) {
                jinfo("Card card %d, Device %d: %s (%s)", card, dev, snd_pcm_info_get_name(pcm_info), snd_pcm_info_get_id(pcm_info));
            }
        }
        snd_ctl_close(ctl);
    }
}

std::vector<AlsaHWPcmDevice> AlsaUtils::ListAlsaPcmDevices() {
    std::vector<AlsaHWPcmDevice> devices;
    int card = -1;
    int index = 0;

    while (snd_card_next(&card) >= 0 && card >= 0) {
        snd_ctl_t *ctl;
        char card_name[32];
        snprintf(card_name, sizeof(card_name), "hw:%d", card);
        if (snd_ctl_open(&ctl, card_name, 0) < 0) {
            continue;
        }
        
        int dev = -1;
        while (snd_ctl_pcm_next_device(ctl, &dev) >= 0 && dev >= 0) {
            for (auto stream_type: {SND_PCM_STREAM_PLAYBACK, SND_PCM_STREAM_CAPTURE}) {
                snd_pcm_info_t *pcm_info;
                snd_pcm_info_alloca(&pcm_info);
                snd_pcm_info_set_device(pcm_info, dev);
                snd_pcm_info_set_subdevice(pcm_info, 0);
                snd_pcm_info_set_stream(pcm_info, stream_type);
                if (snd_ctl_pcm_info(ctl, pcm_info) >= 0) {
                    AlsaHWPcmDevice d;
                    d.index = index++;
                    d.card = card;
                    d.device = dev;
                    d.id = snd_pcm_info_get_id(pcm_info);
                    d.name = snd_pcm_info_get_name(pcm_info);
                    d.type = (stream_type == SND_PCM_STREAM_PLAYBACK) ? "Playback" : "Capture";

                    char hw_buf[32];
                    snprintf(hw_buf, sizeof(hw_buf), "hw:%d,%d", card, dev);
                    d.hw_string = hw_buf;

                    // snd_pcm_t *handle;
                    // if (snd_pcm_open(&handle, d.hw_string.c_str(), stream_type, SND_PCM_NONBLOCK) == 0) {
                    //     snd_pcm_hw_params_t *params;
                    //     snd_pcm_hw_params_alloca(&params);
                    //     if (snd_pcm_hw_params_any(handle, params) == 0) {
                    //         for (auto rate: {8000, 16000, 32000, 44100, 48000, 96000}) {
                    //             if (snd_pcm_hw_params_test_rate(handle, params, rate, 0) == 0) {
                    //                 d.sample_rates.push_back(rate);
                    //             }
                    //         }
                    //         unsigned int ch_min, ch_max;
                    //         if (snd_pcm_hw_params_get_channels_min(params, &ch_min) == 0 &&
                    //             snd_pcm_hw_params_get_channels_max(params, &ch_max) == 0) {
                    //                 d.channels_min = ch_min;
                    //                 d.channels_max = ch_max;
                    //         }
                    //         for (int fmt = 0; fmt < SND_PCM_FORMAT_LAST; ++fmt) {
                    //             if (snd_pcm_hw_params_test_format(handle, params, (snd_pcm_format_t)fmt) == 0) {
                    //                 d.formats.push_back(snd_pcm_format_name((snd_pcm_format_t)fmt));
                    //             }
                    //         }
                    //     }
                    //     snd_pcm_close(handle);
                    // }
                    devices.push_back(d);
                }
            }
        }
        snd_ctl_close(ctl);
    }
    return devices;
}

std::vector<AlsaHWPcmDevice> AlsaUtils::ListAlsaPcmDevices(snd_pcm_stream_t stream_type) {
    std::vector<AlsaHWPcmDevice> devices;
    int card = -1;
    int index = 0;

    while (snd_card_next(&card) >= 0 && card >= 0) {
        snd_ctl_t *ctl;
        char card_name[32];
        snprintf(card_name, sizeof(card_name), "hw:%d", card);
        if (snd_ctl_open(&ctl, card_name, 0) < 0) {
            continue;
        }
        
        int dev = -1;
        while (snd_ctl_pcm_next_device(ctl, &dev) >= 0 && dev >= 0) {
            snd_pcm_info_t *pcm_info;
            snd_pcm_info_alloca(&pcm_info);
            snd_pcm_info_set_device(pcm_info, dev);
            snd_pcm_info_set_subdevice(pcm_info, 0);
            snd_pcm_info_set_stream(pcm_info, stream_type);
            if (snd_ctl_pcm_info(ctl, pcm_info) >= 0) {
                AlsaHWPcmDevice d;
                d.index = index++;
                d.card = card;
                d.device = dev;
                d.id = snd_pcm_info_get_id(pcm_info);
                d.name = snd_pcm_info_get_name(pcm_info);
                d.type = (stream_type == SND_PCM_STREAM_PLAYBACK) ? "Playback" : "Capture";

                char hw_buf[32];
                snprintf(hw_buf, sizeof(hw_buf), "hw:%d,%d", card, dev);
                d.hw_string = hw_buf;
                devices.push_back(d);
            }
        }
        snd_ctl_close(ctl);
    }
    return devices;    
}

bool AlsaUtils::GetAlsaHWPcmDevice(snd_pcm_stream_t stream_type, AlsaHWPcmDevice &alsa_device) {
    std::vector<AlsaHWPcmDevice> devices;
    int card = -1;
    int index = 0;

    while (snd_card_next(&card) >= 0 && card >= 0) {
        snd_ctl_t *ctl;
        char card_name[32];
        snprintf(card_name, sizeof(card_name), "hw:%d", card);
        if (snd_ctl_open(&ctl, card_name, 0) < 0) {
            continue;
        }
        
        int dev = -1;
        while (snd_ctl_pcm_next_device(ctl, &dev) >= 0 && dev >= 0) {
            snd_pcm_info_t *pcm_info;
            snd_pcm_info_alloca(&pcm_info);
            snd_pcm_info_set_device(pcm_info, dev);
            snd_pcm_info_set_subdevice(pcm_info, 0);
            snd_pcm_info_set_stream(pcm_info, stream_type);
            if (snd_ctl_pcm_info(ctl, pcm_info) >= 0 && alsa_device.index == index++) {
                alsa_device.card = card;
                alsa_device.device = dev;
                alsa_device.id = snd_pcm_info_get_id(pcm_info);
                alsa_device.name = snd_pcm_info_get_name(pcm_info);
                alsa_device.type = (stream_type == SND_PCM_STREAM_PLAYBACK) ? "Playback" : "Capture";

                char hw_buf[32];
                snprintf(hw_buf, sizeof(hw_buf), "hw:%d,%d", card, dev);
                alsa_device.hw_string = hw_buf;
                snd_ctl_close(ctl);
                return true;
            }
        }
        snd_ctl_close(ctl);
    }
    return false;
}

std::vector<AlsaPcmDeviceInfo> AlsaUtils::GetAllAlsaPcmDevices() {
    std::vector<AlsaPcmDeviceInfo> devices;
    void **hints;
    int err = 0;
    if ((err = snd_device_name_hint(-1, "pcm", &hints)) < 0) {
        jerror("snd_device_name_hint failed: %s", snd_strerror(err));
        return devices; // Return an appropriate value to indicate failure.
    }
    void **n = hints;
    int index = 0;
    char *name = nullptr, *desc = nullptr;//, *io = nullptr;
    while (*n != NULL) {
        name = snd_device_name_get_hint(*n, "NAME");
        desc = snd_device_name_get_hint(*n, "DESC");
        // io   = snd_device_name_get_hint(*n, "IOID");

        if (name) {
            AlsaPcmDeviceInfo info;
            info.name = name;
            info.desc = (desc != nullptr ? desc : "");

            // 尝试打开播放，如果是hw可能因设备忙而打开失败
            if (supports_capture(name)) {
                info.can_capture = true;
            }
            if (supports_playback(name)) {
                info.can_playback = true;
            }
            info.index = index;
            devices.push_back(info);
            index++;
        }
        if (name) free(name);
        if (desc) free(desc);
        name = nullptr;
        desc = nullptr;
        // free(io);
        n++;
    }
    err = snd_device_name_free_hint(hints);
    return devices;
}

bool AlsaUtils::supports_playback(const std::string &name) {
    snd_pcm_t *handle = nullptr;
    int err = snd_pcm_open(&handle, name.c_str(), SND_PCM_STREAM_PLAYBACK, SND_PCM_NONBLOCK);
    if (err >= 0) {
        snd_pcm_close(handle);
        return true;
    }
    return false;
}
bool AlsaUtils::supports_capture(const std::string &name) {
    snd_pcm_t *handle = nullptr;
    int err = snd_pcm_open(&handle, name.c_str(), SND_PCM_STREAM_CAPTURE, SND_PCM_NONBLOCK);
    if (err >= 0) {
        snd_pcm_close(handle);
        return true;
    }
    return false;
}

} // namespace panocom