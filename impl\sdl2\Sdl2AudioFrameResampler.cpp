#include "Sdl2AudioFrameResampler.h"
#include "Frame.h"
#include "Utils.h"
#include <json.hpp>
#include <ljcore/jlog.h>
#include <stdio.h>

using namespace panocom;

Sdl2AudioFrameResampler::Sdl2AudioFrameResampler(const std::string& jsonParams)
{
    FN_BEGIN;
    jinfo("Sdl2AudioFrameResampler %s", jsonParams.c_str());
    name_ = "Sdl2AudioFrameResampler";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    int src_chn = 1;
    int src_samplerate = 16000;
    int dst_chn = 1;
    int dst_samplerate = 48000;
    if (j.contains("src"))
    {
        if (j["src"].contains("channel"))
        {
            src_chn = j["src"]["channel"];
        }
        if (j["src"].contains("samplerate"))
        {
            src_samplerate = j["src"]["samplerate"];
        }
    }
    if (j.contains("dst"))
    {
        if (j["dst"].contains("channel"))
        {
            dst_chn = j["dst"]["channel"];
        }
        if (j["dst"].contains("samplerate"))
        {
            dst_samplerate = j["dst"]["samplerate"];
        }
    }
    
    stream_ = std::shared_ptr<SDL_AudioStream>(SDL_NewAudioStream(AUDIO_S16, src_chn, src_samplerate, AUDIO_S16, dst_chn, dst_samplerate), [](SDL_AudioStream* stream){
        SDL_FreeAudioStream(stream);
    });

    thread_.start();
    FN_END;
}

Sdl2AudioFrameResampler::~Sdl2AudioFrameResampler()
{
    FN_BEGIN;
    thread_.stop(true);
    FN_END;
}

void Sdl2AudioFrameResampler::onFrame(const std::shared_ptr<Frame>& frame)
{
    
}