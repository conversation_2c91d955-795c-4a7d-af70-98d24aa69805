#ifndef IMPL_AUDIOCODEC_L16AUDIOFRAMEENCODER_H_
#define IMPL_AUDIOCODEC_L16AUDIOFRAMEENCODER_H_
#include <condition_variable>
#include "AudioFrameEncoder.h"
#include "Frame.h"
#include <resampler/resampler.h>
#include <resampler/push_resampler.h>


namespace panocom
{
class L16AudioFrameEncoder : public AudioFrameEncoder
{
private:
    /* data */
public:
    L16AudioFrameEncoder(const std::string& jsonParams);
    ~L16AudioFrameEncoder();
	void onFrame(const std::shared_ptr<Frame> &f) override;
private:
    FrameFormat fmt_;
	FrameFormat out_fmt_;
    int samplerate_;
    int num_channels_;

#ifdef WEBRTC_RESAMPLE_ANOTHER
	nswebrtc::PushResampler<int16_t> resampler_;
#else
	nswebrtc::Resampler resampler_;
#endif   
	bool initResampler_ = false;
	int source_samplerate_;
};
    
} // namespace panocom


#endif