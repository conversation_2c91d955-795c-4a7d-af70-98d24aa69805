{"name": "VST", "audio": {"capturer": {"name": "AudioSpec", "samplerate": 16000, "channel": 1, "dev": -1}, "render": {"name": "AudioSpec", "samplerate": 16000, "channel": 1, "dev": -1}, "encoder": {"name": "native", "codec": [{"name": "opus", "samplerate": 16000, "channel": 1, "fec": 1, "rc": "vbr"}, {"name": "g711", "samplerate": 8000, "channel": 1}]}, "decoder": {"name": "native", "codec": [{"name": "opus", "samplerate": 16000, "channel": 1, "fec": 1, "rc": "vbr"}, {"name": "g711", "samplerate": 8000, "channel": 1}]}, "uplink": {"processer": [{"name": "AudioFramePacer", "ptime": 20}, {"name": "RtcAudioFrameAec", "samplerate": 16000, "channel": 1}, {"name": "RnnAudioFrameNS", "samplerate": 48000, "channel": 1}], "file-src": {"name": "PCMFileFrameSource"}, "rtp": "JRtpEngine"}, "downlink": {"processer": [{"name": "AudioFrameAsFarEnd", "samplerate": 16000, "channel": 1, "bind": "RtcAudioFrameAec"}], "file-src": {"name": "PCMFileFrameSource"}, "rtp": "JRtpEngine"}}, "video": {"capturer": {"name": "V4L2DMAVideoFrameCapturer", "width": 1920, "height": 1080, "fps": 30, "format": "bgr3", "dev": -1, "devNames": ["/dev/video33", "/dev/video22", "/dev/video11", "/dev/video0"]}, "render": {"name": "RKVideoRender", "shm": "cuIPC", "frameCount": 8}, "encoder": {"name": "RKVideoEncoder", "codec": [{"name": "h264", "width": 1920, "height": 1080, "fps": 30, "gop": 30, "rc": "AVBR", "bitrate": 2048, "max-qp": 35, "min-qp": 15}, {"name": "h265", "width": 1920, "height": 1080, "fps": 30, "gop": 30, "rc": "AVBR", "bitrate": 2048, "max-qp": 35, "min-qp": 15}]}, "decoder": {"name": "RKVideoDecoder", "codec": [{"name": "h264", "width": 1920, "height": 1080}, {"name": "h265", "width": 1920, "height": 1080}, {"name": "mjpeg", "width": 1920, "height": 1080}]}, "uplink": {"processer": [], "file-src": {"name": "H2645FileFrameSource"}, "rtp": "JRtpEngine", "rtp-proxy": "RtcRtpVideoFrameDestination"}, "downlink": {"processer": [], "file-src": {"name": "H2645FileFrameSource"}, "rtp": "JRtpEngine", "rtp-proxy": "RtcRtpVideoFrameSource"}}}