#include "AudioFrameAsLineIn.h"
#include "Frame.h"
#include "Utils.h"
#include <ljcore/jlog.h>
#include <stdio.h>
#include <json.hpp>

using namespace panocom;

AudioFrameAsLineIn::AudioFrameAsLineIn(const std::string& jsonParams)
{
    FN_BEGIN;
    name_ = "AudioFrameAsLineIn";
    FN_END;
}

AudioFrameAsLineIn::~AudioFrameAsLineIn()
{
    FN_BEGIN;
    FN_END;
}

void AudioFrameAsLineIn::onFrame(const std::shared_ptr<Frame> &frame)
{
    auto f = Frame::CreateFrame(frame->getFormatFarEnd());
    if (f)
    {
        //jinfo("AudioFrameAsLineIn::onFrame %d %d", f->getFrameFormat(), f->getFrameSize());
        f->createFrameBuffer(frame->getFrameSize());
        memcpy(f->getFrameBuffer(), frame->getFrameBuffer(), frame->getFrameSize());
        deliverFrame(f);
    }
}