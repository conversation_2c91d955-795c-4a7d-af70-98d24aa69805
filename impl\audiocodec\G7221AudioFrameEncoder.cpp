#include "G7221AudioFrameEncoder.h"

#include <json.hpp>

namespace panocom {
G7221AudioFrameEncoder::G7221AudioFrameEncoder(const std::string &jsonParams)
    : encode_state_(nullptr)
    , fmt_(FRAME_FORMAT_G722_16000_1)
    , bit_rate_(48000)
    , samplerate_(32000) {
    name_ = "G7221AudioFrameEncoder";
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    if (j.contains("bitrate")) {
        bit_rate_ = j["bitrate"];
    }
    if (j.contains("samplerate")) {
        samplerate_ = j["samplerate"];
    }
	encoded_buf_ = new uint8_t[640];
    encode_state_ = g722_1_encode_init(encode_state_, bit_rate_, samplerate_);
	if (encode_state_) {
        running_ = true;
        encode_future_ = std::async([this]() -> bool {
            while (running_) {
                std::unique_lock<std::mutex> lock(mutex_);
                queue_available_.wait(lock, [this] { return !frame_queue_.empty() || !running_; });
                if (!running_) {
                    break;
                }
                auto frame = std::move(frame_queue_.front());
                frame_queue_.pop();
                lock.unlock();
				// if (!ofs_.is_open()) ofs_.open("toencode.pcm", std::ios::binary | std::ios::out);
				// ofs_.write(frame->getFrameBuffer(), frame->getFrameBufferSize());
                if (encode_state_) {
                    size_t ret = g722_1_encode(encode_state_, encoded_buf_, (int16_t *)frame->getFrameBuffer(), frame->getFrameBufferSize() / 2);
                    // printf("encoder ret = %d\n", ret);
                    if (ret > 0) {
                        // jinfo("encoded len = %d", ret);
                        std::shared_ptr<Frame> out_frame = Frame::CreateFrame(fmt_);
                        out_frame->createFrameBuffer(ret);
                        memcpy(out_frame->getFrameBuffer(), encoded_buf_, ret);
                        deliverFrame(out_frame);
                    } else
                        printf("g722 encode failed\n");
                }
            }
            return true;
        });		
	}
}

G7221AudioFrameEncoder::~G7221AudioFrameEncoder() {
    running_ = false;
    queue_available_.notify_all();
    if (encode_future_.valid()) {
        auto status = encode_future_.wait_for(std::chrono::milliseconds(500));
		if (status == std::future_status::timeout) {
			 jerror("g722 encoder stop timeout");
		} else {
			bool res = encode_future_.get();
            jinfo("g722 encoder stop status: %d", res);
		}
	} 
    if (encode_state_) {
        g722_1_encode_release(encode_state_);
        encode_state_ = nullptr;
    }
	if (encoded_buf_) delete encoded_buf_;
	encoded_buf_ = nullptr;

	// if (ofs_.is_open()) ofs_.close();
}

void G7221AudioFrameEncoder::onFrame(const std::shared_ptr<Frame> &frame) {
	if (!encode_state_) return;
    int samplerate = 0;
    int chn = 0;
    auto f = frame;
    if (Frame::getSamplerate(f->getFrameFormat(), samplerate, chn)) {
        if (samplerate_ != samplerate) {
            if (!initResampler_ || samplerate != source_samplerate_) {
                source_samplerate_ = samplerate;
                initResampler_ = true;
#ifdef WEBRTC_RESAMPLE_ANOTHER
                resampler_.InitializeIfNeeded(samplerate, samplerate_, 1);
#else
                resampler_.ResetIfNeeded(samplerate, samplerate_, 1);
#endif
            }
            auto frame = Frame::CreateFrame(FRAME_FORMAT_COMMON_AUDIO);
            frame->createFrameBuffer(f->getFrameSize() * samplerate_ / samplerate);
            memset(frame->getFrameBuffer(), 0, frame->getFrameSize());
#ifdef WEBRTC_RESAMPLE_ANOTHER
            resampler_.Resample((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2);
#else
            size_t outlen = 0;
            resampler_.Push((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2, outlen);
#endif
            f = frame;
            if (first_) {
                first_ = false;
                jinfo("G7221AudioFrameEncoder resample %d -> %d", samplerate, samplerate_);
            }
        }
    }
	{
		std::lock_guard<std::mutex> lock(mutex_);
		if (frame_queue_.size() > 20) {
			frame_queue_.pop();
		}
		frame_queue_.emplace(f);
	}
	queue_available_.notify_one();    
}
} // namespace panocom
