#include "TinyALSACapturer.h"
#include "Frame.h"
#include <json.hpp>
#include <ljcore/jlog.h>

using namespace panocom;

TinyALSACapturer::TinyALSACapturer(const std::string& jsonParams)
{
    name_ = "TinyALSACapturer";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    card_ = 0;
    if (j.contains("card"))
    {
        card_ = j["card"];
    }
    dev_ = 0;
    if (j.contains("dev"))
    {
        dev_ = j["dev"];
    }
    samplerate_ = 16000;
    if (j.contains("samplerate"))
    {
        samplerate_ = j["samplerate"];
    }
    chn_ = 1;
    if (j.contains("chn"))
    {
        chn_ = j["chn"];
    }
    ptime_ = 20;
    if (j.contains("ptime"))
    {
        ptime_ = j["ptime"];
    }
    ptimeDataLen_ = samplerate_ / 1000 * ptime_  * sizeof(int16_t);
    start();
}

TinyALSACapturer::~TinyALSACapturer()
{
    stop();
}

void TinyALSACapturer::start()
{
    if (!pcm_)
    {
        struct pcm_config config;
        memset(&config, 0, sizeof(config));
        config.channels = chn_;
        config.rate = samplerate_;
        config.period_size = 512;
        config.period_count = 2;
        config.format = PCM_FORMAT_S16_LE;
        config.start_threshold = 0;
        config.stop_threshold = 0;
        config.silence_threshold = 0;

        readbufferLen_ = config.period_size * config.period_count;
        readbuffer_.reset(new int16_t[readbufferLen_]);
        if (chn_ > 1)
        {
            readbufferMono_.reset(new int16_t[readbufferLen_ / chn_]);
        }
        pcm_ = pcm_open(dev_, card_, PCM_IN, &config);
        if (pcm_ == NULL) {
            jerror("failed to allocate memory for PCM\n");
            return;
        } else if (!pcm_is_ready(pcm_)){
            jerror("failed to open PCM(%d, %d) err(%s)\n", dev_, card_, pcm_get_error(pcm_));
            pcm_close(pcm_);
            pcm_ = nullptr;
            return;
        } else {
            jinfo("TinyALSACapturer pcm_open success");
        }
        thread_.loop()->setInterval(2, [this](hv::TimerID id){
            int ret = pcm_read(pcm_, readbuffer_.get(), readbufferLen_ * sizeof(int16_t));
            if (!ret)
            {
                // readBytes_ += readbufferLen_ * 2;
                // if (GetTickCount() - readTick_ >= 1000)
                // {
                //     jinfo("readBytes %d per second", readBytes_);
                //     readTick_ = GetTickCount();
                //     readBytes_ = 0;
                // }
                if (chn_ > 1)
                {
                    for (size_t i = 0; i < readbufferLen_ / chn_; i++)
                    {
                        readbufferMono_[i] = readbuffer_[2 * i];
                    }
                    std::unique_lock<std::mutex> locker(mutex_);
                    bbuf_.WriteBytes((const char *)readbufferMono_.get(), readbufferLen_ / chn_ * sizeof(int16_t));
                }
                else
                {
                    std::unique_lock<std::mutex> locker(mutex_);
                    bbuf_.WriteBytes((const char *)readbuffer_.get(), readbufferLen_ * sizeof(int16_t));
                }
            }
            else
            {
                jerror("pcm_read fail");
            }
        });
        thread_.start();
        threadPacer_.loop()->setInterval(ptime_, [this](hv::TimerID id){
            if (bbuf_.Length() >= ptimeDataLen_)
            {
                std::shared_ptr<Frame> f = Frame::CreateFrame(Frame::getPCMFormat(1, samplerate_));
                f->createFrameBuffer(ptimeDataLen_);
                {
                    std::unique_lock<std::mutex> locker(mutex_);
                    memcpy(f->getFrameBuffer(), bbuf_.Data(), ptimeDataLen_);
                    bbuf_.Consume(ptimeDataLen_);
                }
                deliverFrame(f);
                printOutputStatus("TinyALSACapturer");
            }
        });
        threadPacer_.start();
    }
}

void TinyALSACapturer::stop()
{
    if (thread_.isRunning())
    {
        thread_.stop(true);
    }
    if (threadPacer_.isRunning())
    {
        threadPacer_.stop(true);
    }
    if (pcm_)
    {
        pcm_close(pcm_);
        pcm_ = nullptr;
    }
}