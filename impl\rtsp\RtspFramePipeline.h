#ifndef P_RtspFramePipeline_h
#define P_RtspFramePipeline_h

#include "FramePipeline.h"
#include "Frame.h"
#include "Parser.h"
#include <hv/EventLoopThread.h>
#include <librtsp/rtsp-client.h>
#include <Network/TcpClient.h>

using namespace toolkit;

namespace panocom
{
    class RtspFramePipeline : public FramePipeline, public TcpClient
    {
    public:
        RtspFramePipeline(const std::string& jsonParams);
        ~RtspFramePipeline() override;
        void onFrame(const std::shared_ptr<Frame> &f) override;

        static int rtsp_client_send(void* param, const char* uri, const void* req, size_t bytes);
        static int rtpport(void* param, int media, const char* source, unsigned short rtp[2], char* ip, int len);
        static void onrtp(void* param, uint8_t channel, const void* data, uint16_t bytes);
        static int onannounce(void* param);
        static int ondescribe(void* param, const char* sdp, int len);
        static int onsetup(void* param, int timeout, int64_t duration);
        static int onteardown(void* param);
        static int onplay(void* param, int media, const uint64_t *nptbegin, const uint64_t *nptend, const double *scale, const struct rtsp_rtp_info_t* rtpinfo, int count);
        static int onpause(void* param);

        int _rtsp_client_send(const char* uri, const void* req, size_t bytes);
        int _rtpport(int media, const char* source, unsigned short rtp[2], char* ip, int len);
        void _onrtp(uint8_t channel, const void* data, uint16_t bytes);
        int _onannounce();
        int _ondescribe(const char* sdp, int len);
        int _onsetup(int timeout, int64_t duration);
        int _onteardown();
        int _onplay(int media, const uint64_t *nptbegin, const uint64_t *nptend, const double *scale, const struct rtsp_rtp_info_t* rtpinfo, int count);
        int _onpause();
    protected:
        void onConnect(const SockException &ex) override;
        void onRecv(const toolkit::Buffer::Ptr &buf) override;
        void onError(const toolkit::SockException &ex) override;
    private:
        struct rtsp_client_t* rtsp_;
        hv::EventLoopThread thread_;
        std::vector<FramePipeline::Ptr> rtp_;
        std::string rtpEngineName_;
        mediakit::RtspUrl rtspUrl_;
        std::vector<FrameFormat> fmts_;
    };
} // namespace panocom


#endif