#include "VideoFrameMixer.h"
#ifdef USE_LIBYUV
#include "libyuv/LibyuvVideoFrameMixer.h"
#endif
#ifdef RK_PLATFORM
#include "rk/RKVideoFrameMixer.h"
#endif
#ifdef USE_CUDA
#include "cuda/CuVideoFrameMixer.h"
#endif

#include <algorithm>

using namespace panocom;

bool VideoFrameMixer::isRegistered = false;

std::vector<std::string> VideoFrameMixer::mixers_;

void VideoFrameMixer::RegistMixers()
{
    if (!isRegistered)
    {
#ifdef USE_LIBYUV
        mixers_.push_back("LibyuvVideoFrameMixer");
#endif
#ifdef RK_PLATFORM
		mixers_.push_back("RKVideoFrameMixer");
#endif
#ifdef USE_CUDA
        mixers_.push_back("CuVideoFrameMixer");
#endif
        isRegistered = true;
    }
}

std::vector<std::string>& VideoFrameMixer::GetSupportMixers()
{
    RegistMixers();
    return mixers_;
}

bool VideoFrameMixer::IsSupportMixer(const std::string &mixerName)
{
    RegistMixers();
    return std::find(mixers_.begin(), mixers_.end(), mixerName) != mixers_.end();
}

std::shared_ptr<VideoFrameMixer> VideoFrameMixer::CreateVideoMixer(const std::string &mixerName, const std::string &jsonParams)
{
    std::shared_ptr<VideoFrameMixer> ret;
#ifdef USE_LIBYUV
    if (mixerName == "LibyuvVideoFrameMixer")
    {
        ret = std::make_shared<LibyuvVideoFrameMixer>(jsonParams);
    }
#endif
#ifdef RK_PLATFORM
	if (mixerName == "RKVideoFrameMixer") 
    {
		ret = std::make_shared<RKVideoFrameMixer>(jsonParams);
	}
#endif
#ifdef USE_CUDA
    if (mixerName == "CuVideoFrameMixer")
    {
        ret = std::make_shared<CuVideoFrameMixer>(jsonParams);
    }
#endif
    return ret;
}