cmake_minimum_required(VERSION 3.10)

project(Test)

unset(CMAKE_INCLUDE_DIR CACHE)

set(CMAKE_PREFIX_PATH ${CMAKE_CURRENT_SOURCE_DIR}/../ ../3rdpart/)

include(./cmake/common.cmake)

set(CMAKE_CXX_STANDARD 17)

if (ENABLE_VIDEO)
  add_definitions(-DENABLE_VIDEO)
endif ()
if (USE_CUDA)
  add_definitions(-DUSE_CUDA)
endif ()

if (USE_JRTP)
  add_definitions(-DUSE_JRTP)
endif ()
if (USE_WEBRTC)
  add_definitions(-DUSE_WEBRTC -DWEBRTC_POSIX -DWEBRTC_LINUX)
endif ()

if (ENABLE_VIDEO)
    if (USE_CUDA)
        find_library(CUVID_LIB nvcuvid)
        find_library(NVENCODEAPI_LIB nvidia-encode)
        find_package(CUDA)

        find_library(FREEGLUT_LIB glut)
        find_library(GLEW32_LIB GLEW)
        find_library(X11_LIB X11)
        find_library(GL_LIB GL)
        find_library(CUDART_LIB cudart HINTS ${CUDA_TOOLKIT_ROOT_DIR}/lib64)
    endif()
endif()

include_directories(${CMAKE_CURRENT_SOURCE_DIR}
${CMAKE_CURRENT_SOURCE_DIR}/../audio
${CMAKE_CURRENT_SOURCE_DIR}/../video
${CMAKE_CURRENT_SOURCE_DIR}/../
${CMAKE_CURRENT_SOURCE_DIR}/../3rdpart/include 
${CMAKE_CURRENT_SOURCE_DIR}/../3rdpart/include/abseil-cpp-new
${CUDA_INCLUDE_DIRS})
link_directories(../3rdpart/lib)
link_directories(../release/lib)
link_directories(../build/MediaManager)

add_definitions(-Wall -O3 -g -fexceptions -fpermissive)

add_link_options(-Wl,--as-needed)

# message(STATUS "lib ${CUDA_CUDA_LIBRARY} ${CUDART_LIB} ${CMAKE_DL_LIBS} ${NVENCODEAPI_LIB} ${CUVID_LIB} ${FREEGLUT_LIB} ${GLEW32_LIB} ${X11_LIB} ${GL_LIB}")

foreach(index RANGE 1 18 1)
    if (index EQUAL 18)
      add_executable(${PROJECT_NAME}${index} test${index}.cpp mmi.pb.cc mmi.grpc.pb.cc)
    else()
      add_executable(${PROJECT_NAME}${index} test${index}.cpp)
    endif()
    if (ENABLE_VIDEO)
      if (index EQUAL 18)
        target_link_libraries(${PROJECT_NAME}${index} PRIVATE MediaPipeline libv4l2cpp avcodec swscale avutil SDL2 jrtp jthread ljcore yuv hv_static ZLToolKit ssl crypto sdptransform aec rnnoise pthread dl nppicc nppig 
        ${_REFLECTION}
        ${_GRPC_GRPCPP}
        ${_PROTOBUF_LIBPROTOBUF} ${CUDA_CUDA_LIBRARY} ${CUDART_LIB} ${CMAKE_DL_LIBS} ${NVENCODEAPI_LIB} ${CUVID_LIB} ${FREEGLUT_LIB} ${GLEW32_LIB} ${X11_LIB} ${GL_LIB})
      else ()
        target_link_libraries(${PROJECT_NAME}${index} PRIVATE MediaPipeline libv4l2cpp avcodec swscale avutil SDL2 jrtp jthread ljcore yuv hv_static ZLToolKit ssl crypto sdptransform aec rnnoise pthread dl nppicc nppig 
        ${CUDA_CUDA_LIBRARY} ${CUDART_LIB} ${CMAKE_DL_LIBS} ${NVENCODEAPI_LIB} ${CUVID_LIB} ${FREEGLUT_LIB} ${GLEW32_LIB} ${X11_LIB} ${GL_LIB})
      endif ()
    else ()
      target_link_libraries(${PROJECT_NAME}${index} PRIVATE MediaPipeline SDL2 jrtp jthread ljcore hv_static sdptransform aec rnnoise opus ZLToolKit pthread dl)
    endif ()

    install(TARGETS ${PROJECT_NAME}${index} RUNTIME DESTINATION ${CMAKE_INSTALL_PREFIX}/release/)
endforeach()

add_subdirectory(CuQML)
add_subdirectory(CuWidget)
add_subdirectory(sipcall)



