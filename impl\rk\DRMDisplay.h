#ifndef DISPLAY_DRMDISPLAY_H_
#define DISPLAY_DRMDISPLAY_H_
#include <stdint.h>
#include <future>
#include <queue>
#ifdef __cplusplus
extern "C" {
#endif
#include "xf86drm.h"
#include "xf86drmMode.h"
#ifdef __cplusplus
}
#endif
#include "VideoFrameRender.h"

namespace panocom
{
class DRMDisplay : public VideoFrameRender
{
private:
	/* data */
public:
	DRMDisplay(int fd, const std::string& jsonParams = "");
	~DRMDisplay();
	virtual void onFrame(const std::shared_ptr<Frame> &f) override;


    bool InitRender();
private:
	struct buffer_object
	{
		uint32_t width;
		uint32_t height;
		uint32_t pitch;
		uint32_t handle;
		uint32_t size;
		uint32_t *vaddr;
		uint32_t fb_id;
	};
	int modeset_create_fb2(int fd, struct buffer_object *bo, uint32_t color);
	uint32_t get_property_id(int fd, drmModeObjectProperties *props, const char *name);
	bool GetCrtc(drmModeRes *res);
	bool GetConnector(drmModeRes *res);
    bool GetPlane();
	
    struct FB {
        uint32_t fb_handle;
        uint32_t index;
        FB()
            : fb_handle(0)
            , index(0) {}
    };
	
    bool SetCrtc(const FB &fb);
    bool SetPlane(const FB &fb);
	bool PageFlip(const FB &fb);
	bool RenderBuffer();

    bool IsInitialized() const { return inited_; }

	int fd_;
	bool inited_;

	int32_t crtc_index_;
    uint32_t crtc_id_;
    uint32_t con_id_;
    uint32_t encoder_id_;
    uint32_t plane_id_;
	drmModeModeInfo mode_;

    unsigned int format_;
    unsigned int width_;
    unsigned int height_;
    struct CrtcRect {
		CrtcRect() : left(0), top(0), width(1920), height(1080) {}
        int32_t left;
        int32_t top;
        uint32_t width;
        uint32_t height;
    };
	CrtcRect crtc_rect_;
	
	std::future<bool> display_future_;
	bool running_;

	uint32_t fb_id_;
	std::shared_ptr<Frame> frame_;
	std::queue<uint32_t> fb_ids_;

	struct atomic_property
	{
		uint32_t crtc_id;
		uint32_t fb_id;
		uint32_t crtc_x;
		uint32_t crtc_y;
		uint32_t crtc_w;
		uint32_t crtc_h;
		uint32_t src_x;
		uint32_t src_y;
		uint32_t src_w;
		uint32_t src_h;
	};
	atomic_property property_;	
};



} // namespace panocom


#endif