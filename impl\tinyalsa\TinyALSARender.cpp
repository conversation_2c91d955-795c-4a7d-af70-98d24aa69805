#include "TinyALSARender.h"
#include <json.hpp>
#include <ljcore/jlog.h>

using namespace panocom;

TinyALSARender::TinyALSARender(const std::string& jsonParams)
{
    name_ = "TinyALSARender";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    card_ = 0;
    if (j.contains("card"))
    {
        card_ = j["card"];
    }
    dev_ = 0;
    if (j.contains("dev"))
    {
        dev_ = j["dev"];
    }
    samplerate_ = 16000;
    if (j.contains("samplerate"))
    {
        samplerate_ = j["samplerate"];
    }
    chn_ = 1;
    if (j.contains("chn"))
    {
        chn_ = j["chn"];
    }
    start();
}

TinyALSARender::~TinyALSARender()
{
    stop();
}

std::shared_ptr<Frame> TinyALSARender::raiseChn(const std::shared_ptr<Frame>& frame)
{
    auto f = frame;
    if (chn_ > 1)
    {
        auto frame = Frame::CreateFrame(Frame::getPCMFormat(chn_, samplerate_));
        frame->createFrameBuffer(f->getFrameSize() * chn_);
        for (size_t i = 0; i < f->getFrameSize(); i++)
        {
            frame->getFrameBuffer()[i * 2] = f->getFrameBuffer()[i];
            frame->getFrameBuffer()[i * 2 + 1] = f->getFrameBuffer()[i];
        }
        f = frame;
    }
    return f;
}

void TinyALSARender::onFrame(const std::shared_ptr<Frame>& frame)
{
    if (!thread_.isRunning()) return;
    thread_.loop()->runInLoop([this, frame](){
        auto f = frame;
        int samplerate = 0;
        int chn = 0;
        if (Frame::getSamplerate(frame->getFrameFormat(), samplerate, chn))
        {
            if (chn_ != chn)
            {
                
            }
            if (samplerate_ != samplerate)
            {
                if (!initResampler_ || source_samplerate_ != samplerate)
                {
                    source_samplerate_ = samplerate;
                    initResampler_ = true;
#ifdef WEBRTC_RESAMPLE_ANOTHER
                    resampler_.InitializeIfNeeded(samplerate, samplerate_, 1);
#else
                    resampler_.ResetIfNeeded(samplerate, samplerate_, 1);
#endif
                }
                auto frame = Frame::CreateFrame(Frame::getPCMFormat(1, samplerate_));
                frame->createFrameBuffer(f->getFrameSize() * samplerate_ / samplerate);
                memset(frame->getFrameBuffer(), 0, frame->getFrameSize());
#ifdef WEBRTC_RESAMPLE_ANOTHER
                resampler_.Resample((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2);
#else
                size_t outlen = 0;
                resampler_.Push((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2, outlen);
#endif
                f = raiseChn(frame);
            }
        }
        bbuf_.WriteBytes((const char *)f->getFrameBuffer(), f->getFrameSize());
        while (bbuf_.Length() >= writebufferLen_ * chn_)
        {
            int ret = pcm_write(pcm_, bbuf_.Data(), writebufferLen_ * chn_);
            bbuf_.Consume(writebufferLen_ * chn_);
        }
    });
}

void TinyALSARender::start()
{
    if (!pcm_)
    {
        struct pcm_config config;
        memset(&config, 0, sizeof(config));
        config.channels = chn_;
        config.rate = samplerate_;
        config.period_size = 512;
        config.period_count = 2;
        config.format = PCM_FORMAT_S16_LE;
        config.start_threshold = 0;
        config.stop_threshold = 0;
        config.silence_threshold = 0;
        writebufferLen_ = config.period_size * config.period_count * sizeof(int16_t);
        pcm_ = pcm_open(dev_, card_, PCM_OUT, &config);
        if (pcm_ == NULL) {
            jerror("failed to allocate memory for PCM\n");
            return;
        } else if (!pcm_is_ready(pcm_)){
            jerror("failed to open PCM(%d, %d) err(%s)\n", dev_, card_, pcm_get_error(pcm_));
            pcm_close(pcm_);
            return;
        } else {
            jinfo("TinyALSARender pcm_open(%d %d) success", samplerate_, chn_);
        }
        thread_.start();
    }
}

void TinyALSARender::stop()
{
    if (thread_.isRunning())
    {
        thread_.stop(true);
    }
    if (pcm_)
    {
        pcm_close(pcm_);
        pcm_ = nullptr;
    }
    initResampler_ = false;
}