#ifndef P_AudioFrameDispatcher_h
#define P_AudioFrameDispatcher_h

#include "../FramePipeline.h"
#include "../Frame.h"
namespace panocom
{
    class AudioFrameDispatcher : public FramePipeline
    {
    public:
        static void RegistDispatchers();
        static std::vector<std::string>& GetSupportDispatchers();
        static bool IsSupportDispatcher(const std::string &DispatcherName);
        static std::shared_ptr<AudioFrameDispatcher> CreateAudioDispatcher(const std::string &DispatcherName, const std::string &jsonParams = "");

        virtual ~AudioFrameDispatcher() = default;      
    protected:
        static bool isRegistered;
        static std::vector<std::string> dispatchers_;
    };
}

#endif