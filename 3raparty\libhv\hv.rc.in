#include <winresrc.h>

VS_VERSION_INFO     VERSIONINFO
FILEVERSION         ${PROJECT_VERSION_MAJOR},${PROJECT_VERSION_MINOR},${PROJECT_VERSION_PATCH},0
PRODUCTVERSION      ${PROJECT_VERSION_MAJOR},${PROJECT_VERSION_MINOR},${PROJECT_VERSION_PATCH},0
FILEFLAGSMASK       VS_FFI_FILEFLAGSMASK
#ifdef _DEBUG
FILEFLAGS           VS_FF_DEBUG
#else
FILEFLAGS           0x0L
#endif
FILEOS              VOS_NT
FILETYPE            VFT_DLL
FILESUBTYPE         VFT2_UNKNOWN
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "080404B0"
        BEGIN
            VALUE "CompanyName", "https://github.com/ithewei/libhv"
            VALUE "FileDescription", "${PROJECT_NAME} Library"
            VALUE "FileVersion", "${PROJECT_VERSION}"
            VALUE "InternalName", "${PROJECT_NAME}"
            VALUE "LegalCopyright", "Copyright (C) 2020 ithewei All rights reserved."
            VALUE "LegalTrademarks", "${PROJECT_NAME}"
            VALUE "OriginalFilename", "${PROJECT_NAME}.dll"
            VALUE "ProductName", "${PROJECT_NAME}"
            VALUE "ProductVersion", "${PROJECT_VERSION}"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x0804, 0x04B0
    END
END
