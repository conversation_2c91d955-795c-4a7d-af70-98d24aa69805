// Copyright (C) <2019> Intel Corporation
//
// SPDX-License-Identifier: Apache-2.0

#ifndef P_TaskRunnerPool_h
#define P_TaskRunnerPool_h

#include "WebRTCTaskRunner.h"
#include <vector>

namespace panocom {

/**
 * `TaskRunnerPool` contains a fixed number of TaskRunners,
 * `GetTaskRunner` function will get one with round robin.
 */
class TaskRunnerPool {
public:
    static TaskRunnerPool& GetInstance();
    std::shared_ptr<WebRTCTaskRunner> GetTaskRunner();

private:
    TaskRunnerPool();
    ~TaskRunnerPool();

    int m_nextRunner;
    std::vector<std::shared_ptr<WebRTCTaskRunner> > m_taskRunners;
};

} /* namespace owt_base */

#endif /* TaskRunnerPool_h */
