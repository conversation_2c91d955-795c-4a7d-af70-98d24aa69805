#ifndef P_MM_MACROS_H
#define P_MM_MACROS_H

#ifdef USE_MLOG
MediaManagerSP::LogWrapper mm_logger;

#define A_LogD(fmt, ...) sessions_[AUDIO][id].logger.writeDebug(_format11(fmt, ##__VA_ARGS__))
#define A_LogI(fmt, ...) sessions_[AUDIO][id].logger.writeInfo(_format11(fmt, ##__VA_ARGS__))
#define A_LogN(fmt, ...) sessions_[AUDIO][id].logger.writeNormal(_format11(fmt, ##__VA_ARGS__))
#define A_LogW(fmt, ...) sessions_[AUDIO][id].logger.writeWarning(_format11(fmt, ##__VA_ARGS__))
#define A_LogF(fmt, ...) sessions_[AUDIO][id].logger.writeFatal(_format11(fmt, ##__VA_ARGS__))
#define A_LogD_F(fmt, ...) sessions_[AUDIO][id].logger.writeDebug(__FUNCTION__+std::string(" ")+_format11(fmt, ##__VA_ARGS__))
#define A_LogI_F(fmt, ...) sessions_[AUDIO][id].logger.writeInfo(__FUNCTION__+std::string(" ")+_format11(fmt, ##__VA_ARGS__))
#define A_LogN_F(fmt, ...) sessions_[AUDIO][id].logger.writeNormal(__FUNCTION__+std::string(" ")+_format11(fmt, ##__VA_ARGS__))
#define A_LogW_F(fmt, ...) sessions_[AUDIO][id].logger.writeWarning(__FUNCTION__+std::string(" ")+_format11(fmt, ##__VA_ARGS__))
#define A_LogF_F(fmt, ...) sessions_[AUDIO][id].logger.writeFatal(__FUNCTION__+std::string(" ")+_format11(fmt, ##__VA_ARGS__))

#define V_LogD(fmt, ...) sessions_[VIDEO][id].logger.writeDebug(_format11(fmt, ##__VA_ARGS__))
#define V_LogI(fmt, ...) sessions_[VIDEO][id].logger.writeInfo(_format11(fmt, ##__VA_ARGS__))
#define V_LogN(fmt, ...) sessions_[VIDEO][id].logger.writeNormal(_format11(fmt, ##__VA_ARGS__))
#define V_LogW(fmt, ...) sessions_[VIDEO][id].logger.writeWarning(_format11(fmt, ##__VA_ARGS__))
#define V_LogF(fmt, ...) sessions_[VIDEO][id].logger.writeFatal(_format11(fmt, ##__VA_ARGS__))
#define V_LogD_F(fmt, ...) sessions_[VIDEO][id].logger.writeDebug(__FUNCTION__+std::string(" ")+_format11(fmt, ##__VA_ARGS__))
#define V_LogI_F(fmt, ...) sessions_[VIDEO][id].logger.writeInfo(__FUNCTION__+std::string(" ")+_format11(fmt, ##__VA_ARGS__))
#define V_LogN_F(fmt, ...) sessions_[VIDEO][id].logger.writeNormal(__FUNCTION__+std::string(" ")+_format11(fmt, ##__VA_ARGS__))
#define V_LogW_F(fmt, ...) sessions_[VIDEO][id].logger.writeWarning(__FUNCTION__+std::string(" ")+_format11(fmt, ##__VA_ARGS__))
#define V_LogF_F(fmt, ...) sessions_[VIDEO][id].logger.writeFatal(__FUNCTION__+std::string(" ")+_format11(fmt, ##__VA_ARGS__))

#define T_LogD(fmt, ...) sessions_[type][id].logger.writeDebug(_format11(fmt, ##__VA_ARGS__))
#define T_LogI(fmt, ...) sessions_[type][id].logger.writeInfo(_format11(fmt, ##__VA_ARGS__))
#define T_LogN(fmt, ...) sessions_[type][id].logger.writeNormal(_format11(fmt, ##__VA_ARGS__))
#define T_LogW(fmt, ...) sessions_[type][id].logger.writeWarning(_format11(fmt, ##__VA_ARGS__))
#define T_LogF(fmt, ...) sessions_[type][id].logger.writeFatal(_format11(fmt, ##__VA_ARGS__))
#define T_LogD_F(fmt, ...) sessions_[type][id].logger.writeDebug(__FUNCTION__+std::string(" ")+_format11(fmt, ##__VA_ARGS__))
#define T_LogI_F(fmt, ...) sessions_[type][id].logger.writeInfo(__FUNCTION__+std::string(" ")+_format11(fmt, ##__VA_ARGS__))
#define T_LogN_F(fmt, ...) sessions_[type][id].logger.writeNormal(__FUNCTION__+std::string(" ")+_format11(fmt, ##__VA_ARGS__))
#define T_LogW_F(fmt, ...) sessions_[type][id].logger.writeWarning(__FUNCTION__+std::string(" ")+_format11(fmt, ##__VA_ARGS__))
#define T_LogF_F(fmt, ...) sessions_[type][id].logger.writeFatal(__FUNCTION__+std::string(" ")+_format11(fmt, ##__VA_ARGS__))

#else
#include <ljcore/jlog.h>
#include <ljcore/jini.h>

#define JLOG_CFG_FILE   "jlog.ini"

#define LogD      jdebug
#define LogI      jinfo
#define LogN      jtrace
#define LogW      jwarn
#define LogF      jfatal
#define LogD_F    jdebug
#define LogI_F    jinfo
#define LogN_F    jtrace
#define LogW_F    jwarn
#define LogF_F    jfatal

#define A_LogD      jdebug
#define A_LogI      jinfo
#define A_LogN      jtrace
#define A_LogW      jwarn
#define A_LogF      jfatal
#define A_LogD_F    jdebug
#define A_LogI_F    jinfo
#define A_LogN_F    jtrace
#define A_LogW_F    jwarn
#define A_LogF_F    jfatal

#define V_LogD      jdebug
#define V_LogI      jinfo
#define V_LogN      jtrace
#define V_LogW      jwarn
#define V_LogF      jfatal
#define V_LogD_F    jdebug
#define V_LogI_F    jinfo
#define V_LogN_F    jtrace
#define V_LogW_F    jwarn
#define V_LogF_F    jfatal

#define T_LogD      jdebug
#define T_LogI      jinfo
#define T_LogN      jtrace
#define T_LogW      jwarn
#define T_LogF      jfatal
#define T_LogD_F    jdebug
#define T_LogI_F    jinfo
#define T_LogN_F    jtrace
#define T_LogW_F    jwarn
#define T_LogF_F    jfatal

#endif

#endif