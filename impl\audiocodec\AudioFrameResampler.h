// created by gyj 2024-3-28
#ifndef P_AudioFrameResampler_h
#define P_AudioFrameResampler_h

#include "AudioFrameProcesser.h"
#include <resampler/resampler.h>
#include <resampler/push_resampler.h>
namespace panocom
{
    class AudioFrameResampler : public AudioFrameProcesser
    {
    public:
        AudioFrameResampler(const std::string& jsonParams);
        ~AudioFrameResampler() override;

        void onFrame(const std::shared_ptr<Frame>& frame) override;
    private:
        int src_chn_ = 1;
        int src_samplerate_ = 16000;
        int dst_chn_ = 1;
        int dst_samplerate_ = 48000;
        FILE* pf_ = nullptr;
#ifdef WEBRTC_RESAMPLE_ANOTHER
        nswebrtc::PushResampler<int16_t> resampler_;
#else
        nswebrtc::Resampler resampler_;
#endif
        bool initResampler_ = false;
    };
}

#endif