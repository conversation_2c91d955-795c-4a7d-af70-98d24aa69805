#include "QCuVideoItem.h"

using namespace panocom;

QCuVideoItem::QCuVideoItem()
:  mProfile(new MasterProfile),
   mPollGrp(FdPollGrp::create()),
   mEventInterruptor(new EventThreadInterruptor(*mPollGrp)),
   mDum(mStack),
   mStackThread(mStack, *mEventInterruptor, *mPollGrp),
   areaId_(0)
{
    connect(this, &QQuickItem::windowChanged, this, [this](QQuickWindow *window){
        if (window) {
            connect(window, &QQuickWindow::beforeRendering, this, &QCuVideoItem::sync,
                    Qt::DirectConnection);
            connect(window, &QQuickWindow::sceneGraphInvalidated, this, &QCuVideoItem::cleanup,
                    Qt::DirectConnection);
            window->setClearBeforeRendering(false);
        } 
    });
    for (size_t i = 0; i < 16; i++)
    {
        idleChn_.insert(i);
    }
    
    Log::initialize(Log::Cout, Log::Debug, "test");
    initStack();
}

void QCuVideoItem::moveVideoRight(){
	//给个例子,点击界面最右边按钮的时候会触发这个函数
    int w = width();
    int h = height();
    curX_ += 100;
    if (instances_.size() > 0) {
        m_renderer->moveVideoFrameRender(0, curX_, 0, 0, w / 4, h / 4);
    }
        
}

void QCuVideoItem::addVideoPlayWindow(){
	//给个例子,点击界面最右边按钮的时候会触发这个函数
    int w = width();
    int h = height();
    createInstance(w, h, 0);
}

void QCuVideoItem::removeVideoPlayWindow()
{
    // destroyInstance(0);
    destroyWindowInstance();
}

void QCuVideoItem::stopVideoPlayWindow()
{
}

void QCuVideoItem::setVideoPlayWindowZ()
{
    static int i = 0;
    m_renderer->moveVideoFrameRender(0, 0, 0, ++i % 2, 1050, 675);
}

void QCuVideoItem::call(const QString& ip)
{
    if (idleChn_.empty())
        return;
    auto it = idleChn_.begin();
    int dev = *it;
    std::string chn = std::to_string(dev);
    idleChn_.erase(it);
    nlohmann::json jj;
    jj["remote"]["video"]["dev"] = dev;
    mMmi.CreateSession(chn, jj.dump());
    std::string sdp;
    int ret = mMmi.GetLocalSDP(chn, sdp, "{\"video\": true}");
    if (ret == 0)
    {
        HeaderFieldValue hfv(sdp.data(), sdp.size());
        Mime type("Application", "sdp");
        Contents *sdpContent = ContentsFactoryBase::getFactoryMap()[type]->create(hfv, type);
        Data destinationUri = ip.toStdString().c_str();
        jinfo("=========================\n%s", sdpContent->getBodyData().c_str());
        // Create the invite message
        auto invitemsg = mDum.makeInviteSession(
            NameAddr(destinationUri),
            mProfile,
            sdpContent);
        
        instances_[dev].id = dev;
        instances_[dev].callid = invitemsg->header(h_CallID).value().c_str();
        instances_[dev].ip = ip.toStdString();

        invitemsg->header(h_Vias).front().sentHost() = mProfile->getDefaultFrom().uri().host();
        invitemsg->header(h_Vias).front().sentPort() = mProfile->getDefaultFrom().uri().port();
        invitemsg->header(h_Vias).front().transport() = "UDP";

        invitemsg->header(h_Contacts).front().uri() = mProfile->getDefaultFrom().uri();

        Data encoded;
        DataStream encodeStream(encoded);
        invitemsg->encode(encodeStream);
        encodeStream.flush();

        jinfo("--------------------------\n%s", encoded.c_str());
        // Send the invite message
        mDum.send(std::move(invitemsg));

        delete sdpContent;
    }
}

void QCuVideoItem::hangup(const QString& ip)
{
    for (auto& pair : instances_)
    {
        if (pair.second.ip == ip.toStdString() && pair.second.mH.isValid())
        {
            pair.second.mH->end();
            break;
        }
    }
}

void QCuVideoItem::sync()
{
    if (!m_renderer)
    {
        jlog_init(nullptr);
        m_renderer = new QCuRender(window(), width(), height());
        // int dev = 0;
        // nlohmann::json jj;
        // jj["path"] = "4k-test.h264";
        // instances_[dev].filer = FileFramePipeline::CreateFileSource("H2645FileFrameSource", jj.dump());
        // jj.clear();
        // jj["codec"] = "h264";
        // jj["gpuIndex"] = dev < 8 ? 1 : 0;
        // instances_[dev].decoder = VideoFrameDecoder::CreateVideoDecoder("CuVideoDecoder", jj.dump());
        // m_renderer->createVideoFrameRender(dev, 0, 0, 0, 1050, 675, 0, dev < 8 ? 1 : 0);
        // instances_[dev].filer->addVideoDestination(instances_[dev].decoder);
        // instances_[dev].decoder->addVideoDestination(m_renderer->render());
        // instances_[dev].decoder->setGroupId(dev);

        m_renderer->initializeGL();
        connect(window(), &QQuickWindow::beforeRendering, this, [this]() {
            window()->resetOpenGLState();
            m_renderer->display();
        }, Qt::DirectConnection);
    }
}

void QCuVideoItem::createInstance(int w, int h, int dev)
{
    m_renderer->createVideoFrameRender(dev, areaId_, (w >> 2) * (areaId_ % 4), (h >> 2) * (areaId_ >> 2), 0, w / 4, h / 4, 0, dev < 8 ? 0 : 1);
    areaId_++;
    if (video_ids_.count(dev) == 0) {
        mMmi.AddVideoRender(std::to_string(dev), m_renderer->render());
        video_ids_.emplace(dev);
    }
    return;
    for (size_t i = 0; i < 4; i++)
    {
        for (size_t j = 0; j < 4; j++)
        {
            // int dev = i * 4 + j;
            // if (instances_.find(dev) == instances_.end())
            // {
                // # 1
                // nlohmann::json jj;
                // jj["path"] = "4k-test.h264";
                // instances_[dev].filer = FileFramePipeline::CreateFileSource("H2645FileFrameSource", jj.dump());
                // jj.clear();
                // jj["codec"] = "h264";
                // jj["gpuIndex"] = dev < 8 ? 1 : 0;
                // instances_[dev].decoder = VideoFrameDecoder::CreateVideoDecoder("CuVideoDecoder", jj.dump());
                // m_renderer->createVideoFrameRender(dev, w / 4 * i, h / 4 * j, 0, w / 4, h / 4, 0, dev < 8 ? 1 : 0);
                // instances_[dev].filer->addVideoDestination(instances_[dev].decoder);

                // # 2
                // nlohmann::json jj;
                // jj.clear();
                // jj["dev"] = dev;
                // jj["gpuIndex"] = dev < 8 ? 0 : 1;
                // instances_[dev].capturer = VideoFrameCapturer::CreateVideoCapturer("CuIPCFrameCapturer", jj.dump());
                // instances_[dev].capturer->setGroupId(dev);
                // instances_[dev].capturer->addVideoDestination(m_renderer->render());

                // # 3
            if (dev == i * 4 + j)
            {
                m_renderer->createVideoFrameRender(dev, 0, w / 4 * i, h / 4 * j, 0, w / 4, h / 4, 0, dev < 8 ? 0 : 1);
                mMmi.AddVideoRender(std::to_string(dev), m_renderer->render());
                return;
            }
            //}
        }
    }
}

void QCuVideoItem::destroyInstance(int dev)
{
    // if (instances_.size() > 0)
    // {
        // # 1
        // int dev = instances_.begin()->first;
        // instances_[dev].filer->removeVideoDestination(instances_[dev].decoder);
        // instances_[dev].decoder->removeVideoDestination(m_renderer->render());
        // m_renderer->destroyVideoFrameRender(dev);
        // instances_[dev].filer->stop();
        // instances_[dev].decoder->stop();
        // instances_.erase(dev);

        // # 2
        // int dev = instances_.begin()->first;
        // instances_[dev].capturer->removeVideoDestination(m_renderer->render());
        // m_renderer->destroyVideoFrameRender(dev);
        // instances_[dev].capturer->stop();
        // instances_.erase(dev);

        // # 3
        //int dev = instances_.begin()->first;
        if (video_ids_.count(dev) != 0) {
            mMmi.RemoveVideoRender(std::to_string(dev));
            video_ids_.erase(dev);
        }
        for (int id = 0; id < areaId_; id++)
            m_renderer->destroyVideoFrameRender(id);
        areaId_ = 0;
        m_renderer->render()->start(); // cuDecoder会关闭下游的pipeline，保证安全退出，因此要重新start.
        instances_.erase(dev);
        idleChn_.insert(dev);
    //}
}

void QCuVideoItem::destroyWindowInstance() {
    if (areaId_ > 0) {
        areaId_--;
        m_renderer->destroyVideoFrameRender(areaId_);
    }
}

void QCuVideoItem::cleanup()
{
    if (m_renderer)
    {
        delete m_renderer;
        m_renderer = nullptr;
    }
}

quint32 QCuVideoItem::index() const
{
    return index_;
}

void QCuVideoItem::setIndex(quint32 index)
{
    index_ = index;
}

void QCuVideoItem::callUpdate()
{
    window()->update();
}

void QCuVideoItem::process()
{
    mDum.process();
}

void QCuVideoItem::initStack()
{
    // Add transports
    Data host = mMmi.GetDefaultIP().c_str();
    jinfo("host = %s", host.c_str());
    try
    {
        mStack.addTransport(UDP, 5060, V4, StunEnabled);
    }
    catch (BaseException &e)
    {
        std::cerr << "Likely a port is already in use" << endl;
        exit(-1);
    }

    // Disable Statisitics Manager
    mStack.statisticsManagerEnabled() = false;

    // Setup MasterProfile
    mProfile->clearSupportedMimeTypes();
    mProfile->addSupportedMimeType(INVITE, Mime("application", "sdp"));

    mProfile->clearSupportedMethods();
    mProfile->addSupportedMethod(INVITE);
    mProfile->addSupportedMethod(ACK);
    mProfile->addSupportedMethod(CANCEL);
    mProfile->addSupportedMethod(BYE);

    mProfile->clearSupportedSchemes();
    mProfile->addSupportedScheme("sip");

    NameAddr addr;
    addr.uri().host() = host;
    addr.uri().port() = 5060;

    mProfile->setDefaultFrom(addr);

    mProfile->setUserAgent("sipcall");

    // Install Handlers
    mDum.setMasterProfile(mProfile);

    // Install this Widget as handler
    mDum.setInviteSessionHandler(this);

    mStack.run();
    mStackThread.run();
}

void QCuVideoItem::onNewSession(ClientInviteSessionHandle h, InviteSession::OfferAnswerType oat, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void QCuVideoItem::onNewSession(ServerInviteSessionHandle h, InviteSession::OfferAnswerType oat, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void QCuVideoItem::onFailure(ClientInviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}
      
void QCuVideoItem::onEarlyMedia(ClientInviteSessionHandle h, const SipMessage& msg, const SdpContents& sdp)
{
    InfoLog (<< msg);
}

void QCuVideoItem::onProvisional(ClientInviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void QCuVideoItem::onConnected(ClientInviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void QCuVideoItem::onConnected(InviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void QCuVideoItem::onStaleCallTimeout(ClientInviteSessionHandle h)
{
}

void QCuVideoItem::onTerminated(InviteSessionHandle h, InviteSessionHandler::TerminatedReason reason, const SipMessage* msg)
{
    InfoLog (<< msg);
    if (msg)
    {
        std::string callid = msg->header(h_CallID).value().c_str();
        for (auto& pair : instances_)
        {
            if (pair.second.callid == callid)
            {
                jinfo("ReleaseSession %d", pair.first);
                mMmi.ReleaseSession(std::to_string(pair.first));
                destroyInstance(pair.first);
                break;
            }
        }
    }
    
}

void QCuVideoItem::onRedirected(ClientInviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void QCuVideoItem::onAnswer(InviteSessionHandle h, const SipMessage& msg, const SdpContents& sdp)
{
    std::string callid = msg.header(h_CallID).value().c_str();
    for (auto& pair : instances_)
    {
        if (pair.second.callid == callid)
        {
            pair.second.mH = h;
            std::string answerSdp = sdp.getBodyData().c_str();
            jinfo("SetRemoteSDP %s", answerSdp.c_str());
            std::string output;
            int ret = mMmi.SetRemoteSDP(std::to_string(pair.first), answerSdp, output);
            createInstance(width(), height(), pair.first);
            break;
        }
    }
}

void QCuVideoItem::onOffer(InviteSessionHandle h, const SipMessage& msg, const SdpContents& sdp)
{
    InfoLog (<< msg);
}

void QCuVideoItem::onOfferRequired(InviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void QCuVideoItem::onOfferRejected(InviteSessionHandle h, const SipMessage* msg)
{
    InfoLog (<< msg);
}

void QCuVideoItem::onOfferRequestRejected(InviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void QCuVideoItem::onRemoteSdpChanged(InviteSessionHandle h, const SipMessage& msg, const SdpContents& sdp)
{
    InfoLog (<< msg);
}

void QCuVideoItem::onInfo(InviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void QCuVideoItem::onInfoSuccess(InviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void QCuVideoItem::onInfoFailure(InviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void QCuVideoItem::onRefer(InviteSessionHandle h, ServerSubscriptionHandle ssh, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void QCuVideoItem::onReferAccepted(InviteSessionHandle h, ClientSubscriptionHandle csh, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void QCuVideoItem::onReferRejected(InviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void QCuVideoItem::onReferNoSub(InviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void QCuVideoItem::onMessage(InviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void QCuVideoItem::onMessageSuccess(InviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void QCuVideoItem::onMessageFailure(InviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void QCuVideoItem::onForkDestroyed(ClientInviteSessionHandle h)
{
}