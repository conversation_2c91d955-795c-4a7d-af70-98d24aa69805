// created by gyj 2024-2-20
#ifndef P_FfHwVideoDecoder_h
#define P_FfHwVideoDecoder_h

#include <hv/EventLoopThread.h>
#include "VideoFrameDecoder.h"

extern "C" {
#include <libavutil/opt.h>
#include <libavutil/imgutils.h>
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavutil/hwcontext.h>
#include <libswscale/swscale.h>
}

namespace panocom
{
    class FfHwVideoDecoder : public VideoFrameDecoder
    {
    public:
        FfHwVideoDecoder(const std::string& jsonParams);
        virtual ~FfHwVideoDecoder();

        void onFrame(const std::shared_ptr<Frame>& frame) override;
        void start() override;
        void stop() override;


        static enum AVPixelFormat hw_pix_fmt;

        int hwDecoderInit(AVCodecContext *ctx, const enum AVHWDeviceType type);
        static enum AVPixelFormat getHwformat(AVCodecContext *ctx,const enum AVPixelFormat *pix_fmts);

        bool isInitSuccess(){
            return initSuccess;
        }
    private:
        AVCodec *avcodec_ = nullptr;
        AVCodecContext *ctx_ = nullptr;
        AVCodecParserContext* parser_ = nullptr;
        std::string codecName_;
        int fps_;
        int kbps_;
        int gop_;
        int width_;
        int height_;
        int extra_hw_frames_;
        hv::EventLoopThread thread_;
        //FILE* pf_ = nullptr;
        bool initSuccess;

        AVBufferRef *hw_device_ctx = nullptr;


    };
} // namespace panocom


#endif