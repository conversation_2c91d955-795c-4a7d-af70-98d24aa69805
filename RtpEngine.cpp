#include "RtpEngine.h"

#include <algorithm>

#ifdef USE_JRTP
#include "jrtp/JRtpEngine.h"
#endif

#ifdef USE_WEBRTC
#include "webrtc/RtcRtpVideoFrameDestination.h"
#include "webrtc/RtcRtpVideoFrameDestination2.h"
#include "webrtc/RtcRtpVideoFrameSource.h"
#endif

#ifdef USE_DTLS_RTP
#include <DTLSTool/dtlsrtpsession.h>
#endif

using namespace panocom;

std::vector<std::string> RtpEngine::rtps_;

bool RtpEngine::isRegistered = false;

void RtpEngine::RegistRTPEngine()
{
    if (!isRegistered)
    {
#ifdef USE_JRTP
        rtps_.push_back("JRtpEngine");
#endif

#ifdef USE_WEBRTC
        rtps_.push_back("RtcRtpVideoFrameDestination");
        rtps_.push_back("RtcRtpVideoFrameSource");
#endif
        isRegistered = true;
    }
}

std::vector<std::string> &RtpEngine::GetSupportRtpEngines()
{
    RegistRTPEngine();
    return rtps_;
}

bool RtpEngine::IsSupportRtpEngine(const std::string &rtpEngineName)
{
    RegistRTPEngine();
    return std::find(rtps_.begin(), rtps_.end(), rtpEngineName) != rtps_.end();
}

std::shared_ptr<RtpEngine> RtpEngine::CreateRTPEngine(const std::string &rtpEngineName, const std::string &jsonParams)
{
    RegistRTPEngine();
    std::shared_ptr<RtpEngine> ret;
#ifdef USE_JRTP
    if (rtpEngineName == "JRtpEngine")
    {
        ret = std::shared_ptr<RtpEngine>(new JRtpEngine(jsonParams));
    }
#endif

#ifdef USE_WEBRTC
    if (rtpEngineName == "RtcRtpVideoFrameDestination")
    {
        ret = std::shared_ptr<RtpEngine>(new RtcRtpVideoFrameDestination2(jsonParams));
    }
    if (rtpEngineName == "RtcRtpVideoFrameSource")
    {
        ret = std::shared_ptr<RtpEngine>(new RtcRtpVideoFrameSource(jsonParams));
    }
#endif
    return ret;
}

bool RtpEngine::IsRTPEngine(const FramePipeline::Ptr& ptr)
{
#ifdef USE_JRTP
    if (ptr->name() == "JRtpEngine")
    {
        return true;
    }
#endif
#ifdef USE_WEBRTC
    if (ptr->name() == "RtcRtpVideoFrameDestination")
    {
        return true;
    }
    if (ptr->name() == "RtcRtpVideoFrameSource")
    {
        return true;
    }
#endif
    return false;
}

std::string RtpEngine::GetFingerPrint()
{
#ifdef USE_DTLS_RTP
    return DTLSRTPSession::GetFingerprint();
#else
    return "";
#endif
}