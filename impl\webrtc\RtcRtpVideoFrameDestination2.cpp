#include "RtcRtpVideoFrameDestination2.h"
#include "Frame.h"
#include "Utils.h"
#include "thread/ProcessThreadProxy.h"
#include "thread/StaticTaskQueueFactory.h"
#include <api/video_codecs/video_encoder_config.h>
#include <media/base/media_constants.h>
#include <media/engine/webrtc_video_engine.h>

#include <api/video/video_codec_type.h>
#include <api/video_codecs/video_codec.h>
#include "api/video/builtin_video_bitrate_allocator_factory.h"
#include <modules/include/module_common_types.h>
#include <modules/pacing/packet_router.h>
#include <modules/rtp_rtcp/source/rtcp_packet/transport_feedback.h>
#include <modules/rtp_rtcp/source/rtp_video_header.h>
#include <modules/rtp_rtcp/source/ulpfec_generator.h>
#include <rtc_base/logging.h>
#include <sys/time.h>
#include <system_wrappers/include/field_trial.h>

#include <json.hpp>
#include <ljcore/jlog.h>

#include <api/array_view.h>
#include <modules/rtp_rtcp/source/rtcp_packet/common_header.h>
#include <modules/rtp_rtcp/source/rtcp_packet/nack.h>
#include <modules/rtp_rtcp/source/rtcp_packet/rtpfb.h>

namespace panocom
{
static std::unique_ptr<webrtc::FieldTrialBasedConfig> g_fieldTrial= []()
{
    auto config = std::make_unique<webrtc::FieldTrialBasedConfig>();
    /**/
    webrtc::field_trial::InitFieldTrialsFromString(
        "WebRTC-KeyframeInterval/"
        "max_wait_for_keyframe_ms:500,"
        "max_wait_for_frame_ms:1500/"
        "WebRTC-TaskQueuePacer/Enabled/");
    return config;
}();

RtcRtpVideoFrameDestination2::RtcRtpVideoFrameDestination2(const std::string &jsonParams)
    : random_(rtc::TimeMicros())
    , ssrc_(0)
    , ssrcGenerator_(SsrcGenerator::GetSsrcGenerator()) {
    FN_BEGIN;
    name_ = "RtcRtpVideoFrameDestination";
    jinfo("RtcRtpVideoFrameDestination2 constructor jsonParams: %s", jsonParams.c_str());
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);

    payload_type_ = 96;
    if (j.contains("payloadType"))
    {
        payload_type_ = j["payloadType"];
    }
    if (j.contains("remote_payload_type")) {
        payload_type_ = j["remote_payload_type"];
    }
    red_payload_type_ = 116;
    if (j.contains("remote_red_payload_type"))
    {
        red_payload_type_ = j["remote_red_payload_type"];
    }
    ulpfec_payload_type_ = 127;
    if (j.contains("remote_ulpfec_payload_type"))
    {
        ulpfec_payload_type_ = j["remote_ulpfec_payload_type"];
    }
    startBitrate_ = 3000000;
    if (j.contains("startBitrate"))
    {
        int bitrate = j["startBitrate"];
        startBitrate_ = bitrate * 1024;
    }
    minBitrate_ = 2000000;
    if (j.contains("startBitrate"))
    {
        int bitrate = j["startBitrate"];
        minBitrate_ = bitrate * 1024;
    }
    maxBitrate_ = 4000000;
    if (j.contains("maxBitrate"))
    {
        int bitrate = j["maxBitrate"];
        maxBitrate_= bitrate * 1024;
    }
    avgBitrate_ = 4000000;
    if (j.contains("avgBitrate"))
    {
        int bitrate = j["avgBitrate"];
        avgBitrate_ = bitrate * 1024;
    }
    curBitrate_ = startBitrate_;

    if (j.contains("NACK")) {
        nack_enabled_ = j["NACK"];
    }
    if (j.contains("ULPFEC")) {
        ulpfec_enabled_ = j["ULPFEC"];
    }
    if (j.contains("AJB")) {
        ajb_enabled_ = j["AJB"];
    }
    if (j.contains("local_media") && j["local_media"].contains("ext")) {
        rtp_extmap_ = j["local_media"]["ext"];
    }
    if (j.contains("remote_media"))
    {
        if (j["remote_media"].contains("ext")) {
            remote_rtp_extmap_ = j["remote_media"]["ext"];
        }
        if (j["remote_media"].contains("rtcpFb")) {
            remote_rtp_rtcpfb_ = j["remote_media"]["rtcpFb"];
        }
    }
    if (ajb_enabled_)
    {
        if (minBitrate_ > 64 * 1024)
        { //webrtc带宽预测，不限最低带宽
            minBitrate_ = 64 * 1024;
        }
    }

    eventLog_ = std::make_shared<webrtc::RtcEventLogNull>();
    videoEncoderFactory_ = std::make_unique<CustomVideoEncoderFactory>();
    video_bitrate_allocator_factory_ = webrtc::CreateBuiltinVideoBitrateAllocatorFactory();
    taskQueueFactory_ = createStaticTaskQueueFactory(TaskQueueType::kEncoding);
    start();
    FN_END;
}
RtcRtpVideoFrameDestination2::~RtcRtpVideoFrameDestination2() {
    FN_BEGIN;
    stop();
    FN_END;
}
bool RtcRtpVideoFrameDestination2::init() { return true; }

void RtcRtpVideoFrameDestination2::start() {
    if (taskQueue_) return;
    FN_BEGIN;

    {
        std::unique_lock<std::mutex> locker(mutex_);
        taskQueue_ = std::make_shared<rtc::TaskQueue>(taskQueueFactory_->CreateTaskQueue("CallTaskQueue", webrtc::TaskQueueFactory::Priority::NORMAL));
        if (taskQueue_ == nullptr) {
            jinfo("taskQueue_ is null");
            return;
        }
        started_.Reset();
        taskQueue_->PostTask([this]() {
            ssrc_ = ssrcGenerator_->CreateSsrc();
            ssrcGenerator_->RegisterSsrc(ssrc_);
        
            webrtc::Call::Config call_config(eventLog_.get());
            call_config.task_queue_factory = taskQueueFactory_.get();
            call_config.trials = g_fieldTrial.get();

            call_config.network_state_predictor_factory = nullptr;
            call_config.network_controller_factory = nullptr;

            call_config.bitrate_config.min_bitrate_bps = minBitrate_;
            call_config.bitrate_config.start_bitrate_bps = startBitrate_;
            call_config.bitrate_config.max_bitrate_bps = maxBitrate_;

            call_.reset(webrtc::Call::Create(call_config));

            call_->SignalChannelNetworkState(webrtc::MediaType::VIDEO, webrtc::NetworkState::kNetworkUp);

            started_.Set();
        });   
        started_.Wait(rtc::Event::kForever);
    }

    FN_END;
}
void RtcRtpVideoFrameDestination2::stop() {
    FN_BEGIN;
    std::unique_lock<std::mutex> locker(mutex_);
    if (taskQueue_)
    {
        stopped_.Reset();
        taskQueue_->PostTask([this]() {
            call_->SignalChannelNetworkState(webrtc::MediaType::VIDEO, webrtc::NetworkState::kNetworkDown);
            if (sendStream_) 
            {
                RTC_DLOG(LS_INFO) << "Destroy videoSendStream_ with SSRC: " << ssrc_;
                call_->DestroyVideoSendStream(sendStream_);
                sendStream_ = nullptr;
            }
            call_.reset();
            stopped_.Set();
        });
        stopped_.Wait(rtc::Event::kForever);
        taskQueue_.reset();
    }
    FN_END;
}

void RtcRtpVideoFrameDestination2::onFrame(const std::shared_ptr<Frame> &f) {
    std::unique_lock<std::mutex> locker(mutex_);
    if (taskQueue_)
    {
        taskQueue_->PostTask([this, f]() {
            auto fmt = f->getFrameFormat();
            if (fmt != FRAME_FORMAT_RTCP && (!sendStream_ || fmt != fmt_)) {
                fmt_ = fmt;
                resetSendStream();
            }
            if (!sendStream_)
            {
                return;
            }
            uint8_t *frameBuffer = f->getFrameBuffer();
            int frameSize = f->getFrameSize();

            if (fmt == FRAME_FORMAT_H264 || fmt == FRAME_FORMAT_H265)
            {
                webrtc::EncodedImage encodedImage;
                encodedImage.SetEncodedData(webrtc::EncodedImageBuffer::Create(frameBuffer, frameSize));
                encodedImage._encodedWidth = encoded_width_;
                encodedImage._encodedHeight = encoded_height_;
                encodedImage._frameType = Frame::isKeyFrame(f) ? webrtc::VideoFrameType::kVideoFrameKey : webrtc::VideoFrameType::kVideoFrameDelta;
                encodedImage.capture_time_ms_ = presetNextTimestamp() / 90;
                encodedImage.SetTimestamp(presetNextTimestamp());
                webrtc::CodecSpecificInfo codec_specific_info;
                codec_specific_info.codecType = (fmt == FRAME_FORMAT_H264 ? webrtc::VideoCodecType::kVideoCodecH264 : webrtc::VideoCodecType::kVideoCodecH265);

                if (videoEncoderFactory_->get_encoder()) {
                    videoEncoderFactory_->get_encoder()->PushEncodedFrameFrame(encodedImage, &codec_specific_info);
                }
            }
            else if (fmt == FRAME_FORMAT_RTCP)
            {
                // jinfo("rtcp packet size: %d", f->getFrameSize());
                rtc::CopyOnWriteBuffer buffer(f->getFrameBuffer(), f->getFrameSize());
                call_->Receiver()->DeliverPacket(
                                        webrtc::MediaType::VIDEO,
                                        buffer,
                                        rtc::TimeUTCMicros()); 
            }
        });
    }
}

bool RtcRtpVideoFrameDestination2::SendRtp(const uint8_t* packet, size_t length, const webrtc::PacketOptions& options) {
    std::shared_ptr<Frame> f = Frame::CreateFrame(FRAME_FORMAT_RTP);
    if (f)
    {
        //jinfo(" RtcRtpVideoFrameDestination::SendRtp %d", length);
        f->createFrameBuffer(length);
        memcpy(f->getFrameBuffer(), packet, length);
        deliverFrame(f);
        return true;
    }
    return false;
}

bool RtcRtpVideoFrameDestination2::SendRtcp(const uint8_t* packet, size_t length) { 
    return true; 
}

uint32_t RtcRtpVideoFrameDestination2::convertToRTPTimestamp(struct timeval tv)
{
    // Begin by converting from "struct timeval" units to RTP timestamp units:
    u_int32_t timestampIncrement = (90000 * tv.tv_sec);
    timestampIncrement += (u_int32_t)(90000 * (tv.tv_usec / 1000000.0) + 0.5); // note: rounding

    // Then add this to our 'timestamp base':
    if (fNextTimestampHasBeenPreset_)
    {
        // Make the returned timestamp the same as the current "fTimestampBase",
        // so that timestamps begin with the value that was previously preset:
        fTimestampBase_ -= timestampIncrement;
        fNextTimestampHasBeenPreset_ = false;
    }

    u_int32_t const rtpTimestamp = fTimestampBase_ + timestampIncrement;
#ifdef DEBUG_TIMESTAMPS
    fprintf(stderr, "fTimestampBase: 0x%08x, tv: %lu.%06ld\n\t=> RTP timestamp: 0x%08x\n",
            fTimestampBase, tv.tv_sec, tv.tv_usec, rtpTimestamp);
    fflush(stderr);
#endif

    return rtpTimestamp;
}

uint32_t RtcRtpVideoFrameDestination2::presetNextTimestamp()
{
    struct timeval timeNow;
    gettimeofday(&timeNow, NULL);

    u_int32_t tsNow = convertToRTPTimestamp(timeNow);
    // fTimestampBase = tsNow;
    // fNextTimestampHasBeenPreset = true;
    return tsNow;
}

std::string RtcRtpVideoFrameDestination2::GetStats2()
{
    stats_ready_.Reset();
    taskQueue_->PostTask([this]() {
        if (!sendStream_) return;
        stats_ = sendStream_->GetStats();
        stats_ready_.Set();
    });
    if (!stats_ready_.Wait(100)) return "";
    nlohmann::json j;
    j["bps"] = stats_.media_bitrate_bps;
    j["fps"] = (float)stats_.encode_frame_rate / 1000.0f;
    if (stats_.substreams.count(ssrc_) != 0) {
        j["jitter"] = stats_.substreams[ssrc_].rtcp_stats.jitter;
        j["sendPacketCount"] = stats_.substreams[ssrc_].rtp_stats.transmitted.packets;
    }
    // jinfo("send stats: %s", j.dump().c_str());
    return j.dump();
}

bool RtcRtpVideoFrameDestination2::resetSendStream()
{
    RTC_DCHECK_RUN_ON(taskQueue_.get());
    if (sendStream_) {
        RTC_DLOG(LS_INFO) << "Destroy videoSendStream_ with SSRC: " << ssrc_;
        call_->DestroyVideoSendStream(sendStream_);
        sendStream_ = nullptr;
    }
    webrtc::VideoSendStream::Config send_config(this);
    send_config.encoder_settings.encoder_factory = videoEncoderFactory_.get();
    send_config.encoder_settings.bitrate_allocator_factory = video_bitrate_allocator_factory_.get();
    send_config.rtp.payload_type = payload_type_;
    send_config.rtp.payload_name = (fmt_ == FRAME_FORMAT_H264 ? cricket::kH264CodecName : cricket::kH265CodecName);
    send_config.rtp.ssrcs.push_back(ssrc_);

    webrtc::VideoEncoderConfig encoder_config;
    encoder_config.content_type = webrtc::VideoEncoderConfig::ContentType::kRealtimeVideo;
    encoder_config.codec_type = webrtc::PayloadStringToCodecType(send_config.rtp.payload_name);
    if (send_config.rtp.payload_name == cricket::kVp8CodecName) {
        webrtc::VideoCodecVP8 settings = webrtc::VideoEncoder::GetDefaultVp8Settings();
        encoder_config.encoder_specific_settings = new rtc::RefCountedObject<webrtc::VideoEncoderConfig::Vp8EncoderSpecificSettings>(settings);
    } else if (send_config.rtp.payload_name == cricket::kVp9CodecName) {
        webrtc::VideoCodecVP9 settings = webrtc::VideoEncoder::GetDefaultVp9Settings();
        encoder_config.encoder_specific_settings = new rtc::RefCountedObject<webrtc::VideoEncoderConfig::Vp9EncoderSpecificSettings>(settings);
    } else if (send_config.rtp.payload_name == cricket::kH264CodecName) {
        webrtc::VideoCodecH264 settings = webrtc::VideoEncoder::GetDefaultH264Settings();
        encoder_config.encoder_specific_settings = new rtc::RefCountedObject<webrtc::VideoEncoderConfig::H264EncoderSpecificSettings>(settings);
    } else if (send_config.rtp.payload_name == cricket::kH265CodecName) {
        webrtc::VideoCodecH265 settings = webrtc::VideoEncoder::GetDefaultH265Settings();
        encoder_config.encoder_specific_settings = new rtc::RefCountedObject<webrtc::VideoEncoderConfig::H265EncoderSpecificSettings>(settings);
    }

    encoder_config.video_format.name = send_config.rtp.payload_name;
    encoder_config.min_transmit_bitrate_bps = 0;
    encoder_config.max_bitrate_bps = maxBitrate_;
    encoder_config.content_type = webrtc::VideoEncoderConfig::ContentType::kRealtimeVideo;
    encoder_config.number_of_streams = send_config.rtp.ssrcs.size();
    encoder_config.video_stream_factory = new rtc::RefCountedObject<cricket::EncoderStreamFactory>(
        send_config.rtp.payload_name, /*max qp*/ 56, /*screencast*/ false,
        /*screenshare enabled*/ false);
    // Configure the simulcast layers.
    encoder_config.number_of_streams = send_config.rtp.ssrcs.size();
    encoder_config.bitrate_priority = 1.0;
    encoder_config.simulcast_layers.resize(encoder_config.number_of_streams);
    for (size_t i = 0; i < encoder_config.number_of_streams; ++i) {
        encoder_config.simulcast_layers[i].active = true;
        encoder_config.simulcast_layers[i].min_bitrate_bps = minBitrate_;
        encoder_config.simulcast_layers[i].max_bitrate_bps = maxBitrate_;
        encoder_config.simulcast_layers[i].max_framerate = 60;
    }
    sendStream_ = call_->CreateVideoSendStream(std::move(send_config), std::move(encoder_config));
    if (sendStream_) {
        jinfo("Created video send stream success.");
        sendStream_->Start();
    } else {
        jerror("Failed to create video send stream.");
    }
    return false;
}
} // namespace panocom
