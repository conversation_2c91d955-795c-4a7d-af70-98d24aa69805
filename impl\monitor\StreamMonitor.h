#ifndef IMPL_MONITOR_STREAMMONITOR_H_
#define IMPL_MONITOR_STREAMMONITOR_H_
#include <hv/EventLoopThread.h>
#include "FramePipeline.h"

namespace panocom
{
class StreamMonitor : public FramePipeline
{
public:
    StreamMonitor(const std::string &jsonParams);
    virtual ~StreamMonitor();
    // 用于断流检测
    void setFrameTimeoutNotify(int ms, void* param, const TimeoutNotifyCallback& cb) override;
    // 用于双流切换
    void setFrameTimeoutNotify2(int ms, void* param, const TimeoutNotifyCallback& cb) override;
    int updateParam(const std::string& jsonParam) override;
private:
    hv::EventLoopThread check_timeout_thread_;
    bool is_master_;    // 
};    
} // namespace panocom

#endif