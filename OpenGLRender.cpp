#include "OpenGLRender.h"
#ifdef USE_CUDA
#include "cuda/CuOpenGLRender.h"
#endif

using namespace panocom;

std::shared_ptr<OpenGLRender> OpenGLRender::CreateOpenGLRender(const std::string& name, const std::string& jsonParam)
{
    std::shared_ptr<OpenGLRender> ret;
#ifdef USE_CUDA
    if (name == "CUDA")
    {
        ret = std::shared_ptr<OpenGLRender>(new CuOpenGLRender(jsonParam));
    }
#endif
    return ret;
}