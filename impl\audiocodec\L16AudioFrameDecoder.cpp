#include "L16AudioFrameDecoder.h"
#include <json.hpp>

namespace panocom
{
L16AudioFrameDecoder::L16AudioFrameDecoder(const std::string &jsonParams)
    : samplerate_(16000)
    , num_channels_(1) {
    name_ = "L16AudioFrameDecoder";
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    if (j.contains("samplerate")) {
        samplerate_ = j["samplerate"];
    }
    if (j.contains("channel")) {
        num_channels_ = j["channel"];
    }
    out_fmt_ = Frame::getPCMFormat(num_channels_, samplerate_);
}

L16AudioFrameDecoder::~L16AudioFrameDecoder()
{
}

void L16AudioFrameDecoder::onFrame(const std::shared_ptr<Frame> &frame) {
    int samplerate = 0;
    int chn = 0;
    auto f = frame;
    if (Frame::getSamplerate(f->getFrameFormat(), samplerate, chn)) {
        if (num_channels_ != chn) {
            auto fmt = Frame::getPCMFormat(num_channels_, samplerate);
            auto frame = Frame::CreateFrame(fmt);
            int size = f->getFrameSize() * num_channels_ / chn;
            frame->createFrameBuffer(f->getFrameSize() * num_channels_ / chn); 
            if (num_channels_ == 1 && chn == 2)
                stereo_to_mono((int16_t *)f->getFrameBuffer(), (int16_t *)frame->getFrameBuffer(), size / 2);
            if (num_channels_ == 2 && chn == 1)
                mono_to_stereo((int16_t *)f->getFrameBuffer(), (int16_t *)frame->getFrameBuffer(), size / 2);
            f = frame;
        }
        if (samplerate_ != samplerate) {
            if (!initResampler_ || samplerate != source_samplerate_) {
                source_samplerate_ = samplerate;
                initResampler_ = true;
#ifdef WEBRTC_RESAMPLE_ANOTHER
                resampler_.InitializeIfNeeded(samplerate, samplerate_, 1);
#else
                resampler_.ResetIfNeeded(samplerate, samplerate_, 1);
#endif
                jinfo("L16AudioFrameDecoder resample %d -> %d", samplerate, samplerate_);
            }
            auto frame = Frame::CreateFrame(out_fmt_);
            frame->createFrameBuffer(f->getFrameSize() * samplerate_ / samplerate * num_channels_);
            memset(frame->getFrameBuffer(), 0, frame->getFrameSize());
#ifdef WEBRTC_RESAMPLE_ANOTHER
            resampler_.Resample((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2);
#else
            size_t outlen = 0;
            resampler_.Push((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2, outlen);
#endif
            f = frame;
        }
    }   
    deliverFrame(f); 
}
} // namespace panocom