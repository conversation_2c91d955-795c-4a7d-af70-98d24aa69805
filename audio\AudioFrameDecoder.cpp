#include "AudioFrameDecoder.h"
#include "audiocodec/OpusAudioFrameDecoder.h"
#include "audiocodec/G711AudioFrameDecoder.h"
#include "audiocodec/G722AudioFrameDecoder.h"
#include "audiocodec/G7221AudioFrameDecoder.h"
#include "audiocodec/G729AudioFrameDecoder.h"
#include "audiocodec/AACAudioFrameDecoder.h"
#include "audiocodec/L16AudioFrameDecoder.h"

#if defined(USE_WEBRTC) || defined(USE_NETEQ)
#include "webrtc/neteq/NetEqAudioDecoder.h"
#endif
#include "audiocodec/AudioFrameDecoderWrapper.h"
#include <algorithm>
#include <json.hpp>

using namespace panocom;

std::list<std::string> AudioFrameDecoder::decoderTypes_;
std::list<std::string> AudioFrameDecoder::codecs_;

bool AudioFrameDecoder::isRegistered = false;

void AudioFrameDecoder::Regist()
{
    if (!isRegistered)
    {
        decoderTypes_.push_back("native");
        decoderTypes_.push_back("NetEqAudioDecoder");
        decoderTypes_.push_back("AudioFrameDecoderWrapper");
        codecs_.push_back("opus");
        codecs_.push_back("PCMA");
        codecs_.push_back("PCMU");
		codecs_.push_back("G722");
		codecs_.push_back("G729");
		codecs_.push_back("AAC");
        codecs_.push_back("MP4A-LATM");
        codecs_.push_back("L16");
        isRegistered = true;
    }
}

std::list<std::string> &AudioFrameDecoder::GetSupportDecoderTypes()
{
    Regist();
    return decoderTypes_;
}

bool AudioFrameDecoder::IsSupportDecoderType(const std::string &decoderType)
{
    Regist();
    return std::find(decoderTypes_.begin(), decoderTypes_.end(), decoderType) != decoderTypes_.end();
}

std::shared_ptr<AudioFrameDecoder> AudioFrameDecoder::CreateAudioDecoder(const std::string &decoderType, const std::string &jsonParams)
{
    Regist();
    std::shared_ptr<AudioFrameDecoder> ret;
    if (decoderType == "native")
    {
        nlohmann::json j;
        if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
        std::string codecName = "opus";
        if (j.contains("codec")) {
            codecName = j["codec"];
        }
        if (strcasecmp(codecName.c_str(), "opus") == 0) {
            ret = std::make_shared<OpusAudioFrameDecoder>(jsonParams);
        } else if (strcasecmp(codecName.c_str(), "g711") == 0 || strcasecmp(codecName.c_str(), "PCMA") == 0 || strcasecmp(codecName.c_str(), "PCMU") == 0) {
            if (strcasecmp(codecName.c_str(), "PCMU") == 0) {
                j["ulaw"] = true;
            }
            ret = std::make_shared<G711AudioFrameDecoder>(j.dump());
        } else if (strcasecmp(codecName.c_str(), "g722") == 0) {
            ret = std::make_shared<G722AudioFrameDecoder>(jsonParams);
        } else if (strcasecmp(codecName.c_str(), "g7221") == 0) {
            ret = std::make_shared<G7221AudioFrameDecoder>(jsonParams);
        } else if (strcasecmp(codecName.c_str(), "g729") == 0) {
            ret = std::make_shared<G729AudioFrameDecoder>(jsonParams);
        } else if (strcasecmp(codecName.c_str(), "aac") == 0 || strcasecmp(codecName.c_str(), "MP4A-LATM") == 0) {
            ret = std::make_shared<AACAudioFrameDecoder>(jsonParams);
        } else if (strcasecmp(codecName.c_str(), "l16") == 0) {
            ret = std::make_shared<L16AudioFrameDecoder>(jsonParams);
        }
    }
#if defined(USE_WEBRTC) || defined(USE_NETEQ)
    else if (decoderType == "NetEqAudioDecoder")
    {
        ret = std::make_shared<NetEqAudioDecoder>(jsonParams);
    }
#endif
    else if (decoderType == "AudioFrameDecoderWrapper") {
        ret = std::make_shared<AudioFrameDecoderWrapper>(jsonParams);
    }
    return ret;
}

std::list<std::string> &AudioFrameDecoder::GetSupportCodecs()
{
    Regist();
    return codecs_;
}

bool AudioFrameDecoder::IsSupportCodec(const std::string &codec)
{
    Regist();
    return std::find(codecs_.begin(), codecs_.end(), codec) != codecs_.end();
}

bool AudioFrameDecoder::IsAudioFrameDecoder(const FramePipeline::Ptr &ptr) {
    if (ptr->name() == "OpusAudioFrameDecoder") {
        return true;
    } else if (ptr->name() == "G711AudioFrameDecoder") {
        return true;
    } else if (ptr->name() == "G722AudioFrameDecoder") {
        return true;
    } else if (ptr->name() == "G7221AudioFrameDecoder") {
        return true;
    } else if (ptr->name() == "G729AudioFrameDecoder") {
        return true;
    } else if (ptr->name() == "AACAudioFrameDecoder") {
        return true;
    } else if (ptr->name() == "L16AudioFrameDecoder") {
        return true;
    } 
#if defined(USE_WEBRTC) || defined(USE_NETEQ)
    else if (ptr->name() == "NetEqAudioDecoder") {
        return true;
    }
#endif
    else if (ptr->name() == "AudioFrameDecoderWrapper") {
        return true;
    }
    return false;
}