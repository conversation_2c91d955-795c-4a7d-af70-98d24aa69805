#include "RKVideoFrameMixer.h"
#include <json.hpp>

#include "rga/im2d.hpp"
#include "rockchip/mpp_frame.h"
#include <hv/hlog.h>
#include <rga/rga.h>
namespace panocom {
// TODO: DRY
std::unordered_map<std::string, RgaSURF_FORMAT> tab_s_rgapixel {
	{"nv12", RK_FORMAT_YCbCr_420_SP},
	{"nv16", RK_FORMAT_YCbCr_422_SP}
};

static bool CopyFrom(MppFrame output, MppFrame input, int32_t x, int32_t y, int32_t width, int32_t height, const std::string &format_str="nv12") {
	MppBuffer srcbuffer = mpp_frame_get_buffer(input);
	if (!srcbuffer) return false;
	MppBuffer dstbuffer = mpp_frame_get_buffer(output);
	if (!dstbuffer) return false;
    RK_U32 src_width = mpp_frame_get_width(input);
    RK_U32 src_height = mpp_frame_get_height(input);
    RK_U32 src_hor_stride = mpp_frame_get_hor_stride(input);
    RK_U32 src_ver_stride = mpp_frame_get_ver_stride(input);

    RK_U32 dst_width = mpp_frame_get_width(output);
    RK_U32 dst_height = mpp_frame_get_height(output);
    RK_U32 dst_hor_stride = mpp_frame_get_hor_stride(output);
    RK_U32 dst_ver_stride = mpp_frame_get_ver_stride(output);

    im_rect src_rect;
    im_rect dst_rect;
    rga_buffer_t src;
    rga_buffer_t dst;
    // rga_buffer_handle_t src_handle;
    // rga_buffer_handle_t dst_handle;

    int src_fd = mpp_buffer_get_fd(srcbuffer);
    int dst_fd = mpp_buffer_get_fd(dstbuffer);

    // im_handle_param_t src_param = {(uint32_t)src_width, (uint32_t)src_height, (uint32_t)RK_FORMAT_YCbCr_420_SP};
    // im_handle_param_t dst_param = {(uint32_t)dst_width, (uint32_t)dst_height, (uint32_t)RK_FORMAT_YCbCr_420_SP};

    // src_handle = importbuffer_fd(src_fd, &src_param);
    // if (src_handle <= 0) {
    //     LOGE("Failed to importbuffer_fd for src channel!\n");
    //     return false;
    // }
    // dst_handle = importbuffer_fd(dst_fd, &dst_param);
    // if (dst_handle <= 0) {
    //     LOGE("Failed to importbuffer_fd for dst channel!\n");
    //     releasebuffer_handle(src_handle);
    //     return false;
    // }
	auto format = RK_FORMAT_YCbCr_420_SP;
	if (tab_s_rgapixel.count(format_str) != 0) format = tab_s_rgapixel[format_str];
    src = wrapbuffer_fd(src_fd, src_width, src_height, format, src_hor_stride, src_ver_stride);
    dst = wrapbuffer_fd(dst_fd, dst_width, dst_height, format, dst_hor_stride, dst_ver_stride);

    int usage = 0;
    IM_STATUS ret = IM_STATUS_NOERROR;

    im_opt_t opt { 0 };
    rga_buffer_t pat { 0 };
    im_rect srect { 0 };
    im_rect drect { 0 };
    im_rect prect { 0 };

    // empty_structure(NULL, NULL, &pat, &srect, &drect, &prect, &opt);

    usage |= IM_SYNC;

    srect.width = src_width & ~1;
    srect.height = src_height & ~1;
    drect.x = x & ~1;
    drect.y = y & ~1;
    drect.width = width & ~1;
    drect.height = height & ~1;

    LOGD("CopyFrom size(%d x %d)(%d x %d) to pos(%d, %d) size(%d x %d)(%d x %d)", srect.width, srect.height, src_hor_stride, src_ver_stride, drect.x, drect.y, drect.width, drect.height, dst_hor_stride, dst_ver_stride);

    ret = improcess(src, dst, pat, srect, drect, prect, -1, NULL, &opt, usage);

    // releasebuffer_handle(src_handle);
    // releasebuffer_handle(dst_handle);
    mpp_buffer_put(srcbuffer);
    mpp_buffer_put(dstbuffer);
    if (ret == IM_STATUS_SUCCESS)
        return true;
    jerror("MIXER: Failed to CopyFrom size(%d x %d)(%d x %d) to pos(%d, %d) size(%d x %d)(%d x %d)", srect.width, srect.height, src_hor_stride, src_ver_stride, drect.x, drect.y, drect.width, drect.height, dst_hor_stride, dst_ver_stride);
	jerror("ret = %d", ret);
    return false;
}

static bool SetBlack(MppFrame output, int32_t x, int32_t y, uint32_t width, uint32_t height, int color=0, const std::string &format_str="nv12") {
    IM_STATUS ret = IM_STATUS_NOERROR;
	MppBuffer dstbuffer = mpp_frame_get_buffer(output);
	if (!dstbuffer) return false;

    RK_U32 dst_width = mpp_frame_get_width(output);
    RK_U32 dst_height = mpp_frame_get_height(output);
    RK_U32 dst_hor_stride = mpp_frame_get_hor_stride(output);
    RK_U32 dst_ver_stride = mpp_frame_get_ver_stride(output);

    width = (width > dst_width ? dst_width : width);
    height = (height > dst_height ? dst_height : height);

    rga_buffer_t dst;

    int dst_fd = mpp_buffer_get_fd(dstbuffer);

	auto format = RK_FORMAT_YCbCr_420_SP;
	if (tab_s_rgapixel.count(format_str) != 0) format = tab_s_rgapixel[format_str];
    dst = wrapbuffer_fd(dst_fd, dst_width, dst_height, format, dst_hor_stride, dst_ver_stride);

    im_rect drect { 0 };
    drect.x = x & ~1;
    drect.y = y & ~1;
    drect.width = width & ~1;
    drect.height = height & ~1;

    ret = imcheck({}, dst, {}, drect, IM_COLOR_FILL);
    if (IM_STATUS_NOERROR != ret) {
        jerror("%d, check error! %s", __LINE__, imStrError((IM_STATUS)ret));
        return false;
    }

    ret = imfill(dst, drect, color);
    mpp_buffer_put(dstbuffer);
    if (ret == IM_STATUS_SUCCESS)
        return true;
    jerror("MIXER: Failed to SetBlack pos(%d, %d) size(%d x %d)(%d x %d)",  drect.x, drect.y, drect.width, drect.height, dst_hor_stride, dst_ver_stride);
	jerror("ret = %d", ret);
    return false;
}

RKVideoFrameMixer::RKVideoFrameMixer(const std::string &jsonParams)
    : running_(false), format_str_("nv12"), timeoutMs_(100) {
    name_ = "RKVideoFrameMixer";
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    width_ = 1920;
    if (j.contains("width")) {
        width_ = j["width"];
    }
    height_ = 1080;
    if (j.contains("height")) {
        height_ = j["height"];
    }
    int fps = 25;
    if (j.contains("fps")) {
        fps = j["fps"];
        fps_ = j["fps"];
    }
    if (fps_ <= 0) fps_ = 30;
    if (j.contains("format")) format_str_ = j["format"];
    outputGap_ = 1000 / fps;
	output_time_ = std::chrono::steady_clock::now();
    if (j.contains("videoLayout") && j["videoLayout"].is_array())
    {
        for (int i = 0; i < j["videoLayout"].size(); ++i)
        {
            if (j["videoLayout"][i].contains("id") &&
                j["videoLayout"][i].contains("rect") &&
                j["videoLayout"][i]["rect"].contains("x") &&
                j["videoLayout"][i]["rect"].contains("y") &&
                j["videoLayout"][i]["rect"].contains("w") &&
                j["videoLayout"][i]["rect"].contains("h"))
            {
                Region r;
                r.id = j["videoLayout"][i]["id"];
                r.rect.x = j["videoLayout"][i]["rect"]["x"];
                r.rect.y = j["videoLayout"][i]["rect"]["y"];
                r.rect.w = j["videoLayout"][i]["rect"]["w"];
                r.rect.h = j["videoLayout"][i]["rect"]["h"];
                // jinfo("video layout id = %d x = %d y= %d w = %d h = %d", r.id, r.rect.x, r.rect.y, r.rect.w, r.rect.h);
                video_layout_.push_back(r);
            }
        }
    }
	frameBufferManager_ = std::make_shared<RKBufferManager>(4/*, width_, height_*/);
    addInput(0, { 0, 0, width_ / 2, height_ / 2 });
    start();
}

RKVideoFrameMixer::~RKVideoFrameMixer() {
    jinfo("~RKVideoFrameMixer begin");
    stop();
    jinfo("~RKVideoFrameMixer end");
}

void RKVideoFrameMixer::onFrame(const std::shared_ptr<Frame> &frame) {
    if (running_ && frame && frame->getFrameFormat() == FRAME_FORMAT_RK) {
        int id = frame->getGroupId();
        auto &channel = input_channels_[id];
        std::lock_guard<std::mutex> lock(channel.mtx);
        if (channel.cache.size() >= 3) {
            channel.cache.pop_front();
        }
        channel.cache.push_back({frame, std::chrono::steady_clock::now()});
    }
}

void RKVideoFrameMixer::generateOutputFrame(uint32_t width, uint32_t height) {

	MppFrame mpp_frame = frameBufferManager_->getFreeFrame(width, height);
	// auto buffer = mpp_frame_get_buffer(mpp_frame);
	// memset(mpp_buffer_get_ptr(buffer), 0, mpp_buffer_get_size(buffer));
    if (mpp_frame) {
        output_frame_ = std::make_shared<WrapRKMppFrame>(mpp_frame);
        output_frame_->setGroupId(getGroupId());
    }

}

std::shared_ptr<Frame> RKVideoFrameMixer::getNextValidFrame(int inputId) {
    if (input_channels_.count(inputId) == 0) return nullptr;
    
    auto &channel = input_channels_[inputId];
    std::lock_guard<std::mutex> lock(channel.mtx);
    auto &cache = channel.cache;
    auto now = std::chrono::steady_clock::now();

    while (!cache.empty()) {
        auto &front = cache.front();
        auto frame = front.frame;
        input_channels_[inputId].last_frame = frame;
        int ageMs = std::chrono::duration_cast<std::chrono::milliseconds>(now - front.timestamp).count();
        cache.pop_front();
        if (ageMs <= timeoutMs_) {
            return frame;
        }
    }
    // return nullptr;
    return input_channels_[inputId].last_frame;
}

void RKVideoFrameMixer::addInput(int inputId, const LayoutRect& layout) {
    std::lock_guard<std::mutex> lock(layout_mtx_);
    layout_map_[inputId] = layout;
}

void RKVideoFrameMixer::removeInput(int inputId) {
    {
        std::lock_guard<std::mutex> lock(layout_mtx_);
        if (layout_map_.count(inputId) != 0)
            layout_map_.erase(inputId);
    }
    if (input_channels_.erase(inputId) != 0)
        input_channels_.erase(inputId);
}

void RKVideoFrameMixer::updateLayout(int inputId, const LayoutRect& layout) {
    std::lock_guard<std::mutex> lock(layout_mtx_);
    if (layout_map_.count(inputId) != 0)
        layout_map_[inputId] = layout;
}

void RKVideoFrameMixer::renderLoop() {
    int timeout_ms = 1000 / fps_;
    while (running_)
    {
        {
            std::unique_lock<std::mutex> lock(render_mtx_);
            render_cv_.wait_for(lock, std::chrono::milliseconds(timeout_ms));
        }
        doCompose();
    }
    
}

void RKVideoFrameMixer::doCompose() {
    std::unordered_map<int, LayoutRect> current_layout;
    {
        std::lock_guard<std::mutex> lock(layout_mtx_);
        current_layout = layout_map_;
    }
    std::shared_ptr<WrapRKMppFrame> wrap_frame;
    for (const auto& kv: current_layout) {
        auto frame = getNextValidFrame(kv.first);
        if (!frame) continue;
        if (!output_frame_) {
            generateOutputFrame(width_, height_);    
            // SetBlack(output_frame_->InnerFrame(), 0, 0, width_, height_);        
        }

        if (frame->getFrameFormat() == FRAME_FORMAT_RK) {
            wrap_frame = std::dynamic_pointer_cast<WrapRKMppFrame>(frame);
        }
        bool res = CopyFrom(output_frame_->InnerFrame(), wrap_frame->InnerFrame(), kv.second.x, kv.second.y, kv.second.w, kv.second.h, format_str_);
        if (!res)
            jerror("failed to copy frame");
    }
    if (output_frame_) {
        deliverFrame(output_frame_);
        output_frame_.reset();
    }
}

void RKVideoFrameMixer::start() {
    running_ = true;
    int timeout_ms = 1000 / fps_;
    render_thread_.loop()->setInterval(timeout_ms, [this](hv::TimerID id) {
        if (running_) {
            doCompose();
        }
    });
    if (!render_thread_.isRunning()) render_thread_.start(true);
    render_thread_.loop()->runInLoop([this]() {
        /* Configure the current thread to use only RGA3_core0 or RGA3_core1. */
        // imconfig(IM_CONFIG_SCHEDULER_CORE, IM_SCHEDULER_RGA3_CORE0 | IM_SCHEDULER_RGA3_CORE1);
    });
    // render_future_ = std::async([this]() {
    //     renderLoop();
    //     return true;
    // });
}
void RKVideoFrameMixer::stop() {
    running_ = false;
    if (render_thread_.isRunning()) {
        render_thread_.stop(true);
        render_thread_.join();
    }
    // render_cv_.notify_all();
    // if (render_future_.valid()) {
    //     auto status = render_future_.wait_for(std::chrono::milliseconds(500));
    //     if (status == std::future_status::timeout) {
    //         jerror("RKVideoFrameMixer stop timeout");
    //     } else {
    //         bool res = render_future_.get();
    //         jinfo("RKVideoFrameMixer stop status: %d", res);
    //     }
    // }
}
} // namespace panocom