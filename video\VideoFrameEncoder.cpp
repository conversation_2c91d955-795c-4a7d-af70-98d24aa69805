#include "VideoFrameEncoder.h"
#ifdef USE_FFMPEG
#include "ffmpeg/FfVideoEncoder.h"
#endif
#ifdef USE_CUDA
#include "cuda/CuVideoEncoder.h"
#endif

#include <algorithm>
#ifdef RK_PLATFORM
#include "rk/rk_video_encoder.h"
#endif

using namespace panocom;

std::list<std::string> VideoFrameEncoder::encoderTypes_;
std::list<CodecInst> VideoFrameEncoder::codecs_;

bool VideoFrameEncoder::isRegistered = false;

void VideoFrameEncoder::Regist()
{
    if (!isRegistered)
    {
#ifdef USE_FFMPEG
        encoderTypes_.push_back("FfVideoEncoder");
#endif
#ifdef USE_CUDA
        encoderTypes_.push_back("CuVideoEncoder");
#endif
#ifdef RK_PLATFORM
        encoderTypes_.push_back("RKVideoEncoder");
#endif
        getCodecInst("h264", codecs_);
        getCodecInst("h265", codecs_);
        isRegistered = true;
    }
}

std::list<std::string> &VideoFrameEncoder::GetSupportEncoderTypes()
{
    Regist();
    return encoderTypes_;
}

bool VideoFrameEncoder::IsSupportEncoderType(const std::string &encoderType)
{
    Regist();
    return std::find(encoderTypes_.begin(), encoderTypes_.end(), encoderType) != encoderTypes_.end();
}

std::shared_ptr<VideoFrameEncoder> VideoFrameEncoder::CreateVideoEncoder(const std::string &encoderType, const std::string &jsonParams)
{
    Regist();
    std::shared_ptr<VideoFrameEncoder> ret;
#ifdef USE_FFMPEG
    if (encoderType == "FfVideoEncoder")
    {
        ret = std::make_shared<FfVideoEncoder>(jsonParams);
    }
#endif
#ifdef USE_CUDA
    if (encoderType == "CuVideoEncoder")
    {
        ret = std::make_shared<CuVideoEncoder>(jsonParams);
    }
#endif
#ifdef RK_PLATFORM
    if (encoderType == "RKVideoEncoder") 
    {
        ret = std::make_shared<RKVideoEncoder>(jsonParams);
    }
#endif
    return ret;
}

std::list<CodecInst> &VideoFrameEncoder::GetSupportCodecs()
{
    Regist();
    return codecs_;
}

bool VideoFrameEncoder::IsSupportCodec(const CodecInst &codec)
{
    Regist();
    return std::find(codecs_.begin(), codecs_.end(), codec) != codecs_.end();
}

bool VideoFrameEncoder::IsVideoFrameEncoder(const FramePipeline::Ptr& ptr)
{
#ifdef USE_FFMPEG
    if (ptr->name() == "FfVideoEncoder")
    {
        return true;
    }
#endif
#ifdef USE_CUDA
    if (ptr->name() == "CuVideoEncoder")
    {
        return true;
    }
#endif
#ifdef RK_PLATFORM
    if (ptr->name() == "RKVideoEncoder") 
    {
        return true;
    }
#endif
    return false;
}
