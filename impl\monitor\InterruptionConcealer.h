#ifndef IMPL_MONITOR_INTERRUPTIONCONCEALER_H_
#define IMPL_MONITOR_INTERRUPTIONCONCEALER_H_
#include <hv/EventLoopThread.h>
#include "FramePipeline.h"
#include "Frame.h"

namespace panocom
{
class InterruptionConcealer : public FramePipeline
{
private:
    /* data */
public:
    InterruptionConcealer(const std::string &jsonParams);
    virtual ~InterruptionConcealer();
    void onFrame(const std::shared_ptr<Frame> &frame) override;
    void start() override;
private:
    hv::EventLoopThread check_timeout_thread_;
    int ptime_;
    int timeoutMS_;
    bool send_flag_;
    int len_;
    FrameFormat fmt_;
};
    
} // namespace panocom

#endif