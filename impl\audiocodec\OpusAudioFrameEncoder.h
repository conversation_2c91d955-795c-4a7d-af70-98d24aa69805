// created by gyj 2024-3-28
#ifndef P_OpusAudioFrameEncoder_h
#define P_OpusAudioFrameEncoder_h

#include "AudioFrameEncoder.h"
#include <opus/opus.h>
#include <hv/EventLoopThread.h>
#include <resampler/resampler.h>
#include <resampler/push_resampler.h>

namespace panocom
{
    class OpusAudioFrameEncoder : public AudioFrameEncoder
    {
    public:
        OpusAudioFrameEncoder(const std::string& jsonParams);
        ~OpusAudioFrameEncoder() override;

        void onFrame(const std::shared_ptr<Frame> &f) override;
        void start() override;
        void stop() override;
    private:
        std::shared_ptr<OpusEncoder> encoder_;
        std::shared_ptr<OpusDecoder> decoder_;
        std::vector<uint8_t> buffer_;
        hv::EventLoopThread thread_;
        int samplerate_ = 0;
        int chn_ = 0;
        int fmt_ = 0;
        FILE* pf_ = nullptr;
        FILE* pfDecode_ = nullptr;
        bool first_ = true;
#ifdef WEBRTC_RESAMPLE_ANOTHER
        nswebrtc::PushResampler<int16_t> resampler_;
#else
        nswebrtc::Resampler resampler_;
#endif       
        bool initResampler_ = false;
        int source_samplerate_;
    };
}

#endif