#include "MediaPipeline.h"
#include <json.hpp>
#include <stdio.h>
#include <ljcore/jlog.h>
#include <hv/EventLoop.h>

using namespace panocom;

int main()
{
    auto loop = std::make_shared<hv::EventLoop>();
    jlog_init(nullptr);
    nlohmann::json j;
    auto capturer = VideoFrameCapturer::CreateVideoCapturer("V4L2VideoFrameCapturer", j.dump());
    capturer->setGroupId(1);
    j.clear();
    j["codec"] = "mjpeg_cuvid";
    auto decoder = VideoFrameDecoder::CreateVideoDecoder("FfVideoDecoder", j.dump());
    decoder->setGroupId(1);
    j.clear();
    j["videoLayout"] = nlohmann::json::array();
    nlohmann::json r;

    r["id"] = 1;
    r["rect"]["x"] = 0;
    r["rect"]["y"] = 0;
    r["rect"]["w"] = 1280 / 2;
    r["rect"]["h"] = 720 / 2;
    j["videoLayout"].push_back(r);

    r["id"] = 1;
    r["rect"]["x"] = 0;
    r["rect"]["y"] = 720 / 2;
    r["rect"]["w"] = 1280 / 2;
    r["rect"]["h"] = 720 / 2;
    j["videoLayout"].push_back(r);

    r["id"] = 1;
    r["rect"]["x"] = 1280 / 2;
    r["rect"]["y"] = 0;
    r["rect"]["w"] = 1280 / 2;
    r["rect"]["h"] = 720 / 2;
    j["videoLayout"].push_back(r);

    r["id"] = 1;
    r["rect"]["x"] = 1280 / 2;
    r["rect"]["y"] = 720 / 2;
    r["rect"]["w"] = 1280 / 2;
    r["rect"]["h"] = 720 / 2;
    j["videoLayout"].push_back(r);

    auto render = VideoFrameRender::CreateVideoRender("Sdl2VideoFrameRender", j.dump());
    capturer->addVideoDestination(decoder);
    decoder->addVideoDestination(render);
    loop->run();
    return 0;
}