#ifndef IMPL_JRTP_CPING_H_
#define IMPL_JRTP_CPING_H_

// #include <windows.h>
#include <unistd.h>
#include <stdint.h>
#include <stdlib.h>
#include <string.h>
#include <sys/socket.h>  
#include <sys/types.h>  
#include <sys/time.h>  
#include <arpa/inet.h>  
#include <netinet/ip_icmp.h>  
#include <netdb.h>  
#include <errno.h>

#include <unordered_map>
#include <list>
#include <future>

#define DEF_PACKET_SIZE 32
#define ECHO_REQUEST 8
#define ECHO_REPLY 0

struct IPHeader
{
    uint8_t m_byVerHLen; //4位版本+4位首部长度
    uint8_t m_byTOS; //服务类型
    uint16_t m_usTotalLen; //总长度
    uint16_t m_usID; //标识
    uint16_t m_usFlagFragOffset; //3位标志+13位片偏移
    uint8_t m_byTTL; //TTL
    uint8_t m_byProtocol; //协议
    uint16_t m_usHChecksum; //首部检验和
    uint32_t m_ulSrcIP; //源IP地址
    uint32_t m_ulDestIP; //目的IP地址
};

struct ICMPHeader
{
    uint8_t m_byType; //类型
    uint8_t m_byCode; //代码
    uint16_t m_usChecksum; //检验和
    uint16_t m_usID; //标识符
    uint16_t m_usSeq; //序号
    uint64_t m_ulTimeStamp; //时间戳（非标准ICMP头部）
};

struct PingReply
{
    uint16_t m_usSeq;
    double m_dwRoundTripTime;
    uint64_t m_dwBytes;
    uint64_t m_dwTTL;
    PingReply() : m_usSeq(0), m_dwRoundTripTime(0.0), m_dwBytes(0), m_dwTTL(0) {}
};

class CPing
{
public:
    static CPing &instance();

    void AddServer(const std::string &dst_ip);
    void RemoveServer(const std::string &dst_ip);
    PingReply GetReply(const std::string &dst_ip);
    void StartPingServers();
private:
    CPing();
    ~CPing();
    bool Ping(const uint64_t dwDestIP, PingReply *pPingReply = NULL, uint64_t dwTimeout = 1000);
    bool Ping(const char *szDestIP, PingReply *pPingReply = NULL, uint64_t dwTimeout = 1000);
    //创建关闭socket  
    bool CreateSocket();    
    bool CloseSocket();   
    bool PingCore(uint64_t dwDestIP, PingReply *pPingReply, uint64_t dwTimeout);
    uint16_t CalCheckSum(uint16_t *pBuffer, int nSize);
    uint64_t GetTickCountCalibrate();

    void PingLoop();
private:
    int m_nSocketfd; //套接字
    struct sockaddr_in m_dest_addr;     //用来保存目标IP的套接字地址  
    struct sockaddr_in m_from_addr;     //用来保存来自目标IP的套接字地址  
    // WSAEVENT m_event;
    uint16_t m_usCurrentProcID;
    bool m_bIsInitSucc;
    int32_t m_nMaxTimeWait;
    char *m_szICMPData;
private:
    uint16_t s_usPacketSeq;

    std::unordered_map<std::string, PingReply> server_replies_;
    std::unordered_map<std::string, int> server_refs_;
    std::future<bool> receive_future_;
    std::mutex server_mutex_;
    bool loop_running_;

    std::future<bool> send_future_;
};

#endif