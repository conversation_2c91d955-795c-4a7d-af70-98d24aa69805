#ifndef P_AudioFrameRender_h
#define P_AudioFrameRender_h

#include "../FramePipeline.h"

namespace panocom
{
    class AudioFrameRender : public FramePipeline
    {
    public:
        static void RegistRenders();
        static std::vector<std::string>& GetSupportRenders();
        static bool IsSupportRender(const std::string &renderName);
        static std::shared_ptr<AudioFrameRender> CreateAudioRender(const std::string &renderName, const std::string &jsonParams = "");
        static bool isAudioFrameRender(const FramePipeline::Ptr& ptr);
        static std::string loadDefaultConfig(const std::string& name);
        
        AudioFrameRender() = default;
        AudioFrameRender(const FramePipeline::Ptr& ptr)
        { 
            proxy_ = ptr; 
            proxy_->start(); 
        }
        
        virtual ~AudioFrameRender()
        {
            if (proxy_)
            {
                proxy_->stop();
            }
        }

        std::string name() override
        {
            if (proxy_)
            {
                return proxy_->name();
            }
            return name_;
        }

        void onFrameTransfer(const std::shared_ptr<Frame> &f) override 
        { 
            if(proxy_) 
            {
                proxy_->onFrameTransfer(f);
            }
            else
            {
                return FramePipeline::onFrameTransfer(f);
            }
        }
        FramePipeline::Ptr addAudioDestination(const FramePipeline::Ptr& dst)  override 
        { 
            if(proxy_) 
            {
                return proxy_->addAudioDestination(dst);  
            }
            else
            {
                return FramePipeline::addAudioDestination(dst);
            }
        }

        FramePipeline::Ptr removeAudioDestination(const FramePipeline::Ptr& dst) override
        { 
            if(proxy_) 
            {
                return proxy_->removeAudioDestination(dst);  
            }
            else
            {
                return FramePipeline::removeAudioDestination(dst);
            }
        }

        FramePipeline::Ptr addVideoDestination(const FramePipeline::Ptr& dst) override
        { 
            if(proxy_) 
            {
                return proxy_->addVideoDestination(dst);  
            }
            else
            {
                return FramePipeline::addVideoDestination(dst);
            }
        }

        FramePipeline::Ptr removeVideoDestination(const FramePipeline::Ptr& dst) override
        { 
            if(proxy_) 
            {
                return proxy_->removeVideoDestination(dst);  
            }
            else
            {
                return FramePipeline::removeVideoDestination(dst);
            }
        }

        FramePipeline::Ptr addDataDestination(const FramePipeline::Ptr& dst) override
        { 
            if(proxy_) 
            {
                return proxy_->addDataDestination(dst);  
            }
            else
            {
                return FramePipeline::addDataDestination(dst);
            }
        }

        FramePipeline::Ptr removeDataDestination(const FramePipeline::Ptr& dst) override
        { 
            if(proxy_) 
            {
                return proxy_->removeDataDestination(dst);  
            }
            else
            {
                return FramePipeline::removeDataDestination(dst);
            }
        }
        void addAudioSource(const FramePipeline::Ptr& source) override {
            if(proxy_) 
            {
                return proxy_->addAudioSource(source);  
            }
            else
            {
                return FramePipeline::addAudioSource(source);
            }
        }
        void removeAudioSource(const FramePipeline::Ptr& source) override {
            if(proxy_) 
            {
                return proxy_->removeAudioSource(source);  
            }
            else
            {
                return FramePipeline::removeAudioSource(source);
            }
        }
        std::list<FramePipeline::WPtr> getAudioSources() {
            if(proxy_) 
            {
                return proxy_->getAudioSources();  
            }
            else
            {
                return FramePipeline::getAudioSources();
            }
        }
        std::list<FramePipeline::WPtr> getDataSources() {
            if(proxy_) 
            {
                return proxy_->getDataSources();  
            }
            else
            {
                return FramePipeline::getDataSources();
            }
        }
        int updateParam(const std::string &jsonParams) override
        {
            if(proxy_) 
            {
                return proxy_->updateParam(jsonParams);  
            }
            else
            {
                return FramePipeline::updateParam(jsonParams);
            }
        }
        bool isRunning() override {
            if (proxy_) {
                return proxy_->isRunning();
            } else {
                return FramePipeline::isRunning();
            }
        }
        void resetConnectState(bool state) override {
            if (proxy_) {
                return proxy_->resetConnectState(state);
            } else {
                return FramePipeline::resetConnectState(state);
            }            
        }

        void setFrameTimeoutNotify(int ms, void* param, const TimeoutNotifyCallback& cb) {
            if (proxy_) {
                return proxy_->setFrameTimeoutNotify(ms, param, cb);
            } else {
                return FramePipeline::setFrameTimeoutNotify(ms, param, cb);
            }            
        }

		void setFrameTimeoutNotify2(int ms, void* param, const TimeoutNotifyCallback& cb) {
			if (proxy_) {
				return proxy_->setFrameTimeoutNotify2(ms, param, cb);
			}
			else {
				return FramePipeline::setFrameTimeoutNotify2(ms, param, cb);
			}
		}
        void setNotify(const std::string &key, const std::string &jsonParam, void *param, const NotifyCallback &cb, bool use_async = true) override {
            if (proxy_) {
                return proxy_->setNotify(key, jsonParam, param, cb, use_async);
            } else {
                return FramePipeline::setNotify(key, jsonParam, param, cb, use_async);
            }
        }

    protected:
        static bool isRegistered;
        static std::vector<std::string> renders_;
        FramePipeline::Ptr proxy_;
    };
}

#endif