cmake_minimum_required(VERSION 3.10)

project(terminal)

set(CMAKE_CXX_STANDARD 14)

include_directories(${CMAKE_CURRENT_SOURCE_DIR} ${CMAKE_PREFIX_PATH}/include)
link_directories(${CMAKE_PREFIX_PATH}/lib)
link_directories(${CUDA_TOOLKIT_ROOT_DIR}/targets/x86_64-linux/lib)

add_definitions(-Wall -O3 -g -fexceptions -fpermissive)
add_executable(${PROJECT_NAME} test.cpp)
target_link_libraries(${PROJECT_NAME} PRIVATE MediaPipeline ptoolkit jrtp jthread ljcore hv_static ZLToolKit aec rnnoise opus g729 fdk-aac DTLSTool nice glib-2.0 gio-2.0 gobject-2.0 gmodule-2.0 z ffi pcre2-8 srtp2 PNPcm SDL2 ssl crypto pthread dl nppicc nppig ${CUDA_CUDA_LIBRARY} ${CUDART_LIB} ${CMAKE_DL_LIBS} ${NVENCODEAPI_LIB} ${CUVID_LIB} ${FREEGLUT_LIB} ${GLEW32_LIB} ${X11_LIB} ${GL_LIB})



