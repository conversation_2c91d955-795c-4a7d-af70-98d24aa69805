#include "MediaPipeline.h"
#include "Frame.h"
#include <json.hpp>
#include <stdio.h>
#include <ljcore/jlog.h>
#include <string>
#include <hv/EventLoop.h>

using namespace panocom;

void sdpCallback(const std::string& key, const std::string& sdp, void* param)
{
    jinfo("sdpCallback %s", sdp.c_str());
}

void iceCallback(const std::string& key, const std::string& state, void* param)
{
    jinfo("iceCallback %s", state.c_str());
}

int main(int argc, char *argv[])
{
    if (argc < 6)
    {
        printf("exe [stun-server-ip] [stun-server-port] [isServer] [isRtcp] [type]");
        return -1;
    }
    auto loop = std::make_shared<hv::EventLoop>();
    jlog_init(nullptr);
    std::string stunServerIP = argv[1];
    int stunServerPort = atoi(argv[2]);
    bool isServer = atoi(argv[3]);
    bool isRtcp = atoi(argv[4]);
    int type = atoi(argv[5]);

    nlohmann::json j;
    j["stun-server"] = stunServerIP;
    j["stun-port"] = stunServerPort;

    if (type)
    {
        auto agent = FramePipeline::CreateFramePipeline("LibniceAgent", j.dump());
        agent->setNotify("sdp", "", NULL, sdpCallback);
        agent->setNotify("ice", "", NULL, iceCallback);

        j.clear();
        j["isServer"] = isServer;
        j["isRtcp"] = isRtcp;
        auto dtls = FramePipeline::CreateFramePipeline("DTLSFramePipeline", j.dump());

        agent->addDataDestination(dtls);
        dtls->addDataDestination(agent);
        agent->start();
        dtls->start();

        loop->run();
    }
    else
    {
        auto jrtp = RtpEngine::CreateRTPEngine("JRtpEngine", j.dump());
        jrtp->setNotify("sdp", "", NULL, sdpCallback);
        jrtp->setNotify("ice", "", NULL, iceCallback);
        jrtp->start();
        loop->run();
    }
    return 0;
}