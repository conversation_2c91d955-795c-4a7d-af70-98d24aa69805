#include "drm_test.h"

#include <fcntl.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/mman.h>
#include <unistd.h>
#include <signal.h>
#include <drm/drm_fourcc.h>
#include <hv/hlog.h>
// #include "custom_data.h"

namespace panocom {
static std::once_flag oc;

DRMTest::DRMTest(/* args */) {}

DRMTest::~DRMTest() {}

DRMTest &DRMTest::inst() {
	static DRMTest instance;
	return instance;
}

void DRMTest::Init() {
	std::call_once(oc, [this] {
		CheckHdmiConnected();
	});
}

int32_t DRMTest::modeset_create_fb(int fd, struct buffer_object *bo) {
	int32_t ret = -1;
    drm_mode_create_dumb create {};
    drm_mode_map_dumb map {};
    // create a dumb-buffer, the pixel format is XRGB888
    create.width = bo->width;
    create.height = bo->height;
    create.bpp = 32;
    ret = drmIoctl(fd, DRM_IOCTL_MODE_CREATE_DUMB, &create);
    if (ret != 0) {
        LOGE("drmIoctl failed, ret = %d", ret);
        return ret;
    }

    // bind the dumb-buffer to an FB object
    bo->pitch = create.pitch;
    bo->size = create.size;
    bo->handle = create.handle;
    ret = drmModeAddFB(fd, bo->width, bo->height, 24, 32, bo->pitch, bo->handle, &bo->fb_id);
    if (ret != 0) {
        LOGE("drmModeAddFB failed, ret = %d", ret);
        return ret;
    }
    // map the dumb-buffer to userspace
    map.handle = create.handle;
    ret = drmIoctl(fd, DRM_IOCTL_MODE_MAP_DUMB, &map);
    if (ret != 0) {
        LOGE("drmIoctl failed, ret = %d", ret);
        return ret;
    }
    bo->vaddr = (uint32_t *)mmap(0, create.size, PROT_READ | PROT_WRITE, MAP_SHARED, fd, map.offset);
    // initialize the dumb-buffer with white-color
    memset((uint8_t*)bo->vaddr, 0xb0, bo->size);
    return 0;
}

int32_t DRMTest::modeset_create_fb(int fd, struct buffer_object *bo, uint32_t color) {
	int32_t ret = -1;
    drm_mode_create_dumb create {};
    drm_mode_map_dumb map {};
    // create a dumb-buffer, the pixel format is XRGB888
    create.width = bo->width;
    create.height = bo->height;
    create.bpp = 32;
    ret = drmIoctl(fd, DRM_IOCTL_MODE_CREATE_DUMB, &create);
    if (ret != 0) {
        LOGE("drmIoctl failed, ret = %d", ret);
        return ret;
    }

    // bind the dumb-buffer to an FB object
    bo->pitch = create.pitch;
    bo->size = create.size;
    bo->handle = create.handle;
    ret = drmModeAddFB(fd, bo->width, bo->height, 24, 32, bo->pitch, bo->handle, &bo->fb_id);
    if (ret != 0) {
        LOGE("drmModeAddFB failed, ret = %d", ret);
        return ret;
    }
    // map the dumb-buffer to userspace
    map.handle = create.handle;
    ret = drmIoctl(fd, DRM_IOCTL_MODE_MAP_DUMB, &map);
    if (ret != 0) {
        LOGE("drmIoctl failed, ret = %d", ret);
        return ret;
    }
    bo->vaddr = (uint32_t *)mmap(0, create.size, PROT_READ | PROT_WRITE, MAP_SHARED, fd, map.offset);
    // initialize the dumb-buffer with white-color
    for (int i = 0; i < (bo->size / 4); ++i)
		bo->vaddr[i] = color;
    return 0;
}

int32_t DRMTest::modeset_create_fb2(int fd, struct buffer_object *bo, uint32_t color) {
	int32_t ret = -1;
    drm_mode_create_dumb create {};
    drm_mode_map_dumb map {};
	uint32_t handles[4] = {0}, pitches[4] = {0}, offsets[4] = {0};
    // create a dumb-buffer, the pixel format is XRGB888
    create.width = bo->width;
    create.height = bo->height;
    create.bpp = 32;
    ret = drmIoctl(fd, DRM_IOCTL_MODE_CREATE_DUMB, &create);
    if (ret != 0) {
        LOGE("drmIoctl failed, ret = %d", ret);
        return ret;
    }

    // bind the dumb-buffer to an FB object
    bo->pitch = create.pitch;
    bo->size = create.size;
    bo->handle = create.handle;
    // ret = drmModeAddFB(fd, bo->width, bo->height, 24, 32, bo->pitch, bo->handle, &bo->fb_id);
    // if (ret != 0) {
    //     LOGE("drmModeAddFB failed, ret = %d", ret);
    //     return ret;
    // }
	handles[0] = bo->handle;
	pitches[0] = bo->pitch;
	ret = drmModeAddFB2(fd, bo->width, bo->height, DRM_FORMAT_XRGB8888, handles,
						pitches, offsets, &bo->fb_id, 0);
    if (ret != 0) {
        LOGE("drmModeAddFB2 failed, ret = %d", ret);
        return ret;
    }
    // map the dumb-buffer to userspace
    map.handle = create.handle;
    ret = drmIoctl(fd, DRM_IOCTL_MODE_MAP_DUMB, &map);
    if (ret != 0) {
        LOGE("drmIoctl failed, ret = %d", ret);
        return ret;
    }
    bo->vaddr = (uint32_t *)mmap(0, create.size, PROT_READ | PROT_WRITE, MAP_SHARED, fd, map.offset);
    // initialize the dumb-buffer with white-color
    for (int i = 0; i < (bo->size / 4); ++i)
		bo->vaddr[i] = color;
    return 0;
}

void DRMTest::modestset_destroy_fb(int fd, struct buffer_object *bo) {
    drm_mode_destroy_dumb destroy {};
    drmModeRmFB(fd, bo->fb_id);
    munmap(bo->vaddr, bo->size);
    destroy.handle = bo->handle;
    drmIoctl(fd, DRM_IOCTL_MODE_DESTROY_DUMB, &destroy);
}

void DRMTest::SingleBufferTest() {
	int32_t ret = -1;
    int fd;
    drmModeConnector *conn;
    drmModeRes *res;
    uint32_t conn_id;
    uint32_t crtc_id;
	int dev = 1;
    fd = open("/dev/dri/card0", O_RDWR | O_CLOEXEC);

    res = drmModeGetResources(fd);
	if (res) {
		LOGD("count_connectors = %d, count_crtcs = %d", res->count_connectors, res->count_crtcs);
	}
    crtc_id = res->crtcs[dev];
    conn_id = res->connectors[dev];

    conn = drmModeGetConnector(fd, conn_id);
	if (!conn) {
		LOGE("drmModeGetConnector failed.");
		return;
	} else {
		LOGD("count_modes = %d, connector_type = %d", conn->count_modes, conn->connector_type);
	}

    buf_.width = conn->modes[0].hdisplay;
    buf_.height = conn->modes[0].vdisplay;
	LOGD("mode = %s",conn->modes[0].name);

    ret = modeset_create_fb(fd, &buf_);
	if (ret != 0) 
		LOGE("modeset_create_fb failed, ret = %d", ret);
		
    ret = drmModeSetCrtc(fd, crtc_id, buf_.fb_id, 0, 0, &conn_id, 1, &conn->modes[0]);
	if (ret != 0)
		LOGE("drmModeSetCrtc failed, ret = %d", ret);

    getchar();

    modestset_destroy_fb(fd, &buf_);
    drmModeFreeConnector(conn);
    drmModeFreeResources(res);
    close(fd);
}

void DRMTest::DoubleBufferTest() {
	int32_t ret = -1;
    int fd;
    drmModeConnector *conn;
    drmModeRes *res;
    uint32_t conn_id;
    uint32_t crtc_id;
	int dev = 2;

	fd = open("/dev/dri/card0", O_RDWR | O_CLOEXEC);

	res = drmModeGetResources(fd);
	if (res) {
		LOGD("count_connectors = %d, count_crtcs = %d", res->count_connectors, res->count_crtcs);
	}
	crtc_id = res->crtcs[dev];
	conn_id = res->connectors[dev];
	conn = drmModeGetConnector(fd, conn_id);
	if (!conn) {
		LOGE("drmModeGetConnector failed.");
		return;
	} else {
		LOGD("count_modes = %d, connector_type = %d", conn->count_modes, conn->connector_type);
	}
	double_bufs_[0].width = conn->modes[0].hdisplay;
	double_bufs_[0].height = conn->modes[0].vdisplay;
	double_bufs_[1].width = conn->modes[0].hdisplay;
	double_bufs_[1].height = conn->modes[0].vdisplay;
    ret = modeset_create_fb(fd, &double_bufs_[0], 0xff0000);
	if (ret != 0) 
		LOGE("modeset_create_fb failed, ret = %d", ret);
    ret = modeset_create_fb(fd, &double_bufs_[1], 0x0000ff);
	if (ret != 0) 
		LOGE("modeset_create_fb failed, ret = %d", ret);

    ret = drmModeSetCrtc(fd, crtc_id, double_bufs_[0].fb_id, 0, 0, &conn_id, 1, &conn->modes[0]);
	if (ret != 0)
		LOGE("drmModeSetCrtc failed, ret = %d", ret);
	getchar();
    ret = drmModeSetCrtc(fd, crtc_id, double_bufs_[1].fb_id, 0, 0, &conn_id, 1, &conn->modes[0]);
	if (ret != 0)
		LOGE("drmModeSetCrtc failed, ret = %d", ret);

    getchar();

    modestset_destroy_fb(fd, &double_bufs_[1]);
	modestset_destroy_fb(fd, &double_bufs_[0]);
    drmModeFreeConnector(conn);
    drmModeFreeResources(res);
    close(fd);
}

static bool terminate = false;

static void modeset_page_flip_handler(int fd, uint32_t frame, uint32_t sec, uint32_t usec, void* data) {
	static int i = 0;
	uint32_t crtc_id = *(uint32_t*)data;
	i ^= 1;
	drmModePageFlip(fd, crtc_id, DRMTest::inst().double_bufs_[i].fb_id, DRM_MODE_PAGE_FLIP_EVENT, data);
	usleep(500000);
}

static void sigint_handler(int arg) {
	terminate = true;
}

void DRMTest::PageFlipTest() {
	int32_t ret = -1;
    int fd;
    drmModeConnector *conn;
    drmModeRes *res;
	drmEventContext ev {};
    uint32_t conn_id;
    uint32_t crtc_id;
	int dev = 2;

	signal(SIGINT, sigint_handler);

	ev.version = DRM_EVENT_CONTEXT_VERSION;
	ev.page_flip_handler = modeset_page_flip_handler;

	fd = open("/dev/dri/card0", O_RDWR | O_CLOEXEC);

	res = drmModeGetResources(fd);
	if (res) {
		LOGD("count_connectors = %d, count_crtcs = %d", res->count_connectors, res->count_crtcs);
	}
	crtc_id = res->crtcs[dev];
	conn_id = res->connectors[dev];
	conn = drmModeGetConnector(fd, conn_id);
	if (!conn) {
		LOGE("drmModeGetConnector failed.");
		return;
	} else {
		LOGD("count_modes = %d, connector_type = %d", conn->count_modes, conn->connector_type);
	}
	double_bufs_[0].width = conn->modes[0].hdisplay;
	double_bufs_[0].height = conn->modes[0].vdisplay;
	double_bufs_[1].width = conn->modes[0].hdisplay;
	double_bufs_[1].height = conn->modes[0].vdisplay;
    ret = modeset_create_fb(fd, &double_bufs_[0], 0xff0000);
	if (ret != 0) 
		LOGE("modeset_create_fb failed, ret = %d", ret);
    ret = modeset_create_fb(fd, &double_bufs_[1], 0x0000ff);
	if (ret != 0) 
		LOGE("modeset_create_fb failed, ret = %d", ret);

    ret = drmModeSetCrtc(fd, crtc_id, double_bufs_[0].fb_id, 0, 0, &conn_id, 1, &conn->modes[0]);
	if (ret != 0)
		LOGE("drmModeSetCrtc failed, ret = %d", ret);
    ret = drmModePageFlip(fd, crtc_id, double_bufs_[0].fb_id, DRM_MODE_PAGE_FLIP_EVENT, &crtc_id);
	if (ret != 0)
		LOGE("drmModePageFlip failed, ret = %d", ret);

    while (!terminate)
		drmHandleEvent(fd, &ev);
	
    modestset_destroy_fb(fd, &double_bufs_[1]);
	modestset_destroy_fb(fd, &double_bufs_[0]);
    drmModeFreeConnector(conn);
    drmModeFreeResources(res);
    close(fd);
}

void DRMTest::PlaneTest() {
	int32_t ret = -1;
    int fd;
    drmModeConnector *conn;
    drmModeRes *res;
	drmModePlaneRes *plane_res;
    uint32_t conn_id;
    uint32_t crtc_id;
	uint32_t plane_id;
	int dev = 1;

	fd = open("/dev/dri/card0", O_RDWR | O_CLOEXEC);

	res = drmModeGetResources(fd);
	if (res) {
		LOGD("count_connectors = %d, count_crtcs = %d", res->count_connectors, res->count_crtcs);
	}

	ret = drmSetClientCap(fd, DRM_CLIENT_CAP_UNIVERSAL_PLANES, 1);
	if (ret != 0) 
		LOGE("drmSetClientCap failed, ret = %d", ret);
	plane_res = drmModeGetPlaneResources(fd);
	if (plane_res) {
		LOGD("count_planes = %d", plane_res->count_planes);
	}
	plane_id = plane_res->planes[dev];

	crtc_id = res->crtcs[dev];
	conn_id = res->connectors[dev];
	conn = drmModeGetConnector(fd, conn_id);
	if (!conn) {
		LOGE("drmModeGetConnector failed.");
		return;
	} else {
		LOGD("count_modes = %d, connector_type = %d", conn->count_modes, conn->connector_type);
	}
	double_bufs_[0].width = conn->modes[0].hdisplay;
	double_bufs_[0].height = conn->modes[0].vdisplay;
	double_bufs_[1].width = conn->modes[0].hdisplay;
	double_bufs_[1].height = conn->modes[0].vdisplay;
    ret = modeset_create_fb(fd, &double_bufs_[0], 0xff0000);
	if (ret != 0) 
		LOGE("modeset_create_fb failed, ret = %d", ret);
    ret = modeset_create_fb(fd, &double_bufs_[1], 0x0000ff);
	if (ret != 0) 
		LOGE("modeset_create_fb failed, ret = %d", ret);

    ret = drmModeSetCrtc(fd, crtc_id, double_bufs_[0].fb_id, 0, 0, &conn_id, 1, &conn->modes[0]);
	if (ret != 0)
		LOGE("drmModeSetCrtc failed, ret = %d", ret);
	getchar();
    ret = drmModeSetPlane(fd, plane_id, crtc_id, double_bufs_[1].fb_id, 0, 50, 50, 320, 320, 100 << 16, 150 << 16, 320 << 16, 320 << 16);
	if (ret != 0)
		LOGE("drmModeSetPlane failed, ret = %d", ret);

    getchar();
	
    modestset_destroy_fb(fd, &double_bufs_[1]);
	modestset_destroy_fb(fd, &double_bufs_[0]);
    drmModeFreeConnector(conn);
	drmModeFreePlaneResources(plane_res);
    drmModeFreeResources(res);
    close(fd);
}

uint32_t DRMTest::get_property_id(int fd, drmModeObjectProperties *props, const char *name) {
	drmModePropertyPtr property;
	uint32_t i, id = 0;
	
	// find property according to the name
	for (i = 0; i < props->count_props; i++) {
		property = drmModeGetProperty(fd, props->props[i]);
		if (!strcmp(property->name, name))
			id = property->prop_id;
		drmModeFreeProperty(property);
		if (id) break;
	}
	return id;
}

void DRMTest::AtomicCrtcTest() {
	int32_t ret = -1;
    int fd;
    drmModeConnector *conn;
    drmModeRes *res;
	drmModePlaneRes *plane_res;
	drmModeObjectProperties *props;
	drmModeAtomicReq *req;
    uint32_t conn_id;
    uint32_t crtc_id;
	uint32_t plane_id;
	uint32_t blob_id;
	uint32_t property_crtc_id;
	uint32_t property_mode_id;
	uint32_t property_active;
	int dev = 1;

	fd = open("/dev/dri/card0", O_RDWR | O_CLOEXEC);

	res = drmModeGetResources(fd);
	if (res) {
		LOGD("count_connectors = %d, count_crtcs = %d", res->count_connectors, res->count_crtcs);
	}
	crtc_id = res->crtcs[dev];
	conn_id = res->connectors[dev];

	ret = drmSetClientCap(fd, DRM_CLIENT_CAP_UNIVERSAL_PLANES, 1);
	if (ret != 0) 
		LOGE("drmSetClientCap failed, ret = %d", ret);
	plane_res = drmModeGetPlaneResources(fd);
	if (plane_res) {
		LOGD("count_planes = %d", plane_res->count_planes);
	}
	plane_id = plane_res->planes[dev];

	conn = drmModeGetConnector(fd, conn_id);
	if (!conn) {
		LOGE("drmModeGetConnector failed.");
		return;
	} else {
		LOGD("count_modes = %d, connector_type = %d", conn->count_modes, conn->connector_type);
	}
	double_bufs_[0].width = conn->modes[0].hdisplay;
	double_bufs_[0].height = conn->modes[0].vdisplay;
	double_bufs_[1].width = conn->modes[0].hdisplay;
	double_bufs_[1].height = conn->modes[0].vdisplay;
    ret = modeset_create_fb(fd, &double_bufs_[0], 0xff0000);
	if (ret != 0) 
		LOGE("modeset_create_fb failed, ret = %d", ret);
    ret = modeset_create_fb(fd, &double_bufs_[1], 0x0000ff);
	if (ret != 0) 
		LOGE("modeset_create_fb failed, ret = %d", ret);

	ret = drmSetClientCap(fd, DRM_CLIENT_CAP_ATOMIC, 1);
	if (ret != 0) 
		LOGE("drmSetClientCap failed, ret = %d", ret);

	// get connector properties
	props = drmModeObjectGetProperties(fd, conn_id, DRM_MODE_OBJECT_CONNECTOR);
	property_crtc_id = get_property_id(fd, props, "CRTC_ID");
	drmModeFreeObjectProperties(props);

	// get crtc properties
	props = drmModeObjectGetProperties(fd, crtc_id, DRM_MODE_OBJECT_CRTC);
	property_active = get_property_id(fd, props, "ACTIVE");
	property_mode_id = get_property_id(fd, props, "MODE_ID");
	drmModeFreeObjectProperties(props);
	LOGD("property_crtc_id = %d, property_active = %d, property_mode_id = %d", property_crtc_id, property_active, property_mode_id);
	// create blob to store current mode, and return the blob id
	drmModeCreatePropertyBlob(fd, &conn->modes[0], sizeof(conn->modes[0]), &blob_id);

	// start modeseting
	req = drmModeAtomicAlloc();
	drmModeAtomicAddProperty(req, crtc_id, property_active, 1);
	drmModeAtomicAddProperty(req, crtc_id, property_mode_id, blob_id);
	drmModeAtomicAddProperty(req, conn_id, property_crtc_id, crtc_id);
	drmModeAtomicCommit(fd, req, DRM_MODE_ATOMIC_ALLOW_MODESET, NULL);
	drmModeAtomicFree(req);
	
	LOGD("drmModeAtomicCommit SetCrtc");
	getchar();

    ret = drmModeSetPlane(fd, plane_id, crtc_id, double_bufs_[1].fb_id, 0, 50, 50, 320, 320, 100 << 16, 150 << 16, 320 << 16, 320 << 16);
	if (ret != 0)
		LOGE("drmModeSetPlane failed, ret = %d", ret);

    getchar();
	
    modestset_destroy_fb(fd, &double_bufs_[1]);
	modestset_destroy_fb(fd, &double_bufs_[0]);
    drmModeFreeConnector(conn);
	drmModeFreePlaneResources(plane_res);
    drmModeFreeResources(res);
    close(fd);
}

void DRMTest::AtomicPlaneTest() {
	int32_t ret = -1;
    int fd;
    drmModeConnector *conn;
    drmModeRes *res;
	drmModePlaneRes *plane_res;
	drmModeObjectProperties *props;
	drmModeAtomicReq *req;
    uint32_t conn_id;
    uint32_t crtc_id;
	uint32_t plane_id;
	uint32_t blob_id;
	uint32_t property_crtc_id;
	uint32_t property_mode_id;
	uint32_t property_active;
	uint32_t property_fb_id;
	uint32_t property_crtc_x;
	uint32_t property_crtc_y;
	uint32_t property_crtc_w;
	uint32_t property_crtc_h;
	uint32_t property_src_x;
	uint32_t property_src_y;
	uint32_t property_src_w;
	uint32_t property_src_h;
	int dev = 1;
	signal(SIGINT, sigint_handler);
	fd = open("/dev/dri/card0", O_RDWR | O_CLOEXEC);

	res = drmModeGetResources(fd);
	if (res) {
		LOGD("count_connectors = %d, count_crtcs = %d", res->count_connectors, res->count_crtcs);
	}
	crtc_id = res->crtcs[dev];
	conn_id = res->connectors[dev];

	ret = drmSetClientCap(fd, DRM_CLIENT_CAP_UNIVERSAL_PLANES, 1);
	if (ret != 0) 
		LOGE("drmSetClientCap failed, ret = %d", ret);
	plane_res = drmModeGetPlaneResources(fd);
	if (plane_res) {
		LOGD("count_planes = %d", plane_res->count_planes);
	}
	plane_id = plane_res->planes[dev];

	conn = drmModeGetConnector(fd, conn_id);
	if (!conn) {
		LOGE("drmModeGetConnector failed.");
		return;
	} else {
		LOGD("count_modes = %d, connector_type = %d", conn->count_modes, conn->connector_type);
	}
	double_bufs_[0].width = conn->modes[0].hdisplay;
	double_bufs_[0].height = conn->modes[0].vdisplay;
	double_bufs_[1].width = conn->modes[0].hdisplay;
	double_bufs_[1].height = conn->modes[0].vdisplay;
    ret = modeset_create_fb(fd, &double_bufs_[0], 0xff0000);
	if (ret != 0) 
		LOGE("modeset_create_fb failed, ret = %d", ret);
    ret = modeset_create_fb(fd, &double_bufs_[1], 0x0000ff);
	if (ret != 0) 
		LOGE("modeset_create_fb failed, ret = %d", ret);

	ret = drmSetClientCap(fd, DRM_CLIENT_CAP_ATOMIC, 1);
	if (ret != 0) 
		LOGE("drmSetClientCap failed, ret = %d", ret);

	// get connector properties
	props = drmModeObjectGetProperties(fd, conn_id, DRM_MODE_OBJECT_CONNECTOR);
	property_crtc_id = get_property_id(fd, props, "CRTC_ID");
	drmModeFreeObjectProperties(props);

	// get crtc properties
	props = drmModeObjectGetProperties(fd, crtc_id, DRM_MODE_OBJECT_CRTC);
	property_active = get_property_id(fd, props, "ACTIVE");
	property_mode_id = get_property_id(fd, props, "MODE_ID");
	drmModeFreeObjectProperties(props);
	LOGD("property_crtc_id = %d, property_active = %d, property_mode_id = %d", property_crtc_id, property_active, property_mode_id);

	// create blob to store current mode, and return the blob id
	drmModeCreatePropertyBlob(fd, &conn->modes[0], sizeof(conn->modes[0]), &blob_id);

	// start modeseting
	req = drmModeAtomicAlloc();
	drmModeAtomicAddProperty(req, crtc_id, property_active, 1);
	drmModeAtomicAddProperty(req, crtc_id, property_mode_id, blob_id);
	drmModeAtomicAddProperty(req, conn_id, property_crtc_id, crtc_id);
	// drmModeAtomicCommit(fd, req, DRM_MODE_ATOMIC_ALLOW_MODESET, NULL);
	// drmModeAtomicFree(req);
	
	// LOGD("drmModeAtomicCommit SetCrtc");
	// getchar();

	// get plane properties
	props = drmModeObjectGetProperties(fd, plane_id, DRM_MODE_OBJECT_PLANE);
	property_crtc_id = get_property_id(fd, props, "CRTC_ID");
	property_fb_id = get_property_id(fd, props, "FB_ID");
	property_crtc_x = get_property_id(fd, props, "CRTC_X");
	property_crtc_y = get_property_id(fd, props, "CRTC_Y");
	property_crtc_w = get_property_id(fd, props, "CRTC_W");
	property_crtc_h = get_property_id(fd, props, "CRTC_H");
	property_src_x = get_property_id(fd, props, "SRC_X");
	property_src_y = get_property_id(fd, props, "SRC_Y");
	property_src_w = get_property_id(fd, props, "SRC_W");
	property_src_h = get_property_id(fd, props, "SRC_H");
	drmModeFreeObjectProperties(props);

	// atomic plane update
	// req = drmModeAtomicAlloc();
	drmModeAtomicAddProperty(req, plane_id, property_crtc_id, crtc_id);
	drmModeAtomicAddProperty(req, plane_id, property_fb_id, double_bufs_[1].fb_id);
	drmModeAtomicAddProperty(req, plane_id, property_crtc_x, 50);
	drmModeAtomicAddProperty(req, plane_id, property_crtc_y, 50);
	drmModeAtomicAddProperty(req, plane_id, property_crtc_w, 320);
	drmModeAtomicAddProperty(req, plane_id, property_crtc_h, 320);
	drmModeAtomicAddProperty(req, plane_id, property_src_x, 0);
	drmModeAtomicAddProperty(req, plane_id, property_src_y, 0);
	drmModeAtomicAddProperty(req, plane_id, property_src_w, 320 << 16);
	drmModeAtomicAddProperty(req, plane_id, property_src_h, 320 << 16);
	drmModeAtomicCommit(fd, req, 0, NULL);
	drmModeAtomicFree(req);

	LOGD("drmModeAtomicCommit SetPlane");

    // getchar();
	while (!terminate) usleep(10000);
	
    modestset_destroy_fb(fd, &double_bufs_[1]);
	modestset_destroy_fb(fd, &double_bufs_[0]);
    drmModeFreeConnector(conn);
	drmModeFreePlaneResources(plane_res);
    drmModeFreeResources(res);
    close(fd);	
}

void DRMTest::Test() {
	int32_t ret = -1;
    int fd;
    drmModeConnector *conn;
    drmModeRes *res;
	drmModePlaneRes *plane_res;
	drmModeObjectProperties *props;
	drmModeAtomicReq *req;
    uint32_t conn_id;
    uint32_t crtc_id;
	uint32_t plane_id;
	uint32_t plane_id_1;
	uint32_t blob_id;
	uint32_t property_crtc_id;
	uint32_t property_mode_id;
	uint32_t property_active;
	uint32_t property_fb_id;
	uint32_t property_crtc_x;
	uint32_t property_crtc_y;
	uint32_t property_crtc_w;
	uint32_t property_crtc_h;
	uint32_t property_src_x;
	uint32_t property_src_y;
	uint32_t property_src_w;
	uint32_t property_src_h;
	int dev = 2;
	signal(SIGINT, sigint_handler);
	fd = open("/dev/dri/card0", O_RDWR | O_CLOEXEC);

	res = drmModeGetResources(fd);
	if (res) {
		LOGD("count_connectors = %d, count_crtcs = %d", res->count_connectors, res->count_crtcs);
	}
	crtc_id = res->crtcs[dev];
	conn_id = res->connectors[dev];

	ret = drmSetClientCap(fd, DRM_CLIENT_CAP_UNIVERSAL_PLANES, 1);
	if (ret != 0) 
		LOGE("drmSetClientCap failed, ret = %d", ret);
	plane_res = drmModeGetPlaneResources(fd);
	if (plane_res) {
		LOGD("count_planes = %d", plane_res->count_planes);
	}
	plane_id = plane_res->planes[dev];
	plane_id_1 = plane_res->planes[1];
	auto ptr = drmModeGetPlane(fd, plane_id_1);
	uint32_t fb_id_1 = 0;
	if (ptr) {
		fb_id_1 = ptr->fb_id;
		LOGD("fb_id_1 = %d", fb_id_1);
	}

	conn = drmModeGetConnector(fd, conn_id);
	if (!conn) {
		LOGE("drmModeGetConnector failed.");
		return;
	} else {
		LOGD("count_modes = %d, connector_type = %d", conn->count_modes, conn->connector_type);
	}
	if (conn->count_modes == 0) {
		LOGE("connector mode list is empty, id = %d", conn_id);
		drmModeFreeConnector(conn);
		drmModeFreePlaneResources(plane_res);
		drmModeFreeResources(res);
		close(fd);
		return;
	}
	double_bufs_[0].width = conn->modes[0].hdisplay;
	double_bufs_[0].height = conn->modes[0].vdisplay;
	double_bufs_[1].width = conn->modes[0].hdisplay;
	double_bufs_[1].height = conn->modes[0].vdisplay;
    ret = modeset_create_fb(fd, &double_bufs_[0], 0xff0000);
	if (ret != 0) 
		LOGE("modeset_create_fb failed, ret = %d", ret);
    ret = modeset_create_fb(fd, &double_bufs_[1], 0x0000ff);
	if (ret != 0) 
		LOGE("modeset_create_fb failed, ret = %d", ret);

	ret = drmSetClientCap(fd, DRM_CLIENT_CAP_ATOMIC, 1);
	if (ret != 0) 
		LOGE("drmSetClientCap failed, ret = %d", ret);

	// get connector properties
	props = drmModeObjectGetProperties(fd, conn_id, DRM_MODE_OBJECT_CONNECTOR);
	property_crtc_id = get_property_id(fd, props, "CRTC_ID");
	drmModeFreeObjectProperties(props);

	// get crtc properties
	props = drmModeObjectGetProperties(fd, crtc_id, DRM_MODE_OBJECT_CRTC);
	property_active = get_property_id(fd, props, "ACTIVE");
	property_mode_id = get_property_id(fd, props, "MODE_ID");
	drmModeFreeObjectProperties(props);
	LOGD("property_crtc_id = %d, property_active = %d, property_mode_id = %d", property_crtc_id, property_active, property_mode_id);

	// create blob to store current mode, and return the blob id
	drmModeCreatePropertyBlob(fd, &conn->modes[0], sizeof(conn->modes[0]), &blob_id);

	// start modeseting
	req = drmModeAtomicAlloc();
	drmModeAtomicAddProperty(req, crtc_id, property_active, 1);
	drmModeAtomicAddProperty(req, crtc_id, property_mode_id, blob_id);
	drmModeAtomicAddProperty(req, conn_id, property_crtc_id, crtc_id);
	// drmModeAtomicCommit(fd, req, DRM_MODE_ATOMIC_ALLOW_MODESET, NULL);
	// drmModeAtomicFree(req);
	
	// LOGD("drmModeAtomicCommit SetCrtc");
	// getchar();

	// get plane properties
	props = drmModeObjectGetProperties(fd, plane_id, DRM_MODE_OBJECT_PLANE);
	property_crtc_id = get_property_id(fd, props, "CRTC_ID");
	property_fb_id = get_property_id(fd, props, "FB_ID");
	property_crtc_x = get_property_id(fd, props, "CRTC_X");
	property_crtc_y = get_property_id(fd, props, "CRTC_Y");
	property_crtc_w = get_property_id(fd, props, "CRTC_W");
	property_crtc_h = get_property_id(fd, props, "CRTC_H");
	property_src_x = get_property_id(fd, props, "SRC_X");
	property_src_y = get_property_id(fd, props, "SRC_Y");
	property_src_w = get_property_id(fd, props, "SRC_W");
	property_src_h = get_property_id(fd, props, "SRC_H");
	drmModeFreeObjectProperties(props);

	// atomic plane update
	// req = drmModeAtomicAlloc();
	drmModeAtomicAddProperty(req, plane_id, property_crtc_id, crtc_id);

	drmModeAtomicAddProperty(req, plane_id, property_crtc_x, 0);
	drmModeAtomicAddProperty(req, plane_id, property_crtc_y, 0);
	drmModeAtomicAddProperty(req, plane_id, property_crtc_w, 2560);
	drmModeAtomicAddProperty(req, plane_id, property_crtc_h, 1600);
	drmModeAtomicAddProperty(req, plane_id, property_src_x, 0);
	drmModeAtomicAddProperty(req, plane_id, property_src_y, 0);
	drmModeAtomicAddProperty(req, plane_id, property_src_w, 1920 << 16);
	drmModeAtomicAddProperty(req, plane_id, property_src_h, 1080 << 16);
	while (!terminate) {
		drmModeAtomicAddProperty(req, plane_id, property_fb_id, fb_id_1);
		drmModeAtomicCommit(fd, req, 0, NULL);
	}

	drmModeAtomicFree(req);

	LOGD("drmModeAtomicCommit SetPlane");

    // getchar();
	
    modestset_destroy_fb(fd, &double_bufs_[1]);
	modestset_destroy_fb(fd, &double_bufs_[0]);
    drmModeFreeConnector(conn);
	drmModeFreePlaneResources(plane_res);
    drmModeFreeResources(res);
    close(fd);		
}

void DRMTest::CheckHdmiConnected() {
	int32_t ret = -1;
	uint8_t dev = 1;	// hdmi1对应的vodev为1
	int fd = open("/dev/dri/card0", O_RDWR | O_CLOEXEC);
	if (fd < 0) {
		LOGE("open /dev/dri/card0 failed, errno = %d", errno);
		return;
	}
	drmModeRes *res = drmModeGetResources(fd);
	if (res) {
		LOGD("count_connectors = %d, count_crtcs = %d", res->count_connectors, res->count_crtcs);
	}
	uint32_t conn_id = res->connectors[dev];
	drmModeConnector *conn = drmModeGetConnector(fd, conn_id);
	if (!conn) {
		LOGE("drmModeGetConnector failed.");
	} else {
		LOGD("count_modes = %d, connector_type = %d", conn->count_modes, conn->connector_type);
        if (conn->count_modes == 0) {
            LOGE("connector mode list is empty, id = %d", conn_id);
            hdmi_connected_ = false;
        } else
            hdmi_connected_ = true;
		drmModeFreeConnector(conn);
    }
    drmModeFreeResources(res);
    close(fd);	
}

void DRMTest::StartMultiScreen() {
	multi_screen_running_ = true;
    multi_screen_future_ = std::async([this] {
        int32_t ret = -1;
        int fd;
        drmModeConnector *conn;
        drmModeRes *res;
        drmModePlaneRes *plane_res;
        drmModeObjectProperties *props;
        drmModeAtomicReq *req;
        uint32_t conn_id;
        uint32_t crtc_id;
        uint32_t plane_id;
        uint32_t plane_id_extend;
        uint32_t blob_id;
        uint32_t property_crtc_id;
        uint32_t property_mode_id;
        uint32_t property_active;
        uint32_t property_fb_id;
        uint32_t property_crtc_x;
        uint32_t property_crtc_y;
        uint32_t property_crtc_w;
        uint32_t property_crtc_h;
        uint32_t property_src_x;
        uint32_t property_src_y;
        uint32_t property_src_w;
        uint32_t property_src_h;
        uint8_t dev_1 = 1;	// hdmi1
		uint8_t dev_2 = 2; // mipi-DSI
        fd = open("/dev/dri/card0", O_RDWR | O_CLOEXEC);
		if (fd < 0) return false;
        res = drmModeGetResources(fd);
        if (res) {
            LOGD("count_connectors = %d, count_crtcs = %d", res->count_connectors, res->count_crtcs);
        }
        conn_id = res->connectors[dev_1];

      	ret = drmSetClientCap(fd, DRM_CLIENT_CAP_ATOMIC, 1);
        if (ret != 0)
            LOGE("drmSetClientCap failed, ret = %d", ret);
        ret = drmSetClientCap(fd, DRM_CLIENT_CAP_UNIVERSAL_PLANES, 1);
        if (ret != 0)
            LOGE("drmSetClientCap failed, ret = %d", ret);
        plane_res = drmModeGetPlaneResources(fd);
        if (plane_res) {
            LOGD("count_planes = %d", plane_res->count_planes);
        } else {
			LOGE("drmModeGetPlaneResources failed.");
		}

        conn = drmModeGetConnector(fd, conn_id);
        if (!conn) {
            LOGE("drmModeGetConnector failed.");
            drmModeFreeConnector(conn);
			drmModeFreePlaneResources(plane_res);
            drmModeFreeResources(res);
            close(fd);
			return false;
        } else {
            LOGD("count_modes = %d, connector_type = %d", conn->count_modes, conn->connector_type);
        }
        // if (conn->count_modes == 0) {
        //     LOGE("connector mode list is empty, id = %d", conn_id);
        //     hdmi_connected_ = false;
        // } else
        //     hdmi_connected_ = true;

		if (hdmi_connected_) {
			LOGD("Extend screen is %d", dev_2);
        	plane_id = plane_res->planes[dev_1];
        	plane_id_extend = plane_res->planes[dev_2];
			crtc_id = res->crtcs[dev_2];
        	conn_id = res->connectors[dev_2];
		} else {
			LOGD("Extend screen is %d", dev_1);
        	plane_id = plane_res->planes[dev_2];
        	plane_id_extend = plane_res->planes[dev_1];
			crtc_id = res->crtcs[dev_1];
        	conn_id = res->connectors[dev_1];
		}
		if (conn)
			drmModeFreeConnector(conn);
		conn = drmModeGetConnector(fd, conn_id);
        if (!conn) {
            LOGE("drmModeGetConnector failed.");
			hdmi_connected_ = false;
            drmModeFreeConnector(conn);
        	drmModeFreePlaneResources(plane_res);
            drmModeFreeResources(res);
            close(fd);
			return false;
        } else {
            LOGD("count_modes = %d, connector_type = %d", conn->count_modes, conn->connector_type);
        }

        auto ptr = drmModeGetPlane(fd, plane_id);
        uint32_t fb_id = 0;
        if (ptr) {
            fb_id = ptr->fb_id;
            LOGD("fb_id = %d, count_formats = %d", fb_id, ptr->count_formats);
        } else {
			LOGE("plane ptr is nullptr");
		}

        // get connector properties
        props = drmModeObjectGetProperties(fd, conn_id, DRM_MODE_OBJECT_CONNECTOR);
        property_crtc_id = get_property_id(fd, props, "CRTC_ID");
        drmModeFreeObjectProperties(props);

        // get crtc properties
        props = drmModeObjectGetProperties(fd, crtc_id, DRM_MODE_OBJECT_CRTC);
        property_active = get_property_id(fd, props, "ACTIVE");
        property_mode_id = get_property_id(fd, props, "MODE_ID");
        drmModeFreeObjectProperties(props);
        LOGD("property_crtc_id = %d, property_active = %d, property_mode_id = %d", property_crtc_id, property_active, property_mode_id);

        // create blob to store current mode, and return the blob id
        drmModeCreatePropertyBlob(fd, &conn->modes[0], sizeof(conn->modes[0]), &blob_id);
		LOGD("conn->modes[0] = %s", conn->modes[0].name);

        // start modeseting
        req = drmModeAtomicAlloc();
        drmModeAtomicAddProperty(req, crtc_id, property_active, 1);
        drmModeAtomicAddProperty(req, crtc_id, property_mode_id, blob_id);
        drmModeAtomicAddProperty(req, conn_id, property_crtc_id, crtc_id);
		drmModeAtomicCommit(fd, req, DRM_MODE_ATOMIC_ALLOW_MODESET, NULL);
		drmModeAtomicFree(req);

        // get plane properties
        props = drmModeObjectGetProperties(fd, plane_id_extend, DRM_MODE_OBJECT_PLANE);
        property_crtc_id = get_property_id(fd, props, "CRTC_ID");
        property_fb_id = get_property_id(fd, props, "FB_ID");
        property_crtc_x = get_property_id(fd, props, "CRTC_X");
        property_crtc_y = get_property_id(fd, props, "CRTC_Y");
        property_crtc_w = get_property_id(fd, props, "CRTC_W");
        property_crtc_h = get_property_id(fd, props, "CRTC_H");
        property_src_x = get_property_id(fd, props, "SRC_X");
        property_src_y = get_property_id(fd, props, "SRC_Y");
        property_src_w = get_property_id(fd, props, "SRC_W");
        property_src_h = get_property_id(fd, props, "SRC_H");
        drmModeFreeObjectProperties(props);

		req = drmModeAtomicAlloc();
        drmModeAtomicAddProperty(req, plane_id_extend, property_crtc_id, crtc_id);
        drmModeAtomicAddProperty(req, plane_id_extend, property_crtc_x, 0);
        drmModeAtomicAddProperty(req, plane_id_extend, property_crtc_y, 0);
		uint32_t crtc_w, crtc_h, src_w, src_h;
		if (hdmi_connected_) {
			crtc_w = 2560;
			crtc_h = 1600;
			src_w = 1920;
			src_h = 1080;
		} else {
			crtc_w = 1920;
			crtc_h = 1080;
			src_w = 2560;
			src_h = 1600;			
		}
        drmModeAtomicAddProperty(req, plane_id_extend, property_crtc_w, crtc_w);
        drmModeAtomicAddProperty(req, plane_id_extend, property_crtc_h, crtc_h);
        drmModeAtomicAddProperty(req, plane_id_extend, property_src_w, src_w << 16);
        drmModeAtomicAddProperty(req, plane_id_extend, property_src_h, src_h << 16);
        drmModeAtomicAddProperty(req, plane_id_extend, property_src_x, 0);
        drmModeAtomicAddProperty(req, plane_id_extend, property_src_y, 0);

		double_bufs_[0].width = conn->modes[0].hdisplay;
		double_bufs_[0].height = conn->modes[0].vdisplay;
		ret = modeset_create_fb(fd, &double_bufs_[0], 0x000000);
		if (ret != 0) 
			LOGE("modeset_create_fb failed, ret = %d", ret);

		while (multi_screen_running_) {
			ptr = drmModeGetPlane(fd, plane_id);
			drmModeAtomicAddProperty(req, plane_id_extend, property_fb_id, ptr->fb_id);
			drmModeAtomicCommit(fd, req, 0, NULL);
			drmModeFreePlane(ptr);
			usleep(10000);
		}

        drmModeAtomicFree(req);
		drmModeDestroyPropertyBlob(fd, blob_id);
        LOGD("drmModeAtomicCommit SetPlane");

        drmModeFreeConnector(conn);
        drmModeFreePlaneResources(plane_res);
        drmModeFreeResources(res);
        close(fd);
        return true;
    });
}

void DRMTest::StartMultiScreen(bool extend_is_hdmi) {
	if (multi_screen_running_) return;
	multi_screen_running_ = true;
    multi_screen_future_ = std::async([this, extend_is_hdmi] {
        do {
#ifndef WIN32
            cpu_set_t mask;
            CPU_ZERO(&mask);
            for (int i = 4; i < 8; i++) {
                CPU_SET(i, &mask);
            }
            if (sched_setaffinity(0, sizeof(mask), &mask) == -1)
                LOGE("sched_setaffinity cpu fail");
            else
                LOGD("sched_setaffinity cpu success");
#endif // !WIN32
        } while (0);
        int32_t ret = -1;
        int fd;
        drmModeConnector *conn;
        drmModeRes *res;
        drmModePlaneRes *plane_res;
        drmModeObjectProperties *props;
        drmModeAtomicReq *req;
        uint32_t conn_id;
        uint32_t crtc_id;
        uint32_t plane_id;
        uint32_t plane_id_extend;
        uint32_t blob_id;
        uint32_t property_crtc_id;
        uint32_t property_mode_id;
        uint32_t property_active;
        uint32_t property_fb_id;
        uint32_t property_crtc_x;
        uint32_t property_crtc_y;
        uint32_t property_crtc_w;
        uint32_t property_crtc_h;
        uint32_t property_src_x;
        uint32_t property_src_y;
        uint32_t property_src_w;
        uint32_t property_src_h;
        uint8_t dev_1 = 1;	// hdmi1
		uint8_t dev_2 = 2; // mipi-DSI
		uint32_t crtc_w, crtc_h, src_w, src_h;
        fd = open("/dev/dri/card0", O_RDWR | O_CLOEXEC);
		if (fd < 0) return false;
        res = drmModeGetResources(fd);
        if (res) {
            LOGD("count_connectors = %d, count_crtcs = %d", res->count_connectors, res->count_crtcs);
        }
        conn_id = res->connectors[dev_1];

      	ret = drmSetClientCap(fd, DRM_CLIENT_CAP_ATOMIC, 1);
        if (ret != 0)
            LOGE("drmSetClientCap failed, ret = %d", ret);
        ret = drmSetClientCap(fd, DRM_CLIENT_CAP_UNIVERSAL_PLANES, 1);
        if (ret != 0)
            LOGE("drmSetClientCap failed, ret = %d", ret);
        plane_res = drmModeGetPlaneResources(fd);
        if (plane_res) {
            LOGD("count_planes = %d", plane_res->count_planes);
        } else {
			LOGE("drmModeGetPlaneResources failed.");
		}

        conn = drmModeGetConnector(fd, conn_id);
        if (!conn || conn->count_modes == 0) {
            LOGE("drmModeGetConnector failed.");
            drmModeFreeConnector(conn);
			drmModeFreePlaneResources(plane_res);
            drmModeFreeResources(res);
            close(fd);
			multi_screen_running_ = false;
			return false;
        } else {
            LOGD("count_modes = %d, connector_type = %d", conn->count_modes, conn->connector_type);
        }
		// 液晶屏是固定的，就不再检测对应connector的mode了，如果不需要液晶屏就需要获取
		// 对应的connector
		src_w = 2560;
		src_h = 1600;

		if (!extend_is_hdmi) {
			LOGD("Extend screen is %d", dev_2);
        	plane_id = plane_res->planes[dev_1];
        	plane_id_extend = plane_res->planes[dev_2];
			crtc_id = res->crtcs[dev_2];
        	conn_id = res->connectors[dev_2];
		} else {
			LOGD("Extend screen is %d", dev_1);
        	plane_id = plane_res->planes[dev_2];
        	plane_id_extend = plane_res->planes[dev_1];
			crtc_id = res->crtcs[dev_1];
        	conn_id = res->connectors[dev_1];
		}
		if (conn)
			drmModeFreeConnector(conn);
		conn = drmModeGetConnector(fd, conn_id);
        if (!conn || conn->count_modes == 0) {
            LOGE("drmModeGetConnector failed.");
            drmModeFreeConnector(conn);
        	drmModeFreePlaneResources(plane_res);
            drmModeFreeResources(res);
            close(fd);
			multi_screen_running_ = false;
			return false;
        } else {
            LOGD("count_modes = %d, connector_type = %d", conn->count_modes, conn->connector_type);
        }

        auto ptr = drmModeGetPlane(fd, plane_id);
        uint32_t fb_id = 0;
        if (ptr) {
            fb_id = ptr->fb_id;
            LOGD("fb_id = %d, count_formats = %d", fb_id, ptr->count_formats);
        } else {
			LOGE("plane ptr is nullptr");
		}

        // get connector properties
        props = drmModeObjectGetProperties(fd, conn_id, DRM_MODE_OBJECT_CONNECTOR);
        property_crtc_id = get_property_id(fd, props, "CRTC_ID");
        drmModeFreeObjectProperties(props);

        // get crtc properties
        props = drmModeObjectGetProperties(fd, crtc_id, DRM_MODE_OBJECT_CRTC);
        property_active = get_property_id(fd, props, "ACTIVE");
        property_mode_id = get_property_id(fd, props, "MODE_ID");
        drmModeFreeObjectProperties(props);
        LOGD("property_crtc_id = %d, property_active = %d, property_mode_id = %d", property_crtc_id, property_active, property_mode_id);

        // create blob to store current mode, and return the blob id
        drmModeCreatePropertyBlob(fd, &conn->modes[0], sizeof(conn->modes[0]), &blob_id);
		LOGD("conn->modes[0] = %s", conn->modes[0].name);

        // start modeseting
        req = drmModeAtomicAlloc();
        drmModeAtomicAddProperty(req, crtc_id, property_active, 1);
        drmModeAtomicAddProperty(req, crtc_id, property_mode_id, blob_id);
        drmModeAtomicAddProperty(req, conn_id, property_crtc_id, crtc_id);
		drmModeAtomicCommit(fd, req, DRM_MODE_ATOMIC_ALLOW_MODESET, NULL);
		drmModeAtomicFree(req);

        // get plane properties
        props = drmModeObjectGetProperties(fd, plane_id_extend, DRM_MODE_OBJECT_PLANE);
        property_crtc_id = get_property_id(fd, props, "CRTC_ID");
        property_fb_id = get_property_id(fd, props, "FB_ID");
        property_crtc_x = get_property_id(fd, props, "CRTC_X");
        property_crtc_y = get_property_id(fd, props, "CRTC_Y");
        property_crtc_w = get_property_id(fd, props, "CRTC_W");
        property_crtc_h = get_property_id(fd, props, "CRTC_H");
        property_src_x = get_property_id(fd, props, "SRC_X");
        property_src_y = get_property_id(fd, props, "SRC_Y");
        property_src_w = get_property_id(fd, props, "SRC_W");
        property_src_h = get_property_id(fd, props, "SRC_H");
        drmModeFreeObjectProperties(props);

		req = drmModeAtomicAlloc();
        drmModeAtomicAddProperty(req, plane_id_extend, property_crtc_id, crtc_id);
        drmModeAtomicAddProperty(req, plane_id_extend, property_crtc_x, 0);
        drmModeAtomicAddProperty(req, plane_id_extend, property_crtc_y, 0);

		crtc_w = conn->modes[0].hdisplay;
		crtc_h = conn->modes[0].vdisplay;

        drmModeAtomicAddProperty(req, plane_id_extend, property_crtc_w, crtc_w);
        drmModeAtomicAddProperty(req, plane_id_extend, property_crtc_h, crtc_h);
        drmModeAtomicAddProperty(req, plane_id_extend, property_src_w, src_w << 16);
        drmModeAtomicAddProperty(req, plane_id_extend, property_src_h, src_h << 16);
        drmModeAtomicAddProperty(req, plane_id_extend, property_src_x, 0);
        drmModeAtomicAddProperty(req, plane_id_extend, property_src_y, 0);

		double_bufs_[0].width = src_w;
		double_bufs_[0].height = src_h;
		ret = modeset_create_fb2(fd, &double_bufs_[0], 0x000000);
		if (ret != 0) 
			LOGE("modeset_create_fb failed, ret = %d", ret);
		drmModeAtomicAddProperty(req, plane_id_extend, property_fb_id, double_bufs_[0].fb_id);
		ret = drmModeAtomicCommit(fd, req, 0, NULL);
		if (ret != 0) 
		{
			LOGE("drmModeAtomicCommit failed!!!, ret = %d", ret);
			drmModeDestroyPropertyBlob(fd, blob_id);
			drmModeAtomicFree(req);
			modestset_destroy_fb(fd, &double_bufs_[0]);
			drmModeFreeConnector(conn);
			drmModeFreePlaneResources(plane_res);
			drmModeFreeResources(res);
			close(fd);
			return false;
		}

		drmEventContext ev {};	
		ev.version = DRM_EVENT_CONTEXT_VERSION;
		ev.page_flip_handler = nullptr;

		int last_fb_id = 0;
		while (multi_screen_running_) {
			ptr = drmModeGetPlane(fd, plane_id);
			if (last_fb_id != ptr->fb_id) {
				drmModeAtomicAddProperty(req, plane_id_extend, property_fb_id, ptr->fb_id);
				ret = drmModePageFlip(fd, crtc_id, ptr->fb_id, DRM_MODE_PAGE_FLIP_EVENT, &crtc_id);
				if (ret != 0) LOGE("drmModePageFlip failed!!!, ret = %d", ret);
				drmHandleEvent(fd, &ev);
				last_fb_id = ptr->fb_id;
			} else usleep(3000);
			drmModeFreePlane(ptr);
		}

		drmModeDestroyPropertyBlob(fd, blob_id);
        drmModeAtomicFree(req);
		modestset_destroy_fb(fd, &double_bufs_[0]);
        drmModeFreeConnector(conn);
        drmModeFreePlaneResources(plane_res);
        drmModeFreeResources(res);
        close(fd);
        return true;
    });
}


void DRMTest::StartMultiScreenNotAtomic() {
	multi_screen_running_ = true;
    multi_screen_future_ = std::async([this] {
        int32_t ret = -1;
        int fd;
        drmModeConnector *conn;
        drmModeRes *res;
        drmModePlaneRes *plane_res;
        uint32_t conn_id;
        uint32_t crtc_id;
        uint32_t plane_id;
        uint32_t plane_id_extend;
		uint32_t src_w, src_h, crtc_w, crtc_h;


        uint8_t dev_1 = 1;	// hdmi1
		uint8_t dev_2 = 2; // mipi-DSI
        fd = open("/dev/dri/card0", O_RDWR | O_CLOEXEC);
		if (fd < 0) return false;
        res = drmModeGetResources(fd);
        if (res) {
            LOGD("count_connectors = %d, count_crtcs = %d", res->count_connectors, res->count_crtcs);
        }
        conn_id = res->connectors[dev_1];

        ret = drmSetClientCap(fd, DRM_CLIENT_CAP_UNIVERSAL_PLANES, 1);
        if (ret != 0)
            LOGE("drmSetClientCap failed, ret = %d", ret);
        plane_res = drmModeGetPlaneResources(fd);
        if (plane_res) {
            LOGD("count_planes = %d", plane_res->count_planes);
        } else {
			LOGE("drmModeGetPlaneResources failed.");
		}

        conn = drmModeGetConnector(fd, conn_id);
        if (!conn) {
            LOGE("drmModeGetConnector failed.");
            drmModeFreeConnector(conn);
			drmModeFreePlaneResources(plane_res);
            drmModeFreeResources(res);
            close(fd);
			return false;
        } else {
            LOGD("count_modes = %d, connector_type = %d", conn->count_modes, conn->connector_type);
        }
        // if (conn->count_modes == 0) {
        //     LOGE("connector mode list is empty, id = %d", conn_id);
        //     hdmi_connected_ = false;
        // } else
        //     hdmi_connected_ = true;


		LOGD("Extend screen is %d", dev_1);
		plane_id = plane_res->planes[dev_2];
		plane_id_extend = plane_res->planes[dev_1];
		crtc_id = res->crtcs[dev_1];
		conn_id = res->connectors[dev_1];
		src_w = 2560;
		src_h = 1600;
		crtc_w = 1920;
		crtc_h = 1080;

		if (conn)
			drmModeFreeConnector(conn);
		conn = drmModeGetConnector(fd, conn_id);
        if (!conn) {
            LOGE("drmModeGetConnector failed.");
			hdmi_connected_ = false;
            drmModeFreeConnector(conn);
        	drmModeFreePlaneResources(plane_res);
            drmModeFreeResources(res);
            close(fd);
			return false;
        } else {
            LOGD("count_modes = %d, connector_type = %d", conn->count_modes, conn->connector_type);
        }
		drmModePlanePtr ptr;
		buf_.width = conn->modes[0].hdisplay;
		buf_.height = conn->modes[0].vdisplay;
		ret = modeset_create_fb(fd, &buf_, 0x000000);
		if (ret != 0) 
			LOGE("modeset_create_fb failed, ret = %d", ret);
		ret = drmModeSetCrtc(fd, crtc_id, buf_.fb_id, 0, 0, &conn_id, 1, &conn->modes[0]);
		if (ret != 0)
			LOGE("drmModeSetCrtc failed, ret = %d", ret);
		int last_fb_id = 0;
		while (multi_screen_running_) {
			ptr = drmModeGetPlane(fd, plane_id);
			if (ptr->fb_id != last_fb_id) {
				ret = drmModeSetPlane(fd, plane_id_extend, crtc_id, ptr->fb_id, 0, 0, 0, crtc_w, crtc_h, 0, 0, src_w << 16, src_h << 16);
				if (ret != 0)
					LOGE("drmModeSetPlane failed, ret = %d", ret);
				last_fb_id = ptr->fb_id;
			}

			drmModeFreePlane(ptr);
			usleep(10000);
		}

		modestset_destroy_fb(fd, &buf_);
        drmModeFreeConnector(conn);
        drmModeFreePlaneResources(plane_res);
        drmModeFreeResources(res);
        close(fd);
        return true;
    });
}

bool DRMTest::StopMultiScreen() {
	if (!multi_screen_running_) return false;
	multi_screen_running_ = false;
	auto status = multi_screen_future_.wait_for(std::chrono::milliseconds(300));
	if (status == std::future_status::timeout) {
		LOGE("Cancel multi screens timeout.");
		return false;
	}
	return multi_screen_future_.get();
}

} // namespace panocom