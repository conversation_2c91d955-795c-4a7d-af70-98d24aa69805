<project>
  <files
    default_encoding="System"
    readonly="false"
    maxfilesize="10485760"
    enforce_portability="false">
    <dir
      name=".">
      <dir
        name="3raparty">
        <dir
          name="include">
          <dir
            name="hv"/>
        </dir>
        <dir
          name="libhv">
          <dir
            name="base"/>
          <dir
            name="build">
            <dir
              name="CMakeFiles">
              <dir
                name="4.0.2">
                <dir
                  name="CompilerIdC"/>
                <dir
                  name="CompilerIdCXX"/>
              </dir>
            </dir>
            <dir
              name="include">
              <dir
                name="hv"/>
            </dir>
          </dir>
          <dir
            name="cpputil"/>
          <dir
            name="echo-servers"/>
          <dir
            name="event">
            <dir
              name="kcp"/>
            <dir
              name="wepoll"/>
          </dir>
          <dir
            name="evpp"/>
          <dir
            name="examples">
            <dir
              name="consul"/>
            <dir
              name="httpd"/>
            <dir
              name="jsonrpc"/>
            <dir
              name="kcptun">
              <dir
                name="client"/>
              <dir
                name="server"/>
              <dir
                name="smux"/>
            </dir>
            <dir
              name="mqtt"/>
            <dir
              name="multi-thread"/>
            <dir
              name="nmap"/>
            <dir
              name="protorpc">
              <dir
                name="handler"/>
            </dir>
          </dir>
          <dir
            name="http">
            <dir
              name="client"/>
            <dir
              name="server"/>
          </dir>
          <dir
            name="include">
            <dir
              name="hv"/>
          </dir>
          <dir
            name="misc"/>
          <dir
            name="mqtt"/>
          <dir
            name="protocol"/>
          <dir
            name="ssl"/>
          <dir
            name="unittest"/>
          <dir
            name="util"/>
        </dir>
      </dir>
      <dir
        name="audio"/>
      <dir
        name="cmake-build-debug">
        <dir
          name="CMakeFiles">
          <dir
            name="4.0.2">
            <dir
              name="CompilerIdC"/>
            <dir
              name="CompilerIdCXX"/>
          </dir>
        </dir>
      </dir>
      <dir
        name="file"/>
      <dir
        name="impl">
        <dir
          name="aec"/>
        <dir
          name="alsa"/>
        <dir
          name="audiocodec">
          <dir
            name="test"/>
        </dir>
        <dir
          name="audiodispatcher"/>
        <dir
          name="audiomixer">
          <dir
            name="test"/>
        </dir>
        <dir
          name="audioprocessing"/>
        <dir
          name="cuda"/>
        <dir
          name="dtls"/>
        <dir
          name="ffmpeg"/>
        <dir
          name="file"/>
        <dir
          name="ip"/>
        <dir
          name="jrtp"/>
        <dir
          name="lec"/>
        <dir
          name="libnice"/>
        <dir
          name="libyuv"/>
        <dir
          name="monitor"/>
        <dir
          name="ns"/>
        <dir
          name="rk">
          <dir
            name="camera"/>
          <dir
            name="test"/>
        </dir>
        <dir
          name="rtsp"/>
        <dir
          name="sdl2"/>
        <dir
          name="spec"/>
        <dir
          name="tinyalsa"/>
        <dir
          name="uart"/>
        <dir
          name="v4l2">
          <dir
            name="test"/>
        </dir>
        <dir
          name="webrtc">
          <dir
            name="neteq"/>
          <dir
            name="thread"/>
          <dir
            name="ulpfec"/>
          <dir
            name="ulpfec2"/>
        </dir>
      </dir>
      <dir
        name="out">
        <dir
          name="build">
          <dir
            name="x64-Debug">
            <dir
              name="CMakeFiles">
              <dir
                name="3.30.5-msvc23">
                <dir
                  name="CompilerIdC"/>
                <dir
                  name="CompilerIdCXX"/>
              </dir>
              <dir
                name="ShowIncludes"/>
            </dir>
          </dir>
        </dir>
      </dir>
      <dir
        name="selector"/>
      <dir
        name="service"/>
      <dir
        name="test">
        <dir
          name="CuQML"/>
        <dir
          name="CuWidget"/>
        <dir
          name="FfIPCVideoRender"/>
        <dir
          name="FfVideoEncoder"/>
        <dir
          name="conference"/>
        <dir
          name="csph3"/>
        <dir
          name="sipcall"/>
        <dir
          name="stun_dtls"/>
        <dir
          name="terminal"/>
      </dir>
      <dir
        name="video"/>
      <dir
        name="win32">
        <dir
          name="test"/>
      </dir>
    </dir>
    <dir
      name="F:">
      <dir
        name="mediamanager-cbuild_dtls_stun">
        <watched
          excludes=".*"
          subdirs="true">
          <filter
            name="C"/>
          <filter
            name="C++"/>
          <filter
            name="CUDA"/>
          <filter
            name="Objective-C"/>
          <filter
            name="Objective-C++"/>
        </watched>
      </dir>
    </dir>
  </files>
  <languages>
    <language
      name="C++"/>
    <language_options
      useLanguagesAsFilter="true"/>
  </languages>
  <file_types/>
  <annotations_options/>
  <codecheck_options/>
  <comparison_options
    comparison_db=""
    git_repo=""
    git_commit=""
    diff_flags="1"/>
  <cpp_options
    add_found_include_files="false"
    add_found_system_include_files="false"
    allow_nested_comments="false"
    analyze_headers_in_isolation="true"
    bracket_depth="256"
    cache_ast_files="true"
    c_std=""
    compiler="Linux"
    compiler_include_path=""
    create_implicit_special_member_functions="false"
    create_references_in_assembly="true"
    create_references_in_inactive_code="true"
    create_references_to_local_objects="true"
    create_references_to_macros_during_macro_expansion="false"
    create_references_to_parameters="true"
    cxx_std=""
    cuda_arch="sm_20"
    delayed_template_parsing="true"
    triple=""
    ignore_dir_in_include_files="false"
    ignore_preprocessor_conditionals="false"
    ios_min_version="5.0"
    linkage_append_text=""
    linkage_prepend_text=""
    macos_min_version="10.7"
    macro_expansion_trunc="0"
    merge_structs="true"
    msc_version=""
    prompt_for_missing_include_files="false"
    objectDataMembers="false"
    objectFunctionMembers="false"
    save_comments_associated_with_entities="true"
    save_duplicate_references="false"
    save_macro_expansion_text="false"
    simplify_macro_expansion="false"
    search_for_include_files_among_project_files="true"
    start_worker_processes_serially="false"
    sysroot=""
    treat_system_includes_as_user_includes="true"
    compare_includes_by_content="false"
    use_clang="true"
    use_include_cache="true"
    objc_memory_mode="MrrMode"
    worker_process_timeout="2"/>
  <dependency_options
    exclude_standard="true"
    use_includes="true"
    mode="link"/>
  <imports/>
  <metrics
    declared_in_file_display_mode="no_path"
    file_display_mode="no_path"
    show_declared_in_file="false"
    show_function_parameter_types="false"
    write_column_titles="true"
    add_uniquename_column="false"
    metric_filter="28532"/>
  <reports
    date_stamping="false"
    display_parameters="false"
    index_by_method="false"
    group_by_directory="false"
    filename_mode="short"
    generation_status="needs_generation"
    generation_time="0"
    html_clear_directory="false"
    html_generation="true"
    html_mode="alpha"
    headertext_mode="false"
    headerusertext_mode="false"
    headertext_text="Report generated by Understand"
    headerusertext_text=""
    html_size="250"
    text_clear_directory="false"
    text_generation="true"
    text_mode="single">
    <report
      name="Data Dictionary"
      enabled="true"/>
    <report
      name="File Contents"
      enabled="true"/>
    <report
      name="Program Unit Cross Reference"
      enabled="true"/>
    <report
      name="Object Cross Reference"
      enabled="true"/>
    <report
      name="Type Cross Reference"
      enabled="true"/>
    <report
      name="Macro Cross Reference"
      enabled="true"/>
    <report
      name="Include File Cross Reference"
      enabled="true"/>
    <report
      name="Declaration Tree"
      enabled="true"/>
    <report
      name="Extend Tree"
      enabled="false"/>
    <report
      name="Invocation Tree"
      enabled="false"/>
    <report
      name="Simple Invocation Tree"
      enabled="true"/>
    <report
      name="Imports"
      enabled="true"/>
    <report
      name="With Tree"
      enabled="true"/>
    <report
      name="Simple With Tree"
      enabled="true"/>
    <report
      name="Generic Instantiation"
      enabled="true"/>
    <report
      name="Exception Cross Reference"
      enabled="true"/>
    <report
      name="Renames"
      enabled="true"/>
    <report
      name="Program Unit Complexity"
      enabled="true"/>
    <report
      name="Project Metrics"
      enabled="true"/>
    <report
      name="Program Unit Metrics"
      enabled="true"/>
    <report
      name="File Metrics"
      enabled="true"/>
    <report
      name="File Average Metrics"
      enabled="true"/>
    <report
      name="Fortran Extension Usage"
      enabled="true"/>
    <report
      name="Class Metrics"
      enabled="true"/>
    <report
      name="Class OO Metrics"
      enabled="true"/>
    <report
      name="Implicitly Declared Objects"
      enabled="true"/>
    <report
      name="Uninitialized Items"
      enabled="true"/>
    <report
      name="Unused Objects and Functions"
      enabled="true"/>
    <report
      name="Unused Objects"
      enabled="true"/>
    <report
      name="Unused Types"
      enabled="true"/>
    <report
      name="Unused Program Units"
      enabled="true"/>
    <report
      name="Uses Not Needed"
      enabled="true"/>
    <report
      name="Withs Not Needed"
      enabled="true"/>
  </reports>
</project>
