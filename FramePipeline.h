#ifndef P_FramePipeline_h
#define P_FramePipeline_h

#include <functional>
#include <memory>
#include <mutex>
#include <unordered_map>
#include <list>
#include <vector>
#include <set>
#include <string>
#include <ljcore/jlog.h>
#include "Utils.h"

#define FOOT_PRINT  jinfo("----%s %d----\n", __FUNCTION__, __LINE__)
#define FN_BEGIN jinfo("###=== [%s-%s-%p] start ====###\n", typeid(*this).name(), __FUNCTION__, this)
#define FN_END jinfo("###=== [%s-%s-%p] end ====###\n", typeid(*this).name(), __FUNCTION__, this)

// #define FN_BEGIN printf("###=== [%s-%p] start ====###\n", __FUNCTION__, this)
// #define FN_END printf("###=== [%s-%p] end ====###\n", __FUNCTION__, this)

namespace panocom
{
    class Frame;
    class FramePipeline : public std::enable_shared_from_this<FramePipeline>
    {
    public:
        using Ptr = std::shared_ptr<FramePipeline>;
        using WPtr = std::weak_ptr<FramePipeline>;
        static FramePipeline::Ptr CreateFramePipeline(const std::string &name, const std::string& jsonParams = "");

        FramePipeline() = default;
        virtual ~FramePipeline();

        virtual std::string name() { return name_; }
        FramePipeline::Ptr findFramePipelineByName(const std::string& name)
        {
            std::set<FramePipeline::Ptr> ptrSet;
            return findFramePipelineByName(name, ptrSet);
        }

        FramePipeline::Ptr findFramePipelineByName(const std::string& name, std::set<FramePipeline::Ptr>& ptrSet)
        {
            jinfo("findFramePipelineByName(%p-%s) %s\n", this, name_.c_str(), name.c_str());
            FramePipeline::Ptr ret;
            if (name == name_)
            {
                jinfo("%s is found", name.c_str());
                return shared_from_this();
            }
            if (ptrSet.find(shared_from_this()) != ptrSet.end())
            {
                return ret;
            }
            ptrSet.insert(shared_from_this());
            for (auto dest: audio_dests_)
            {
                auto dst = dest.lock();
                if (dst)
                {
                    ret = dst->findFramePipelineByName(name, ptrSet);
                    if (ret)
                    {
                        return ret;
                    }
                }
            }
            for (auto dest: video_dests_)
            {
                auto dst = dest.lock();
                if (dst)
                {
                    ret = dst->findFramePipelineByName(name, ptrSet);
                    if (ret)
                    {
                        return ret;
                    }
                }
            }
            for (auto dest: data_dests_)
            {
                auto dst = dest.lock();
                if (dst)
                {
                    ret = dst->findFramePipelineByName(name, ptrSet);
                    if (ret)
                    {
                        return ret;
                    }
                }
            }
            if (!ret)
            {
                jinfo("%s cannot be found", name.c_str());
            }
            return ret;
        }

        virtual void onFeedbackFrame(const std::shared_ptr<Frame> &f){};

        virtual FramePipeline::Ptr addAudioDestination(const FramePipeline::Ptr& dst);
        virtual FramePipeline::Ptr removeAudioDestination(const FramePipeline::Ptr& dst);

        virtual FramePipeline::Ptr addVideoDestination(const FramePipeline::Ptr& dst);
        virtual FramePipeline::Ptr removeVideoDestination(const FramePipeline::Ptr& dst);

        virtual FramePipeline::Ptr addDataDestination(const FramePipeline::Ptr& dst);
        virtual FramePipeline::Ptr removeDataDestination(const FramePipeline::Ptr& dst);

        virtual std::list<FramePipeline::WPtr> getAudioDestinations();
        virtual std::list<FramePipeline::WPtr> getVideoDestinations();
        virtual std::list<FramePipeline::WPtr> getDataDestinations();

        virtual bool isAudioDestination(const FramePipeline::Ptr& dst);
        virtual bool isVideoDestination(const FramePipeline::Ptr& dst);
        virtual bool isDataDestination(const FramePipeline::Ptr& dst);

        void clearPipeline();
        void stopPipeline();

        virtual void setGroupId(int groupId) { groupId_ = groupId; }
        virtual int getGroupId() { return groupId_; }

        virtual void printOutputStatus(const std::string& name);

        virtual void start();
        virtual void pause();
        virtual void stop();
        virtual void reset() {}

        virtual bool isRunning() { return false; }

        enum WorkState
        {
            STOPPED,
            STOPPING,
            STARTED,
            STARTING,
            PAUSE,
        };
        virtual WorkState workState();

        virtual void onFrameTransfer(const std::shared_ptr<Frame> &f);

        virtual void onVideoSourceChanged() {}

        virtual void checkFrameTimeout();

        using FrameNotifyCallback = std::function<void(int, int, uint32_t, FramePipeline::Ptr)>;
        void setFrameNotify(const FrameNotifyCallback& cb) 
        { 
            frameNotifyCB_ = cb; 
        }

        using TimeoutNotifyCallback = std::function<void(long, void*)>;
        virtual void setFrameTimeoutNotify(int ms, void* param, const TimeoutNotifyCallback& cb)
        { 
            timeoutMS_ = ms; 
            timeoutNotifyCB_ = cb; 
            timeoutTick_ = GetTickCount(); 
            param_ = param;
        }
		virtual void setFrameTimeoutNotify2(int ms, void* param, const TimeoutNotifyCallback& cb)
		{
			timeoutMS4Pool_ = ms;
			timeoutNotifyCB4Pool_ = cb;
			timeoutTick4Pool_ = GetTickCount();
			param4Pool_ = param;
		}
		virtual void resetFrameTimeoutNotify2(int ms)
		{
			timeoutMS4Pool_ = ms;
			timeoutTick4Pool_ = GetTickCount();
		}
        virtual void resetTimeoutTick()
        { 
            timeoutTick_ = GetTickCount(); 
        }
        virtual void resetTimeoutTick4Pool()
        { 
            timeoutTick4Pool_ = GetTickCount(); 
        }
        using NotifyCallback = std::function<void(const std::string&, const std::string&, void* param)>;
        using NotifyCallback2 = std::function<void(const std::string&, const std::string&, const std::string&, void* param)>;
        struct NotifyInfo
        {
            NotifyCallback cb;
            NotifyCallback2 cb2;
            std::string jsonParam;
            bool async;
            void* param;
            std::string id;
        };
        virtual void setNotify(const std::string& key, const std::string& jsonParam,  void* param, const NotifyCallback& cb, bool use_async=true)
        {
            jinfo("setNotify [%s] [%s] [%p] [%p] [%d]", key.c_str(), jsonParam.c_str(), param, cb, use_async);
            notifycallbacks_[key].jsonParam = jsonParam;
            notifycallbacks_[key].param = param;
            notifycallbacks_[key].cb = cb;
            notifycallbacks_[key].async = use_async;
        }
        virtual void setNotify2(const std::string &id, const std::string& key, const std::string& jsonParam,  void* param, const NotifyCallback2& cb, bool use_async=true)
        {
            jinfo("setNotify2 [%s] [%s] [%s] [%p] [%p] [%d]", id.c_str(), key.c_str(), jsonParam.c_str(), param, cb, use_async);
            notifycallbacks_[key].jsonParam = jsonParam;
            notifycallbacks_[key].param = param;
            notifycallbacks_[key].cb2 = cb;
            notifycallbacks_[key].async = use_async;
            notifycallbacks_[key].id = id;
        }
        using FrameListener = std::function<void(const std::shared_ptr<Frame> &f, void* param)>;
        virtual void setPreOnFrameListener(const FrameListener& frameListener, void* param)
        {
            preOnFrameListener_ = frameListener;
            preOnFrameParam_ = param;
        }
        virtual void setAfterOnFrameListener(const FrameListener& frameListener, void* param)
        {
            afterOnFrameListener_ = frameListener;
            afterOnFrameParam_ = param;
        }

        virtual void setPreDeliverFrameListener(const FrameListener& frameListener, void* param)
        {
            preDeliverFrameListener_ = frameListener;
            preDeliverFrameParam_ = param;
        }
        virtual void setAfterDeliverFrameListener(const FrameListener& frameListener, void* param)
        {
            afterDeliverFrameListener_ = frameListener;
            afterDeliverFrameParam_ = param;
        }

        virtual int updateParam(const std::string& jsonParam) { return 0; };

        virtual void addAudioSource(const FramePipeline::Ptr& source);
        virtual void removeAudioSource(const FramePipeline::Ptr& source);

        virtual void addVideoSource(const FramePipeline::Ptr& source);
        virtual void removeVideoSource(const FramePipeline::Ptr& source);

        virtual void addDataSource(const FramePipeline::Ptr& source);
        virtual void removeDataSource(const FramePipeline::Ptr& source);

        virtual std::list<FramePipeline::WPtr> getAudioSources();
        virtual std::list<FramePipeline::WPtr> getVideoSources();
        virtual std::list<FramePipeline::WPtr> getDataSources();

        virtual void printInputStatus(const std::string& name);
        virtual void printDestinationStatus(const std::string& name);
        virtual void resetConnectState(bool state);
        virtual bool getConnectState() { return connected_; }

        // 获取模块的状态
        virtual bool getStatus(std::string &status) { return true; }
        virtual void setMuted(bool muted) { muted_ = muted; }
    protected:
        virtual void onFrame(const std::shared_ptr<Frame> &f) {}
        virtual void deliverFrame(const std::shared_ptr<Frame> &f);
        virtual void clearCache() {};
        void setStarted();
        void setStopped();
        void deliverFeedbackFrame(const std::shared_ptr<Frame> &f);
    protected:
        int outputFps_;
        std::string name_;
        std::mutex mutexState_;
        WorkState workState_ = STOPPED;
        FrameNotifyCallback frameNotifyCB_;
        int timeoutMS_ = 0;
        TimeoutNotifyCallback timeoutNotifyCB_;
		int timeoutMS4Pool_ = 0;
		TimeoutNotifyCallback timeoutNotifyCB4Pool_;
        std::mutex frame_mutex_;

        std::unordered_map<std::string, NotifyInfo> notifycallbacks_;

        FrameListener preOnFrameListener_;
        void* preOnFrameParam_;

        FrameListener afterOnFrameListener_;
        void* afterOnFrameParam_;

        FrameListener preDeliverFrameListener_;
        void* preDeliverFrameParam_;

        FrameListener afterDeliverFrameListener_;
        void* afterDeliverFrameParam_;

        // FIXME: 使用map或者子类中实现
        bool connected_ = false;
        bool connected_timeout_ = false;    // 用于自定义超时时间的断流检测

        bool muted_ = false;
        long inputFrameCount_;
        long outputFrameCount_;
        bool printed_;
    private:
        std::list<FramePipeline::WPtr> audio_dests_;
        std::mutex audio_dests_mutex_;
        std::list<FramePipeline::WPtr> video_dests_;
        std::mutex video_dests_mutex_;
        std::list<FramePipeline::WPtr> data_dests_;
        std::mutex data_dests_mutex_;

        int groupId_ = 0;
        long outputTick_;
        int outputFpsCounter_;

        int inputFps_;
        int destinationFps_;
        
    private:
        std::list<FramePipeline::WPtr> audio_srcs_;
        std::mutex audio_src_mutex_;
        std::list<FramePipeline::WPtr> video_srcs_;
        std::mutex video_src_mutex_;
        std::list<FramePipeline::WPtr> data_srcs_;
        std::mutex data_src_mutex_;

        int inputFpsCounter_;
        long inputTick_;

        int destinationFpsCounter_;
        long destinationTick_;

        long timeoutTick_ = 0;
        long timeoutTick4Pool_ = 0;
        void* param_ = nullptr;
        void* param4Pool_ = nullptr;
    };
}

#endif