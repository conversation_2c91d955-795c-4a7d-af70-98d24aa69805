#include <MediaManagerInterface.h>
#include <hv/EventLoop.h>

static std::string sdpTemplate = R"(v=0
o=- 0 0 IN IP4 127.0.0.1
s=
t=0 0
m=audio 20000 RTP/AVPF 8 111
c=IN IP4 127.0.0.1
a=rtpmap:8 PCMA/8000
a=rtpmap:111 opus/48000
a=ptime:20
a=sendrecv
m=video 20002 RTP/AVPF 96 98
c=IN IP4 127.0.0.1
a=rtpmap:96 H264/90000
a=rtpmap:98 H265/90000
a=sendrecv
)";

using namespace panocom;

//int main(int argc, char **argv)
//{
//    auto loop = std::make_shared<hv::EventLoop>();
//
//    MediaManagerInterface mmi;
//    mmi.CreateSession("0");
//    std::string sdp;
//    int ret = mmi.GetLocalSDP("0", sdp, "{\"video\": true}");
//    printf("GetLocalSDP [%s] ret=[%d]\n", sdp.c_str(), ret);
//    loop->setTimeout(3000, [&mmi](hv::TimerID id) {
//        std::string sdp;
//        int ret = mmi.SetRemoteSDP("0", sdpTemplate, sdp);
//    });
//    loop->run();
//    return 0;
//}


int main(int argc, char** argv)
{
    auto loop = std::make_shared<hv::EventLoop>();

    MediaManagerInterface mmi;
    mmi.CreateSession("0");
    mmi.CreateSession("1");
    std::string localSdp;
    std::string remoteSdp;
    int ret = mmi.GetLocalSDP("0", localSdp, "{\"video\": true}");
    ret = mmi.SetRemoteSDP("1", localSdp, remoteSdp, "{\"video\": true}");
    ret = mmi.SetRemoteSDP("0", remoteSdp, localSdp);
    loop->run();
    return 0;
}