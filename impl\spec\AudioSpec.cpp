#include "AudioSpec.h"
#include "Frame.h"
#include "Utils.h"
#include <json.hpp>
#include <ljcore/jlog.h>

using namespace panocom;

extern int mmi_record_audio;
extern int mmi_print_state;

std::string AudioSpec::devName_;
int AudioSpec::pcmFd_ = -1;
int AudioSpec::maxChn_ = 4;
std::vector<int> AudioSpec::chnState_;
std::mutex AudioSpec::mutex_;

void AudioSpec::openDev(int dev)
{
    std::unique_lock<std::mutex> locker(mutex_);
    if (pcmFd_ < 0)
    {
        jinfo("AudioSpec openDev %s dev[%d]", devName_.c_str(), dev);
        pcmFd_ = openPCM(devName_.c_str());
        if (pcmFd_ < 0)
        {
            jerror("%s open %s fail", __FUNCTION__, devName_.c_str());
            return;
        }
        else
        {
            for (size_t i = 0; i < maxChn_; i++)
            {
                Set_Type(i, IOCTL_CMD_PCM_TYPE_HIFI);
                chnState_.push_back(0);
            }
        }
    }
    if (dev >= 0 && dev < chnState_.size())
    {
        chnState_[dev]++;
        jinfo("AudioSpec openDev start dev[%d] ref[%d]", dev, chnState_[dev]);
    }
}

void AudioSpec::closeDev(int dev)
{
    std::unique_lock<std::mutex> locker(mutex_);
    if (dev >= 0 && dev < chnState_.size())
    {
        if (chnState_[dev])
            chnState_[dev]--;
        jinfo("AudioSpec closeDev stop dev[%d] ref[%d]", dev, chnState_[dev]);
    }
    for (size_t i = 0; i < chnState_.size(); i++)
    {
        if (chnState_[i])
        {
            return;
        }
    }
    chnState_.clear();
    if (pcmFd_ >= 0)
    {
        jinfo("AudioSpec closeDev closePCM");
        closePCM();
        pcmFd_ = -1;
    }
}

AudioSpec::AudioSpec(const std::string &jsonParams) : debug_mode_(0), ref_count(0), enable_volume_(true), volume_(0)
{
    FN_BEGIN;
    jinfo("AudioSpec %s", jsonParams.c_str());
    name_ = "AudioSpec";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    {
        std::unique_lock<std::mutex> locker(mutex_);
        if (devName_ == "")
        {
            devName_ = "/dev/pcm_dev0";
            if (j.contains("devName"))
            {
                devName_ = j["devName"];
            }
            maxChn_ = 4;
            if (j.contains("maxChn"))
            {
                maxChn_ = j["maxChn"];
            }
        }
    }
    
    dev_ = 0;
    if (j.contains("dev"))
    {
        dev_ = j["dev"];
    }
    if (j.contains("ptime"))
    {
        ptime_ = j["ptime"];
    }
    samplerate_ = 16000;
    if (j.contains("samplerate"))
    {
        samplerate_ = j["samplerate"];
    }
    channel_ = 1;
    if (j.contains("channel")) {
        channel_ = j["channel"];
    }
    renderOnly_ = false;
    if (j.contains("RenderOnly"))
    {
        renderOnly_ = j["RenderOnly"];
    }
    if (j.contains("debug")) debug_mode_ = j["debug"];
    fmt_ = Frame::getPCMFormat(channel_, samplerate_);
    jinfo("create AudioSpec %s dev[%d]", devName_.c_str(), dev_);
    start();
    FN_END;
}

AudioSpec::~AudioSpec()
{
    FN_BEGIN;
    stop();
    FN_END;
}

void AudioSpec::start()
{
    // ref_count++;
    // jinfo("AudioSpec::start ref %d", ref_count);
    if (readThread_.isRunning() || writeThread_.isRunning()) return;
    FN_BEGIN;
    openDev(dev_);
    if (!renderOnly_)
    {
        readThread_.loop()->setInterval(2, [this](hv::TimerID id){
            if (!isSetReadThreadAffinity)
            {
                setThreadAffinity(6);
                isSetReadThreadAffinity = true;
            }
            char buffer[320] = { 0 };
            int ret = ReadPcm(dev_, buffer, 320);
            if (ret > 0)
            {
                bbuf_.WriteBytes(buffer, ret);
                if (mmi_record_audio)
                {
                    if (!sendf)
                    {
                        std::string fileName = name_ + std::to_string(samplerate_) + "-send-" + std::to_string((long)this) + ".pcm";
                        sendf = fopen(fileName.c_str(), "w+b");
                    }
                    if (sendf)
                    {
                        fwrite(buffer, 1, ret, sendf);
                    }
                }
            } 
            int sampleBytes = samplerate_ / 1000 * ptime_ * 2;
            while (bbuf_.Length() >= sampleBytes)
            {
                std::shared_ptr<Frame> f = Frame::CreateFrame((FrameFormat)fmt_);
                f->createFrameBuffer(sampleBytes);
                f->setGroupId(dev_);
                memcpy(f->getFrameBuffer(), bbuf_.Data(), sampleBytes);
                f->setGroupId(getGroupId());
                deliverFrame(f);
                if (notifycallbacks_.find("AudioCapturerStatus") != notifycallbacks_.end() && notifycallbacks_["AudioCapturerStatus"].cb) {
                    volume_ = quantize_volume(caculateRMS((int16_t *)bbuf_.Data(), sampleBytes / 2));
                    if (notifycallbacksThread_.isRunning()) {
                        notifycallbacksThread_.loop()->runInLoop([this](){
                            nlohmann::json notify;
                            notify["deviceId"] = dev_;
                            notify["samplerate"] = samplerate_;
                            notify["channel"] = channel_;
                            notify["fmt"] = fmt_;
                            notify["ptime"] = ptime_;
                            notify["volume"] = volume_;
                            if (notifycallbacks_.find("AudioCapturerStatus") != notifycallbacks_.end() && notifycallbacks_["AudioCapturerStatus"].cb) {
                                notifycallbacks_["AudioCapturerStatus"].cb("AudioCapturerStatus", notify.dump(), notifycallbacks_["AudioCapturerStatus"].param);
                            }
                        });
                    }  
                }
                bbuf_.Consume(sampleBytes);
                printOutputStatus("AudioSpec");
            }
        });

        readThread_.start();
    }
    
    writeThread_.start();
    notifycallbacksThread_.start();
    FN_END;
}

void AudioSpec::stop()
{
    // ref_count--;
    // jinfo("AudioSpec::stop ref[%p] %d", this, ref_count);
    // if (ref_count > 0) return;
    if (!readThread_.isRunning() && !writeThread_.isRunning()) return;
    if (notifycallbacksThread_.isRunning()) {
        notifycallbacksThread_.stop(true);
        notifycallbacksThread_.join();
    }
    ref_count = 0;
    readThread_.stop(true);
    writeThread_.stop(true);
    readThread_.join();
    writeThread_.join();
    if (check_timeout_thread_.isRunning()) {
        check_timeout_thread_.stop(true);
        check_timeout_thread_.join();
    }
    closeDev(dev_);
    initResampler_ = false;
    isSetReadThreadAffinity = false;
    isSetWriteThreadAffinity = false;
}

void AudioSpec::onFrame(const std::shared_ptr<Frame> &frame)
{
    int samplerate = 0;
    int chn = 0;
    if (Frame::getSamplerate(frame->getFrameFormat(), samplerate, chn))
    {
        if (!writeThread_.isRunning()) return;
        writeThread_.loop()->runInLoop([this, frame, samplerate](){
            auto f = frame;
            if (!isSetWriteThreadAffinity)
            {
                setThreadAffinity(7);
                isSetWriteThreadAffinity = true;
            }
            if (samplerate_ != samplerate)
            {
                if (!initResampler_ || source_samplerate_ != samplerate)
                {
                    source_samplerate_ = samplerate;
                    initResampler_ = true;
#ifdef WEBRTC_RESAMPLE_ANOTHER
                    resampler_.InitializeIfNeeded(samplerate, samplerate_, 1);
#else
                    resampler_.ResetIfNeeded(samplerate, samplerate_, 1);
#endif
                }
                auto frame = Frame::CreateFrame((FrameFormat)fmt_);
                frame->createFrameBuffer(f->getFrameSize() * samplerate_ / samplerate);
                memset(frame->getFrameBuffer(), 0, frame->getFrameSize());
#ifdef WEBRTC_RESAMPLE_ANOTHER
                resampler_.Resample((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2);
#else
                size_t outlen = 0;
                resampler_.Push((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2, outlen);
#endif
                f = frame;
            }
            if (pcmFd_ >= 0)
            {
                int index = 0;
                uint8_t* buf = f->getFrameBuffer();
                int bufSize = f->getFrameSize();
                while (bufSize > 0)
                {
                    int ret = WritePcm(dev_, buf, bufSize);
                    if (ret > 0)
                    {
                        if (mmi_record_audio)
                        {
                            if (!recvf)
                            {
                                std::string fileName = name_ + std::to_string(samplerate_) + "-recv-" + std::to_string((long)this) + ".pcm";
                                recvf = fopen(fileName.c_str(), "w+b");
                            }
                            if (recvf)
                            {
                                fwrite(buf, 1, ret, recvf);
                            }
                        }
                        buf += ret;
                        index += ret;
                        bufSize -= ret;
                    }
                    else
                    {
                        // jerror("WritePcm ret = %d", ret);
                        break;
                    }
                }
            }
            printInputStatus("AudioSpec");
        });
    }
    else
    {
        jerror("AudioSpec::onFrame error format[%d]", frame->getFrameFormat());
    }
}
// TODO
int AudioSpec::updateParam(const std::string& jsonParams) {
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    int dev = -1;
    if (j.contains("dev")) {
        dev = j["dev"];
    }
    if (dev != -1 && dev != dev_) {
        closeDev(dev_);
        jinfo("AudioSpec updateParams %s", jsonParams.c_str());
        jinfo("UpdateParam AudioSpec %s dev[%d]", devName_.c_str(), dev_);
        openDev(dev_);
        dev_ = dev;
    }
    return 0;
}

void AudioSpec::setFrameTimeoutNotify(int ms, void* param, const TimeoutNotifyCallback& cb) {
    if (ms <= 0) {
        jinfo("AudioSpec setFrameTimeoutNotify timeout must > 0");
        return;
    }
    FramePipeline::setFrameTimeoutNotify(ms, param, cb);
    if (renderOnly_ && !check_timeout_thread_.isRunning()) {
        check_timeout_thread_.loop()->setInterval(30, [this](hv::TimerID id) {
            checkFrameTimeout();
        });
        check_timeout_thread_.start(true);
    }
}

void AudioSpec::setFrameTimeoutNotify2(int ms, void* param, const TimeoutNotifyCallback& cb) {
	if (ms <= 0) {
		jinfo("AudioSpec setFrameTimeoutNotify timeout must > 0");
		return;
	}
	FramePipeline::setFrameTimeoutNotify2(ms, param, cb);
	if (renderOnly_ && !check_timeout_thread_.isRunning()) {
		check_timeout_thread_.loop()->setInterval(30, [this](hv::TimerID id) {
			checkFrameTimeout();
		});
		check_timeout_thread_.start(true);
	}
}

bool AudioSpec::getStatus(std::string& status) {
    nlohmann::json j;
    j["dev"] = dev_;
    j["samplerate"] = samplerate_;
    j["channel"] = channel_;
    j["fmt"] = fmt_;
    j["ptime"] = ptime_;
    j["volume"] = volume_;
    status = j.dump();
    return true;
}