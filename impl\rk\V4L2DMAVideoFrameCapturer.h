#ifndef IMPL_V4L2_V4L2DMAVIDEOFRAMECAPTURER_H_
#define IMPL_V4L2_V4L2DMAVIDEOFRAMECAPTURER_H_
#include "VideoFrameCapturer.h"

#include <future>
#include <atomic>
#include <memory>
#include <sys/epoll.h>

#include "drm_utils.h"
#include "rk_frame_buffer.h"
// #define DEBUG_RK

#ifdef DEBUG_RK
#include <fstream>
#endif
namespace panocom
{
struct FormatInfo {
    uint32_t width;
    uint32_t height;
    uint32_t rate;
};


class V4L2DMAVideoFrameCapturer : public VideoFrameCapturer
{
private:
    /* data */
public:
    V4L2DMAVideoFrameCapturer(const std::string& jsonParams);
    ~V4L2DMAVideoFrameCapturer();
    bool GetConnectStatus() override { return connected_; }
private:
    bool Init();
    bool ReinitAsync();
    bool ConfigureDevice();
    bool CleanupDevice();
    int32_t String2PixelFormat(const std::string &str) const;
    std::string PixelFormat2String(const int32_t fmt) const;

    int32_t subscribe_event(int fd, uint32_t event);
    int32_t handle_event(int fd);
private:
    int dev_;
    std::string format_str_;
    int fmt_;
    int width_;
    int height_;
    int fps_ = 0;

    // int target_width_;
    // int target_height_;

    int fd_;
    int epfd_;
    struct epoll_event ev_{0}, events_[5]{0};
    std::atomic_bool running_;
    bool is_mutiplannar_;

    std::string app_name_;

    std::future<bool> capture_future_;

    std::vector<buffer_object> bo_vec_;

    int req_count_;
    enum DeviceName {
        RK_HDMIRX, RKCIF
    };
    DeviceName device_name_;
    std::vector<std::string> dev_names_;
#ifdef DEBUG_RK
    std::ofstream ofs_;
#endif

    std::shared_ptr<RKBufferManager> frameBufferManager_;
    bool connected_ = false;
;};    
} // namespace panocom


#endif