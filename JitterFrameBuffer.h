// created by gyj 2024-3-5
#ifndef P_JitterFrameBuffer_h
#define P_JitterFrameBuffer_h

#include <memory>
#include <queue>

namespace panocom
{
    class Frame;
    class JitterFrameBuffer
    {
    public:
        JitterFrameBuffer(int initFps, int minDelay, int maxDelay, int detectFpsSec = 3);
        ~JitterFrameBuffer();

        void pushFrame(const std::shared_ptr<Frame>& f);
        std::shared_ptr<Frame> popFrame();
    
    private:
        int fps_;
        long frameCount_;
        long tick_;
        int minDelay_;
        int cacheFrameCount_;
        int maxDelay_;
        int dropMaxFrameCount_;
        int dropFrameCount_;
        bool readyPop_; 
        int detectFpsSec_;
        std::queue<std::shared_ptr<Frame>> fb_;
    };
}

#endif