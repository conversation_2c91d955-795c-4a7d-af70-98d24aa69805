#include "MediaManagerInterface.h"
#include <hv/EventLoop.h>
#include <ljcore/jlog.h>
#include <signal.h>
#include "sdptransform.hpp"

using namespace panocom;

extern int mmi_print_state;

#if 1

int main(int argc, char *argv[])
{
    signal(SIGSEGV, [](int) {
        jlog_uninit();
        (void) signal(SIGSEGV, SIG_DFL);  
    }); // 设置退出信号

    signal(SIGINT, [](int) {
        jlog_uninit();
        (void) signal(SIGINT, SIG_DFL);  
    }); // 设置退出信号

    signal(SIGTERM, [](int) {
        jlog_uninit();
        (void) signal(SIGTERM, SIG_DFL);  
    });
    mmi_print_state = 1;
    auto loop = std::make_shared<hv::EventLoop>();
    MediaManagerInterface mmi;
    mmi.CreateSession("0");
    mmi.CreateSession("1");
    std::string sdp;
    mmi.GetLocalSDP("0", sdp, "{\"video\": true, \"dtls\": true}");
    std::string sdp2;
    mmi.SetRemoteSDP("1", sdp, sdp2, "{\"video\": true, \"dtls\": true}");
    std::string sdp3;
    mmi.SetRemoteSDP("0", sdp2, sdp3);
    loop->run();
    return 0;
}

#else

std::string params = "{\"video\": true, \"dtls\": true, \"stun-server\": \"192.9.200.227\", \"stun-port\": 3478}";

int main(int argc, char *argv[])
{
    mmi_print_state = 1;
    auto loop = std::make_shared<hv::EventLoop>();
    MediaManagerInterface mmi;
    mmi.CreateSession("0");
    mmi.CreateSession("1");
    mmi.GetLocalSDP("0", params, [&mmi, &loop](const std::string& id, const std::string& sdp, int ret){
        jinfo("0 GetLocalSDP sdp=\n%s", sdp.c_str());
        loop->runInLoop([sdp, &mmi, &loop](){
            jinfo("1 SetRemoteSDP sdp=\n%s", sdp.c_str());
            mmi.SetRemoteSDP("1", sdp, params, [&mmi, &loop](const std::string& id, const std::string& sdp, int ret){
                loop->runInLoop([sdp, &mmi](){
                    jinfo("0 SetRemoteSDP sdp=\n%s", sdp.c_str());
                    mmi.SetRemoteSDP("0", sdp, params, [](const std::string& id, const std::string& sdp, int ret){});
                });
            });
        });
    });
    loop->run();
    return 0;
}

#endif