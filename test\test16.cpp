#include "MediaManagerInterface.h"
#include <hv/EventLoop.h>
#include <ljcore/jlog.h>

static std::string sdpTemplate = R"(v=0
o=- 0 0 IN IP4 *************
s=
t=0 0
m=audio 10000 RTP/AVPF 8 111
c=IN IP4 *************
a=rtpmap:8 PCMA/8000
a=rtpmap:111 opus/48000
a=ptime:20
a=sendrecv
m=video 10002 RTP/AVPF 96 98
c=IN IP4 *************
a=rtpmap:96 H264/90000
a=rtpmap:98 H265/90000
a=sendrecv
)";

using namespace panocom;

int main(int argc, char *argv[])
{
    auto loop = std::make_shared<hv::EventLoop>();

    jlog_init(nullptr);
    MediaManagerInterface mmi;
    if (argc > 1)
    {
        for (size_t i = 0; i < 10; i++)
        {
            std::string sdp;
            int ret = mmi.SetRemoteSDP(std::to_string(i), sdpTemplate, sdp);
            jinfo("SetRemoteSDP(%d) ret(%d): \n%s", i, ret, sdp.c_str());
        }
    }
    else
    {
        for (size_t i = 0; i < 10; i++)
        {
            std::string sdp;
            int ret = mmi.GetLocalSDP(std::to_string(i), sdp);
            jinfo("GetLocalSDP(%d) ret(%d): \n%s", i, ret, sdp.c_str());
        }
    }
    loop->run();
    return 0;
}