// created by gyj 2024-3-28
#ifndef P_Sdl2AudioFrameCapturer_h
#define P_Sdl2AudioFrameCapturer_h

#include "AudioFrameCapturer.h"
#include <SDL2/SDL.h>

namespace panocom
{
    class Sdl2AudioFrameCapturer : public AudioFrameCapturer
    {
    public:
        Sdl2AudioFrameCapturer(const std::string& jsonParams);
        ~Sdl2AudioFrameCapturer() override;

        void deliverFrame(const uint8_t* data, int len);
    private:
        SDL_AudioDeviceID devid_ = 0;
        FILE *pf_ = nullptr;
    };
}

#endif