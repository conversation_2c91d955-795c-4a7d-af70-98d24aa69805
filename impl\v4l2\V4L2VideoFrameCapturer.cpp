#include "V4L2VideoFrameCapturer.h"
#include "Frame.h"
#include "Utils.h"
#include <json.hpp>
#include <ljcore/jlog.h>

using namespace panocom;

V4L2VideoFrameCapturer::V4L2VideoFrameCapturer(const std::string& jsonParams)
{
    FN_BEGIN;
    name_ = "V4L2VideoFrameCapturer";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    dev_ = 0;
    if (j.contains("dev"))
    {
        dev_ = j["dev"];
        if (dev_ < 0)
        {
            dev_ = 0;
        }
    }
    format_ = "mjpeg";
    if (j.contains("format"))
    {
        format_ = j["format"];
    }
    width_ = 1280;
    if (j.contains("width"))
    {
        width_ = j["width"];
    }
    height_ = 720;
    if (j.contains("height"))
    {
        height_ = j["height"];
    }
    fps_ = 30;
    if (j.contains("fps"))
    {
        fps_ = j["fps"];
    }
    fmt_ = V4L2_PIX_FMT_MJPEG;
    if (format_ == "mjpeg")
    {
        fmt_ = V4L2_PIX_FMT_MJPEG;
    }
    else if (format_ == "yuyv")
    {
        fmt_ = V4L2_PIX_FMT_YUYV;
    }
    start();
    FN_END;
}

V4L2VideoFrameCapturer::~V4L2VideoFrameCapturer()
{
    FN_BEGIN;
    stop();
    FN_END;
}

void V4L2VideoFrameCapturer::start()
{
    if (thread_) return;
    FN_BEGIN;
    V4l2IoType ioTypeIn = IOTYPE_MMAP;
    std::string dev = "/dev/video" + std::to_string(dev_);
    V4L2DeviceParameters param(dev.c_str(), fmt_, width_, height_, fps_, ioTypeIn, 1);
    videoCapture_.reset(V4l2Capture::create(param));
    if (!videoCapture_)
    {
        jerror("V4l2Capture::create(%s) fail", dev.c_str());
        return;
    }
    frameBufferManager_ = std::make_unique<FrameBufferManager>(5, videoCapture_->getBufferSize(), FRAME_FORMAT_COMMON_VIDEO);

    thread_ = std::make_unique<std::thread>([this]{
        running_ = true;
        timeval tv;
        while (running_)
        {
            tv.tv_sec = 1;
            tv.tv_usec = 0;
            int ret = videoCapture_->isReadable(&tv);
            if (ret == 1)
            {
                std::shared_ptr<Frame> f = frameBufferManager_->getFrame();
                if (f)
                {
                    int rsize = videoCapture_->read((char*)f->getFrameBuffer(), f->getFrameBufferSize());
                    if (rsize == -1)
                    {
                        running_ = false;
                    }
                    else
                    {
                        //jinfo("v4l2 read %d", rsize);
                        f->setFrameSize(rsize);
                        f->setGroupId(getGroupId());
                        deliverFrame(f);
                        printOutputStatus("v4l2");
                    }
                }
            }
            else if (ret == -1)
            {
                running_ = false;
            }
        }
    });
    FN_END;
}

void V4L2VideoFrameCapturer::stop()
{
    FN_BEGIN;
    if (running_)
    {
        running_ = false;
        thread_->join();
        thread_.reset();
        videoCapture_.reset();
    }
    FN_END;
}