#include "CuCommon.h"
#include <vector>
#include <ljcore/jlog.h>

#include <stdio.h>
#include <fcntl.h>
#include <sys/mman.h>
#include <stdio.h>
#include <errno.h>
#include <sys/wait.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <memory.h>
#include <sys/un.h>
#include <unistd.h>

using namespace panocom;

CuCommon& CuCommon::instance()
{
    static CuCommon inst;
    return inst;
}

void CuCommon::init()
{
    std::unique_lock<std::mutex> lock(mutex_);
    // TODO: cuInit有可能，inited_未使用，存在隐患，使用cuda API时应判断inited_状态
    if (!inited_)
    {
        auto ret = cuInit(0);
        if (ret != CUDA_SUCCESS) {
            const char* err_message = nullptr;
            cuGetErrorString(ret, &err_message);
            jerror("cuInit failed. ret = %d, message: %s", ret, err_message);
        } else {
            inited_ = true;
            jerror("cuInit success!!!!!!!!!!!!!!!");
        }
    }
}

void CuCommon::free()
{

}

std::shared_ptr<CUcontext> CuCommon::createCudaContext(int gpuIndex)
{
    init();
    // if (!inited_) return nullptr;
    std::shared_ptr<CUcontext> ret = std::shared_ptr<CUcontext>(new CUcontext, [](CUcontext* ctx){
        cuCtxDestroy(*ctx);
        delete ctx;
    });
    CUdevice cuDevice = 0;
    cuDeviceGet(&cuDevice, gpuIndex);
    char szDeviceName[80];
    cuDeviceGetName(szDeviceName, sizeof(szDeviceName), cuDevice);
    cuCtxCreate(ret.get(), 0, cuDevice);
    return ret;
}

std::shared_ptr<CUcontext> CuCommon::getCudaContext(uint32_t id, int gpuIndex)
{
    // if (!inited_) return nullptr;
    std::unique_lock<std::mutex> lock(mutexCtx_);
    if (ctxs_[gpuIndex].find(id) == ctxs_[gpuIndex].end() || ctxs_[gpuIndex][id] == nullptr)
    {
        ctxs_[gpuIndex][id] = createCudaContext(gpuIndex);
    }
    return ctxs_[gpuIndex][id];
}

uint32_t CuCommon::GetWidthInBytes(const NV_ENC_BUFFER_FORMAT bufferFormat, const uint32_t width)
{
    switch (bufferFormat) {
    case NV_ENC_BUFFER_FORMAT_NV12:
    case NV_ENC_BUFFER_FORMAT_YV12:
    case NV_ENC_BUFFER_FORMAT_IYUV:
    case NV_ENC_BUFFER_FORMAT_YUV444:
        return width;
    case NV_ENC_BUFFER_FORMAT_YUV420_10BIT:
    case NV_ENC_BUFFER_FORMAT_YUV444_10BIT:
        return width * 2;
    case NV_ENC_BUFFER_FORMAT_ARGB:
    case NV_ENC_BUFFER_FORMAT_ARGB10:
    case NV_ENC_BUFFER_FORMAT_AYUV:
    case NV_ENC_BUFFER_FORMAT_ABGR:
    case NV_ENC_BUFFER_FORMAT_ABGR10:
        return width * 4;
    default:
        jinfo("Invalid Buffer format %d", bufferFormat);
        return 0;
    }
}

uint32_t CuCommon::GetNumChromaPlanes(const NV_ENC_BUFFER_FORMAT bufferFormat)
{
    switch (bufferFormat) 
    {
    case NV_ENC_BUFFER_FORMAT_NV12:
    case NV_ENC_BUFFER_FORMAT_YUV420_10BIT:
        return 1;
    case NV_ENC_BUFFER_FORMAT_YV12:
    case NV_ENC_BUFFER_FORMAT_IYUV:
    case NV_ENC_BUFFER_FORMAT_YUV444:
    case NV_ENC_BUFFER_FORMAT_YUV444_10BIT:
        return 2;
    case NV_ENC_BUFFER_FORMAT_ARGB:
    case NV_ENC_BUFFER_FORMAT_ARGB10:
    case NV_ENC_BUFFER_FORMAT_AYUV:
    case NV_ENC_BUFFER_FORMAT_ABGR:
    case NV_ENC_BUFFER_FORMAT_ABGR10:
        return 0;
    default:
        jinfo("Invalid Buffer format %d", bufferFormat);
        return -1;
    }
}

uint32_t CuCommon::GetChromaPitch(const NV_ENC_BUFFER_FORMAT bufferFormat,const uint32_t lumaPitch)
{
    switch (bufferFormat)
    {
    case NV_ENC_BUFFER_FORMAT_NV12:
    case NV_ENC_BUFFER_FORMAT_YUV420_10BIT:
    case NV_ENC_BUFFER_FORMAT_YUV444:
    case NV_ENC_BUFFER_FORMAT_YUV444_10BIT:
        return lumaPitch;
    case NV_ENC_BUFFER_FORMAT_YV12:
    case NV_ENC_BUFFER_FORMAT_IYUV:
        return (lumaPitch + 1)/2;
    case NV_ENC_BUFFER_FORMAT_ARGB:
    case NV_ENC_BUFFER_FORMAT_ARGB10:
    case NV_ENC_BUFFER_FORMAT_AYUV:
    case NV_ENC_BUFFER_FORMAT_ABGR:
    case NV_ENC_BUFFER_FORMAT_ABGR10:
        return 0;
    default:
        jinfo("Invalid Buffer format %d", bufferFormat);
        return -1;
    }
}

void CuCommon::GetChromaSubPlaneOffsets(const NV_ENC_BUFFER_FORMAT bufferFormat, const uint32_t pitch, const uint32_t height, std::vector<uint32_t>& chromaOffsets)
{
    chromaOffsets.clear();
    switch (bufferFormat)
    {
    case NV_ENC_BUFFER_FORMAT_NV12:
    case NV_ENC_BUFFER_FORMAT_YUV420_10BIT:
        chromaOffsets.push_back(pitch * height);
        return;
    case NV_ENC_BUFFER_FORMAT_YV12:
    case NV_ENC_BUFFER_FORMAT_IYUV:
        chromaOffsets.push_back(pitch * height);
        chromaOffsets.push_back(chromaOffsets[0] + (GetChromaPitch(bufferFormat, pitch) * GetChromaHeight(bufferFormat, height)));
        return;
    case NV_ENC_BUFFER_FORMAT_YUV444:
    case NV_ENC_BUFFER_FORMAT_YUV444_10BIT:
        chromaOffsets.push_back(pitch * height);
        chromaOffsets.push_back(chromaOffsets[0] + (pitch * height));
        return;
    case NV_ENC_BUFFER_FORMAT_ARGB:
    case NV_ENC_BUFFER_FORMAT_ARGB10:
    case NV_ENC_BUFFER_FORMAT_AYUV:
    case NV_ENC_BUFFER_FORMAT_ABGR:
    case NV_ENC_BUFFER_FORMAT_ABGR10:
        return;
    default:
        jinfo("Invalid Buffer format %d", bufferFormat);
        return;
    }
}

uint32_t CuCommon::GetChromaHeight(const NV_ENC_BUFFER_FORMAT bufferFormat, const uint32_t lumaHeight)
{
    switch (bufferFormat)
    {
    case NV_ENC_BUFFER_FORMAT_YV12:
    case NV_ENC_BUFFER_FORMAT_IYUV:
    case NV_ENC_BUFFER_FORMAT_NV12:
    case NV_ENC_BUFFER_FORMAT_YUV420_10BIT:
        return (lumaHeight + 1)/2;
    case NV_ENC_BUFFER_FORMAT_YUV444:
    case NV_ENC_BUFFER_FORMAT_YUV444_10BIT:
        return lumaHeight;
    case NV_ENC_BUFFER_FORMAT_ARGB:
    case NV_ENC_BUFFER_FORMAT_ARGB10:
    case NV_ENC_BUFFER_FORMAT_AYUV:
    case NV_ENC_BUFFER_FORMAT_ABGR:
    case NV_ENC_BUFFER_FORMAT_ABGR10:
        return 0;
    default:
        jinfo("Invalid Buffer format %d", bufferFormat);
        return 0;
    }
}

uint32_t CuCommon::GetChromaWidthInBytes(const NV_ENC_BUFFER_FORMAT bufferFormat, const uint32_t lumaWidth)
{
    switch (bufferFormat)
    {
    case NV_ENC_BUFFER_FORMAT_YV12:
    case NV_ENC_BUFFER_FORMAT_IYUV:
        return (lumaWidth + 1) / 2;
    case NV_ENC_BUFFER_FORMAT_NV12:
        return lumaWidth;
    case NV_ENC_BUFFER_FORMAT_YUV420_10BIT:
        return 2 * lumaWidth;
    case NV_ENC_BUFFER_FORMAT_YUV444:
        return lumaWidth;
    case NV_ENC_BUFFER_FORMAT_YUV444_10BIT:
        return 2 * lumaWidth;
    case NV_ENC_BUFFER_FORMAT_ARGB:
    case NV_ENC_BUFFER_FORMAT_ARGB10:
    case NV_ENC_BUFFER_FORMAT_AYUV:
    case NV_ENC_BUFFER_FORMAT_ABGR:
    case NV_ENC_BUFFER_FORMAT_ABGR10:
        return 0;
    default:
        jinfo("Invalid Buffer format %d", bufferFormat);
        return 0;
    }
}

uint32_t CuCommon::GetFrameSize(const NV_ENC_BUFFER_FORMAT bufferFormat, const uint32_t width, const uint32_t height)
{
    switch (bufferFormat)
    {
    case NV_ENC_BUFFER_FORMAT_YV12:
    case NV_ENC_BUFFER_FORMAT_IYUV:
    case NV_ENC_BUFFER_FORMAT_NV12:
        return width * (height + (height + 1) / 2);
    case NV_ENC_BUFFER_FORMAT_YUV420_10BIT:
        return 2 * width * (height + (height + 1) / 2);
    case NV_ENC_BUFFER_FORMAT_YUV444:
        return width * height * 3;
    case NV_ENC_BUFFER_FORMAT_YUV444_10BIT:
        return 2 * width * height * 3;
    case NV_ENC_BUFFER_FORMAT_ARGB:
    case NV_ENC_BUFFER_FORMAT_ARGB10:
    case NV_ENC_BUFFER_FORMAT_AYUV:
    case NV_ENC_BUFFER_FORMAT_ABGR:
    case NV_ENC_BUFFER_FORMAT_ABGR10:
        return 4 * width * height;
    default:
        jinfo("Invalid Buffer format", NV_ENC_ERR_INVALID_PARAM);
        return 0;
    }
}

SharedMemory::SharedMemory() : addr_(nullptr), size_(0), shmFd_(-1)
{

}

SharedMemory::~SharedMemory()
{

}

int SharedMemory::sharedMemoryCreate(const char *name, size_t sz)
{
    int status = 0;
    size_ = sz;
    shmFd_ = shm_open(name, O_RDWR | O_CREAT, 0777);
    if (shmFd_ < 0)
    {
        jinfo("sharedMemoryCreate %d", errno);
        return errno;
    }

    status = ftruncate(shmFd_, sz);
    if (status != 0)
    {
        jinfo("ftruncate %d", status);
        return status;
    }

    addr_ = mmap(0, sz, PROT_READ | PROT_WRITE, MAP_SHARED, shmFd_, 0);
    if (addr_ == NULL)
    {
        jinfo("mmap %d", errno);
        return errno;
    }
    memset(addr_, 0, sz);

    return 0;
}

int SharedMemory::sharedMemoryOpen(const char *name, size_t sz)
{
    size_ = sz;

    shmFd_ = shm_open(name, O_RDWR, 0777);
    if (shmFd_ < 0)
    {
        return errno;
    }
    addr_ = mmap(0, sz, PROT_READ | PROT_WRITE, MAP_SHARED, shmFd_, 0);
    if (addr_ == NULL)
    {
        return errno;
    }
    return 0;
}

void SharedMemory::sharedMemoryClose()
{
    if (addr_)
    {
        munmap(addr_, size_);
        addr_ = nullptr;
    }
    if (shmFd_)
    {
        close(shmFd_);
        shmFd_ = -1;
    }
}