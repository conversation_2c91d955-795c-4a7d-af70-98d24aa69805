#ifndef P_CuOpenGLRender_h
#define P_CuOpenGLRender_h

#include <cuda.h>
#include <cudaGL.h>
#include <mutex>
#include <map>

#include "OpenGLRender.h"

namespace panocom
{
    class Frame;
    class CuOpenGLRender: public OpenGLRender
    {
    public:
        CuOpenGLRender(const std::string& jsonParams);
        ~CuOpenGLRender() override;
        // 除addFrameRect外videoId无作用
        int addFrameRect(int videoId, int areaId, int x, int y, int z, int w, int h, int cudaId, int gpuIndex) override;
        int changeFrameRect(int videoId, int areaId, int x, int y, int z, int w, int h) override;
        int removeFrameRect(int videoId, int areaId) override;
        int maskFrameRect(int videoId, int areaId, bool mask, int r = 0, int g = 0, int b = 0) override;
        int setSrcRectROI(int videoId, int areaId, int x, int y, int w, int h) override;
        void setPBO(int fd, int width, int height) override;
        bool prepareFrame() override;
        void onFrame(const std::shared_ptr<Frame> &f) override;
        int width() override { return width_; }
        int height() override { return height_; }

        void start() override;
        void stop() override;
    private:
        bool registerTex();
        void unRegisterTex();
        CUdeviceptr getTexBuffer(int cudaId, int gpuIndex);
        void releaseTexBuffer(int cudaId, int gpuIndex);
    private:
        struct NewRenderArea
        {
            int x;
            int y;
            int z;
            int w;
            int h;
            double roi_x_ratio = 0.0;
            double roi_y_ratio = 0.0;
            double roi_w_ratio = 1.0;
            double roi_h_ratio = 1.0;
        };
        
        struct RenderArea
        {
            NewRenderArea newArea;
            int id = -1;
            int x;
            int y;
            int z;
            int w;
            int h;
            int cudaId;
            int gpuIndex;
            int videoId;
            bool changed;
            bool mask;
            int r;
            int g;
            int b;
            std::shared_ptr<Frame> frame;
            CUdeviceptr rgb = 0;
            int rgbsize = 0;
            int rgb_width;
            int rgb_height;

            double roi_x_ratio = 0.0;
            double roi_y_ratio = 0.0;
            double roi_w_ratio = 1.0;
            double roi_h_ratio = 1.0;

            bool cropped = false;
        };
        std::map<int, RenderArea> renderArea_;

        std::map<int, std::list<RenderArea*>> renderAreaPriority_;
        struct TexResource
        {
            //CUgraphicsResource cuda_tex_resource;
            CUdeviceptr ptr;
        };
        std::map<int, TexResource> texResource_;
        CUgraphicsResource cuda_tex_resource_[2];
        
        std::mutex mutex_;
        int maxWidth_ = 0;
        int maxHeight_ = 0;
        int width_ = 0;
        int height_ = 0;
        int fd_ = -1;
        int dev_ = 0;
        int cudaId_ = 0;
        int gpuIndex_ = 0;
        bool stopped = false;
    };
}

#endif