#include "widget.h"
#include "QCuVideoWidget.h"
#include "./ui_widget.h"
#include <QResizeEvent>

Widget::Widget(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::Widget)
{
    ui->setupUi(this);
    resize(1920, 1080);

    int width = 1920;
    int height = 1080;
    wid = new panocom::QCuVideoWidget(0, this);
    wid->setGeometry(0, 0, width, height);
    wid->show();
}

Widget::~Widget()
{
    delete ui;
}

void Widget::resizeEvent(QResizeEvent *event)
{
    int width = event->size().width();
    int height = event->size().height();
    wid->setGeometry(0, 0, width, height);
}