#ifndef P_CuVideoDecoder_h
#define P_CuVideoDecoder_h

#include <nvcuvid.h>
#include <hv/EventLoopThread.h>
#include "VideoFrameDecoder.h"

namespace panocom
{
    struct Rect
    {
        int l, t, r, b;
    };

    class CuFrameBufferManager;
    // TODO: reconfigure for scale and crop function
    class CuVideoDecoder : public VideoFrameDecoder
    {

    public:
        CuVideoDecoder(const std::string &jsonParams);
        ~CuVideoDecoder() override;

        void onFrame(const std::shared_ptr<Frame> &frame) override;
        void start() override;
        void stop() override;
    private:
        /**
         *   @brief  Callback function to be registered for getting a callback when decoding of sequence starts
         */
        static int CUDAAPI HandleVideoSequenceProc(void *pUserData, CUVIDEOFORMAT *pVideoFormat) { return ((CuVideoDecoder *)pUserData)->HandleVideoSequence(pVideoFormat); }

        /**
         *   @brief  Callback function to be registered for getting a callback when a decoded frame is ready to be decoded
         */
        static int CUDAAPI HandlePictureDecodeProc(void *pUserData, CUVIDPICPARAMS *pPicParams) { return ((CuVideoDecoder *)pUserData)->HandlePictureDecode(pPicParams); }

        /**
         *   @brief  Callback function to be registered for getting a callback when a decoded frame is available for display
         */
        static int CUDAAPI HandlePictureDisplayProc(void *pUserData, CUVIDPARSERDISPINFO *pDispInfo) { return ((CuVideoDecoder *)pUserData)->HandlePictureDisplay(pDispInfo); }

        /**
        *   @brief  This function gets called when a sequence is ready to be decoded. The function also gets called
            when there is format change
        */
        int HandleVideoSequence(CUVIDEOFORMAT *pVideoFormat);

        /**
         *   @brief  This function gets called when a picture is ready to be decoded. cuvidDecodePicture is called from this function
         *   to decode the picture
         */
        int HandlePictureDecode(CUVIDPICPARAMS *pPicParams);

        /**
        *   @brief  This function gets called after a picture is decoded and available for display. Frames are fetched and stored in
            internal buffer
        */
        int HandlePictureDisplay(CUVIDPARSERDISPINFO *pDispInfo);
    private:
        hv::EventLoopThread thread_;
        int cudaId_ = 0;
        CUvideoctxlock ctxLock_;
        CUvideoparser parser_ = NULL;
        std::shared_ptr<CUvideodecoder> decoder_;
        // dimension of the output
        unsigned int width_ = 0, lumaHeight_ = 0, chromaHeight_ = 0;
        unsigned int hStride_ = 0;
        unsigned int vStride_ = 0;
        unsigned int numChromaPlanes_ = 0;
        cudaVideoChromaFormat chromaFormat_ = cudaVideoChromaFormat_420;
        cudaVideoSurfaceFormat outputFormat_ = cudaVideoSurfaceFormat_NV12;
        int bitDepthMinus8_ = 0;
        int BPP_ = 1;
        CUVIDEOFORMAT videoFormat_ = {};

        unsigned int maxWidth_ = 0, maxHeight_ = 0;
        Rect displayRect_ = {};
        int fmt_ = 0;
        //FILE* pf_ = nullptr;
        int gpuIndex_ = 0;

        int frames_recieved_ = 0;
        int frames_to_decode_ = 0;
        int frames_decoded_ = 0;
        std::chrono::steady_clock::time_point last_tick_;
    };
}

#endif