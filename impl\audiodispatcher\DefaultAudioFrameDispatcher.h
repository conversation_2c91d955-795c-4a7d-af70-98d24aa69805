#ifndef IMPL_AUDIODISPATCHER_DEFAULTAUDIODISPATCHER_H_
#define IMPL_AUDIODISPATCHER_DEFAULTAUDIODISPATCHER_H_
#include "AudioFrameDispatcher.h"

namespace panocom
{
class DefaultAudioFrameDispatcher : public AudioFrameDispatcher
{
public:
    DefaultAudioFrameDispatcher(const std::string &jsonParams);
    ~DefaultAudioFrameDispatcher();
    void onFrame(const std::shared_ptr<Frame> &frame) override;
};    
} // namespace panocom

#endif