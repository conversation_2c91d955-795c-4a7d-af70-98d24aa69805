#include "AudioFrameCapturer.h"
#ifdef USE_SDL2
#include "sdl2/Sdl2AudioFrameCapturer.h"
#endif
#ifdef USE_SPEC
#include "spec/AudioSpecMulticaster.h"
#include "spec/AudioSpec.h"
#include "spec/MixedAudioSpec.h"
#include "spec/AudioSpecMulticasterSingle.h"
#include "spec/AudioSpecMulticasterMulti.h"
#endif
#ifdef USE_TINYALSA
#include "tinyalsa/TinyALSACapturer.h"
#endif
#include <algorithm>
#ifdef RK_PLATFORM
#include "rk/RKAudioFrameCapturer.h"
#endif
#ifdef USE_UART
#include "uart/UartReader.h"
#endif
#ifdef USE_ALSA
#include "alsa/AlsaAudioFrameCapturer.h"
#endif
#include <json.hpp>


using namespace panocom;

std::vector<std::string> AudioFrameCapturer::capturers_;

bool AudioFrameCapturer::isRegistered = false;

void AudioFrameCapturer::RegistCapturers()
{
    if (!isRegistered)
    {
#ifdef USE_SDL2
        capturers_.push_back("Sdl2AudioFrameCapturer");
#endif
#ifdef USE_SPEC
        capturers_.push_back("AudioSpecMulticaster");
        capturers_.push_back("AudioSpec");
        capturers_.push_back("MixedAudioSpec");
        capturers_.push_back("AudioSpecMulticasterSingle");
        capturers_.push_back("AudioSpecMulticasterMulti");
#endif
#ifdef RK_PLATFORM
        capturers_.push_back("RKAudioFrameCapturer");
#endif
#ifdef USE_ALSA
        capturers_.push_back("AlsaAudioFrameCapturer");
#endif
#ifdef USE_TINYALSA
        capturers_.push_back("TinyALSACapturer");
#endif
#ifdef USE_UART
        capturers_.push_back("UartReader");
#endif
        isRegistered = true;
    }
}

std::vector<std::string> &AudioFrameCapturer::GetSupportCapturers()
{
    RegistCapturers();
    return capturers_;
}

bool AudioFrameCapturer::IsSupportCapturer(const std::string &captureName)
{
    RegistCapturers();
    return std::find(capturers_.begin(), capturers_.end(), captureName) != capturers_.end();
}

std::shared_ptr<AudioFrameCapturer> AudioFrameCapturer::CreateAudioCapturer(const std::string &captureName, const std::string &jsonParams)
{
    jinfo("CreateAudioCapturer %s: %s", captureName.c_str(), jsonParams.c_str());
    RegistCapturers();
    std::shared_ptr<AudioFrameCapturer> ret;
#ifdef USE_SDL2
    if (captureName == "Sdl2AudioFrameCapturer")
    {
        ret = std::make_shared<Sdl2AudioFrameCapturer>(jsonParams);
    } 
#endif
#ifdef RK_PLATFORM
    if (captureName == "RKAudioFrameCapturer") 
    {
        ret = std::make_shared<RKAudioFrameCapturer>(jsonParams);
    } 
#endif
#ifdef USE_ALSA
    if (captureName == "AlsaAudioFrameCapturer") 
    {
        ret = std::make_shared<AlsaAudioFrameCapturer>(jsonParams);
    }
#endif
#ifdef USE_SPEC
    if (captureName == "AudioSpecMulticaster")
    {
        ret = std::make_shared<AudioFrameCapturer>(AudioSpecMulticaster::CreateInstance(jsonParams));
    }
    if (captureName == "AudioSpec")
    {
        ret = std::make_shared<AudioFrameCapturer>(std::make_shared<AudioSpec>(jsonParams));
    }
    if (captureName == "MixedAudioSpec")
    {
        ret = std::make_shared<AudioFrameCapturer>(std::make_shared<MixedAudioSpec>(jsonParams));
    }
    if (captureName == "AudioSpecMulticasterSingle")
    {
        ret = std::make_shared<AudioFrameCapturer>(AudioSpecMulticasterSingle::CreateInstance(jsonParams));
    }
    if (captureName == "AudioSpecMulticasterMulti")
    {
        ret = std::make_shared<AudioFrameCapturer>(std::make_shared<AudioSpecMulticasterMulti>(jsonParams));
    }
#endif
#ifdef USE_TINYALSA
    if (captureName == "TinyALSACapturer")
    {
        ret = std::make_shared<TinyALSACapturer>(jsonParams);
    }
#endif
#ifdef USE_UART
    if (captureName == "UartReader") {
        ret = std::make_shared<UartReader>(jsonParams);
    }
#endif
    return ret;
}


bool AudioFrameCapturer::isAudioFrameCapturer(const FramePipeline::Ptr& ptr)
{
#ifdef USE_SDL2
    if (ptr->name() == "Sdl2AudioFrameCapturer")
    {
        return true;
    }
#endif
#ifdef RK_PLATFORM
    if (ptr->name() == "RKAudioFrameCapturer")
    {
        return true;
    } 
#endif
#ifdef USE_ALSA 
    if (ptr->name() == "AlsaAudioFrameCapturer") 
    {
        return true;
    }
#endif
#ifdef USE_SPEC    
    if (ptr->name() == "AudioSpecMulticaster")
    {
        return true;
    }
    if (ptr->name() == "AudioSpec")
    {
        return true;
    }
    if (ptr->name() == "MixedAudioSpec")
    {
        return true;
    }
    if (ptr->name() == "AudioSpecMulticasterSingle")
    {
        return true;
    }
    if (ptr->name() == "AudioSpecMulticasterMulti")
    {
        return true;
    }
#endif
#ifdef USE_TINYALSA
    if (ptr->name() == "TinyALSACapturer")
    {
        return true;
    }
#endif
#ifdef USE_UART
    if (ptr->name() == "UartReader") {
        return true;
    }
#endif
    return false;
}

std::string AudioFrameCapturer::loadDefaultConfig(const std::string& capturerName) {
    if (!IsSupportCapturer(capturerName)) return "";
    nlohmann::json j;
#ifdef RK_PLATFORM
    if (capturerName == "AlsaAudioFrameCapturer") {
        j["samplerate"] = 48000;
        j["channel"] = 2;
        return j.dump();
    }
#endif
#ifdef USE_SPEC   
    if (capturerName == "AudioSpec") {
        j["samplerate"] = 16000;
        j["channel"] = 1;
        j["maxChn"] = 8;
        return j.dump();
    }
#endif
    return "";
}