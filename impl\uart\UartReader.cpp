#include "UartReader.h"
#include "UartCommon.h"
#include <json.hpp>

namespace panocom
{
UartReader::UartReader(const std::string &jsonParams) : dev_(0), capture_len_(320)
{
    jinfo("UartReader %s", jsonParams.c_str());
    name_ = "UartReader";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    if (j.contains("dev")) dev_ = j["dev"];
    if (j.contains("gain")) gain_ = j["gain"];
    start();
}

UartReader::~UartReader()
{
    jinfo("~UartReader start");
    // stop();
    if (!readThread_.isRunning()) return;
    readThread_.stop(true);
    readThread_.join();
    jinfo("~UartReader end");
}

void UartReader::start() {
    jinfo("UartStart");
    jinfo("uastate:%d", UartChnlSta());
    if (readThread_.isRunning()) return;
    ClearRecvQue(dev_);
    readThread_.loop()->setInterval(2, [this](hv::TimerID id){ 
        char buffer[640] = {0};
        int ret = GetUartMsg(dev_, buffer, 640);
        if (ret > 0) {
            bbuf_.WriteBytes(buffer, ret);
        }
        if (bbuf_.Length() >= sizeof(UartData)) {
            UartData* data = (UartData*)bbuf_.Data();
            if (data->magic == 1) {
                int32_t len = data->len;
                if (len + sizeof(UartData) <= bbuf_.Length()) {
                    int16_t fmt = data->codec;
                    int16_t gain = data->gain + gain_;
                    gain = std::min<int16_t>(12, std::max<int16_t>(-12, gain));
                    auto frame = Frame::CreateFrame((FrameFormat)fmt);
                    frame->createFrameBuffer(len);
                    frame->setGain(gain);
                    uint8_t* dataTmp = (uint8_t*)data;
                    memcpy(frame->getFrameBuffer(), dataTmp + sizeof(UartData), len);
                    deliverFrame(frame);
                    bbuf_.Consume(len + sizeof(UartData));
                }
            } else {

            }
        }
    });
    readThread_.start();
}
void UartReader::stop() {
    jinfo("UartReader stop");
    // if (!readThread_.isRunning()) return;
    // readThread_.stop(true);
    // readThread_.join();
}

int UartReader::updateParam(const std::string& jsonParams) {
    jinfo("UartReader updateParam: %s", jsonParams.c_str());
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    if (j.contains("gain")) gain_ = j["gain"];
    return 0;
}
} // namespace panocomUartReader::UartReader(/* args */)
