#ifndef SRC_DRMTEST_H_
#define SRC_DRMTEST_H_
#include <stdint.h>
#include <future>
#include "xf86drm.h"
#include "xf86drmMode.h"

namespace panocom {
struct buffer_object
{
	uint32_t width;
	uint32_t height;
	uint32_t pitch;
	uint32_t handle;
	uint32_t size;
	uint32_t *vaddr;
	uint32_t fb_id;
};


class DRMTest
{
private:
	/* data */
	int32_t modeset_create_fb(int fd, struct buffer_object *bo);
	int32_t modeset_create_fb(int fd, struct buffer_object *bo, uint32_t color);
	int32_t modeset_create_fb2(int fd, struct buffer_object *bo, uint32_t color);
	void modestset_destroy_fb(int fd, struct buffer_object *bo);
	uint32_t get_property_id(int fd, drmModeObjectProperties *props, const char *name);
	DRMTest(/* args */);
	~DRMTest();
	void CheckHdmiConnected();
public:
	static DRMTest &inst();
	void Init();
	void SingleBufferTest();
	void DoubleBufferTest();
	void PageFlipTest();
	void PlaneTest();
	void AtomicCrtcTest();
	void AtomicPlaneTest();
	void Test();
	void StartMultiScreen();
	void StartMultiScreen(bool extend_is_hdmi);	// 多进程双屏同显
	void StartMultiScreenNotAtomic();
	bool StopMultiScreen();
	bool HdmiConnected() { return hdmi_connected_; }
public:
	struct buffer_object buf_;
	struct buffer_object double_bufs_[2];
private:
	bool hdmi_connected_ = false;
	bool multi_screen_running_ = false;
	std::future<bool> multi_screen_future_;
};
}
#endif