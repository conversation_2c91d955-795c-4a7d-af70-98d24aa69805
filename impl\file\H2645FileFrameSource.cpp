
#ifdef ENABLE_VIDEO
#include "H2645FileFrameSource.h"
#include "Frame.h"
#include "Utils.h"
#include <ljcore/jlog.h>
#include <json.hpp>

using namespace panocom;


void H2645FileFrameSource::readFile(const std::string& path)
{
    FILE* pf = fopen(path.c_str(), "rb");
    if (pf)
    {
        fseek(pf, 0, SEEK_END);
        int ret = ftell(pf);
        jinfo("fopen %s %d", path.c_str(), ret);
        buffer_.resize(ret);
        fseek(pf, 0, SEEK_SET);
        fread(buffer_.data(), 1, buffer_.size(), pf);
        fclose(pf);
        
        int index = 0;
        int nal_start = 0;
        int nal_end = 0;
        int prefixSize = 0;
        int len = findNALU(buffer_.data() + index, buffer_.size() - index, &nal_start, &nal_end, &prefixSize);
        while (len > 0)
        {
            int total = len + prefixSize;
            std::shared_ptr<Frame> f = Frame::CreateFrame(FRAME_FORMAT_COMMON_VIDEO);
            f->createFrameBuffer(buffer_.data() + index, total);
            frames_.push_back(f);
            index += total;
            if (index >= buffer_.size())
            {
                break;
            }
            len = findNALU(buffer_.data() + index, buffer_.size() - index, &nal_start, &nal_end, &prefixSize);
        }
    }
    else
    {

    }
}

H2645FileFrameSource::H2645FileFrameSource(const std::string& jsonParams) : FileFramePipeline(jsonParams)
{
    FN_BEGIN;
    name_ = "H2645FileFrameSource";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    path_ = "test.h264";
    if (j.contains("path"))
    {
        path_ = j["path"];
    }
    fps_ = 25;
    if (j.contains("fps"))
    {
        fps_ = j["fps"];
    }
    start();
    
    FN_END;
}

H2645FileFrameSource::~H2645FileFrameSource()
{
    FN_BEGIN;
    stop();
    FN_END;
}

void H2645FileFrameSource::start()
{
    if (thread_.isRunning()) return;
    FN_BEGIN;
    int gap = 1000 / fps_;

    jinfo("gap = %d", gap);
    
    index_ = 0;
    size_ = 0;
    readFile(path_);
    thread_.loop()->setInterval(gap, [this](hv::TimerID id) {
        if (index_ < frames_.size())
        {
            printOutputStatus("H2645FileFrameSource");
            deliverFrame(frames_[index_]);
            index_++;
        }
        if (index_ >= frames_.size())
        {
            index_ = 0;
        }
    });

    thread_.start();
    FN_END;
}

void H2645FileFrameSource::stop()
{
    FN_BEGIN;
    thread_.stop(true);
    buffer_.clear();
    FN_END;
}

#endif