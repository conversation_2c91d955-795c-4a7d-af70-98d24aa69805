#include <iostream>
#include <memory>
#include <string>
#include <future>
#include <ljcore/jlog.h>

#include <grpcpp/ext/proto_server_reflection_plugin.h>
#include <grpcpp/grpcpp.h>
#include <grpcpp/health_check_service_interface.h>
#include <hv/EventLoopThread.h>

#include "mmi.grpc.pb.h"
#include "MediaManagerInterface.h"

using grpc::Server;
using grpc::ServerBuilder;
using grpc::ServerContext;
using grpc::Status;
using mmi::CreateSessionReply;
using mmi::CreateSessionRequest;
using mmi::GetLocalSDPReply;
using mmi::GetLocalSDPRequest;
using mmi::MediaManager;
using mmi::ReleaseSessionReply;
using mmi::ReleaseSessionRequest;
using mmi::SetRemoteSDPReply;
using mmi::SetRemoteSDPRequest;
using namespace panocom;


// Logic and data behind the server's behavior.
class MediaManagerServiceImpl final : public MediaManager::Service
{
public:
    void start()
    {
        mThread.start();
    }
    Status CreateSession(ServerContext *context, const CreateSessionRequest *request,
                         CreateSessionReply *reply) override
    {
        std::string id = request->id();
        std::string jsonParams = request->jsonparams();
        int ret = 0;
        std::promise<int> p;
        std::future<int> f = p.get_future();
        mThread.loop()->runInLoop([this, id, jsonParams, &ret, &p] {
            ret = mMmi.CreateSession(id, jsonParams);
            p.set_value(0);
        });
        f.wait();
        reply->set_ret(ret);
        return Status::OK;
    }
    Status ReleaseSession(ServerContext *context, const ReleaseSessionRequest *request,
                          ReleaseSessionReply *reply) override
    {
        std::string id = request->id();
        int ret = 0;
        std::promise<int> p;
        std::future<int> f = p.get_future();
        mThread.loop()->runInLoop([this, id, &ret, &p] {
            ret = mMmi.ReleaseSession(id);
            p.set_value(0);
        });
        f.wait();
        reply->set_ret(ret);
        return Status::OK;
    }
    Status GetLocalSDP(ServerContext *context, const GetLocalSDPRequest *request,
                       GetLocalSDPReply *reply) override
    {
        std::string sdp;
        std::string id = request->id();
        std::string jsonParams = request->jsonparams();
        int ret = 0;
        std::promise<int> p;
        std::future<int> f = p.get_future();
        mThread.loop()->runInLoop([this, id, jsonParams, &ret, &sdp, &p] {
            ret = mMmi.GetLocalSDP(id, sdp, jsonParams);
            p.set_value(0);
        });
        f.wait();
        reply->set_ret(ret);
        reply->set_sdp(sdp);
        return Status::OK;
    }
    Status SetRemoteSDP(ServerContext *context, const SetRemoteSDPRequest *request,
                        SetRemoteSDPReply *reply) override
    {
        std::string sdp;
        std::string id = request->id();
        std::string remoteSdp = request->sdp();
        std::string jsonParams = request->jsonparams();
        int ret = 0;
        std::promise<int> p;
        std::future<int> f = p.get_future();
        mThread.loop()->runInLoop([this, id, remoteSdp, jsonParams, &ret, &sdp, &p] {
            ret = mMmi.SetRemoteSDP(id, remoteSdp, sdp, jsonParams);
            p.set_value(0);
        });
        f.wait();
        reply->set_ret(ret);
        reply->set_sdp(sdp);
        return Status::OK;
    }
private:
    MediaManagerInterface mMmi;
    hv::EventLoopThread mThread;
};

void RunServer(uint16_t port)
{
    std::string server_address = "0.0.0.0:" + std::to_string(port);
    MediaManagerServiceImpl service;
    service.start();

    grpc::EnableDefaultHealthCheckService(true);
    grpc::reflection::InitProtoReflectionServerBuilderPlugin();
    ServerBuilder builder;
    // Listen on the given address without any authentication mechanism.
    builder.AddListeningPort(server_address, grpc::InsecureServerCredentials());
    // Register "service" as the instance through which we'll communicate with
    // clients. In this case it corresponds to an *synchronous* service.
    builder.RegisterService(&service);
    // Finally assemble the server.
    std::unique_ptr<Server> server(builder.BuildAndStart());
    std::cout << "Server listening on " << server_address << std::endl;

    // Wait for the server to shutdown. Note that some other thread must be
    // responsible for shutting down the server for this call to ever return.
    server->Wait();
}

int main(int argc, char **argv)
{
    jlog_init(nullptr);
    RunServer(atoi(argv[1]));
    return 0;
}
