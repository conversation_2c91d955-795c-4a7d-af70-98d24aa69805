#include "MediaManagerInterface.h"
#include <ljcore/jlog.h>
#include <json.hpp>
#include <thread>
#include <chrono>

using namespace panocom;
int main(const int argc, const char *argv[]) {
    jinfo("=============Conference Test=============");
    MediaManagerInterface mmi;
    auto devices = mmi.ListAudioDeviceInfos();
    for (const auto &device: devices) {
        jinfo("[%d] Name: %s <%d, %d> Desc: %s", device.index, device.name.c_str(), device.can_capture, device.can_playback, device.desc.c_str());
    }
    nlohmann::json j;
    if (argc <= 1)
        j["audio"]["capturer"]["dev"] = 0;
    else 
        j["audio"]["capturer"]["dev"] = atoi(argv[1]);
    if (argc <= 2)
        j["audio"]["renderer"]["dev"] = 0;
    else 
        j["audio"]["renderer"]["dev"] = atoi(argv[2]);
    j["audio"]["renderer"]["name"] = "AlsaAudioFrameRender";
    jinfo("loopbackTest: %d", mmi.LoopbackTest("A", j.dump()));
    std::this_thread::sleep_for(std::chrono::seconds(30));
    jinfo("StoploopbackTest: %d", mmi.StopLoopbackTest("A"));
    return 0;
}