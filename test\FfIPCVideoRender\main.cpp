#include "MediaPipeline.h"
#include "VideoFrameDecoder.h"
#include "VideoFrameDecoder.h"
#include "VideoFrameRender.h"
#include "./impl/file/VideoFileFrameSource.h"
#include "ffmpeg/FrameSender.h"
#include <json.hpp>
#include <stdio.h>
#include <ljcore/jlog.h>
#include <hv/EventLoop.h>

using namespace panocom;

/***
 *  播放多路视频
 *        路数  视频路径                     解码类型(硬解码优先失败软解、硬解码、软解码)
./run.sh  16   /home/<USER>/videos/race.h265  FfVideoDecoderAdapter
./run.sh  16   /home/<USER>/videos/race.h265  FfHwVideoDecoder
./run.sh  16   /home/<USER>/videos/race.h265  FfVideoDecoder
 *
***/

int main(int argc,char** argv)
{
    jlog_init(nullptr);
    int channelCount = 1;
    if(argc>1){
        channelCount = std::atoi(argv[1]);
        if(channelCount<=0)
            channelCount = 1;
        else if(channelCount >16)
            channelCount = 16;
    }
    jinfo("FfIPCFrameRender main() channelCount：%d",channelCount);

    auto loop = std::make_shared<hv::EventLoop>();
    std::vector<std::shared_ptr<FileFramePipeline>> sources;
    std::vector<std::shared_ptr<VideoFrameDecoder>> decoders;
    std::vector<std::shared_ptr<VideoFrameRender>> renders;


    int maxChn = 25;
    int shmStartId = 10086;
    FrameSender::Inst()->init(maxChn,shmStartId,"J8");

    int id = 0;
    for(; id< channelCount;id ++) {
        jinfo("FfIPCFrameRender main() id:%d ",id);

        nlohmann::json j;
        auto render = VideoFrameRender::CreateVideoRender("FfIPCFrameRender", j.dump());
        render->setGroupId(id);

        j.clear();
        if(argc>2){
            j["path"] = argv[2];
            jinfo("FfIPCFrameRender main() defualt j[path]:%s:",argv[2]);
        }else{
            j["path"] = "/home/<USER>/videos/race.h265";
        }
        j["speed"] = 1.0;

        int gop = 30;
        auto source = FileFramePipeline::CreateFileSource("VideoFileFrameSource", j.dump());
        source->setGroupId(id);
        std::shared_ptr<panocom::VideoFileFrameSource> videoFileFrameSourcePtr = std::dynamic_pointer_cast<panocom::VideoFileFrameSource>(source);
        if(videoFileFrameSourcePtr){
            if(videoFileFrameSourcePtr->getFps()>0){
                j["fps"] = videoFileFrameSourcePtr->getFps();
            }
            if(videoFileFrameSourcePtr->getWidth()>0){
                j["width"] = videoFileFrameSourcePtr->getWidth();
            }
            if(videoFileFrameSourcePtr->getHeight()>0){
                j["height"] = videoFileFrameSourcePtr->getHeight();
            }
            if(!videoFileFrameSourcePtr->getCodec().empty()){
                j["codec"] = videoFileFrameSourcePtr->getCodec(); //codec可选：mjpeg h264 hevc
            }
            if(!videoFileFrameSourcePtr->getCodec().empty()){
                j["gop"] = gop = videoFileFrameSourcePtr->getGop(); //codec可选：mjpeg h264 hevc
            }
        }

        //优先硬件解码
        j["decoderType"] = "hardware";
        int hw_frames = gop;
        if(hw_frames<0)
            hw_frames = 0;
        j["hw_frames"] = hw_frames;
        std::string decoderType = "FfVideoDecoderAdapter";//"FfVideoDecoderAdapter","FfHwVideoDecoder" ,"FfVideoDecoder"

        if(argc>3){
            decoderType = argv[3];
        }
        if(id>10){
            std::string decoderType = "FfVideoDecoder";
        }
        auto decoder = VideoFrameDecoder::CreateVideoDecoder(decoderType, j.dump());
        decoder->setGroupId(id);

        source->addVideoDestination(decoder);
        decoder->addVideoDestination(render);
        sources.push_back(source);
        decoders.push_back(decoder);
        renders.push_back(render);

        videoFileFrameSourcePtr->start();
    }
    loop->run();

    FrameSender::Free();
    jinfo("FfIPCFrameRender main() end  ");
    return 0;
}




/*** 播放单路视频
int main()
{
    auto loop = std::make_shared<hv::EventLoop>();
    jlog_init(nullptr);
    nlohmann::json j;
    j["path"] = "/home/<USER>/videos/test";
    jinfo("FfIPCFrameRender main() start ");
    int id = 0;
    auto source = FileFramePipeline::CreateFileSource("VideoFileFrameSource", j.dump());
    source->setGroupId(id);
    if (j.contains("codec")) {
        std::string codec = j["codec"];
        jinfo("FfIPCFrameRender main() j[codec]:%s", codec.c_str());
    } else {
        j["codec"] = "h264";
    }
    jinfo("FfIPCFrameRender main() j.dump:%s", j.dump());

    auto decoder = VideoFrameDecoder::CreateVideoDecoder("FfVideoDecoder", j.dump());
    decoder->setGroupId(id);
    j.clear();
    j["maxChn"] = 25;
    j["shmStartId"] = 10086;
    j["appName"] = "J8";
    auto render = VideoFrameRender::CreateVideoRender("FfIPCFrameRender", j.dump());
    render->setGroupId(id);
    source->addVideoDestination(decoder);
    decoder->addVideoDestination(render)
    jinfo("FfIPCFrameRender main() end ");
    loop->run();
    return 0;
}
***/