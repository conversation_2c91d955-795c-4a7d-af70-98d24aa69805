#include "Sdl2AudioFrameRender.h"
#include "Frame.h"
#include "Utils.h"
#include <json.hpp>
#include <ljcore/jlog.h>
#include <stdio.h>

extern int mmi_record_audio;

using namespace panocom;

Sdl2AudioFrameRender::Sdl2AudioFrameRender(const std::string& jsonParams)
{
    FN_BEGIN;
    jinfo("Sdl2AudioFrameRender %s", jsonParams.c_str());
    name_ = "Sdl2AudioFrameRender";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    int chn = 1;
    if (j.contains("channel"))
    {
        chn = j["channel"];
    }
    samplerate_ = 16000;
    if (j.contains("samplerate"))
    {
        samplerate_ = j["samplerate"];
    }
    switch (samplerate_)
    {
    case 8000:
        fmt_ = FRAME_FORMAT_PCM_8000_1;
        break;
    case 16000:
        fmt_ = FRAME_FORMAT_PCM_16000_1;
        break;
    case 48000:
        fmt_ = FRAME_FORMAT_PCM_48000_1;
        break;
    default:
        fmt_ = FRAME_FORMAT_PCM_16000_1;
        break;
    }
    int samples = 512;
    if (j.contains("samples"))
    {
        samples = j["samples"];
    }
    int dev = -1;
    if (j.contains("dev"))
    {
        dev = j["dev"];
    }

    SDL_Init(SDL_INIT_AUDIO);
    int deviceCount = SDL_GetNumAudioDevices(SDL_FALSE);
    for (size_t i = 0; i < deviceCount; i++)
    {
        jinfo("sdl list render device[%d]: %s", i, SDL_GetAudioDeviceName(i, SDL_FALSE));
    }
    const char* device = NULL;
    if (dev >= 0 && dev < deviceCount)
    {
        device = SDL_GetAudioDeviceName(dev, SDL_FALSE);
        jinfo("sdl render device: %s", device);
    }

    SDL_AudioSpec wanted;
    SDL_zero(wanted);
    wanted.freq = samplerate_;
    wanted.format = AUDIO_S16LSB;
    wanted.channels = chn;
    wanted.samples = samples;
    wanted.callback = NULL;

    SDL_AudioSpec spec;
    SDL_zero(spec);

    devid_ = SDL_OpenAudioDevice(device, SDL_FALSE, &wanted, &spec, SDL_AUDIO_ALLOW_ANY_CHANGE);
    if (!devid_) 
    {
        jerror("Couldn't open an audio device for playback: %s!\n", SDL_GetError());
    }
    FN_END;
}

Sdl2AudioFrameRender::~Sdl2AudioFrameRender()
{
    FN_BEGIN;
    if (devid_)
    {
        SDL_CloseAudioDevice(devid_);
        devid_ = 0;
    }
    FN_END;
}

void Sdl2AudioFrameRender::onFrame(const std::shared_ptr<Frame>& frame)
{
    //jinfo("Sdl2AudioFrameRender::onFrame %d %d", frame->getFrameFormat(), frame->getFrameSize());
    if (devid_)
    {
        auto f = frame;
        int samplerate = 0;
        int chn = 0;
        if (Frame::getSamplerate(frame->getFrameFormat(), samplerate, chn))
        {
            if (samplerate_ != samplerate)
            {
                if (!initResampler_ || source_samplerate_ != samplerate)
                {
                    source_samplerate_ = samplerate;
                    initResampler_ = true;
#ifdef WEBRTC_RESAMPLE_ANOTHER
                    resampler_.InitializeIfNeeded(samplerate, samplerate_, 1);
#else
                    resampler_.ResetIfNeeded(samplerate, samplerate_, 1);
#endif
                }
                auto frame = Frame::CreateFrame((FrameFormat)fmt_);
                frame->createFrameBuffer(f->getFrameSize() * samplerate_ / samplerate);
                memset(frame->getFrameBuffer(), 0, frame->getFrameSize());
#ifdef WEBRTC_RESAMPLE_ANOTHER
                resampler_.Resample((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2);
#else
                size_t outlen = 0;
                resampler_.Push((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2, outlen);
#endif
                f = frame;
                if (first_)
                {
                    first_ = false;
                    jinfo("Sdl2AudioFrameRender resample %d -> %d", samplerate, samplerate_);
                }
            }
            if (f->getFrameSize() > samplerate_ / 100 * 2 * 2)
            {
                jerror("Sdl2AudioFrameRender::onFrame error size %d", f->getFrameSize());
            }
            else
            {
                printInputStatus("Sdl2AudioFrameRender");
                SDL_QueueAudio(devid_, f->getFrameBuffer(), f->getFrameSize());
                if (mmi_record_audio)
                {
                    if (!pf_)
                    {
                        std::string fileName = name_ + std::to_string((long)this) + ".pcm";
                        pf_ = fopen(fileName.c_str(), "w+b");
                    }
                    if (pf_)
                    {
                        fwrite(f->getFrameBuffer(), 1, f->getFrameSize(), pf_);
                    }
                }
            }
        }
        else
        {
            jerror("Sdl2AudioFrameRender::onFrame %d %d fail", frame->getFrameFormat(), frame->getFrameSize());
        }
    }
}