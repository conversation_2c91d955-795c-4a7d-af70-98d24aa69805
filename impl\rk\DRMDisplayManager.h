#ifndef DISPLAY_DRMDISPLAYMANAGER_H_
#define DISPLAY_DRMDISPLAYMANAGER_H_
#include <unordered_map>
#include <mutex>

#include "DRMDisplay.h"
#include "RKVideoFrameRender.h"
namespace panocom
{
class DRMDisplayManager
{
private:
	/* data */
public:
	static DRMDisplayManager& instance();
	std::shared_ptr<DRMDisplay> CreateDisplay(const std::string& jsonParams = "");
	std::shared_ptr<RKVideoFrameRender> CreateRender(const std::string &jsonParams = "");
	int GetDrmDev(int index);
	bool GetConnectorStatus(int dev, int card = 0);
private:
	DRMDisplayManager(/* args */);
	~DRMDisplayManager();

	// drm_displayer
    bool is_authenticated (int fd, const char *msg);
    int open_driver (const char *dev_path);
    int open_drivers (const char *base_path, int base_id);
	
	std::unordered_map<std::string, int> fds_;
	std::mutex fds_mutex_;
};



} // namespace panocom


#endif