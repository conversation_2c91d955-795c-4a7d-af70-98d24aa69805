## 目录结构

```
.
├── array.h         动态数组
├── hatomic.h       原子操作
├── hbase.h         基础函数
├── hbuf.h          缓存
├── hdef.h          常见宏定义
├── heap.h          堆
├── hendian.h       大小端
├── herr.h          错误码表
├── hlog.h          日志
├── hmain.h         命令行解析
├── hmath.h         数学函数
├── hmutex.h        线程同步锁
├── hplatform.h     平台相关宏
├── hproc.h         进程
├── hsocket.h       套接字
├── hsysinfo.h      系统信息
├── hthread.h       线程
├── htime.h         时间
├── hversion.h      版本
├── list.h          链表
├── queue.h         队列
└── rbtree.h        红黑树

```
