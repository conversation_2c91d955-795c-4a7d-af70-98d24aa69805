// created by gyj 2024-3-29
#ifndef P_AudioFramePacer_h
#define P_AudioFramePacer_h

#include "AudioFrameProcesser.h"
#include <ptoolkit/bytebuffer.h>
#include <hv/EventLoopThread.h>

namespace panocom
{
    class AudioFramePacer : public AudioFrameProcesser
    {
    public:
        AudioFramePacer(const std::string& jsonParams);
        ~AudioFramePacer() override;

        void onFrame(const std::shared_ptr<Frame>& frame) override;
        void start() override;
        void stop() override;
    private:
        int samplerate_ = 0;
        int chn_ = 0;
        int ptime_ = 20;
        int ptimeDataLen_ = 0;
        ByteBuffer bbuf_;
        hv::EventLoopThread thread_;
    };
}

#endif