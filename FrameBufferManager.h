// created by gyj 2024-3-4
#ifndef P_FrameBufferManager_h
#define P_FrameBufferManager_h

#include <memory>
#include <list>
#include <mutex>
#include "Frame.h"

namespace panocom
{
    class FrameBufferManager
    {
    public:
        FrameBufferManager(int maxFrame, int frameSize, FrameFormat fmt, const std::string& jsonParams = "");
        virtual ~FrameBufferManager();
        
        virtual std::shared_ptr<Frame> getFrame(const std::string& jsonParams = "");

    private:
        uint8_t* buffer_;
        int bufferSize_;
        std::list<Frame*> frames_;
        std::mutex mutex_;
        FrameFormat fmt_;
    };
}

#endif