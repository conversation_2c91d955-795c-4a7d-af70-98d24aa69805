#include "CPing.h"
#include <ljcore/jlog.h>
#include <chrono>

CPing &CPing::instance() {
    static CPing inst;
    return inst;
}

CPing::CPing()
    : m_nSocketfd(-1)
    , m_usCurrentProcID(10000)
    , m_bIsInitSucc(false)
    , m_nMaxTimeWait(1000 * 1000)
    , m_szICMPData(NULL) 
    , s_usPacketSeq(0)
    , loop_running_(false) {
    m_usCurrentProcID = getpid();
    if (!CreateSocket()) {
        jerror("ERROR!!! CreateSocket failed!!!");
        CloseSocket();
    } else {
        StartPingServers();
    }
}

CPing::~CPing()
{
    if (receive_future_.valid()) {
        auto status = receive_future_.wait_for(std::chrono::milliseconds(2000));
        if (status == std::future_status::timeout) {
            jerror("receive_future_ stop timeout");
        } else if (status == std::future_status::ready) {
            bool res = receive_future_.get();
            jinfo("receive_future_ stop status: %d", res);
        }
    }

    if (send_future_.valid()) {
        auto status = send_future_.wait_for(std::chrono::milliseconds(3000));
        if (status == std::future_status::timeout) {
            jerror("send_future_ stop timeout");
        } else if (status == std::future_status::ready) {
            bool res = send_future_.get();
            jinfo("send_future_ stop status: %d", res);
        }
    }
    
    CloseSocket();

    if (NULL != m_szICMPData)
    {
        free(m_szICMPData);
        m_szICMPData = NULL;
    }
}

bool CPing::CreateSocket()  
{  
    struct protoent *protocol;              //指向协议protoent的一个结构体  
  
    //1.创建一个socket  
    //1.1通过协议名称icmp获取协议编号  
    if ((protocol = getprotobyname("icmp")) == NULL)    
    {
        jerror("ERROR!!! CreateSocket: getprotobyname failed: %s",strerror(errno)); 
          
        return false;  
    }  
    //1.2创建原始套接字时，需要有root权限，防止应用程序绕过内建安全机制  
    if(-1 == (m_nSocketfd = socket(AF_INET,SOCK_RAW,protocol->p_proto)))  
    {  
        jerror("ERROR!!! CreateSocket: create socket failed: %s",strerror(errno));
        return false;     
    }  
      
    //回收root权限,设置当前用户权限   
    setuid(getuid());  
      
    //2.设置m_dest_addr结构体  
    m_dest_addr.sin_family = AF_INET;  
      
    bzero(&(m_dest_addr.sin_zero),8);
    m_bIsInitSucc = true;

    m_szICMPData = (char *)malloc(DEF_PACKET_SIZE + sizeof(ICMPHeader));

    if (m_szICMPData == NULL) {
        m_bIsInitSucc = false;
        return false;
    }
    return true;
}

//关闭套接字
bool CPing::CloseSocket() {

    bool flag = false;
    if (m_nSocketfd) {
        close(m_nSocketfd);
        flag = true;
        m_nSocketfd = -1;
    }
    return flag;
}

bool CPing::Ping(const uint64_t dwDestIP, PingReply *pPingReply, uint64_t dwTimeout)
{  
    return PingCore(dwDestIP, pPingReply, dwTimeout);
}

bool CPing::Ping(const char *szDestIP, PingReply *pPingReply, uint64_t dwTimeout)
{  
    if (NULL != szDestIP)
    {
        return PingCore(inet_addr(szDestIP), pPingReply, dwTimeout);
    }
    return false;    
}

void CPing::AddServer(const std::string &dst_ip) {
    std::lock_guard<std::mutex> lock(server_mutex_);
    if (server_replies_.count(dst_ip) == 0) {
        server_replies_[dst_ip] = PingReply();
        server_refs_[dst_ip] = 1;
    } else {
        server_refs_[dst_ip]++;
    }
    jinfo("AddServer %s ref %d", dst_ip.c_str(), server_refs_[dst_ip]);
}

void CPing::RemoveServer(const std::string &dst_ip) {
    std::lock_guard<std::mutex> lock(server_mutex_);
    if (server_refs_.count(dst_ip) != 0 && server_refs_[dst_ip] > 0) {
        server_refs_[dst_ip]--;
    }
    if (server_refs_.count(dst_ip) != 0 && server_refs_[dst_ip] <= 0) {
        server_refs_.erase(dst_ip);
        if (server_replies_.count(dst_ip) != 0) {
            server_replies_.erase(dst_ip);
        }
    }
    if (server_refs_.count(dst_ip) != 0) {
        jinfo("RemoveServer %s ref %d", dst_ip.c_str(), server_refs_[dst_ip]);
    } else {
        jinfo("RemoveServer %s ref 0", dst_ip.c_str());
    }
}

PingReply CPing::GetReply(const std::string &dst_ip) {
    std::lock_guard<std::mutex> lock(server_mutex_);
    if (server_replies_.count(dst_ip) != 0) {
        return server_replies_[dst_ip];
    }
    return PingReply();
}

bool CPing::PingCore(uint64_t dwDestIP, PingReply *pPingReply, uint64_t dwTimeout)
{
    //判断初始化是否成功
    if (!m_bIsInitSucc)
    {
        jerror("Please init CPing first");
        return false;
    }

    //配置SOCKET
    sockaddr_in sockaddrDest; 
    sockaddrDest.sin_family = AF_INET; 
    sockaddrDest.sin_addr.s_addr = dwDestIP;
    int nSockaddrDestSize = sizeof(sockaddrDest);

    //构建ICMP包
    int nICMPDataSize = DEF_PACKET_SIZE + sizeof(ICMPHeader);
    uint64_t ulSendTimestamp = GetTickCountCalibrate();
    uint16_t usSeq = ++s_usPacketSeq;    
    memset(m_szICMPData, 0, nICMPDataSize);
    ICMPHeader *pICMPHeader = (ICMPHeader*)m_szICMPData;
    pICMPHeader->m_byType = ECHO_REQUEST; 
    pICMPHeader->m_byCode = 0; 
    pICMPHeader->m_usID = m_usCurrentProcID;    
    pICMPHeader->m_usSeq = usSeq;
    pICMPHeader->m_ulTimeStamp = ulSendTimestamp;
    pICMPHeader->m_usChecksum = CalCheckSum((uint16_t*)m_szICMPData, nICMPDataSize);

    //发送ICMP报文
    if (sendto(m_nSocketfd, m_szICMPData, nICMPDataSize, 0, (struct sockaddr*)&sockaddrDest, nSockaddrDestSize) < 0)
    {
        jerror("Send ICMP packet falied");
        return false;
    }
    
    //判断是否需要接收相应报文
    if (pPingReply == NULL)
    {
        return false;
    }

    char recvbuf[256] = {"\0"};
    int fromlen, n;  
    while (true)
    {
        struct timeval timeout;  
        fd_set readfd;                      //定义一个文件集合
        FD_ZERO(&readfd);                   //将该集合清空
        FD_SET(m_nSocketfd,&readfd);        //将套接字描述符添加到readfd集合中
        int maxfd = m_nSocketfd + 1;        //select监听的最大文件描述符
        timeout.tv_sec = 0; 
        timeout.tv_usec = m_nMaxTimeWait;   //设置select的超时等待时间

        n = select(maxfd, &readfd, NULL, NULL, &timeout); //用select实现非阻塞I/O
        switch (n) {
            case 0:
                jerror("recv_packet: select time out :%s, time:%d", strerror(errno), m_nMaxTimeWait);
                return false;
                break;
            case -1:
                jerror("recv_packet: select error : %s", strerror(errno));
                return false;
                break;
            default: {
                if (FD_ISSET(m_nSocketfd, &readfd)) //通过检测m_nSocketfd是否还在集合readfd中，判断m_nSocketfd是否有可读数据
                {
                    uint64_t nRecvTimestamp = GetTickCountCalibrate();
                    // recevfrom接收数据同时获取数据发送者的源地址，保存在m_from_addr中
                    fromlen = sizeof(m_from_addr);
                    int nPacketSize = recvfrom(m_nSocketfd, recvbuf, sizeof(recvbuf), 0, (struct sockaddr *)&m_from_addr, (socklen_t *)&fromlen);
                    if (nPacketSize < 0) {
                        jerror("ERROR!!! nPacketSize = %d, recv_packet: recv error : %s", nPacketSize, strerror(errno));
                    } else {
                        IPHeader *pIPHeader = (IPHeader *)recvbuf;
                        uint16_t usIPHeaderLen = (uint16_t)((pIPHeader->m_byVerHLen & 0x0f) * 4);
                        ICMPHeader *pICMPHeader = (ICMPHeader *)(recvbuf + usIPHeaderLen);
                        jinfo("[this] %d %d %d %d %d", pICMPHeader->m_usID, m_usCurrentProcID, pICMPHeader->m_byType == ECHO_REPLY, pICMPHeader->m_usSeq, usSeq);
                        if (pICMPHeader->m_usID == m_usCurrentProcID //是当前进程发出的报文
                            && pICMPHeader->m_byType == ECHO_REPLY //是ICMP响应报文
                            && pICMPHeader->m_usSeq == usSeq //是本次请求报文的响应报文
                        ) {
                            pPingReply->m_usSeq = usSeq;
                            pPingReply->m_dwRoundTripTime = ((double)nRecvTimestamp - pICMPHeader->m_ulTimeStamp) / (1000 * 1000);
                            pPingReply->m_dwBytes = nPacketSize - usIPHeaderLen - sizeof(ICMPHeader);
                            pPingReply->m_dwTTL = pIPHeader->m_byTTL;
                            return true;
                        }
                    }
                }
            } break;
        }
        //超时
        if (GetTickCountCalibrate() - ulSendTimestamp >= dwTimeout)
        {
            return false;
        }
    }
    return false;
}

uint16_t CPing::CalCheckSum(uint16_t *pBuffer, int nSize)
{
    unsigned long ulCheckSum=0; 
    while(nSize > 1) 
    { 
        ulCheckSum += *pBuffer++; 
        nSize -= sizeof(uint16_t); 
    }
    if(nSize ) 
    { 
        ulCheckSum += *(uint8_t*)pBuffer; 
    } 

    ulCheckSum = (ulCheckSum >> 16) + (ulCheckSum & 0xffff); 
    ulCheckSum += (ulCheckSum >>16); 

    return (uint16_t)(~ulCheckSum); 
}

uint64_t CPing::GetTickCountCalibrate()
{
    return std::chrono::steady_clock::now().time_since_epoch().count();
}

void CPing::StartPingServers() {
    loop_running_ = true;
    receive_future_ = std::async([this]() -> bool {
        while (loop_running_) {
            char recvbuf[256] = { "\0" };
            struct timeval timeout;
            fd_set readfd; //定义一个文件集合
            FD_ZERO(&readfd); //将该集合清空
            FD_SET(m_nSocketfd, &readfd); //将套接字描述符添加到readfd集合中
            int maxfd = m_nSocketfd + 1; // select监听的最大文件描述符

            // NOTE: tv_usec过大时要改为设置tv_sec，内核会有大小限制
            timeout.tv_sec = 1;
            timeout.tv_usec = 0; //设置select的超时等待时间
            // timeout.tv_sec = 0;
            // timeout.tv_usec = m_nMaxTimeWait;
            int n = select(maxfd, &readfd, NULL, NULL, &timeout); //用select实现非阻塞I/O
            switch (n) {
                case 0:
                    // jerror("recv_packet: select time out :%s, time:%d", strerror(errno), m_nMaxTimeWait);
                    // return false;
                    break;
                case -1:
                    jerror("recv_packet: select error : %s", strerror(errno));
                    // return false;
                    break;
                default: {
                    if (FD_ISSET(m_nSocketfd, &readfd)) //通过检测m_nSocketfd是否还在集合readfd中，判断m_nSocketfd是否有可读数据
                    {
                        uint64_t nRecvTimestamp = GetTickCountCalibrate();
                        // recevfrom接收数据同时获取数据发送者的源地址，保存在m_from_addr中
                        int fromlen = sizeof(m_from_addr);
                        int nPacketSize = recvfrom(m_nSocketfd, recvbuf, sizeof(recvbuf), 0, (struct sockaddr *)&m_from_addr, (socklen_t *)&fromlen);
                        if (nPacketSize < 0) {
                            jerror("ERROR!!! nPacketSize = %d, recv_packet: recv error : %s", nPacketSize, strerror(errno));
                        } else {
                            std::lock_guard<std::mutex> lock(server_mutex_);
                            std::string from_ip = (char *)inet_ntoa(m_from_addr.sin_addr);
                            if (server_replies_.count(from_ip) != 0) {
                                IPHeader *pIPHeader = (IPHeader *)recvbuf;
                                uint16_t usIPHeaderLen = (uint16_t)((pIPHeader->m_byVerHLen & 0x0f) * 4);
                                ICMPHeader *pICMPHeader = (ICMPHeader *)(recvbuf + usIPHeaderLen);
                                // jinfo(
                                //     "[this] %d %d %d %d %d", pICMPHeader->m_usID, m_usCurrentProcID, pICMPHeader->m_byType == ECHO_REPLY, pICMPHeader->m_usSeq,
                                //     server_replies_[from_ip].m_usSeq);
                                if (pICMPHeader->m_usID == m_usCurrentProcID //是当前进程发出的报文
                                    && pICMPHeader->m_byType == ECHO_REPLY //是ICMP响应报文
                                    && pICMPHeader->m_usSeq == server_replies_[from_ip].m_usSeq //是本次请求报文的响应报文
                                ) {
                                    // server_replies_[from_ip].m_usSeq = server_replies_[from_ip].m_usSeq;
                                    server_replies_[from_ip].m_dwRoundTripTime = ((double)nRecvTimestamp - pICMPHeader->m_ulTimeStamp) / (1000 * 1000);
                                    server_replies_[from_ip].m_dwBytes = nPacketSize - usIPHeaderLen - sizeof(ICMPHeader);
                                    server_replies_[from_ip].m_dwTTL = pIPHeader->m_byTTL;
                                }
                            }
                        }
                    }
                } break;
            }
        }
        return true;
    });
    send_future_ = std::async([this]() -> bool {
        while (loop_running_) {
            {
                std::lock_guard<std::mutex> lock(server_mutex_);
                for (auto &pair : server_replies_) {
                    auto &ip = pair.first;
                    //配置SOCKET
                    sockaddr_in sockaddrDest;
                    sockaddrDest.sin_family = AF_INET;
                    sockaddrDest.sin_addr.s_addr = inet_addr(ip.c_str());
                    int nSockaddrDestSize = sizeof(sockaddrDest);

                    //构建ICMP包
                    int nICMPDataSize = DEF_PACKET_SIZE + sizeof(ICMPHeader);
                    uint64_t ulSendTimestamp = GetTickCountCalibrate();
                    uint16_t usSeq = ++pair.second.m_usSeq;
                    memset(m_szICMPData, 0, nICMPDataSize);
                    ICMPHeader *pICMPHeader = (ICMPHeader *)m_szICMPData;
                    pICMPHeader->m_byType = ECHO_REQUEST;
                    pICMPHeader->m_byCode = 0;
                    pICMPHeader->m_usID = m_usCurrentProcID;
                    pICMPHeader->m_usSeq = usSeq;
                    pICMPHeader->m_ulTimeStamp = ulSendTimestamp;
                    pICMPHeader->m_usChecksum = CalCheckSum((uint16_t *)m_szICMPData, nICMPDataSize);

                    //发送ICMP报文
                    // jinfo("sendto %s seq %d", ip.c_str(), pair.second.m_usSeq);
                    if (sendto(m_nSocketfd, m_szICMPData, nICMPDataSize, 0, (struct sockaddr *)&sockaddrDest, nSockaddrDestSize) < 0) {
                        jerror("Send ICMP packet falied");
                        return false;
                    }
                }
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(2000));
        }
        return true;
    });

}