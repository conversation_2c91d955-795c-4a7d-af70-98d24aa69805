#include "AudioFrameDispatcher.h"
#include "audiodispatcher/DefaultAudioFrameDispatcher.h"

#include <algorithm>

using namespace panocom;

bool AudioFrameDispatcher::isRegistered = false;

std::vector<std::string> AudioFrameDispatcher::dispatchers_;

void AudioFrameDispatcher::RegistDispatchers()
{
    if (!isRegistered)
    {
        dispatchers_.emplace_back("DefaultAudioFrameDispatcher");
        isRegistered = true;
    }
}

std::vector<std::string>& AudioFrameDispatcher::GetSupportDispatchers()
{
    RegistDispatchers();
    return dispatchers_;
}

bool AudioFrameDispatcher::IsSupportDispatcher(const std::string &dispatcherName)
{
    RegistDispatchers();
    return std::find(dispatchers_.begin(), dispatchers_.end(), dispatcherName) != dispatchers_.end();
}

std::shared_ptr<AudioFrameDispatcher> AudioFrameDispatcher::CreateAudioDispatcher(const std::string &dispatcherName, const std::string &jsonParams)
{
    std::shared_ptr<AudioFrameDispatcher> ret;
    if (dispatcherName == "DefaultAudioFrameDispatcher") 
    {
        // return DefaultAudioFrameDispatcherManager::instance().CreateDispatcher(jsonParams);
        return std::make_shared<DefaultAudioFrameDispatcher>(jsonParams);
    }
    return ret;
}