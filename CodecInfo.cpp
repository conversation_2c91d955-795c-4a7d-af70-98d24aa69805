
#include "CodecInfo.h"
#include <string.h>

namespace panocom
{
    static const CodecInst codecInsDB[] = {
        {FRAME_FORMAT_PCMU,
         "PCMU", PCMU_8000_PT,
          8000,
          1,
          160,
          64000,
          0,
          20},
        {FRAME_FORMAT_PCMA,
         "PCMA", PCMA_8000_PT,
          8000,
          1,
          160,
          64000,
          0,
          20},
        // {FRAME_FORMAT_OPUS_16000_1,
        //  "opus", OPUS_16000_1_PT,
        //   16000,
        //   1,
        //   320,
        //   64000,
        //   0,
        //   20},
        {FRAME_FORMAT_OPUS_48000_2,
         "opus", OPUS_48000_2_PT,
          48000,
          2,
          960,
          64000,
          0,
          20},
        {FRAME_FORMAT_PCM_16000_1,
         "L16", L16_16000_1_PT,
          16000,
          2,
          320,
          64000,
          0,
          20},
        {FRAME_FORMAT_G722_16000_1,
         "G722", G722_16000_1_PT,
          8000,     // rtp timestamp rate， samplerate 16k
          1,
          160,
          64000,
          0,
          20},
        {FRAME_FORMAT_G7221_16000_32000_1,
         "G7221", G7221_16000_32000_1_PT,
          16000,     // rtp timestamp rate， samplerate 16k
          1,
          320,
          32000,
          0,
          20},
        {FRAME_FORMAT_G7221_32000_48000_1,
         "G7221", G7221_32000_48000_1_PT,
          32000,     // rtp timestamp rate， samplerate 32k
          1,
          640,
          48000,
          0,
          20},
        {FRAME_FORMAT_G7221_16000_24000_1,
         "G7221", G7221_16000_24000_1_PT,
          16000,     // rtp timestamp rate， samplerate 16k
          1,
          320,
          24000,
          0,
          20},
        {FRAME_FORMAT_G729,
         "G729", G729_8000_PT,
          8000,
          1,
          160,
          64000,
          0,
          20},
        // {FRAME_FORMAT_AAC,
        //  "AAC", AAC_16000_1_PT,
        //   16000,
        //   1,
        //   320,
        //   64000,
        //   0,
        //   20},
        // {FRAME_FORMAT_AAC_48000_2,
        //  "AAC", AAC_48000_2_PT,
        //   48000,
        //   2,
        //   960,
        //   64000,
        //   0,
        //   20},
        {FRAME_FORMAT_AAC_48000_1,
         "MP4A-LATM", AAC_48000_1_PT,
          90000,
          1,
          480,
          64000,
          0,
          20},		  
        {FRAME_FORMAT_H264,
         "H264", H264_90000_PT,
          90000,
          1,
          0,
          64000,
          66,
          40},
        {FRAME_FORMAT_H264,
         "H264", H264_90000_HP_PT,
          90000,
          1,
          0,
          64000,
          100,
          40},
        {FRAME_FORMAT_H265,
         "H265", H265_90000_PT,
          90000,
          1,
          0,
          64000,
          0,
          40},
    };

    static const int numCodecIns = sizeof(codecInsDB) / sizeof(codecInsDB[0]);

    bool getCodecInst(int pt, CodecInst &codec) {
        if (pt < 0 || pt > 127) {
            codec.pt = -1;
            strcpy(codec.name, "none");
            return false;
        }
        for (size_t i = 0; i < numCodecIns; ++i) {
            if (codecInsDB[i].pt == pt) {
                codec = codecInsDB[i];
                return true;
            }
        }
        return false;
    }

    bool getCodecInst(FrameFormat format, CodecInst &codec)
    {
        for (size_t i = 0; i < numCodecIns; i++)
        {
            if (codecInsDB[i].fmt == format)
            {
                codec = codecInsDB[i];
                return true;
            }
        }

        return false;
    }

    bool getCodecInst(const char *name, std::list<CodecInst> &codecs)
    {
        bool found = false;
        for (size_t i = 0; i < numCodecIns; i++)
        {
            if (strcasecmp(codecInsDB[i].name, name) == 0)
            {
                found = true;
                codecs.push_back(codecInsDB[i]);
            }
        }
        return found;
    }

    int getPT(FrameFormat format, int profile) {
        for (size_t i = 0; i < numCodecIns; i++) {
            if (codecInsDB[i].fmt == format) {
                if (FRAME_FORMAT_H264 != format || codecInsDB[i].profile == profile) {
                    return codecInsDB[i].pt;
                }
            }
        }
        return 255;
    }

    int getClockrate(int pt) {
       for (size_t i = 0; i < numCodecIns; i++) {
            if (codecInsDB[i].pt == pt) {
                // NOTE-: aac默认采样率是48k，这里需要修改
                if (FRAME_FORMAT_AAC_48000_1 == codecInsDB[i].fmt) {
                    return 48000;
                }
                return codecInsDB[i].rate;
            }
        }
        return -1;
    }

    FrameFormat getFrameFormat(int pt)
    {
        for (size_t i = 0; i < numCodecIns; i++)
        {
            if (codecInsDB[i].pt == pt)
            {
                return codecInsDB[i].fmt;
            }
        }

        return FRAME_FORMAT_UNKNOWN;
    }

    int32_t getSampleRate(const FrameFormat format)
    {
        for (size_t i = 0; i < numCodecIns; i++)
        {
            if (codecInsDB[i].fmt == format)
            {
                return codecInsDB[i].rate;
            }
        }

        return 0;
    }

    uint32_t getChannels(const FrameFormat format)
    {
        for (size_t i = 0; i < numCodecIns; i++)
        {
            if (codecInsDB[i].fmt == format)
            {
                return codecInsDB[i].chn;
            }
        }

        return 0;
    }

    std::string getFormatName(const FrameFormat format) {
        static std::unordered_map<FrameFormat, std::string> format_map {
            { FRAME_FORMAT_PCMU, "G.711u" },
            { FRAME_FORMAT_PCMA, "G.711A" },
            { FRAME_FORMAT_G729, "G.729" },
            { FRAME_FORMAT_AAC_48000_1, "AAC-LD" },
            { FRAME_FORMAT_OPUS_48000_2, "Opus" },
            { FRAME_FORMAT_G722_16000_1, "G.722" },
            { FRAME_FORMAT_G7221_16000_32000_1, "G.722.1" },
            { FRAME_FORMAT_G7221_16000_24000_1, "G.722.1" },
            { FRAME_FORMAT_G7221_32000_48000_1, "G.722.1c" },
        };
        if (format_map.count(format) != 0) {
            return format_map[format];
        }
        return "";
    }

namespace H264
{

Profile GetProfileEnum(int profile) {
    Profile ret = kProfileConstrainedBaseline;
    switch (profile)
    {
    case 66:
        ret = kProfileConstrainedBaseline;
        break;
    case 77:
        ret = kProfileMain;
        break;
    case 100:
        ret = kProfileHigh;
        break;
    default:
        break;
    }
    return ret;
}

std::string ProfileLevelIdToString(const ProfileLevelId& profile_level_id) {
  // Handle special case level == 1b.
  if (profile_level_id.level == kLevel1_b) {
    switch (profile_level_id.profile) {
      case kProfileConstrainedBaseline:
        return {"42f00b"};
      case kProfileBaseline:
        return {"42100b"};
      case kProfileMain:
        return {"4d100b"};
      // Level 1b is not allowed for other profiles.
      default:
        return "";
    }
  }

  const char* profile_idc_iop_string;
  switch (profile_level_id.profile) {
    case kProfileConstrainedBaseline:
      profile_idc_iop_string = "42e0";
      break;
    case kProfileBaseline:
      profile_idc_iop_string = "4200";
      break;
    case kProfileMain:
      profile_idc_iop_string = "4d00";
      break;
    case kProfileConstrainedHigh:
      profile_idc_iop_string = "640c";
      break;
    case kProfileHigh:
      profile_idc_iop_string = "6400";
      break;
    // Unrecognized profile.
    default:
      return "";
  }

  char str[7];
  snprintf(str, 7u, "%s%02x", profile_idc_iop_string, profile_level_id.level);
  return {str};    
}

std::string FmtpConfigToString(CodecParameterMap &params) {
    std::string fmtp_string;
    for (auto &pair: params) {
        fmtp_string += pair.first + "=" + pair.second + ";";
    }
    if (!fmtp_string.empty()) fmtp_string.pop_back();
    return fmtp_string;
}
} // namespace H264

}
