#include "UlpFecDecoder.h"
#include <json.hpp>

using namespace panocom;

UlpFecDecoder::UlpFecDecoder(const std::string& jsonParams)
{
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    ssrc_ = 12345;
    if (j.contains("ssrc"))
    {
        ssrc_ = j["ssrc"];
    }
    ulpfec_payload_type_ = 125;
    if (j.contains("ulpfecPayloadType"))
    {
        ulpfec_payload_type_ = j["ulpfecPayloadType"];
    }
    receiver_.reset(UlpfecReceiver::Create(ssrc_, this));
}

UlpFecDecoder::~UlpFecDecoder()
{

}

void UlpFecDecoder::onFrame(const std::shared_ptr<Frame> &f)
{
    RtpPacket packet;
    packet.SetData(f->getFrameBuffer(), f->getFrameSize());
    RTPHeader parsed_header;
    packet.GetRtpHeader(parsed_header);
    if (receiver_->AddReceivedRedPacket(parsed_header, packet.data(), packet.length(), ulpfec_payload_type_) != 0) {
        return;
    }
    receiver_->ProcessReceivedFec();
}

void UlpFecDecoder::OnRecoveredPacket(const uint8_t* packet, size_t length)
{
    auto f = Frame::CreateFrame(FRAME_FORMAT_RTP);
    f->createFrameBuffer(length);
    memcpy(f->getFrameBuffer(), packet, length);
    deliverFrame(f);
}