#include "RKAudioFrameCapturer.h"

#include <json.hpp>
#include <PNPcm/HDPcm.h>
// #include <libPCM/IODef.h>

#include "audiocodec/g711.h"
#include "Frame.h"

namespace panocom
{

namespace
{
const std::string DEV_NAME =  "/dev/pcm_dev0";
const uint32_t kSampleRate = 8000;
const uint32_t kFrameDurationInMs = 20;
} // namespace


RKAudioFrameCapturer::RKAudioFrameCapturer(const std::string& jsonParams) : nChn_(0), channel_count_(1)
{
    name_ = "RKAudioFrameCapturer";
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    if (j.contains("channel")) {
        channel_count_ = j["channel"];
    }
    std::string dev_name(DEV_NAME);
    if (j.contains("dev_name")) {
        dev_name = j["dev_name"];
    }

    int sample_per_channel = kSampleRate * kFrameDurationInMs;
    int sample_size = sample_per_channel * channel_count_ * 2;

    openPCM(dev_name.c_str());
    running_ = true;
    // capture_future_ = std::async([this, sample_size]() -> bool {
    //     return true;
    // });

    loop_thread_ = std::make_shared<hv::EventLoopThread>();
    loop_ = loop_thread_->loop();
    loop_thread_->start();

    ClearRecvBuf(nChn_);
    uint8_t readBuf[640] = { 0 };
    loop_->setInterval(kFrameDurationInMs, [this, &sample_size, &readBuf](hv::TimerID) {
        while (running_)
        {
            uint32_t buffer_size = Get_RecvLen(nChn_);
            if (buffer_size >= sample_size) {
                uint32_t read_size = ReadPcm(nChn_, readBuf, sample_size);
                // U_IOCTL_STR* IoctlMode = (U_IOCTL_STR*)readBuf;
                if (read_size >= sample_size) {
                    uint8_t* p = readBuf;
                    auto f = Frame::CreateFrame(FRAME_FORMAT_PCM_16000_1);
                    f->createFrameBuffer(sample_size);
                    uint16_t *linear_buf = (uint16_t *)f->getFrameBuffer();
                    for (int i = 0; i < sample_size; i++) {
                        linear_buf[i] = alaw_to_linear(p[i]);
                    }
                    deliverFrame(f);
                }
            }
        }
    });
}

RKAudioFrameCapturer::~RKAudioFrameCapturer()
{
    running_ = false;
    if (capture_future_.valid()) {
        auto status = capture_future_.wait_for(std::chrono::milliseconds(200));
        if (status == std::future_status::timeout) {
            jerror("RKAudioFrameCapturer capture_future_ stop timeout");
        } else if (status == std::future_status::ready){
            capture_future_.get();
        }
    }

    if (loop_thread_) {
        loop_thread_->stop();
        loop_thread_->join();
    }
    ClearRecvBuf(nChn_);
}

} // namespace panocom
