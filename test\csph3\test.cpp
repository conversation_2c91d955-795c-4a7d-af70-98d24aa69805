#if 1
#include "MediaPipeline.h"
#include <json.hpp>
#include <stdio.h>
#include <ljcore/jlog.h>
#include <hv/EventLoop.h>
#include "3raparty/include/hv/EventLoop.h"

using namespace panocom;

extern int mmi_record_audio;

int main(int argc, char *argv[])
{
    jlog_init(nullptr);
    auto loop = std::make_shared<hv::EventLoop>();
    mmi_record_audio = atoi(argv[5]);
    nlohmann::json j;
    int aec = atoi(argv[1]);
    j["aec"] = aec;
    // if (argc < 4)
    //{
        auto render = AudioFrameRender::CreateAudioRender("AudioSpecMulticaster", j.dump());
        auto capture = AudioFrameCapturer::CreateAudioCapturer("AudioSpecMulticaster", j.dump());
        j["mode"] = 1; //LP
        j["bindPort"] = atoi(argv[3]);
        j["multiIP"] = std::string(argv[2]);
        j["multiPort"] = atoi(argv[3]);
        j["localIP"] = argv[4];
        capture->updateParam(j.dump());
    //}
    // else
    // {
    //     auto render = AudioFrameRender::CreateAudioRender("AudioSpecMulticasterMT", j.dump());
    //     auto capture = AudioFrameCapturer::CreateAudioCapturer("AudioSpecMulticasterMT", j.dump());
    // }
    
    loop->run();
    return 0;
}
#else
#include "MediaManagerInterface.h"
#include <hv/EventLoop.h>
#include <ljcore/jlog.h>
#include "sdptransform.hpp"

using namespace panocom;

//std::string sdp = "v=0\r\no=- 3360842071 0 IN IP4 ************\r\ns=X80-80 phoenix\r\nc=IN IP4 ************\r\nt=0 0\r\nm=audio 3000 RTP/AVP 8 18\r\na=sendrecv\r\nm=video 0 RTP/AVP 110\r\nc=IN IP4 0.0.0.0\r\nb=AS:3840\r\na=content:main\r\na=label:1\r\na=sendrecv\r\na=rtpmap:110 H264/90000\r\na=fmtp:110 profile-level-id=64e028;max-mbps=490000;max-fs=8192;max-br=3840\r\n";

extern int mmi_print_state;

#if 1

int main(int argc, char *argv[])
{
    mmi_print_state = 1;
    auto loop = std::make_shared<hv::EventLoop>();
    MediaManagerInterface mmi;
    mmi.CreateSession("0");
    mmi.CreateSession("1");
    std::string sdp;
    mmi.GetLocalSDP("0", sdp);
    std::string sdp2;
    mmi.SetRemoteSDP("1", sdp, sdp2);
    std::string sdp3;
    mmi.SetRemoteSDP("0", sdp2, sdp3);
    loop->run();
    return 0;
}

#else

std::string params = "{\"dtls\": true, \"stun-server\": \"*************\", \"stun-port\": 3478}";

int main(int argc, char *argv[])
{
    mmi_print_state = 1;
    auto loop = std::make_shared<hv::EventLoop>();
    MediaManagerInterface mmi;
    mmi.CreateSession("0");
    mmi.CreateSession("1");
    mmi.GetLocalSDP("0", params, [&mmi, &loop](const std::string& id, const std::string& sdp, int ret){
        jinfo("0 GetLocalSDP sdp=\n%s", sdp.c_str());
        loop->runInLoop([sdp, &mmi, &loop](){
            jinfo("1 SetRemoteSDP sdp=\n%s", sdp.c_str());
            mmi.SetRemoteSDP("1", sdp, params, [&mmi, &loop](const std::string& id, const std::string& sdp, int ret){
                loop->runInLoop([sdp, &mmi](){
                    jinfo("0 SetRemoteSDP sdp=\n%s", sdp.c_str());
                    mmi.SetRemoteSDP("0", sdp, params, [](const std::string& id, const std::string& sdp, int ret){});
                });
            });
        });
    });
    loop->run();
    return 0;
}

#endif
#endif