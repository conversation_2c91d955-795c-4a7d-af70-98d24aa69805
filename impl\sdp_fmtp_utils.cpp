#include "sdp_fmtp_utils.h"
#include <json.hpp>
#include <ljcore/jlog.h>
namespace panocom {
// Max frame rate for video.
const char kFmtpMaxFrameRate[] = "max-fr";
// Max frame size for video.
const char kFmtpMaxFrameSize[] = "max-fs";
const int kFmtpFrameSizeSubBlockPixels = 256;    // 16^2

int SdpFmtpUtils::ParseSdpFmtpLine(const std::string& fmtp, const std::string& key)
{
    nlohmann::json j;
    if (nlohmann::json::accept(fmtp)) {
        j = nlohmann::json::parse(fmtp);
        if (j.contains(key)) {
            int value = j[key];
            if (value >= 0) { 
                return value; 
            }
        }
    }
    return -1;
}

int SdpFmtpUtils::PsrseSdpForMaxFrameRate(const std::string& fmtp)
{
    return ParseSdpFmtpLine(fmtp, kFmtpMaxFrameRate);
}
int SdpFmtpUtils::PsrseSdpForMaxFrameSize(const std::string& fmtp)
{
    auto i = ParseSdpFmtpLine(fmtp, kFmtpMaxFrameSize);
    return i > 0 ? i * kFmtpFrameSizeSubBlockPixels : -1;
}
} // namespace panocom