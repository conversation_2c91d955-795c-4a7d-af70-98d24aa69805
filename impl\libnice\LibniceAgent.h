// created by gyj 2024-5-23
#ifndef P_LibniceAgent_h
#define P_LibniceAgent_h
//#ifdef USE_LIBNICE
#include "Frame.h"
#include "FramePipeline.h"
#include <hv/EventLoopThread.h>
#include <nice/agent.h>
#include <functional>
#include <glib.h>

namespace panocom
{
    class LibniceAgent: public FramePipeline
    {
    public:
        LibniceAgent(const std::string& jsonParams);
        ~LibniceAgent() override;

        int updateParam(const std::string& jsonParams) override;
    
        void onFrame(const std::shared_ptr<Frame>& frame) override;
        void start();
        void stop();

        static void cb_candidate_gathering_done(NiceAgent *agent, guint stream_id, gpointer data);
        static void cb_component_state_changed(NiceAgent *agent, guint stream_id, guint component_id, guint state, gpointer data);
        static void cb_nice_recv(NiceAgent *agent, guint stream_id, guint component_id, guint len, gchar *buf, gpointer data);
    private:
        hv::EventLoopThread eventLoopThread_;
        std::unique_ptr<std::thread> thread_;

        GMainContext* context_;
        GMainLoop* loop_;

        std::atomic<bool> started_;
        std::atomic<bool> closed_;

        NiceAgent* agent_;
        std::string stun_addr_;
        uint32_t stun_port_;
        uint32_t stream_id_;
        int controlling_;
        FrameFormat fmt_;

        bool ready_ = false;
    };
}
#endif
#endif