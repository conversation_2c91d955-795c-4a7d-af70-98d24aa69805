#include "G711AudioFrameDecoder.h"
#include "Frame.h"
#include "g711.h"
#include <json.hpp>
#include <ljcore/jlog.h>

extern int mmi_record_audio;

using namespace panocom;

static size_t WebRtcG711_DecodeA(const uint8_t *encoded,
                                 size_t len,
                                 int16_t *decoded,
                                 int16_t *speechType)
{
  size_t n;
  for (n = 0; n < len; n++)
    decoded[n] = alaw_to_linear(encoded[n]);
  *speechType = 1;
  return len;
}

static size_t WebRtcG711_DecodeU(const uint8_t *encoded,
                                 size_t len,
                                 int16_t *decoded,
                                 int16_t *speechType)
{
  size_t n;
  for (n = 0; n < len; n++)
    decoded[n] = ulaw_to_linear(encoded[n]);
  *speechType = 1;
  return len;
}

G711AudioFrameDecoder::G711AudioFrameDecoder(const std::string &jsonParams) : debug_mode_(0)
{
  FN_BEGIN;
  name_ = "G711AudioFrameDecoder";
  nlohmann::json j;
  if (jsonParams != "")
    j = nlohmann::json::parse(jsonParams);
  fmt_ = FRAME_FORMAT_PCMA;
  if (j.contains("ulaw"))
  {
    if (j["ulaw"])
    {
      fmt_ = FRAME_FORMAT_PCMU;
    }
  }
  samplerate_ = 8000;
  if (j.contains("samplerate"))
  {
    samplerate_ = j["samplerate"];
  }
  if (j.contains("debug")) debug_mode_ = j["debug"];
  FN_END;
}

G711AudioFrameDecoder::~G711AudioFrameDecoder()
{
  FN_BEGIN;
  FN_END;
}

void G711AudioFrameDecoder::onFrame(const std::shared_ptr<Frame> &frame)
{
  if (debug_mode_ == 1 && frame->getFrameSize() > 0) {
    frame->setFrameFormat(FRAME_FORMAT_PCM_8000_1);
    deliverFrame(frame);
    return;
  }
  if (frame->getFrameSize() == 0)
		return;
  std::shared_ptr<Frame> f;
  if (samplerate_ == 8000)
  {
    f = Frame::CreateFrame(FRAME_FORMAT_PCM_8000_1);
  }
  else if (samplerate_ == 16000)
  {
    f = Frame::CreateFrame(FRAME_FORMAT_PCM_16000_1);
  }
  else
  {
    jerror("not support");
    return;
  }
  f->createFrameBuffer(frame->getFrameSize() * 2);
  int16_t speechType = 0;
  if (fmt_ == FRAME_FORMAT_PCMA)
  {
    WebRtcG711_DecodeA(frame->getFrameBuffer(), frame->getFrameSize(), (int16_t *)f->getFrameBuffer(), &speechType);
  }
  else if (fmt_ == FRAME_FORMAT_PCMU)
  {
    WebRtcG711_DecodeU(frame->getFrameBuffer(), frame->getFrameSize(), (int16_t *)f->getFrameBuffer(), &speechType);
  }
  if (mmi_record_audio)
  {
    if (!pf_)
    {
      std::string fileName = name_ + std::to_string(samplerate_) + "-" + std::to_string((long)this) + ".pcm";
      pf_ = fopen(fileName.c_str(), "w+b");
    }
    if (pf_)
    {
      fwrite(f->getFrameBuffer(), 1, f->getFrameSize(), pf_);
      // jinfo("711framesize: %d, buffersize: %d", f->getFrameSize(),f->getFrameBufferSize());
    }
  }
  f->setGain(frame->getGain());
  deliverFrame(f);
  printOutputStatus("G711AudioFrameDecoder");
}