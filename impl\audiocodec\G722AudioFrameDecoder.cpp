#include "G722AudioFrameDecoder.h"
#include <json.hpp>

namespace panocom {
G722AudioFrameDecoder::G722AudioFrameDecoder(const std::string &jsonParams)
    : fmt_(FRAME_FORMAT_G722_16000_1)
    , rate_(64000) {
    name_ = "G722AudioFrameDecoder";
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    // if (j.contains("rate"))
    //     rate_ = j["rate"];
    decoded_buf_ = new int16_t[320];
    state_ = nullptr;
    state_ = WebRtc_g722_decode_init(state_, rate_, 2);
    if (state_) {
        running_ = true;
        decode_future_ = std::async([this]() -> bool {
            while (running_) {
                std::unique_lock<std::mutex> lock(mutex_);
                queue_available_.wait(lock, [this] { return !frame_queue_.empty() || !running_; });
                if (!running_) {
                    break;
                }
                auto frame = std::move(frame_queue_.front());
                frame_queue_.pop();
                lock.unlock();
                if (state_) {
                    size_t ret = WebRtc_g722_decode(state_, decoded_buf_, frame->getFrameBuffer(), frame->getFrameBufferSize());
                    if (ret > 0) {
                        // if (!ofs_.is_open()) ofs_.open("decoded.pcm", std::ios::binary | std::ios::out);
                        // ofs_.write((char*)decoded_buf_, ret * 2);
                        std::shared_ptr<Frame> out_frame = Frame::CreateFrame(FRAME_FORMAT_PCM_16000_1);
                        out_frame->createFrameBuffer(ret * 2);
                        memcpy(out_frame->getFrameBuffer(), (char *)decoded_buf_, ret * 2);
                        out_frame->setGain(frame->getGain());
                        deliverFrame(out_frame);
                    } else
                        printf("g722 decode failed\n");
                }
            }
            return true;
        });
    }
}
G722AudioFrameDecoder::~G722AudioFrameDecoder() {
    running_ = false;
    queue_available_.notify_all();
	if (decode_future_.valid()) {
        auto status = decode_future_.wait_for(std::chrono::milliseconds(500));
		if (status == std::future_status::timeout) {
			 jerror("g722 decoder stop timeout");
		} else {
			bool res = decode_future_.get();
            jinfo("g722 decoder stop status: %d", res);
		}
	}    
    if (state_)
        WebRtc_g722_decode_release(state_);
    state_ = nullptr;
    if (decoded_buf_) delete[] decoded_buf_;
    decoded_buf_ = nullptr;
    // if (ofs_.is_open()) ofs_.close();
}
void G722AudioFrameDecoder::onFrame(const std::shared_ptr<Frame> &frame) {
    if (frame->getFrameSize() == 0 || !state_ || !running_)
        return;
    {
        std::lock_guard<std::mutex> lock(mutex_);
        if (frame_queue_.size() > 20) {
            frame_queue_.pop();
        }
        frame_queue_.emplace(frame);
    }
    queue_available_.notify_one();
}
} // namespace panocom
