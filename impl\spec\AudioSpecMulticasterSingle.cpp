#include "AudioSpecMulticasterSingle.h"

#include <unistd.h>
#include <sstream>
#include <iomanip>

#include "Frame.h"
#include "Utils.h"
#include <json.hpp>
#include <ljcore/jlog.h>
#include <resampler/resampler.h>
#include <resampler/push_resampler.h>
#include "audiomixer/CAudioMix.h"
#include "ShmPcm.h"

using namespace panocom;

namespace {
/*
开关：devmem 0xf2200120 16 0x00
o_dup_sw  <= iv_arm_data[0]; 1：打开半双工 0：关闭 ，默认关
o_afc_sw  <= iv_arm_data[1]; 1：打开防啸叫 0：关闭 ，默认关
o_aec_sw  <= iv_arm_data[2]; 1：打开AEC    0：关闭 ，默认开
o_lec_sw  <= iv_arm_data[3]; 1：打开LEC    0：关闭 ，默认开
*/
int32_t s_reg_val = 0xf;
void ConfigRegister(int32_t value) {
    s_reg_val = value;
    std::string val_str = "devmem 0xf2200120 16 0x";
    std::stringstream ss;
    ss << std::hex << std::setfill('0') << std::setw(2) << value;
    val_str += ss.str();
    jinfo("system: %s", val_str.c_str());
    system(val_str.c_str());
}
}


#ifdef __cplusplus
extern "C" {
#endif

enum {
  kAecNlpConservative = 0,
  kAecNlpModerate,
  kAecNlpAggressive
};

enum {
  kAecFalse = 0,
  kAecTrue
};

typedef struct {
	bool pcm_logging; //是否开启数据打印，默认是false;
	bool aec_on;      //是否开启回声消除aec,默认是true; 
	bool agc_on;      //是否开启自动增益agc,默认是false;
	bool ns_on;       //是否开启降噪,默认是true;
	bool cng_on;      //是否打开舒适噪音,默认是false;
	bool howlingSuppress_on; //是否打开防啸叫，默认是false;
    bool lec_off;     // 是否关闭线路回声
    bool sip_mode;
    bool ai_denoise_on;
    bool goose_mic_dropped;
} AecConfig;

int32_t rtc_aec_create(void** aecInst);
int32_t rtc_aec_free(void* aecInst);
int32_t rtc_aec_init(void* aecInst, int32_t sampFreq);
int32_t rtc_aec_buffer_farend(void* aecInst,
		const int16_t* farend,
		int16_t nrOfSamples);
int32_t rtc_aec_process(void* aecInst,
		const int16_t* nearend,
		int16_t* out,
		int16_t nrOfSamples);
int32_t rtc_aec_process_mics(void* aecInst,
		const int16_t* nearend,
		int16_t* out,
		int16_t nrOfSamples, int micNum);
int rtc_aec_set_config(void* aecInst, AecConfig config);
#include ""
#ifdef __cplusplus
} // extern "C"
#endif

#if 0
int32_t rtc_aec_create(void** aecInst)
{
    return 0;
}

int32_t rtc_aec_free(void* aecInst)
{
    return 0;
}

int32_t rtc_aec_init(void* aecInst, int32_t sampFreq)
{
    return 0;
}

int32_t rtc_aec_buffer_farend(void* aecInst,
		const int16_t* farend,
		int16_t nrOfSamples)
{
    return 0;
}

int32_t rtc_aec_process(void* aecInst,
		const int16_t* nearend,
		int16_t* out,
		int16_t nrOfSamples)
{
    return 0;
}

int rtc_aec_set_config(void* aecInst, AecConfig config)
{
    return 0;
}
#endif

extern int mmi_record_audio;

void AudioSpecMulticasterSingle::farendReadLoop()
{
    // farendReadRunning_ = 1;
    uint8_t farend[MAX_DELAY_MS * BUF_LEN * 2] = { 0 };
    int count = 0;
    ShmPcm::inst().ClearReadBuf(CH_MIX);
    while (farendReadRunning_)
    {
        if (startPlay_)
        {
            // FIXME：读取失败未处理
            int len = ShmPcm::inst().ReadPcm(CH_MIX, farend + count, BUF_LEN);
            writeRecord(recvf, farend + count, BUF_LEN);
            count += len;
            if (count >= delay_ * BUF_LEN)
            {
                rtc_aec_buffer_farend(aec_.get(), (short*)farend, delay_ * SAMPLE_LEN);
                count = 0;
            }
        }
        else
        {
            usleep(1000);
        }
    }
    std::unique_lock<std::mutex> l(mutexFarendRead_);
    farendReadBBuf_.Clear();
}

#if H3_NEW_DRIVER
void AudioSpecMulticasterSingle::captureLoop() {
    jinfo("start AudioSpecMulticasterSingle::captureLoop");
    setThreadAffinity(7);
    uint8_t captureSample[BUF_LEN * 2] = { 0 };
    uint8_t farend[MAX_DELAY_MS * BUF_LEN * 2] = { 0 };
    short inbuf[MAX_DELAY_MS * MAX_CAPTURER * BUF_LEN * 2] = { 0 };
    short outbuf[MAX_DELAY_MS * BUF_LEN * 2] = { 0 };
    short muted_buf[MAX_DELAY_MS * BUF_LEN] = { 0 }; // 静音缓冲区
    int count = 0;
    ShmPcm::inst().ClearReadBuf(CH_MIX);
    for (int i = 0; i < MAX_CAPTURER; i++) {
        ShmPcm::inst().ClearReadBuf(i);
    }
    int fpga_tick_last = ShmPcm::inst().GetTickFPGA();
    int discard_frames_count_lp = discard_frames_count_lp_;
    while (capturing_) {
        // delay_ = 10;
        int fpga_tick_a = ShmPcm::inst().GetTickFPGA();
        int gap = fpga_tick_a - fpga_tick_last;
        if (gap >= 80000) {
            fpga_tick_last = fpga_tick_a;
            jinfo("fpga_tick_a: %d, fpga_tick_last: %d, fpga tick gap: %d, write %d data", fpga_tick_a, fpga_tick_last, gap, count);
            count = 0;
        }
        // auto tick = ShmPcm::inst().Sync();
        // auto gap = tick - last_tick_;
        // jinfo("gap = %d", gap);
        int available = ShmPcm::inst().Get_RecvLen(0);
        if (available >= delay_ * BUF_LEN) {
            int index = (mic_type_ == TYPE_INTERNAL_MIC ? 1 : 0);
            bool flag = false;
            int len = 0;

            for (int i = 0; i < MAX_CAPTURER; i++) {
                len = ShmPcm::inst().ReadPcm(i + 1, (char *)(inbuf + i * delay_ * SAMPLE_LEN), delay_ * BUF_LEN, false);
            }
            {
                // int farend_available = ShmPcm::inst().Get_RecvLen(CH_MIX);
                // len = ShmPcm::inst().ReadPcm(CH_MIX, farend, farend_available, false);
                len = ShmPcm::inst().ReadPcm(CH_MIX, farend, delay_ * BUF_LEN, false);
                if (!muted_) {
                    std::unique_lock<std::mutex> locker(aecMutex_);
#if USEMONITOR
                    if (enableAEC_ > 1 && module_disable_ > 0 && aec_.get()) {
#else
                    if (enableAEC_ > 1 && aec_.get()) {
#endif
                        if (len > 0) {
                            // if (!ofs_2.is_open()) ofs_2.open("aecfarend.pcm", std::ios::out | std::ios::binary);
                            // ofs_2.write((char*)farend, delay_ * SAMPLE_LEN * 2);
                            // rtc_aec_buffer_farend(aec_.get(), (int16_t *)farend, len);
                            rtc_aec_buffer_farend(aec_.get(), (int16_t *)farend, delay_ * SAMPLE_LEN);
                        }
                        // if (useHeadset_) {
                        //     rtc_aec_process(aec_.get(), inbuf, outbuf, delay_ * SAMPLE_LEN);
                        // } else
                        {
                            // if (!ofs_.is_open()) ofs_.open("aecin.pcm", std::ios::out | std::ios::binary);
                            // ofs_.write((char*)inbuf, delay_ * SAMPLE_LEN * 2);
                            rtc_aec_process_mics(aec_.get(), inbuf, outbuf, delay_ * SAMPLE_LEN, MAX_CAPTURER);
                            // if (!ofs_1.is_open()) ofs_1.open("aecout.pcm", std::ios::out | std::ios::binary);
                            // ofs_1.write((char*)outbuf, delay_ * SAMPLE_LEN * 2);
                        }
                    } else if (mic_type_ == TYPE_INTERNAL_MIC && !useHeadset_) {
                        // CAudioMix::AddAndNormalization(inbuf + delay_ * SAMPLE_LEN, MAX_CAPTURER - 1, delay_ * SAMPLE_LEN, outbuf);
                        memcpy(outbuf, inbuf + delay_ * SAMPLE_LEN, delay_ * BUF_LEN);
                    } else {
                        memcpy(outbuf, inbuf, delay_ * BUF_LEN);
                    }
                } else {    // mute
                    memcpy(outbuf, muted_buf, delay_ * BUF_LEN);
                }
            }
            count += delay_ * SAMPLE_LEN * 2;
            frames_captured_ += delay_ * BUF_LEN;
#if USEMONITOR
            if (use_original_ > 0)
                len = ShmPcm::inst().WritePcm(CH_PSTN_SEND, (unsigned char*)inbuf, delay_ * BUF_LEN, false);
            else 
                len = ShmPcm::inst().WritePcm(CH_PSTN_SEND, (unsigned char*)outbuf, delay_ * BUF_LEN, false);
#else
            if (mode_ == MODE_PSTN) {
                if (discard_frames_count_lp > 0) {
                    discard_frames_count_lp -= len;
                    if (discard_frames_count_lp <= 0) {
                        discard_frames_count_lp = 0;
                    }
                    continue;
                }
                len = ShmPcm::inst().WritePcm(CH_PSTN_SEND, (unsigned char *)outbuf, delay_ * BUF_LEN, false);                
            }
            else {
                {
                    std::unique_lock<std::mutex> locker(uplinkMutex_);
                    bbuf_.WriteBytes((const char *)outbuf, delay_ * BUF_LEN);
                }
                setUplinkData((const char *)(outbuf), delay_ * BUF_LEN);
            }
#endif
            // int fpga_tick_c = ShmPcm::inst().GetTickFPGA();
            // jinfo("fpga_tick_c: %d, fpga_tick_b: %d, once gap: %d", fpga_tick_b, fpga_tick_c, fpga_tick_c - fpga_tick_b);
            // memset((void*)inbuf, 0, sizeof(inbuf));
            // memset((void*)outbuf, 0, sizeof(outbuf));

        } else {
            auto ret = ShmPcm::inst().Sync();
        }
        // int fpga_tick_b = ShmPcm::inst().GetTickFPGA();
        // jinfo("fpga tick gap of one loop: %d", fpga_tick_b - fpga_tick_a);
    }
    {

        std::unique_lock<std::mutex> locker(captureMutex_);
        capturebuf_.Clear();
    }
    {
        std::unique_lock<std::mutex> l(uplinkMutex_);
        bbuf_.Clear();
    }
    if (ofs_.is_open()) ofs_.close();
    if (ofs_1.is_open()) ofs_1.close();
}

void AudioSpecMulticasterSingle::lecRefReadLoop()
{
    // lecRefRunning_ = 1;
    int delay = 1;
    uint8_t lecRef[MAX_DELAY_MS * BUF_LEN * 2] = { 0 };
    setThreadAffinity(5);
    uint8_t downlinkSample[MAX_DELAY_MS * BUF_LEN * 2] = { 0 };
    uint8_t lecRcvSample[BUF_LEN * MAX_CAPTURER * MAX_DELAY_MS * 2] = { 0 };
    uint8_t outputSample[MAX_DELAY_MS * BUF_LEN * 2] = { 0 };
    ShmPcm::inst().ClearReadBuf(CH_LEC_FAREND);
    ShmPcm::inst().ClearReadBuf(CH_PSTN_RECV);
    int count = 0;
    int temp_tick = ShmPcm::inst().GetTickFPGA();
    while (lecRefReadRunning_) {
        if (startPlay_)
        {
            // FIXME：读取失败未处理
            int available = ShmPcm::inst().Get_RecvLen(CH_LEC_FAREND);
            int available_pstn_recv = ShmPcm::inst().Get_RecvLen(CH_PSTN_RECV);
            if ((enableLEC_ < 2 && available >= delay * BUF_LEN) || (enableLEC_ > 1 && available_pstn_recv >= delay * BUF_LEN)) {
                // int now_tick = ShmPcm::inst().GetTickFPGA();
                // jinfo("lec_ref gap = %d", now_tick - temp_tick);
                // temp_tick = now_tick;
                
                int len = ShmPcm::inst().ReadPcm(CH_LEC_FAREND, lecRef, delay * BUF_LEN, false);
                if (mode_ == MODE_PSTN) {
                    len = ShmPcm::inst().ReadPcm(CH_PSTN_RECV, lecRcvSample, delay * BUF_LEN, false);
                }
                if (len > 0) {
                    frames_lec_ref_ += len;
                    if (len >= BUF_LEN) {
                        if (mode_ == MODE_PSTN) {
                            if (enableLEC_ > 1) {
                                if (lec_) {
                                    // if (!ofs_.is_open()) ofs_.open("lecin.pcm", std::ios::out | std::ios::binary);
                                    // ofs_.write(lecRcvSample, delay * BUF_LEN);
                                    rtc_aec_buffer_farend(lec_.get(), (short*)lecRef, delay * SAMPLE_LEN);
                                    rtc_aec_process_mics(lec_.get(), (short*)lecRcvSample, (short*)outputSample, delay * SAMPLE_LEN, MAX_CAPTURER);
                                    // if (!ofs_1.is_open()) ofs_1.open("lecout.pcm", std::ios::out | std::ios::binary);
                                    // ofs_1.write(outputSample, delay * BUF_LEN);
                                }
                                len = ShmPcm::inst().WritePcm(CH_COMMON, (uint8_t *)outputSample, delay * BUF_LEN, false);
                                // len = ShmPcm::inst().WritePcm(CH_COMMON, (uint8_t *)lecRcvSample, delay * BUF_LEN, false);
                            } else 
                            {
                                len = ShmPcm::inst().WritePcm(CH_COMMON, (uint8_t *)lecRcvSample, delay * BUF_LEN, false);
                            }

                            // std::unique_lock<std::mutex> l(lecRefMutex_);
                            // lefRefReadBuf_.WriteBytes(lecRef, BUF_LEN);
                        } else {
                            {
                                std::unique_lock<std::mutex> locker(uplinkMutex_);
                                bbuf_.WriteBytes((const char *)lecRef, delay * BUF_LEN);
                            }
                            count += delay * BUF_LEN;
                            setUplinkData((const char *)(lecRef), delay * BUF_LEN);
                            // if (!ofs_.is_open()) ofs_.open("send.pcm", std::ios::binary | std::ios::out);
                            // ofs_.write(lecRef, delay * BUF_LEN);
                        }
                    }
                    memset(lecRef, 0, sizeof(lecRef));
                    memset(lecRcvSample, 0, sizeof(lecRcvSample));
                    memset(outputSample, 0, sizeof(outputSample));
                    // usleep(200);
                } else {
                    jerror("ReadPcm farend data for lec ref failed, len = %d", len);
                }
            } else {
                auto ret = ShmPcm::inst().Sync();
                // jinfo("tick = %d", ret);
            }
        } else {
            usleep(1000);
        }
    }
    std::unique_lock<std::mutex> l(lecRefMutex_);
    lefRefReadBuf_.Clear();
}

void AudioSpecMulticasterSingle::lecProcessLoop() {
    // toolkit::setThreadName("lecProcessLoop");
    const int delay = 10;
    setThreadAffinity(4);
    short inbuf1[SAMPLE_LEN * MAX_CAPTURER * delay * 2] = { 0 };
    short inbuf2[SAMPLE_LEN * delay * 2] = { 0 };
    short outbuf1[SAMPLE_LEN * delay  * 2] = { 0 };
    while (lecProcessRunning_) {
        bool ready = false;
        {
            std::lock_guard<std::mutex> l1(pstnInMutex_);
            std::lock_guard<std::mutex> l(lecRefMutex_);
            if (pstnInBuf_.Length() >= SAMPLE_LEN * delay * 2 && lefRefReadBuf_.Length() >= SAMPLE_LEN * delay * 2) {
                memcpy(inbuf1, pstnInBuf_.Data(), SAMPLE_LEN * delay * 2);
                pstnInBuf_.Consume(SAMPLE_LEN * delay * 2);
                memcpy(inbuf2, lefRefReadBuf_.Data(), SAMPLE_LEN * delay * 2);
                lefRefReadBuf_.Consume(SAMPLE_LEN * delay * 2);
                ready = true;
            }
        }
        if (ready)
        {
            char* p = (char*)outbuf1;
            if (enableLEC_ > 1)
            {
                std::lock_guard<std::mutex> locker(lecMutex_); 
                if (lec_) {
                    rtc_aec_buffer_farend(lec_.get(), inbuf2, delay * SAMPLE_LEN);
                    rtc_aec_process_mics(lec_.get(), inbuf1, outbuf1, delay * SAMPLE_LEN, MAX_CAPTURER);
                }
            }
            else
            {
                p = (char*)inbuf1;
            }
            {
                std::unique_lock<std::mutex> locker(mutexPlay_);
                playbbuf_.WriteBytes(p, SAMPLE_LEN * delay * 2);
            }
            usleep(3000);
        }
    }
    jinfo("lecProcessLoop end");
    std::unique_lock<std::mutex> locker(mutexPlay_);
    playbbuf_.Clear();
}

void AudioSpecMulticasterSingle::aecProcessLoop() {
    int len = 0;
    struct timeval startTime;
    struct timeval endTime;

    // 不知道谁搞越界了，双倍缓存避一下
    short inbuf[MAX_DELAY_MS * MAX_CAPTURER * SAMPLE_LEN * 2] = { 0 };
    short outbuf[MAX_DELAY_MS * SAMPLE_LEN * 2] = { 0 };
    setThreadAffinity(6);
    while (aecProcessRunning_) {
        if (processNum == 0)
            gettimeofday(&startTime, NULL);
        int count = useHeadset_ ? 1 : MAX_CAPTURER;
        if (capturebuf_.Length() >= delay_ * MAX_CAPTURER * BUF_LEN) {
            std::unique_lock<std::mutex> locker(captureMutex_);
            memcpy(inbuf, capturebuf_.Data(), delay_ * MAX_CAPTURER * BUF_LEN);
            frames_captured_ += delay_ * BUF_LEN;
            capturebuf_.Consume(delay_ * MAX_CAPTURER * BUF_LEN);
        } else {
            usleep(1);
            continue;
        }
        if (enableAEC_ > 1)
        {
            std::unique_lock<std::mutex> locker(aecMutex_);
            if (useHeadset_) {
                rtc_aec_process(aec_.get(), inbuf, outbuf, delay_ * SAMPLE_LEN);
            } else {
                rtc_aec_process_mics(aec_.get(), inbuf, outbuf, delay_ * SAMPLE_LEN, MAX_CAPTURER);
            }
            writeRecord(nearf, (char *)outbuf, BUF_LEN);
        }
#if H3_NEW_DRIVER
        int len = ShmPcm::inst().WritePcm(CH_PSTN_SEND, (unsigned char *)outbuf, delay_ * BUF_LEN, false);
        if (mode_ == MODE_PSTN)
            frames_to_send_ += delay_ * BUF_LEN;
#else
        {
            std::unique_lock<std::mutex> locker(uplinkMutex_);
            bbuf_.WriteBytes((const char *)outbuf, delay_ * BUF_LEN);
        }
        { setUplinkData((const char *)outbuf, delay_ * BUF_LEN); }
#endif
    }
}
#endif

void AudioSpecMulticasterSingle::farendWriteLoop()
{
    // farendWriteRunning_ = 1;
    uint8_t farend[MAX_DELAY_MS * BUF_LEN * 2] = { 0 };
    while (farendWriteRunning_)
    {
        if (startPlay_)
        {
            ByteBuffer bbuf;
            {
                std::unique_lock<std::mutex> l(mutexFarendRead_);
                if (farendReadBBuf_.Length() >= delay_ * BUF_LEN)
                {
                    int count = farendReadBBuf_.Length() / (delay_ * BUF_LEN);
                    bbuf.WriteBytes(farendReadBBuf_.Data(), delay_ * BUF_LEN * count);
                    farendReadBBuf_.Consume(delay_ * BUF_LEN * count);
                }
            }

			if (!startAecFarend_ && bbuf.Length() >= delay_ * BUF_LEN)
			{
				startAecFarend_ = true;
				for (int i =0;i< MAX_CAPTURER;i++)
				{
					std::unique_lock<std::mutex> locker(captureMutexs_[i]);
					capturebufs_[i].Clear();
				}
			}

            while (bbuf.Length() >= delay_ * BUF_LEN)
            {
                std::unique_lock<std::mutex> locker(aecMutex_);
                rtc_aec_buffer_farend(aec_.get(), (short*)bbuf.Data(), delay_ * SAMPLE_LEN);
                bbuf.Consume(delay_ * BUF_LEN);
            }
        }
        else
        {
            usleep(1000);
        }
    }
}

void AudioSpecMulticasterSingle::captureLoop(int index)
{
    if (index < 0 || index >= MAX_CAPTURER)
        return;
    jinfo("start AudioSpecMulticasterSingle::captureLoop %d", index);
    setThreadAffinity(7);
	// captureRunnings_[index] = 1;
    //
    uint8_t captureSample[BUF_LEN * 2] = { 0 };
	while (captureRunnings_[index])
	{
		if (pcmFd_ >= 0)
		{
            if (index == 0 && useHeadset_)
            {
                ReadPcm(CH_HEADSET, captureSample, BUF_LEN);
            }
            else
            {
                int len = ReadPcm(CH_GOOSE + index, captureSample, BUF_LEN);
            }
            writeRecord(recordf[index], captureSample, BUF_LEN);
			{
				std::unique_lock<std::mutex> locker(captureMutexs_[index]);
                if (capturebufs_[index].Length() > 1024)
                {
                    if (index == 0) jinfo("captruebufs_ drop data");
                    capturebufs_[index].Consume(capturebufs_[index].Length() - 1024);
                }
                if (index != 0 && useHeadset_)
                {
                    // 用耳麦，只有通道0工作
                }
                else
                {
                    capturebufs_[index].WriteBytes((const char *)captureSample, BUF_LEN);
                }
			}
		}
		else
		{
			usleep(10 * 1000);
		}
	}
    std::unique_lock<std::mutex> locker(captureMutexs_[index]);
    capturebufs_[index].Clear();
}



template<typename A, typename B>
static inline void write4Byte(A &&a, B &&b) {
    memcpy(&a, &b, sizeof(a));
}

int AudioSpecMulticasterSingle::createSocket(const std::string& src)
{
    int socketFd = socket(AF_INET, SOCK_DGRAM, 0);
    jinfo("createSocket %d", socketFd);
    if (socketFd > 0)
    {
        int ret = 0;
        int opt = 1;
        ret = setsockopt(socketFd, SOL_SOCKET, SO_REUSEADDR, (char *)&opt, static_cast<socklen_t>(sizeof(opt)));
        if (ret == -1)
        {
            jerror("设置 SO_REUSEADDR 失败!");
        }
        ret = setsockopt(socketFd, SOL_SOCKET, SO_REUSEPORT, (char *)&opt, static_cast<socklen_t>(sizeof(opt)));
        if (ret == -1)
        {
            jerror("设置 SO_REUSEPORT 失败!");
        }
        struct sockaddr_in servaddr;
        bzero(&servaddr, sizeof(servaddr));
        servaddr.sin_family = AF_INET;
        servaddr.sin_addr.s_addr = INADDR_ANY;
        servaddr.sin_port = htons(bindPort_);
        if (bind(socketFd, (struct sockaddr *)&servaddr, sizeof(servaddr)) == -1)
        {
            jerror("%s bind fail", __FUNCTION__);
        }
        int ttl = 128;
        ret = setsockopt(socketFd, IPPROTO_IP, IP_MULTICAST_TTL, (const char*)&ttl, sizeof(int));
        if (ret == -1)
        {
            jerror("设置 IP_MULTICAST_TTL 失败!");
        }
        if (src == "")
        {
            struct ip_mreq imr;
            bzero(&imr, sizeof(imr));
            imr.imr_multiaddr.s_addr = inet_addr(multiIP_.c_str());
            imr.imr_interface.s_addr = inet_addr(localIP_.c_str());
            ret = setsockopt(socketFd, IPPROTO_IP, IP_ADD_MEMBERSHIP, (char *)&imr, sizeof(struct ip_mreq));
            if (ret == -1)
            {
                jerror("设置 IP_ADD_MEMBERSHIP 失败!");
            }
        }
        else
        {
            struct ip_mreq_source imr;
            write4Byte(imr.imr_multiaddr, inet_addr(multiIP_.c_str()));
            write4Byte(imr.imr_sourceaddr, inet_addr(src.c_str()));
            write4Byte(imr.imr_interface, inet_addr(localIP_.c_str()));

            ret = setsockopt(socketFd, IPPROTO_IP, IP_ADD_SOURCE_MEMBERSHIP, (char *) &imr, sizeof(struct ip_mreq_source));
            if (ret == -1) {
                jerror("setsockopt IP_ADD_SOURCE_MEMBERSHIP failed");
            }
        }
        
        uint8_t loop = 0;
        ret = setsockopt(socketFd, IPPROTO_IP, IP_MULTICAST_LOOP, (char *)&loop, sizeof(loop));
        if (ret == -1)
        {
            jerror("setsockopt IP_MULTICAST_LOOP failed");
        }
        int multicastAll = 0;
        (void)setsockopt(socketFd, IPPROTO_IP, IP_MULTICAST_ALL, (void*)&multicastAll, sizeof multicastAll);
        jinfo("createSocket multicast fd(%d) at %d to %s:%d", socketFd, bindPort_, multiIP_.c_str(), multiPort_);
    }
    else
    {
        jerror("%s create socket fail", __FUNCTION__);
    }
    return socketFd;
}

bool AudioSpecMulticasterSingle::getDownlinkData()
{
    uint8_t downlinkSample[MAX_DELAY_MS * BUF_LEN * 2] = { 0 };
    switch (mode_)
    {
    case MODE_MULTICAST:
        if (socketFd_ > 0)
        {
            //jinfo("START RECV\n");
            socklen_t addrlen;
            struct sockaddr_in addr;
            //bzero(&addr, sizeof(addr));
            int len = recvfrom(socketFd_, downlinkSample, delay_ * BUF_LEN, 0, (struct sockaddr *)&addr, &addrlen);
            int count = CH_MAX;
            if (len > 0)
            {
                // if (localAddr_.sin_addr.s_addr == addr.sin_addr.s_addr && localAddr_.sin_port == addr.sin_port)
                // {
                //    return false;
                // }
                // uint32_t addrUInt32 = htonl(addr.sin_addr.s_addr);
                // unsigned char* p = (unsigned char*)&addrUInt32;
                // if (p[0] == 0)
                // {
                //    return false;
                // }
                //jinfo("recvfrom peer(%s:%d) %d\n", inet_ntoa(addr.sin_addr), ntohs(addr.sin_port), len);
                uint64_t key = addr.sin_addr.s_addr;
                key = key << 32 | addr.sin_port;
                int chn = -1;
                if (devChnMap_.find(key) == devChnMap_.end())
                {
                    std::string src = inet_ntoa(addr.sin_addr);
                    if (src == "127.0.0.1" || src == "127.0.0.0" || src == "0.0.0.0")
                    { // ignore local
                        break;
                    }
                    jinfo("create new peer(%s:%d) %d\n", src.c_str(), ntohs(addr.sin_port), len);
                    if (chnIndex_ < CH_INPUT_MAX)
                    {
                        if (!startPlay_)
                        {
                            startPlay_ = true;
                        }
                        chn = chnIndex_++;
                        devChnMap_[key] = chn;
                        struct sockaddr_in addrIn;
                        memcpy(&addrIn, &addr, sizeof(struct sockaddr_in));
                        jinfo("LP use chn %d\n", chn);
                        playthreads_[chn] = std::make_unique<std::thread>([this, chn, src]() {
                            setThreadAffinity(6);
                            socketFds_[chn] = createSocket(src);
                            ip_[chn] = src;
                            mute_[chn] = true;
                            {
                                std::unique_lock<std::mutex> l(mutexMuteAddr_);
                                if (muteAddr_.find(src) != muteAddr_.end())
                                    mute_[chn] = true;
                            }
                            // struct sockaddr_in addr;
                            // bzero(&addr, sizeof(addr));
                            // addr.sin_family = AF_INET;
                            // addr.sin_addr.s_addr = inet_addr(multiIP_.c_str());
                            // addr.sin_port = htons(multiPort_);
                            // int ret = connect(socketFds_[chn], (struct sockaddr *)&addr, sizeof(addr));
                            // if (ret != 0)
                            // {
                            //     jerror("connect fail");
                            // }
                            short buf[MAX_DELAY_MS * SAMPLE_LEN] = { 0 };
                            running_[chn] = 1;
                            auto last_tick = ShmPcm::inst().GetTickFPGA();
                            int count = 0;
                            int discard_frames_count_lp = discard_frames_count_lp_;
                            while (running_[chn])
                            {
                                auto tick = ShmPcm::inst().GetTickFPGA();
                                auto gap = tick - last_tick;
                                if (gap >= 80000) {
                                    jinfo("LP gap = %ld count = %d", gap, count);
                                    count = 0;
                                    last_tick = tick;
                                }
                                socklen_t addrlen;
                                struct sockaddr_in addr;
                                //bzero(&addr, sizeof(addr));
                                int len = recvfrom(socketFds_[chn], buf, 20 * BUF_LEN, 0, (struct sockaddr *)&addr, &addrlen);
                                if (len > 0)
                                {
                                    if (discard_frames_count_lp > 0) {
                                        discard_frames_count_lp -= len;
                                        if (discard_frames_count_lp <= 0) {
                                            discard_frames_count_lp = 0;
                                        }
                                        continue;
                                    }
                                    // jinfo("len = %d, chn = %d", len, chn);
                                    if (!mute_[chn] && !muted_) 
                                    {
                                        // if (!ofs_1.is_open()) ofs_1.open("recvfrom.pcm", std::ios::out | std::ios::binary);
                                        // ofs_1.write((char*)buf, len);
                                        count += len;
                                        ShmPcm::inst().WritePcm(chn, (char*)buf, len, false);
                                    }
                                }
                                else
                                {
                                    jinfo("2 recvfrom(%d) ret [%d] [%s]", socketFds_[chn], len, strerror(errno));
                                    {
                                        std::unique_lock<std::mutex> l(socketMutex_);
                                        if (socketFds_[chn] > 0)
                                        {
                                            // usleep(100 * 1000);
                                            int fd = socketFds_[chn];
                                            socketFds_[chn] = createSocket();
                                            close(fd);
                                        }
                                        else
                                        {
                                            break;
                                        }
                                    }
                                }
                            }
                        });
                    }
                    else
                    {
                        jerror("chn over use");
                    }
                }
            }
            else
            {
                jinfo("recvfrom(%d) ret [%d] [%s]", socketFd_, len, strerror(errno));
                {
                    std::unique_lock<std::mutex> l(socketMutex_);
                    if (socketFd_ > 0)
                    {
                        usleep(100 * 1000);
                        int fd = socketFd_;
                        socketFd_ = createSocket();
                        close(socketFd_);
                    }
                }
            }
            return true;
        }
        break;
    case MODE_PSTN: {
#if H3_NEW_DRIVER
        int len = ReadPcm(CH_PSTN_RECV, (uint8_t *)downlinkSample, BUF_LEN);
        if (len <= 0) {
            jwarn("ReadPcm [%d] failed %d", CH_PSTN_RECV, len);
        }
#else
        int len = ReadPcm(CH_PSTN, (uint8_t *)downlinkSample, BUF_LEN);
#endif
        if (len > 0) {
            if (pstnIn) {
                fwrite(downlinkSample, 1, BUF_LEN, pstnIn);
            }
            {
                std::unique_lock<std::mutex> l(pstnInMutex_);
                pstnInBuf_.WriteBytes((uint8_t *)downlinkSample, BUF_LEN);
            }
            frames_recieved_ += BUF_LEN;
        }
        return true;
    }
    default:
        break;
    }
    return false;
}

void AudioSpecMulticasterSingle::downlinkLoop()
{
    jinfo("downlinkLoop start");
    setThreadAffinity(4);
    // downlinkRunning_ = 1;
    if (mode_ == MODE_MULTICAST && multiIP_ != "")
    {
        jinfo("bind at %d sendto %s:%d\n", bindPort_, multiIP_.c_str(), multiPort_);
        bzero(&localAddr_, sizeof(localAddr_));
        localAddr_.sin_family = AF_INET;
        localAddr_.sin_addr.s_addr = inet_addr(localIP_.c_str());
        localAddr_.sin_port = htons(bindPort_);
        bzero(&addr_, sizeof(addr_));
        addr_.sin_family = AF_INET;
        addr_.sin_addr.s_addr = inet_addr(multiIP_.c_str());
        addr_.sin_port = htons(multiPort_);
        socketFd_ = createSocket();
    }
    while (downlinkRunning_)
    {
        if (!getDownlinkData())
        {
            usleep(1000 * 100);
        } 
        else {
            // usleep(200);
        }
    }
    jinfo("downlinkLoop done");
    std::unique_lock<std::mutex> l(pstnInMutex_);
    pstnInBuf_.Clear();
}

bool AudioSpecMulticasterSingle::setUplinkData(const char* buf, int len)
{
    switch (mode_)
    {
    case MODE_MULTICAST:
        if (socketFd_ > 0)
        {
            writeRecord(sendf, buf, len);
            // if (!ofs_.is_open()) ofs_.open("sendto.pcm", std::ios::out | std::ios::binary);
            // ofs_.write((char*)buf, len);
            len = sendto(socketFd_, buf, len, 0, (struct sockaddr *)&addr_, sizeof(struct sockaddr));
            if (len < 0)
            {
                //
                jerror("sendto fail for %s", strerror(errno));
            }
        }
        break;
    case MODE_PSTN:
        if (pstnOut)
        {
            fwrite(buf, 1, len, pstnOut);
        }
        {
            std::unique_lock<std::mutex> l(pstnOutMutex_);
            pstnOutBuf_.WriteBytes(buf, len);
        }
        break;
    default:
        break;
    }
    return true;
}

void AudioSpecMulticasterSingle::uplinkLoop()
{
    // uplinkRunning_ = 1;
    int len = 0;
	struct timeval startTime;
	struct timeval endTime;
    
    // 不知道谁搞越界了，双倍缓存避一下
    short inbuf[MAX_DELAY_MS * MAX_CAPTURER * SAMPLE_LEN * 2] = { 0 };
    short outbuf[MAX_DELAY_MS * SAMPLE_LEN * 2] = { 0 };
    setThreadAffinity(6);
    while (uplinkRunning_)
    {
        if (pcmFd_ >= 0)
        {
			if(processNum==0)
				gettimeofday(&startTime, NULL);
            int count = useHeadset_ ? 1 : MAX_CAPTURER;
            for (size_t i = 0; i < count; i++)
            {
                int capLoop = 1;
                while (capLoop && uplinkRunning_)
                {
                    if (useHeadset_ && i != 0) break;
                    if (capturebufs_[i].Length() >= delay_ * 1 * BUF_LEN)
                    {
                        std::unique_lock<std::mutex> locker(captureMutexs_[i]);
                        memcpy(inbuf + i * delay_ * SAMPLE_LEN, capturebufs_[i].Data(), delay_ * BUF_LEN);
                        if (i == 0) frames_captured_ += delay_ * BUF_LEN;
                        capturebufs_[i].Consume(delay_ * BUF_LEN);
                        capLoop = 0;
                    }
                    else
                    {
                        usleep(1);
                        continue;
                    }
                }
            }
            if (enableAEC_ <= 1)
            {
                if (mic_type_ == TYPE_INTERNAL_MIC && !useHeadset_) {
                    CAudioMix::AddAndNormalization(inbuf + delay_ * SAMPLE_LEN, MAX_CAPTURER - 1, delay_ * SAMPLE_LEN, outbuf);
                } else {
                    memcpy(outbuf, inbuf, delay_ * BUF_LEN);
                }
            }
            else
            {
                std::unique_lock<std::mutex> locker(aecMutex_);
                if (useHeadset_)
                {
                    rtc_aec_process(aec_.get(), inbuf, outbuf, delay_ * SAMPLE_LEN);
                }
                else
                {
                    rtc_aec_process_mics(aec_.get(), inbuf, outbuf, delay_ * SAMPLE_LEN, MAX_CAPTURER);
                }
                writeRecord(nearf, (char*)outbuf, BUF_LEN);
            }
#if H3_NEW_DRIVER
            // if (mode_ != MODE_PSTN)
            // {
            //     std::unique_lock<std::mutex> l(pstnOutMutex_);
            //     pstnOutBuf_.WriteBytes((char*)outbuf, delay_ * BUF_LEN);
            // }
            int len = ShmPcm::inst().WritePcm(CH_PSTN_SEND, (unsigned char*)outbuf, delay_ * BUF_LEN, false);
            if (mode_ == MODE_PSTN)
                frames_to_send_ += delay_ * BUF_LEN;
#else
            {
                std::unique_lock<std::mutex> locker(uplinkMutex_);
                bbuf_.WriteBytes((const char *)outbuf, delay_ * BUF_LEN);
            }
            {
                setUplinkData((const char *)outbuf, delay_ * BUF_LEN);
            }
#endif
        }
        else
        {
            usleep(1000 * 1000);
        }
    }
    {
        std::unique_lock<std::mutex> l(uplinkMutex_);
        bbuf_.Clear();
    }
    {
        std::unique_lock<std::mutex> l(pstnOutMutex_);
        pstnOutBuf_.Clear();
    }
}

void AudioSpecMulticasterSingle::pstnInLoop()
{
    jinfo("pstnInLoop start");
    // pstnInRunning_ = 1;
    short inbuf1[ADL_WB_PROC_LEN  * 2] = { 0 };
    short inbuf2[ADL_WB_PROC_LEN  * 2] = { 0 };
    short outbuf1[ADL_WB_PROC_LEN  * 2] = { 0 };
    short outbuf2[ADL_WB_PROC_LEN  * 2] = { 0 };
    setThreadAffinity(5);
    // if (!ofs_.is_open()) ofs_.open("pstnin.pcm", std::ios::out | std::ios::binary);
    // if (!ofs_1.is_open()) ofs_1.open("pstnout.pcm", std::ios::out | std::ios::binary);
    // if (!ofs_2.is_open()) ofs_2.open("out.pcm", std::ios::out | std::ios::binary);
    while (pstnInRunning_)
    {
        if (pcmFd_ >= 0)
        {
            bool ready = false;
            {
                std::unique_lock<std::mutex> l1(pstnInMutex_);
                std::unique_lock<std::mutex> l2(pstnMutex_);
                if (pstnInBuf_.Length() >= ADL_WB_PROC_LEN * 2 && pstnBuf_.Length() >= ADL_WB_PROC_LEN * 2)
                {
                    memcpy(inbuf1, pstnInBuf_.Data(), ADL_WB_PROC_LEN * 2);
                    pstnInBuf_.Consume(ADL_WB_PROC_LEN * 2);
                    memcpy(inbuf2, pstnBuf_.Data(), ADL_WB_PROC_LEN * 2);
                    pstnBuf_.Consume(ADL_WB_PROC_LEN * 2);
                    ready = true;
                }
            }

            if (ready)
            {
                char* p = (char*)outbuf1;
                if (enableLEC_ > 1)
                {
                    ADL_Process(ctxADL_,
                            inbuf1,
                            inbuf2,
                            outbuf1,
                            outbuf2);
                    // ofs_.write((char*)inbuf1, ADL_WB_PROC_LEN * 2);
                    // ofs_1.write((char*)inbuf2, ADL_WB_PROC_LEN * 2);
                    // ofs_2.write((char*)outbuf1, ADL_WB_PROC_LEN * 2);
                }
                else
                {
                    p = (char*)inbuf1;
                }
                std::unique_lock<std::mutex> locker(mutexPlay_);
                playbbuf_.WriteBytes(p, ADL_WB_PROC_LEN * 2);
            }
        }
        else
        {
            usleep(1000 * 1000);
        }
    }
    if (ofs_.is_open()) ofs_.close();
    if (ofs_1.is_open()) ofs_1.close();
    // if (ofs_2.is_open()) ofs_2.close();
    jinfo("pstnInLoop end");
    std::unique_lock<std::mutex> locker(mutexPlay_);
    playbbuf_.Clear();
}

void AudioSpecMulticasterSingle::pstnOutLoop()
{
    jinfo("pstnOutLoop start");
    // pstnOutRunning_ = 1;
    short inbuf[SAMPLE_LEN * 2] = { 0 };
    setThreadAffinity(7);
    int max_buffer_len = ((mode_ == MODE_PSTN) ? BUF_LEN_10MS : BUF_LEN_40MS);
#if H3_NEW_DRIVER
    ClearSendBuf(CH_PSTN_SEND);
#endif    
    while (pstnOutRunning_)
    {
        if (pcmFd_ >= 0)
        {
            ByteBuffer send_buf;
            if (pstnOutBuf_.Length() >= BUF_LEN)
            {
                std::unique_lock<std::mutex> locker(pstnOutMutex_);
                int len = pstnOutBuf_.Length();
                if (len > max_buffer_len) {
                    pstnOutBuf_.Consume(len - max_buffer_len);
                    jwarn("pstnOutBuf_ drop %d", len - max_buffer_len);
                    len = max_buffer_len;
                }
                send_buf.WriteBytes(pstnOutBuf_.Data(), len);
                pstnOutBuf_.Consume(len);               
                // memcpy(inbuf, pstnOutBuf_.Data(), BUF_LEN);
                // pstnOutBuf_.Consume(BUF_LEN);
            }
            while (send_buf.Length() >= BUF_LEN && pstnOutRunning_) {
                auto a = std::chrono::steady_clock::now();
    #if H3_NEW_DRIVER
                WritePcm(CH_PSTN_SEND, (uint8_t*)send_buf.Data(), BUF_LEN);
                // if (!ofs_.is_open()) ofs_.open("pstn.pcm", std::ios::out | std::ios::binary);
                // ofs_.write((char*)send_buf.Data(), BUF_LEN);
    #else
                {
                    std::unique_lock<std::mutex> locker(pstnMutex_);
                    pstnBuf_.WriteBytes((uint8_t*)send_buf.Data(), BUF_LEN);
                }
                WritePcm(CH_PSTN, (uint8_t*)send_buf.Data(), BUF_LEN);
    #endif
                auto b = std::chrono::steady_clock::now();
                auto duration = std::chrono::duration_cast<std::chrono::microseconds>(b - a).count();
                if (duration > 5000)
                    jwarn("pstn send duration = %d", duration);
                if (mode_ == MODE_PSTN)
                    frames_to_send_ += BUF_LEN;
                send_buf.Consume(BUF_LEN);
                // usleep(300);
            }
        }
        else
        {
            usleep(1000 * 1000);
        }
    }
    // if (ofs_.is_open()) ofs_.close();
    jinfo("pstnOutLoop end");
    std::unique_lock<std::mutex> locker(pstnMutex_);
    pstnBuf_.Clear();
}

void AudioSpecMulticasterSingle::pstnInLoop2() {
    jinfo("pstnInLoop2 start");
    const int delay = 10;
    short inbuf1[SAMPLE_LEN * MAX_CAPTURER * delay * 2] = { 0 };
    short inbuf2[SAMPLE_LEN * delay * 2] = { 0 };
    short outbuf1[SAMPLE_LEN * delay  * 2] = { 0 };
    setThreadAffinity(5);
    while (pstnInRunning_)
    {
        if (pcmFd_ >= 0)
        {
            bool ready = false;
            {
                std::unique_lock<std::mutex> l1(pstnInMutex_);
                std::unique_lock<std::mutex> l2(pstnMutex_);
                if (pstnInBuf_.Length() >= SAMPLE_LEN * delay * 2 && pstnBuf_.Length() >= SAMPLE_LEN * delay * 2)
                {
                    memcpy(inbuf1, pstnInBuf_.Data(), SAMPLE_LEN * delay * 2);
                    pstnInBuf_.Consume(SAMPLE_LEN * delay * 2);
                    memcpy(inbuf2, pstnBuf_.Data(), SAMPLE_LEN * delay * 2);
                    pstnBuf_.Consume(SAMPLE_LEN * delay * 2);
                    ready = true;
                }
            }

            if (ready)
            {
                char* p = (char*)outbuf1;
                if (enableLEC_ > 1)
                {
                    std::lock_guard<std::mutex> locker(lecMutex_); 
                    if (lec_) {
                        rtc_aec_buffer_farend(lec_.get(), inbuf2, delay * SAMPLE_LEN);
                        rtc_aec_process_mics(lec_.get(), inbuf1, outbuf1, delay * SAMPLE_LEN, MAX_CAPTURER);
                    }
                }
                else
                {
                    p = (char*)inbuf1;
                }
                std::unique_lock<std::mutex> locker(mutexPlay_);
                playbbuf_.WriteBytes(p, SAMPLE_LEN * delay * 2);
            }
        }
        else
        {
            usleep(1000 * 1000);
        }
    }
    jinfo("pstnInLoop2 end");
    std::unique_lock<std::mutex> locker(mutexPlay_);
    playbbuf_.Clear();
}


std::shared_ptr<AudioSpecMulticasterSingle> AudioSpecMulticasterSingle::inst_;

std::shared_ptr<AudioSpecMulticasterSingle> AudioSpecMulticasterSingle::CreateInstance(const std::string &jsonParams) {
    if (!inst_) {
        jinfo("CreateInstance AudioSpecMulticasterSingle");
        inst_ = std::make_shared<AudioSpecMulticasterSingle>(jsonParams);
    }
    return inst_;
}

void AudioSpecMulticasterSingle::ReleaseInstance() {
    inst_.reset();
}

AudioSpecMulticasterSingle::AudioSpecMulticasterSingle(const std::string &jsonParams)
    : enableAEC_(2)
    , enableCNG_(0)
    , enableNS_(2)
    , anti_howling_(0)
    , enableAGC_(0)
    , delay_(1)
    , ref_count(0)
    , discard_frames_count_(0)
    , discard_frames_count_lp_(0)
    , discard_frames_ms_(0) {
    FN_BEGIN;
    jinfo("AudioSpecMulticasterSingle %s", jsonParams.c_str());
    name_ = "AudioSpecMulticasterSingle";
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    dev_ = 0;
    if (j.contains("dev")) {
        dev_ = j["dev"];
    }
    ptime_ = 20;
    if (j.contains("ptime")) {
        ptime_ = j["ptime"];
    }
    if (j.contains("aec")) {
        enableAEC_ = j["aec"];
    }
    innerAEC_ = enableAEC_;
    if (j.contains("cng")) {
        enableCNG_ = j["cng"];
    }
    if (j.contains("ns")) {
        enableNS_ = j["ns"];
    }
    if (j.contains("anti-howling")) {
        anti_howling_ = j["anti-howling"];
    }
    if (j.contains("agc")) {
        enableAGC_ = j["agc"];
    }
    if (j.contains("log")) {
        logAEC_ = j["log"];
    }
    delay_ = 1;
    if (j.contains("delay")) {
        delay_ = j["delay"];
        if (delay_ < MIN_DELAY_MS)
            delay_ = MIN_DELAY_MS;
        if (delay_ > MAX_DELAY_MS)
            delay_ = MAX_DELAY_MS;
        jinfo("delay = [%d]", delay_);
    }
    // if (enableAGC_ > 1) delay_ = 10;
    if (enableCNG_ > 1) delay_ = 10;
    if (enableNS_ > 2) delay_ = 10;
    devName_ = "/dev/lp_pcm_dev0";
    if (j.contains("devName")) {
        devName_ = j["devName"];
    }
    for (size_t i = 0; i < CH_MAX; i++) {
        socketFds_[i] = -1;
        mute_[i] = true;
    }

    jinfo("AudioSpecMulticasterSingle open %s %d", devName_.c_str(), dev_);
    // start();
    FN_END;
}

AudioSpecMulticasterSingle::~AudioSpecMulticasterSingle()
{
    FN_BEGIN;
    stop();
    for (size_t i = 0; i < MAX_CAPTURER; i++)
    {
        if (recordf[i])
        {
            fclose(recordf[i]);
            recordf[i] = nullptr;
        }
    }
    closeRecord();
    threadPacer_.stop(true);
    threadRecorder_.stop(true);
#if USEMONITOR
    monitorThread_.stop();
#endif
    FN_END;
}

void AudioSpecMulticasterSingle::openRecord()
{
    threadRecorder_.loop()->runInLoop([this](){
        if (!sendf)
            sendf = fopen("send.pcm", "wb");
        if (!recvf)
            recvf = fopen("recv.pcm", "wb");
        if (!nearf)
            nearf = fopen("near.pcm", "wb");
        if (!playf)
            playf = fopen("play.pcm", "wb");
        if (!pstnIn)
            pstnIn = fopen("pstnIn.pcm", "wb");
        if (!pstnOut)
            pstnOut = fopen("pstnOut.pcm", "wb");

        for (size_t i = 0; i < MAX_CAPTURER; i++)
        {
            if (!recordf[i])
            {
                std::string file = std::to_string(i) + "record.pcm";
                recordf[i] = fopen(file.c_str(), "wb");
            }
        }
    });
}

void AudioSpecMulticasterSingle::closeRecord()
{
    threadRecorder_.loop()->runInLoop([this](){
        if(sendf) fclose(sendf);
        if(recvf) fclose(recvf);
        if(nearf) fclose(nearf);
        if(playf) fclose(playf);
        if(pstnIn) fclose(pstnIn);
        if(pstnOut) fclose(pstnOut);
        sendf = nullptr;
        nearf = nullptr;
        recvf = nullptr;
        playf = nullptr;
        pstnIn = nullptr;
        pstnOut = nullptr;
        for (size_t i = 0; i < MAX_CAPTURER; i++)
        {
            if (recordf[i])
            {
                fclose(recordf[i]);
                recordf[i] = nullptr;
            }
        }
    });
}

void AudioSpecMulticasterSingle::writeRecord(FILE* pf, const char* data, int len)
{
    if (recordAudio_ && pf)
    {
        std::vector<char> buf;
        buf.resize(len);
        memcpy(buf.data(), data, len);
        threadRecorder_.loop()->runInLoop([pf, buf](){
            fwrite(buf.data(), 1, buf.size(), pf);
        });
    }
}

void AudioSpecMulticasterSingle::start()
{
    std::unique_lock<std::mutex> l(startstopMutex_);
    ref_count++;
    jinfo("AudioSpecMulticasterSingle::start ref %d", ref_count);
    if (downlinkRunning_) return;
    FN_BEGIN;
    void* core = nullptr;
    rtc_aec_create(&core);
	AecConfig config;
	memset(&config, 0, sizeof(config));
	// config.nlpMode = kAecNlpConservative;
    innerAEC_ = enableAEC_;
	config.agc_on = (enableAGC_ > 1);
    config.ns_on = (enableNS_ > 1);
    config.cng_on = (enableCNG_ > 1);
    config.aec_on = (enableAEC_ > 1);
	config.pcm_logging = (logAEC_ > 1);;
    config.howlingSuppress_on = (anti_howling_ > 1);
    config.lec_off = true;
    config.sip_mode = (mode_ != MODE_PSTN);
    config.ai_denoise_on = (enableNS_ > 2);
    config.goose_mic_dropped = (mic_type_ != TYPE_GOOSE);
	rtc_aec_set_config(core, config);
	rtc_aec_init(core, samplerate_);
    jinfo("AudioSpecMulticasterSingle::start aec=[%d] ns=[%d] agc=[%d] log=[%d] sip_mode=[%d]", innerAEC_, enableNS_, enableAGC_, logAEC_, config.sip_mode);
    {
        std::lock_guard<std::mutex> locker(aecMutex_);
        aec_ = std::shared_ptr<void>(core, [](void* core){
            rtc_aec_free(core);
        });
    }
    FramePipeline::resetTimeoutTick();
	startPlay_ = true;
    connected_ = true;
	startAecFarend_ = false;
    memset(ctxADL_, 0, sizeof(ctxADL_));
    ADL_Init(ctxADL_, ADL_MODE_WB);
    ADL_SetParamSta(ctxADL_, ADL_PARAM_ID_DL_NS, 1);
    ADL_SetParamSta(ctxADL_, ADL_PARAM_ID_UL_NS, 0);
    ADL_SetParamSta(ctxADL_, ADL_PARAM_ID_AEC, 0);
    ADL_SetParamSta(ctxADL_, ADL_PARAM_ID_LEC, 1);
    ADL_SetParamSta(ctxADL_, ADL_PARAM_ID_LEC_RES, 1);
    chnIndex_ = 1;

    discard_frames_count_ = discard_frames_ms_ * BUF_LEN;
    discard_frames_count_lp_ = discard_frames_count_;
    if (discard_frames_count_ > BUF_LEN * 3000)
    {
        jerror("discard_frames_count %d is too large", discard_frames_count_);
    }

    // pcmFd_ = openPCM(devName_.c_str());
    // if (pcmFd_ < 0)
    // {
    //     jerror("%s open %s fail", __FUNCTION__, devName_.c_str());
    //     return;
    // }
    // else
    // {
    //     for (size_t i = 0; i < CH_MAX; i++)
    //     {
    //         Set_Type(i, IOCTL_CMD_PCM_TYPE_HIFI);
    //     }
    // }
    if (!inited_) {
        int ret = ShmPcm::inst().init();
        if (ret < 0) {
            jerror("#####################Failed to init ShmPcm.");
        }
    }
    inited_ = true;
    for (int i = 0; i < 8; i++)
        ShmPcm::inst().ClearReadBuf(i);
    ptimeDataLen_ = samplerate_ / 1000 * ptime_  * sizeof(int16_t);
    jinfo("samplerate=%d ptime=%d ptimeDataLen=%d", samplerate_, ptime_, ptimeDataLen_);
    
    downlinkRunning_ = 1;
    uplinkRunning_ = 0;
    playRunning_ = 0;
    // TODO：状态保护
    // uplinkRunning_ = 1;
    // uplinkThread_ = std::make_unique<std::thread>([this]() { uplinkLoop(); });
    // for (size_t i = 0; i < MAX_CAPTURER; i++)
    // {
    //     captureRunnings_[i] = 1;
    //     captureThreads_[i] = std::make_unique<std::thread>([this, i]() { captureLoop(i); });
    // }
    // playRunning_ = 1;
    // playthread_ = std::make_unique<std::thread>([this]() { playLoop(); });
    // farendReadRunning_ = 1;
    // farendReadThread_ = std::make_unique<std::thread>([this]() { farendReadLoop(); });
    // farendWriteRunning_ = 1;
    // farendWriteThread_ = std::make_unique<std::thread>([this]() { farendWriteLoop(); });
#if H3_NEW_DRIVER
    capturing_ = 1;
    captureThread_ = std::make_unique<std::thread>([this]() { setThreadName("captureLoop"); captureLoop(); });
#endif
    // aecProcessRunning_= 1;
    // aecProcessThread_ = std::make_unique<std::thread>([this]() { setThreadName("aecProcessLoop"); aecProcessLoop(); });
    if (!threadPacer_.isRunning())
    {
        threadPacer_.start();
        ResetStatistics();
        threadPacer_.loop()->setInterval(ptime_ / 2, [this](hv::TimerID id){
            ByteBuffer bbuf;
            {
                std::unique_lock<std::mutex> locker(uplinkMutex_);
                int slice = bbuf_.Length() / ptimeDataLen_;
                if (slice > 0)
                {
                    bbuf.WriteBytes(bbuf_.Data(), ptimeDataLen_ * slice);
                    bbuf_.Consume(ptimeDataLen_ * slice);
                }
            }
            std::shared_ptr<Frame> f = Frame::CreateFrame(FRAME_FORMAT_PCM_16000_1);
            f->createFrameBuffer(ptimeDataLen_);
            while (bbuf.Length() >= ptimeDataLen_)
            {
                memcpy(f->getFrameBuffer(), bbuf.Data(), ptimeDataLen_);
                bbuf.Consume(ptimeDataLen_);
                deliverFrame(f);
                printOutputStatus("AudioSpecMulticasterSingle");
                if (mode_ != MODE_PSTN)
                    frames_to_send_ += ptimeDataLen_;
                LogStatistics();
            }
        });
    }
#if USEMONITOR
    if (!monitorThread_.isRunning()) {
        monitorThread_.start();
        monitorThread_.loop()->setInterval(3000, [this](hv::TimerID id) {
            if (access("aec_config.json", F_OK) == 0) {
                FILE* pf = fopen("aec_config.json", "r");
                if (pf)
                {
                    nlohmann::json config;
                    fseek(pf, 0, SEEK_END);
                    int len = ftell(pf);
                    fseek(pf, 0, SEEK_SET);
                    std::vector<uint8_t> buf;
                    buf.resize(len + 1);
                    memset(buf.data(), 0, len + 1);
                    int ret = fread(buf.data(), 1, len, pf);
                    if (ret > 0)
                    {
                        config = nlohmann::json::parse(buf.data());
                    }
                    fclose(pf);
                    // jinfo("LoadConfig %s", config.dump().c_str());
                    if (config.contains("module-disable")) {
                        module_disable_ = config["module-disable"];
                    }
                    if (config.contains("use-original")) {
                        use_original_ = config["use-original"];
                    }
                    jinfo("module_disable: %d, use_original: %d", module_disable_, use_original_);
                }
            } 
        });
    }
#endif
    if (!threadRecorder_.isRunning())
    {
        threadRecorder_.start();
    }
    {
        std::unique_lock<std::mutex> locker(mutexPlay_);
        jinfo("#################clear playbuf");
        playbbuf_.Clear();
    }
    FN_END;
}

void AudioSpecMulticasterSingle::stop()
{
    std::unique_lock<std::mutex> l(startstopMutex_);
    ref_count--;
    jinfo("AudioSpecMulticasterSingle::stop ref %d", ref_count);
    if (!downlinkRunning_ || ref_count > 0) return;
    ref_count = 0;
    {
        std::unique_lock<std::mutex> l(socketMutex_);
        if (socketFd_ > 0)
        {
            shutdown(socketFd_, SHUT_RDWR);
            close(socketFd_);
            socketFd_ = -1;
        }
        for (size_t i = 0; i < CH_MAX; i++)
        {
            if (socketFds_[i] > 0)
            {
                shutdown(socketFds_[i], SHUT_RDWR);
                close(socketFds_[i]);
                socketFds_[i] = -1;
            }
        }
    }
    downlinkRunning_ = 0;
    uplinkRunning_ = 0;
    farendReadRunning_ = 0;
    farendWriteRunning_ = 0;
    playRunning_ = 0;
    pstnInRunning_ = 0;
    pstnOutRunning_ = 0;
    capturing_ = 0;
#if H3_NEW_DRIVER
    lecRefReadRunning_ = 0;
    lecProcessRunning_ = 0;
    aecProcessRunning_ = 0;
#endif
    jinfo("AudioSpecMulticasterSingle::stop() 1");
    if (farendReadThread_)
    {
        farendReadThread_->join();
        farendReadThread_.reset();
    }
#if H3_NEW_DRIVER
    if (lecProcessThread_.get()) {
        lecProcessThread_->join();
        lecProcessThread_.reset();        
    }
    if (lecRefReadThread_.get())
    {
        lecRefReadThread_->join();
        lecRefReadThread_.reset();
    }
    if (aecProcessThread_.get()) {
        aecProcessThread_->join();
        aecProcessThread_.reset();
    }
#endif
    if (farendWriteThread_)
    {
        farendWriteThread_->join();
        farendWriteThread_.reset();
    }
    jinfo("AudioSpecMulticasterSingle::stop() 2");
    if (downlinkThread_)
    {
        downlinkThread_->join();
        downlinkThread_.reset();
    }
    jinfo("AudioSpecMulticasterSingle::stop() 3");
    if (uplinkThread_) 
    {
        uplinkThread_->join();
        uplinkThread_.reset();
    }
    if (pstnInThread_) 
    {
        pstnInThread_->join();
        pstnInThread_.reset();
    }
    if (pstnOutThread_) 
    {
        pstnOutThread_->join();
        pstnOutThread_.reset();
    }
    jinfo("AudioSpecMulticasterSingle::stop() 4");
    for (size_t i = 0; i < MAX_CAPTURER; i++)
    {
        if (captureThreads_[i]) {
            captureRunnings_[i] = 0;
            captureThreads_[i]->join();
            captureThreads_[i].reset();
        }
    }
    jinfo("AudioSpecMulticasterSingle::stop() 5");
    if (playthread_)
    {
        playthread_->join();
        playthread_.reset();
    }
#if H3_NEW_DRIVER
    if (captureThread_) {
        captureThread_->join();
        captureThread_.reset();
    }
#endif
    // ShmPcm::inst().deinit();
    jinfo("AudioSpecMulticasterSingle::stop() 6");
    startPlay_ = false;

    for (size_t i = 0; i < CH_MAX; i++)
    {
        if (running_[i])
        {
            running_[i] = 0;
            if (playthreads_[i])
            {
                playthreads_[i]->join();
                playthreads_[i].reset();
            }
        }
    }
    {
        std::lock_guard<std::mutex> locker(aecMutex_);
        aec_.reset();
    }
    {
        std::lock_guard<std::mutex> locker(lecMutex_);
        lec_.reset();
    }
    devChnMap_.clear();

    jinfo("AudioSpecMulticasterSingle::stop() 7");
    if (pcmFd_ >= 0)
    {
        closePCM();
        pcmFd_ = -1;
    }
    jinfo("AudioSpecMulticasterSingle::stop() 8");
    {
        std::unique_lock<std::mutex> l(mutexMuteAddr_);
        muteAddr_.clear();
    }
    chnIndex_ = 1;
    mode_ = MODE_COMMON;
    mic_type_ = TYPE_GOOSE;
    initResampler_ = false;
    connected_ = false;

    enableAEC_ = 2;
    enableLEC_ = 1;
    innerAEC_ = 2;
    enableAGC_ = 0;
    enableCNG_ = 0;
    enableNS_ = 2;
    anti_howling_ = 0;
    delay_ = 1;
    logAEC_ = 0;
    recordAudio_ = 0;
    discard_frames_count_ = 0;      // 大包数据
    discard_frames_count_lp_ = 0;   // 小包数据
    muted_ = false;
    if (ofs_.is_open()) ofs_.close();
    if (ofs_1.is_open()) ofs_1.close();
    if (ofs_2.is_open()) ofs_2.close();
}

int AudioSpecMulticasterSingle::updateParam(const std::string& jsonParams)
{
    jinfo("AudioSpecMulticasterSingle::updateParam %s", jsonParams.c_str());
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    int configChanged = 0;
    if (j.contains("mode"))
    {
        int mode = j["mode"];
        if (mode_ != mode) {
            configChanged = 1;
            mode_= mode;
            jinfo("mode change to %d", mode_);
        }
        // mode_ = j["mode"];
        if (mode_ == MODE_MULTICAST)
        {
            localIP_ = j["localIP"];
            bindPort_ = j["bindPort"];
            multiIP_ = j["multiIP"];
            multiPort_ = j["multiPort"];
            if (j.contains("muteNotLP"))
            {
                muteNotLP_ = j["muteNotLP"];
            }
            jinfo("AudioSpecMulticasterSingle %s:%d %s:%d [%d]", localIP_.c_str(), bindPort_, multiIP_.c_str(), multiPort_, muteNotLP_);
            if (!downlinkThread_) {
                downlinkRunning_ = 1;
                downlinkThread_ = std::make_unique<std::thread>([this]() { setThreadName("downlinkLoop"); downlinkLoop(); });
            }
        }
        else if (mode_ == MODE_PSTN)
        {
            // ConfigRegister(0x0f);
            if (j.contains("aec"))
            {
                enableLEC_ = j["aec"];
            }
            // enableLEC_ = 2;
            // if (!downlinkThread_) {
            //     downlinkRunning_ = 1;
            //     downlinkThread_ = std::make_unique<std::thread>([this]() { setThreadName("downlinkLoop"); downlinkLoop(); });
            // }
#if H3_NEW_DRIVER
            if (!lecProcessThread_)
#else
            if (!pstnInThread_) 
#endif
            {
                pstnInRunning_ = 1;
                void* core = nullptr;
                rtc_aec_create(&core);
                AecConfig config;
                memset(&config, 0, sizeof(config));
                // config.nlpMode = kAecNlpConservative;
                config.aec_on = 0;
                config.agc_on = 0;
                config.ns_on = 1;
                config.cng_on = 0;
                config.howlingSuppress_on = 0;
                config.lec_off = false;
                config.sip_mode = false;
                config.ai_denoise_on = false;
                // config.pcm_logging = true;
                int log = 0;
                if (j.contains("log"))
                {
                    log = j["log"];
                }
                config.pcm_logging = log;
                rtc_aec_set_config(core, config);
                rtc_aec_init(core, samplerate_);
                {
                    std::lock_guard<std::mutex> locker(lecMutex_);
                    lec_ = std::shared_ptr<void>(core, [](void* core){
                        rtc_aec_free(core);
                    });
                }
                // pstnInThread_ = std::make_unique<std::thread>([this]() { pstnInLoop(); });
#if H3_NEW_DRIVER
                // lecProcessRunning_ = 1;
                // lecProcessThread_ = std::make_unique<std::thread>([this]() { lecProcessLoop(); });
                // if (!lecRefReadThread_) {
                //     lecRefReadRunning_ = 1;
                //     lecRefReadThread_ = std::make_unique<std::thread>([this]() { setThreadName("lecRefReadLoop"); lecRefReadLoop(); });
                // }
#else
                pstnInThread_ = std::make_unique<std::thread>([this]() { pstnInLoop2(); });
#endif
                
            }
            // if (!pstnOutThread_) {
            //     pstnOutRunning_ = 1;
            //     pstnOutThread_ = std::make_unique<std::thread>([this]() { pstnOutLoop(); });
            // }
        }
    }
#if H3_NEW_DRIVER
    if (mode_ != MODE_PSTN) {
        // ConfigRegister(0x0e);
        // if (!lecRefReadThread_) {
        //     lecRefReadRunning_ = 1;
        //     lecRefReadThread_ = std::make_unique<std::thread>([this]() { setThreadName("lecRefReadLoop"); lecRefReadLoop(); });
        // }
        // if (!pstnOutThread_) {
        //     pstnOutRunning_ = 1;
        //     pstnOutThread_ = std::make_unique<std::thread>([this]() { pstnOutLoop(); }); // 非PSTN模式也写0通道
        // }
    }
#endif
    delay_ = 1;
    if (j.contains("delay"))
    {
        delay_ = j["delay"];
        if (delay_ < MIN_DELAY_MS)
            delay_ = MIN_DELAY_MS;
        if (delay_ > MAX_DELAY_MS)
            delay_ = MAX_DELAY_MS;
        jinfo("delay = [%d]", delay_);
    }
    if (enableCNG_ > 1) delay_ = 10;
    if (j.contains("aec"))
    {
        int aec = j["aec"];
        if (enableAEC_ != aec)
        {
            configChanged = 1;
            innerAEC_ = enableAEC_ = aec;
            jinfo("aec change to %d", enableAEC_);
        }
    }
    if (j.contains("lec"))
    {
        int lec = j["lec"];
        if (enableLEC_ != lec)
        {
            enableLEC_ = lec;
            jinfo("lec change to %d", enableLEC_);
        }
    }
    if (j.contains("cng"))
    {
        int cng = j["cng"];
        if (enableCNG_ != cng && enableCNG_ + cng > 1)
        {
            enableCNG_ = cng;
            configChanged = 1;
            jinfo("cng change to %d", enableCNG_);
        }
    }
    if (enableCNG_ > 1) delay_ = 10;
    if (j.contains("agc"))
    {
        int agc = j["agc"];
        if (enableAGC_ != agc && enableAGC_ + agc > 1)
        {
            enableAGC_ = agc;
            configChanged = 1;
            jinfo("agc change to %d", enableAGC_);
        }
    }
    // if (enableAGC_ > 1) delay_ = 10;
    if (j.contains("use-headset") && j["use-headset"].is_boolean())
    {
        // 使用耳麦时，不使用AGC
        useHeadset_ = j["use-headset"];
        if (useHeadset_ && enableAGC_ > 1) {
            enableAGC_ = 0;
            configChanged = 1;
        }
    }
    if (j.contains("ns"))
    {
        int ns = j["ns"];
        if (enableNS_ != ns && enableNS_ + ns > 1)
        {
            enableNS_ = ns;
            configChanged = 1;
            jinfo("ns change to %d", enableNS_);
        }
    }
    if (enableNS_ > 2) delay_ = 10;
    if (j.contains("anti-howling"))
    {
        int anti_howling = j["anti-howling"];
        if (anti_howling_ != anti_howling && anti_howling_ + anti_howling > 1)
        {
            anti_howling_ = anti_howling;
            configChanged = 1;
            jinfo("anti_howling change to %d", anti_howling_);
        }
    }
    if (j.contains("log"))
    {
        int log = j["log"];
        if (logAEC_ != log && logAEC_ + log > 1)
        {
            logAEC_ = log;
            configChanged = 1;
            jinfo("logAEC change to %d", logAEC_);
        }
    }
    if (j.contains("mic-type")) {
        int mic_type = j["mic-type"];
        if (mic_type_ != mic_type)
        {
            mic_type_ = mic_type;
            configChanged = 1;
            jinfo("mic_type change to %d", mic_type_);
        }
        if (mic_type_ != TYPE_GOOSE) delay_ = 10;
    }
    jinfo("################delay = %d", delay_);
    if (configChanged)
    {
        enableAEC_ = 0;
        jinfo("aec config changed!!!!!!!");
        // TODO: find a better way.
        threadRecorder_.loop()->setTimeout(500, [this](hv::TimerID id) {
            void* core = nullptr;
            rtc_aec_create(&core);
            AecConfig config;
            memset(&config, 0, sizeof(config));
            // config.nlpMode = kAecNlpConservative;
            config.agc_on = (enableAGC_ > 1);
            config.ns_on = (enableNS_ > 1);
            config.cng_on = (enableCNG_ > 1);
            config.aec_on = (innerAEC_ > 1);
            config.pcm_logging = (logAEC_ > 1);
            config.howlingSuppress_on = (anti_howling_ > 1);
            config.lec_off = true;
            config.sip_mode = (mode_ != MODE_PSTN);
            config.ai_denoise_on = (enableNS_ > 2);
            config.goose_mic_dropped = (mic_type_ != TYPE_GOOSE);
            rtc_aec_set_config(core, config);
			rtc_aec_init(core, samplerate_);
            {
                std::lock_guard<std::mutex> locker(aecMutex_);
                aec_ = std::shared_ptr<void>(core, [](void* core){
                    rtc_aec_free(core);
                });
            }
            jinfo("AudioSpecMulticasterSingle::updateParam aec=[%d] ns=[%d] agc=[%d] log=[%d] sip_mode=[%d]", innerAEC_, enableNS_, enableAGC_, logAEC_, config.sip_mode);
            enableAEC_ = innerAEC_;
			startAecFarend_ = false;
        });
    }

    if (j.contains("record-audio"))
    {
        recordAudio_ = j["record-audio"];
        if (recordAudio_)
        {
            openRecord();
        }
        else
        {
            closeRecord();
        }
        jinfo("recordAudio change to %d", recordAudio_);
    }
    if (!j.contains("mute-addr")) {
        std::unique_lock<std::mutex> l(mutexMuteAddr_);
        muteAddr_.clear();
        for (size_t i = 0; i < CH_MAX; i++) {
            mute_[i] = false;
        }
    }
    if (j.contains("mute-addr") && j["mute-addr"].is_array())
    {
        std::unique_lock<std::mutex> l(mutexMuteAddr_);
        muteAddr_.clear();
        std::vector<bool> mute(CH_MAX, false);
        for (size_t i = 0; i < j["mute-addr"].size(); i++)
        {
            std::string ip = j["mute-addr"][i];
            muteAddr_.insert(ip);
            for (size_t j = 0; j < CH_MAX; j++)
            {
                if (ip_[j] == ip)
                {
                    mute[j] = true;
                    break;
                }
            }
        }
        for (size_t i = 0; i < CH_MAX; i++) {
            mute_[i] = mute[i];
        }
    }
    if (j.contains("muted")) {
        muted_ = j["muted"];
    }
    jinfo("updateParam enableAEC: %d, enableLEC: %d, enableAGC: %d, muted: %d", innerAEC_/*enableAEC_*/, enableLEC_, enableAGC_, muted_);
    return 0;
}

// 大包
void AudioSpecMulticasterSingle::onFrame(const std::shared_ptr<Frame> &frame)
{
    if (muteNotLP_)
        return;
    // if (!playRunning_)
    //     return;
    int samplerate = 0;
    int chn = 0;
    if (Frame::getSamplerate(frame->getFrameFormat(), samplerate, chn))
    {
        auto a = std::chrono::steady_clock::now();
        printInputStatus("AudioSpecMulticasterSingle");
        auto f = frame;
        if (samplerate_ != samplerate)
        {
            if (!initResampler_ || source_samplerate_ != samplerate)
            {
                source_samplerate_ = samplerate;
                initResampler_ = true;
#ifdef WEBRTC_RESAMPLE_ANOTHER
                resampler_.InitializeIfNeeded(samplerate, samplerate_, 1);
#else
                resampler_.ResetIfNeeded(samplerate, samplerate_, 1);
#endif
            }
            auto frame = Frame::CreateFrame(FRAME_FORMAT_PCM_16000_1);
            frame->createFrameBuffer(f->getFrameSize() * samplerate_ / samplerate);
            memset(frame->getFrameBuffer(), 0, frame->getFrameSize());
#ifdef WEBRTC_RESAMPLE_ANOTHER
            resampler_.Resample((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2);
#else
            size_t outlen = 0;
            resampler_.Push((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2, outlen);
#endif
            f = frame;
        } 
        // if (playRunning_)
        // {
        //     std::unique_lock<std::mutex> locker(mutexPlay_);
        //     playbbuf_.WriteBytes(f->getFrameBuffer(), f->getFrameSize());
        // }
        if (discard_frames_count_ > 0) {
            discard_frames_count_ -= f->getFrameSize();
            if (discard_frames_count_ <= 0) {
                discard_frames_count_ = 0;
            }
            return;
        }
        if (!muted_)
            ShmPcm::inst().WritePcm(CH_COMMON, f->getFrameBuffer(), f->getFrameSize(), false);
        frames_recieved_ += f->getFrameSize();
        auto b = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(b - a).count();
        if (duration > 5000)
            jwarn("play duration = %d", duration);
    }
}

void AudioSpecMulticasterSingle::writeFarendData(const char* data, int len)
{
    std::unique_lock<std::mutex> l(mutexFarend_);
    farendBBuf_.WriteBytes(data, len);
}

bool AudioSpecMulticasterSingle::readFarendData(char* data, int len)
{
    bool ret = false;
    std::unique_lock<std::mutex> l(mutexFarend_);
    if (farendBBuf_.Length() >= len)
    {
        memcpy(data, farendBBuf_.Data(), len);
        farendBBuf_.Consume(len);
        ret = true;
    }
    return ret;
}

void AudioSpecMulticasterSingle::playLoop()
{
    setThreadAffinity(7);
    // playRunning_ = 1;
    bool needDrop = false;
    int maxDelay = ptimeDataLen_ * 2;
    int dropEveryTime = ptimeDataLen_;
    int maxDelay_PSTN = BUF_LEN_40MS; 
    int dropEveryTime_PSTN = maxDelay_PSTN / 2;
    int consumeDataTime = 0;
#if H3_NEW_DRIVER
    ClearSendBuf(CH_COMMON);
#endif  
    auto last = std::chrono::steady_clock::now();
    while (playRunning_)
    {
        if (mode_ == MODE_PSTN) {
            maxDelay = maxDelay_PSTN;
            dropEveryTime = dropEveryTime_PSTN;
        }
        ByteBuffer playbbuf;
        {
            std::unique_lock<std::mutex> locker(mutexPlay_);
            playbbuf.WriteBytes(playbbuf_.Data(), playbbuf_.Length());
            playbbuf_.Clear();  
        }
        if (playbbuf.Length() == 0)
        {
            //jinfo("playbbuf_ is empty, usleep(1000 * 10)");
#if H3_NEW_DRIVER
            usleep(1000 * 5);
#else 
            usleep(1000 * 10);
#endif
        }
        if (playbbuf.Length() > maxDelay * 2)
        {
            jinfo("AudioSpecMulticasterSingle::playLoop too much data %d > %d", playbbuf.Length(), maxDelay * 2);
            playbbuf.Consume(playbbuf.Length() - maxDelay);
        }
        needDrop = (playbbuf.Length() >= maxDelay);
        if (!needDrop)
            consumeDataTime = 0;
        // int expected = playbbuf.Length() / BUF_LEN;
        // auto c = std::chrono::steady_clock::now();
        while (playbbuf.Length() >= BUF_LEN)
        {
            frames_to_playout_ += BUF_LEN;
            auto a = std::chrono::steady_clock::now();
            WritePcm(CH_COMMON, playbbuf.Data(), BUF_LEN);
            // {
            //     if (!ofs_.is_open()) ofs_.open("play.pcm", std::ios::out | std::ios::binary);
            //     ofs_.write(playbbuf.Data(), BUF_LEN);
            // }
            auto b = std::chrono::steady_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(b - a).count();
            auto duration0 = std::chrono::duration_cast<std::chrono::microseconds>(last - a).count();
            last = a;
            if (duration > 5000)
                jwarn("play duration = %d", duration);
            if (duration0 > 2000)
                jwarn("play delay = %d", duration0);
            writeRecord(playf, playbbuf.Data(), BUF_LEN);
            if (!playRunning_)
            {
                break;
            }
            playbbuf.Consume(BUF_LEN);
            if (needDrop)
            {
                consumeDataTime += BUF_LEN;
                if (consumeDataTime >= dropEveryTime)
                {
                    consumeDataTime = 0;
                    if (playbbuf.Length() >= BUF_LEN * 5)
                    {
                        playbbuf.Consume(BUF_LEN * 5);
                        jinfo("AudioSpecMulticasterSingle::playLoop drop 5ms, remain %d", playbbuf.Length());
                    }
                }
            }
            // usleep(300);
        }
        // auto d = std::chrono::steady_clock::now();
        // auto duration = std::chrono::duration_cast<std::chrono::microseconds>(d - c).count();
        // jinfo("expected %dms, but %dus", expected, duration);
        if (!playRunning_)
        {
            break;
        }
    }
}

// TODO: DRY
void AudioSpecMulticasterSingle::ResetStatistics() {
	statistics_start_time_ = std::chrono::steady_clock::now();
    frames_captured_ = 0;
	frames_recieved_ = 0;
	frames_to_send_ = 0;
    frames_to_playout_ = 0;
    frames_lec_ref_ = 0;
}

void AudioSpecMulticasterSingle::LogStatistics() {
    auto current_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - statistics_start_time_).count();
    if (duration > 10000) {
        jinfo(
            "[AudioSpecMulticasterSingle] Duartion: %dms. Frame captured: %d. Frame to send: %d, frames to playout: %d, frames recieved: %d LEC REF: %d", duration,
            frames_captured_, frames_to_send_, frames_to_playout_, frames_recieved_, frames_lec_ref_);
        ResetStatistics();
    }
}

void AudioSpecMulticasterSingle::setFrameTimeoutNotify(int ms, void *param, const TimeoutNotifyCallback &cb) {
    if (ms <= 0) {
        jinfo("AudioSpecMulticasterSingle setFrameTimeoutNotify timeout must > 0");
        return;
    }
    jinfo("AudioSpecMulticasterSingle setFrameTimeoutNotify %d", ms);
    FramePipeline::setFrameTimeoutNotify(ms, param, cb);
    if (!check_timeout_thread_.isRunning()) {
        check_timeout_thread_.loop()->setInterval(30, [this](hv::TimerID id) {
            if (startPlay_) {
                checkFrameTimeout();
            }
        });
        check_timeout_thread_.start(true);
    }
}

void AudioSpecMulticasterSingle::setFrameTimeoutNotify2(int ms, void* param, const TimeoutNotifyCallback& cb) {
	if (ms <= 0) {
		jinfo("AudioSpecMulticasterSingle setFrameTimeoutNotify timeout must > 0");
		return;
	}
	jinfo("AudioSpecMulticasterSingle setFrameTimeoutNotify %d", ms);
	FramePipeline::setFrameTimeoutNotify2(ms, param, cb);
	if (!check_timeout_thread_.isRunning()) {
		check_timeout_thread_.loop()->setInterval(30, [this](hv::TimerID id) {
			if (startPlay_) {
				checkFrameTimeout();
			}
		});
		check_timeout_thread_.start(true);
	}
}