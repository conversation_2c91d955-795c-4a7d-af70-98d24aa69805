#include <stdint.h>
#include "RnnAudioFrameNS.h"
#include "Frame.h"
#include "Utils.h"
#include <ljcore/jlog.h>
#include <stdio.h>

extern int mmi_record_audio;

using namespace panocom;

RnnAudioFrameNS::RnnAudioFrameNS(const std::string& jsonParams)
{
    FN_BEGIN;
    jinfo("RnnAudioFrameNS %s", jsonParams.c_str());
    name_ = "RnnAudioFrameNS";
    start();
    
    FN_END;
}

RnnAudioFrameNS::~RnnAudioFrameNS()
{
    FN_BEGIN;
    stop();
    FN_END;
}

void RnnAudioFrameNS::start()
{
    if (thread_.isRunning()) return;
    FN_BEGIN;
    ds_ = std::shared_ptr<DenoiseState>(rnnoise_create(NULL), [](DenoiseState* ds){
        rnnoise_destroy(ds);
    });
    thread_.start();
    FN_END;
}

void RnnAudioFrameNS::stop()
{
    if (!thread_.isRunning()) return;
    FN_BEGIN;
    std::unique_lock<std::mutex> locker(frame_mutex_);
    thread_.stop(true);
    ds_.reset();
    initResampler_ = false;
    FN_END;
}

void RnnAudioFrameNS::onFrame(const std::shared_ptr<Frame> &frame)
{
    std::unique_lock<std::mutex> locker(frame_mutex_);
    if (!thread_.isRunning()) return;
    if (Frame::isPCM(frame->getFrameFormat()))
    {
        thread_.loop()->runInLoop([this, frame]() {
            int samplerate = 0;
            int chn = 0;
            auto f = frame;
            if (Frame::getSamplerate(f->getFrameFormat(), samplerate, chn))
            {
                if (samplerate != 48000)
                {
                    if (!initResampler_ || source_samplerate_ != samplerate)
                    {
                        source_samplerate_ = samplerate;
                        initResampler_ = true;
#ifdef WEBRTC_RESAMPLE_ANOTHER
                        resampler_.InitializeIfNeeded(samplerate, 48000, 1);
#else
                        resampler_.ResetIfNeeded(samplerate, 48000, 1);
#endif
                    }
                    auto frame = Frame::CreateFrame(FRAME_FORMAT_PCM_48000_1);
                    frame->createFrameBuffer(f->getFrameSize() * 48000 / samplerate);
                    memset(frame->getFrameBuffer(), 0, frame->getFrameSize());
#ifdef WEBRTC_RESAMPLE_ANOTHER
                    resampler_.Resample((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2);
#else
                    size_t outlen = 0;
                    resampler_.Push((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2, outlen);
#endif
                    f = frame;
                    if (first_)
                    {
                        first_ = false;
                        jinfo("RnnAudioFrameNS resample %d -> 48000", samplerate);
                    }
                }
                int16_t* pcm = (int16_t*)f->getFrameBuffer();
                int pcmsize = f->getFrameSize() / sizeof(int16_t);
                int sample = 480;
                int count = pcmsize / sample;
                float x[480];
                for (size_t i = 0; i < count; i++)
                {
                    for (int j = 0; j < sample; j++)
                        x[j] = pcm[j + i * sample];
                    rnnoise_process_frame(ds_.get(), x, x);
                    for (int j = 0; j < sample; j++)
                        pcm[j + i * sample] = x[j];
                }
                deliverFrame(f);
                printOutputStatus("RnnAudioFrameNS");
                if (mmi_record_audio)
                {
                    if (!pf_)
                    {
                        std::string fileName = name_ + std::to_string((long)this) + ".pcm";
                        pf_ = fopen(fileName.c_str(), "w+b");
                    }
                    if (pf_)
                    {
                        fwrite(f->getFrameBuffer(), 1, f->getFrameSize(), pf_);
                    }
                } 
            }
        });
        printInputStatus("RnnAudioFrameNS");
    }
    else
    {

    }
}