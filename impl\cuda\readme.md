# cuda
1. 使用cuda的接口，必须切换和GPU绑定的上下文，CuCommon管理着cuda的上下文，id暂时没有使用，主要是gpuindex；
2. CuFrame封装了当前编解码过程中的帧变量，为了避免拷贝，帧变量依赖着编解码的上下文，例如CuDecodedNV12Frame是由解码器内部分配，用完必须回收不然会解码失败，并且必须全部回收才能释放解码器；
3. CuIPCFrameCapturer和CuIPCFrameRender利用的cuda全局显存的功能通过共享内存的方式进行进程通信，内部使用了一个简单的无锁算法，例如创建8块共享内存用于存储帧句柄，CuIPCFrameRender在前4块共享内存找到空闲的块，填写帧句柄等信处，CuIPCFrameCapturer扫描前4块共享内存，拿到保存帧句柄的内存，传递给下层pipeline使用，并使用智能指针管理生命周期，在智能指针释放的回调中并把帧id填到后4块共享内存中，CuIPCFrameRender扫描后4块共享内存，发现有用完的帧句柄将其回收，CuIPCFrameCapturer通过扫描前4块内存发现没有后4块内存保存的帧句柄说明帧已被回收则可继续填写新的回收帧句柄，对于CuIPCFrameRender上4块内存可写可读下4块内存只读，对于CuIPCFrameCapturer下4块内存可写可读上4块内存只读，具体查阅实现代码；
4. CuOpenGLRender用于cuda显存的opengl显示，使用npp进行yuv转rbg以及缩放功能；
5. CuFrameScale、CuVideoFrameMixer未实现;
6. CuQML/CuWidget有示例，优先查阅CuQML，支持IP直呼.