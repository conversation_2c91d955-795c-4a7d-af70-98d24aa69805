//
// Created by li-weibing on 2025/4/23.
//
#ifdef ENABLE_VIDEO
#include "FfIPCFrameRender.h"
#include "Frame.h"
#include "Utils.h"
#include <json.hpp>
#include <ljcore/jlog.h>
#include <stdio.h>

using namespace panocom;

FfIPCFrameRender::FfIPCFrameRender(const std::string& jsonParams)
{
    FN_BEGIN;
    name_ = "FfIPCFrameRender";
    start();
    FN_END;
}

FfIPCFrameRender::~FfIPCFrameRender()
{
    FN_BEGIN;
    stop();
    FN_END;
}

void FfIPCFrameRender::start()
{
    if (thread_.isRunning()) return;
    FN_BEGIN;
    thread_.start();
    FN_END;
}

void FfIPCFrameRender::stop()
{
    if (!thread_.isRunning()) return;
    FN_BEGIN;
    std::unique_lock<std::mutex> locker(frame_mutex_);
    thread_.stop(true);
    FN_END;
}

void FfIPCFrameRender::onFrame(const std::shared_ptr<Frame>& frame)
{
#ifdef USE_FFMPEG
    std::unique_lock<std::mutex> locker(frame_mutex_);
    if (frame->getFrameFormat() != FRAME_FORMAT_FFAVFRAME || !thread_.isRunning()) return;
    thread_.loop()->runInLoop([this, frame](){
        int groupId = frame->getGroupId();
        FFAVFrame* f = (FFAVFrame*)frame.get();
        AVFrame* avframe = (AVFrame*)f->getAVFrame();
        FrameSender::Inst()->sendFrame(avframe,groupId);
    });
    printInputStatus("FfIPCFrameRender");
#endif
}
#endif