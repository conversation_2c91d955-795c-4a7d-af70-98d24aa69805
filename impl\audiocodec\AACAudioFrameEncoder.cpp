#include "AACAudioFrameEncoder.h"

#include <json.hpp>

namespace panocom
{
AACAudioFrameEncoder::AACAudioFrameEncoder(const std::string &jsonParams)
    : fmt_(FRAME_FORMAT_AAC)
    , channel_(1)
    , samplerate_(16000)
	, bitrate_(64000)
	, outbuf_size_(20480) {
	name_ = "AACAudioFrameEncoder";
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    if (j.contains("channel")) {
        channel_ = j["channel"];
    }
    if (j.contains("samplerate")) {
        samplerate_ = j["samplerate"];
    }
    if (j.contains("bitrate")) {
        bitrate_ = j["bitrate"];
    }
    if (channel_ == 2 && samplerate_ == 48000) fmt_ = FRAME_FORMAT_AAC_48000_2;
	else if (channel_ == 1 && samplerate_ == 48000) fmt_ = FRAME_FORMAT_AAC_48000_1;
	frame_length_ = samplerate_ / 1000 * 10;
	if (init()) {
		outbuf_ = new uint8_t[outbuf_size_];
		loop_thread_ = std::make_shared<hv::EventLoopThread>();
		loop_ = loop_thread_->loop();
		loop_thread_->start();		
	} else {
		jerror("AACAudioFrameEncoder init falied");
		
	}
	
}
AACAudioFrameEncoder::~AACAudioFrameEncoder() {
	if (loop_thread_ && loop_thread_->isRunning()) {
		loop_thread_->stop();
		loop_thread_->join();
	}
	if (encoder_)
		aacEncClose(&encoder_);
	encoder_ = nullptr;
	if (outbuf_) delete[] outbuf_;
	outbuf_ = nullptr;
	input_buf_.Clear();
}

bool AACAudioFrameEncoder::init() {
    CHANNEL_MODE mode;
	int aot = /*2*/AOT_AAC_LC;	// TODO: 
	int eld_sbr = 0;
	// AACENC_InfoStruct info { 0 };
    switch (channel_) {
        case 1: mode = MODE_1; break;
        case 2: mode = MODE_2; break;
        case 3: mode = MODE_1_2; break;
        case 4: mode = MODE_1_2_1; break;
        case 5: mode = MODE_1_2_2; break;
        case 6: mode = MODE_1_2_2_1; break;
        default: jerror("Unsupported channels %d", channel_); return false;
    }
    switch (fmt_) {
        case FRAME_FORMAT_AAC: aot = 2; break;
        case FRAME_FORMAT_AAC_48000_2: 
		case FRAME_FORMAT_AAC_48000_1: aot = AOT_ER_AAC_LD; break;
        default: break;
    }
    if (aacEncOpen(&encoder_, 0, channel_) != AACENC_OK) {
		jerror("Unable to open encoder");
		return false;
	}
	if (aacEncoder_SetParam(encoder_, AACENC_AOT, aot) != AACENC_OK) {
		jerror("Unable to set the AOT");
		if (encoder_) aacEncClose(&encoder_);
		encoder_ = nullptr;
		return false;
	}
	if (aot == 39 && eld_sbr) {
		if (aacEncoder_SetParam(encoder_, AACENC_SBR_MODE, 1) != AACENC_OK) {
			jerror("Unable to set SBR mode for ELD");
			if (encoder_) aacEncClose(&encoder_);
			encoder_ = nullptr;
			return false;
		}
	}
	if (aacEncoder_SetParam(encoder_, AACENC_SAMPLERATE, samplerate_) != AACENC_OK) {
		jerror("Unable to set the AOT");
		if (encoder_) aacEncClose(&encoder_);
		encoder_ = nullptr;
		return false;
	}
	if (aacEncoder_SetParam(encoder_, AACENC_CHANNELMODE, mode) != AACENC_OK) {
		jerror("Unable to set the channel mode");
		if (encoder_) aacEncClose(&encoder_);
		encoder_ = nullptr;
		return false;
	}
	if (aacEncoder_SetParam(encoder_, AACENC_CHANNELORDER, 1) != AACENC_OK) {
		jerror("Unable to set the wav channel order");
		if (encoder_) aacEncClose(&encoder_);
		encoder_ = nullptr;
		return false;
	}
	if (aacEncoder_SetParam(encoder_, AACENC_BITRATE, bitrate_) != AACENC_OK) {
		jerror("Unable to set the bitrate");
		if (encoder_) aacEncClose(&encoder_);
		encoder_ = nullptr;
		return false;
	}
	if (aot == 2) {
		jinfo("Set the ADTS transmux");
		if (aacEncoder_SetParam(encoder_, AACENC_TRANSMUX, TT_MP4_ADTS) != AACENC_OK) {
			jerror("Unable to set the ADTS transmux");
			if (encoder_) aacEncClose(&encoder_);
			encoder_ = nullptr;
			return false;
		}
	} else {
		// 带内
		jinfo("Set the LATM transmux");
		if (aacEncoder_SetParam(encoder_, AACENC_TRANSMUX, TT_MP4_LATM_MCP1) != AACENC_OK) {
			jerror("Unable to set the LATM transmux");
			if (encoder_) aacEncClose(&encoder_);
			encoder_ = nullptr;
			return false;
		}
	}
	if (aacEncoder_SetParam(encoder_, AACENC_AFTERBURNER, 1) != AACENC_OK) {
		jerror("Unable to set the afterburner mode");
		if (encoder_) aacEncClose(&encoder_);
		encoder_ = nullptr;
		return false;
	}
	if (aacEncoder_SetParam(encoder_, AACENC_GRANULE_LENGTH, frame_length_) != AACENC_OK) {
		jerror("Unable to set the frame length: %d", frame_length_);
		if (encoder_) aacEncClose(&encoder_);
		encoder_ = nullptr;
		return false;
	}
	if (aacEncEncode(encoder_, NULL, NULL, NULL, NULL) != AACENC_OK) {
		jerror(" Unable to initialize the encoder");
		if (encoder_) aacEncClose(&encoder_);
		encoder_ = nullptr;
		return false;
	}
	if (aacEncInfo(encoder_, &info_) != AACENC_OK) {
		jerror("Unable to get the encoder info");
		return false;
	}
	jinfo("AACAudioFrameEncoder init success! FrameLength: %d", info_.frameLength);
    return true;
}

void AACAudioFrameEncoder::onFrame(const std::shared_ptr<Frame> &frame) {
	// int16_t *buf = (int16_t*)frame->getFrameBuffer();
	if (!encoder_) return;
    int samplerate = 0;
    int channels = 0;
    auto f = frame;
    if (Frame::getSamplerate(frame->getFrameFormat(), samplerate, channels)) {
        if (samplerate_ != samplerate || channel_ != channels) {
            auto frame = Frame::CreateFrame((FrameFormat)fmt_);
            frame->createFrameBuffer(f->getFrameSize() * samplerate_ / samplerate * channel_ / channels);
            memset(frame->getFrameBuffer(), 0, frame->getFrameSize());
            if (!initResampler_ || samplerate != source_samplerate_)
            {
				source_samplerate_ = samplerate;
                initResampler_ = true;
#ifdef WEBRTC_RESAMPLE_ANOTHER
                resampler_.InitializeIfNeeded(samplerate, samplerate_, 1);
#else
                resampler_.ResetIfNeeded(samplerate, samplerate_, 1);
#endif
            }
#ifdef WEBRTC_RESAMPLE_ANOTHER
            resampler_.Resample((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2 / channel_ * channels);
#else
            size_t outlen = 0;
            resampler_.Push((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2 / channel_ * channels, outlen);
#endif
            // TODO: 
            if (channel_ == 2 && channels == 1) {
                uint16_t *data = (int16_t*)frame->getFrameBuffer();
                uint32_t frame_size = frame->getFrameBufferSize() / channel_ / 2;
                if (channel_ == 2 && channels == 1)
                    mono_to_stereo(data, frame_size); 
            }
            f = frame;
        }
		input_buf_.WriteBytes((char*)f->getFrameBuffer(), f->getFrameBufferSize());
	}
	
	if (loop_) {
		loop_->queueInLoop([this] {
			while (input_buf_.Length() >= 2 * info_.frameLength)
			{
				AACENC_BufDesc in_buf = { 0 }, out_buf = { 0 };
				AACENC_InArgs in_args = { 0 };
				AACENC_OutArgs out_args = { 0 };
				int in_identifier = IN_AUDIO_DATA;
				int in_size, in_elem_size;
				int out_identifier = OUT_BITSTREAM_DATA;
				int out_size, out_elem_size;
				void *in_ptr, *out_ptr;
				AACENC_ERROR err;
				in_ptr = (int16_t*)input_buf_.Data();
				in_size = 2 * info_.frameLength;
				// in_size = frame->getFrameBufferSize();
				in_elem_size = 2;

				in_buf.numBufs = 1;
				in_buf.bufs = &in_ptr;
				in_buf.bufferIdentifiers = &in_identifier;
				in_buf.bufSizes = &in_size;
				in_buf.bufElSizes = &in_elem_size;
				
				in_args.numInSamples = in_size / 2;

				out_ptr = outbuf_;
				out_size = outbuf_size_;
				out_elem_size = 1;
				out_buf.numBufs = 1;
				out_buf.bufs = &out_ptr;
				out_buf.bufferIdentifiers = &out_identifier;
				out_buf.bufSizes = &out_size;
				out_buf.bufElSizes = &out_elem_size;
				if ((err = aacEncEncode(encoder_, &in_buf, &out_buf, &in_args, &out_args)) != AACENC_OK) {
					jerror("Encoding failed err %d", err);
				} else {
					input_buf_.Consume(2 * info_.frameLength);
					if (out_args.numOutBytes == 0) continue;
					auto out_frame = Frame::CreateFrame(fmt_);
					out_frame->createFrameBuffer(out_args.numOutBytes);
					memcpy(out_frame->getFrameBuffer(), outbuf_, out_args.numOutBytes);
					deliverFrame(out_frame);
				}
			}
		});	
	}	
}
} // namespace panocom