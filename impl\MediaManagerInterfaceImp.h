#ifndef P_MediaManagerInterfaceImp_h
#define P_MediaManagerInterfaceImp_h

#include <string>
#include <set>
#include <unordered_set>
#include <unordered_map>
#include <mutex>
#include <memory>
#include <hv/EventLoopThread.h>
#include "MediaPipeline.h"
#include "VideoLayout.h"
#include "CodecInfo.h"
#include "DevInfo.h"
#include "sdptransform.hpp"
#ifdef USE_RPC
#include <RpcProxy/WsRpcChnl.h>
#include "mediamanagerservice.h"
#endif
#ifdef USE_MLOG
#include "LogWrapper.h"
#endif
#ifdef USE_ALSA
#include "alsa/alsa_utils.h"
#endif
namespace panocom
{
    using SDPNotifyCallback = std::function<void(const std::string&, const std::string&, int)>;
    using MediaTimeoutCallback = std::function<void(const std::string&, bool /*isVideo*/)>;
    using MediaTimeoutCallback2 = std::function<void(const std::string&, bool /*isVideo*/, bool /*connected*/)>;
    using RTPStatisticsNotifyCallback = std::function<void(const std::string& /*id*/, const std::string& /*json*/)>;
    using CommonNotifyCallback = std::function<void(const std::string& /*id*/, const std::string &/*json*/)>;

    using AudioCapturerNotifyCallback = std::function<void(const int &/*id*/, const std::string & /*json*/)>;
    using AudioRendererNotifyCallback = std::function<void(const int &/*id*/, const std::string & /*json*/)>;

    class MediaEndpoint
    {
    public:
        int id = -1;
        std::string ip;
        std::string bindIp;
        std::shared_ptr<uint16_t> rtpPort;
        std::shared_ptr<uint16_t> rtcpPort;
        FramePipeline::Ptr capturer;
        FramePipeline::Ptr caption_capturer;
        std::unordered_map<int, FramePipeline::Ptr> capturers;
        std::unordered_map<int, FramePipeline::Ptr> renders;
        std::unordered_map<int, std::vector<FramePipeline::Ptr>> processors;
        std::set<FramePipeline::Ptr> pipelines;
        std::set<FramePipeline::Ptr> rtcps;
        FramePipeline::Ptr file;
        FramePipeline::Ptr render;
        FramePipeline::Ptr mixer;
        FramePipeline::WPtr rendererPrevious;
        FramePipeline::WPtr gainController;
        // FramePipeline::WPtr standbyFramePipeline;
        std::list<FramePipeline::WPtr> temps;
        FramePipeline::Ptr stream_monitor;
        CodecInst codec;
        int dev = -1;
        nlohmann::json params;
        nlohmann::json mediaPart;
        bool dtls;
        bool isServer;
        std::string fingerprint;
        std::string stunSdp;
        enum
        {
            IDLE,
            WORKING,
        };
        int state = IDLE;
        int gpu_index = 0;
        MediaTimeoutCallback timeoutCB;
        MediaTimeoutCallback2 timeoutCB2;
        int timeoutMS = 0;
        // audio
        bool enableLP = false;
        int bindPort;
        std::string multicastIP;
        int multicastPort;
        int roomid = -1;
        bool muteNotLP = false;
        int aec = 1;
        int lec = 1;
        int ns = 1;
        int agc = 1;
        int delay = 1;
        int anti_howling = 1;
        int cng = 0;
        int vad = 0;
        int jitter_buffer = 0;
        int plc = 0;
        int log = 0;
        int record_audio = 0;
        bool useHeadset = false;
        bool innerAec = true;
        int mic_type = 1;
        
        // video
        bool pip = false;
        bool video_nack_enabled = false; 
        bool video_fec_enabled = false;
        bool video_jitter_buffer = false;
        
        bool codec_changed = false;
        bool addr_changed = false;
        bool found_codec = true;

        uint8_t red_payload_type = RED_90000_PT;
        uint8_t ulpfec_payload_type = ULPFEC_90000_PT;

        uint8_t payload_type = 8;
        uint32_t ptime = 20;

        bool use_uart = false;
        bool use_mixer = false;
        bool use_dispatcher = false;    // FIXME：断流检测无效
        std::unordered_set<int> devs;
        int external_hdmi = -1;
        bool enabled = true;
        bool standby = false; // 画面等待
        std::string videoSrc;
        // max resolution, max framerate, max bitrate for video
        int32_t max_frame_rate = 0;
        int32_t max_frame_size = 0; // max frame size in bytes
        int32_t max_bitrate = 0;
    };
    class MediaSession
    {
    public:
        enum
        {
            INACTIVE,
            SENDONLY,
            RECVONLY,
            SENDRECV,
        };
        int dir = INACTIVE;
        std::string id;
        MediaEndpoint local;
        MediaEndpoint remote;
        std::list<CodecInst> codecs;
        SDPNotifyCallback cb;
        enum
        {
            CALL_IDLE,
            CALL_OFFER,
            CALL_ANSWER,
            CALL_COMPLETED,
        };
        int callstate = CALL_IDLE;
        std::string lastSdp;
#ifdef USE_MLOG
        MediaManagerSP::LogWrapper logger;
#endif
        RTPStatisticsNotifyCallback rtpcb;
        CommonNotifyCallback commoncb;
        bool notify_async;
        std::string rtpParams;
        bool negotiated = false;
        bool dir_changed = false;
        bool is_h323 = false;
        bool call_out = false;
    };

    class MediaDevWorker
    {
    public:
        std::string id;
        std::string jsonParms;
    };
    // TODO: figure it out, add thread worker, fix concurrent.
    class MediaManagerInterfaceImp
#ifdef USE_RPC
    : public MediaManagerService<panocom::MediaManagerInterfaceImp>
#endif
    {
    public:
        enum CodecType
        {
            AUDIO,
            VIDEO,
        };
        using WorkingMediaCallback = std::function<void(const std::string&, const std::string&)>;
        using WorkingMediaCallback2 = std::function<void(const std::string&, const std::string&, const std::string&)>;
        MediaManagerInterfaceImp(const std::string& jsonParams);
        ~MediaManagerInterfaceImp();

        int CreateSession(const std::string& id, const std::string& jsonParams);
        int StartSession(const std::string& id);
        int PauseSession(const std::string& id);
        int StopSession(const std::string& id);
        int ReleaseSession(const std::string& id);
        int ReleaseSession(const std::string& id, bool isVideo);
        int ClearSessions();

        int GetLocalSDP(const std::string& id, std::string& sdp, const std::string& jsonParams = "");
        int SetRemoteSDP(const std::string& id, const std::string& sdp, std::string& localSdp, const std::string& jsonParams = "", bool startSession = true);
        std::string GetDefaultIP();

        int StartUplinkAudioFile(const std::string& id, const std::string& path, const std::string& jsonParams = "");
        int StopUplinkAudioFile(const std::string& id);

        int StartUplinkVideoFile(const std::string& id, const std::string& path, const std::string& jsonParams = "");
        int StopUplinkVideoFile(const std::string& id);

        int StartDownlinkAudioFile(const std::string& id, const std::string& path, const std::string& jsonParams = "");
        int StopDownlinkAudioFile(const std::string& id);

        int StartDownlinkVideoFile(const std::string& id, const std::string& path, const std::string& jsonParams = "");
        int StopDownlinkVideoFile(const std::string& id);

        int GetLocalSDP(const std::string& id, const std::string& jsonParams, const SDPNotifyCallback& cb);
        int SetRemoteSDP(const std::string& id, const std::string& sdp, const std::string& jsonParams, const SDPNotifyCallback& cb);

        int SetRemoteMediaTimeoutListener(const std::string& id, int timeoutMS, const MediaTimeoutCallback& cb);
        int SetRemoteMediaTimeoutListener2(const std::string& id, int timeoutMS, const MediaTimeoutCallback2& cb);
        int AddToMediaPool(const std::string poolId, const std::string& id, const WorkingMediaCallback& cb);
        int AddToMediaPool2(const std::string poolId, const std::string& id, const WorkingMediaCallback2& cb);
        int RemoveFromMediaPool(const std::string poolId, const std::string& id);
        int ClearMediaPool(const std::string poolId);
        int SetActiveMedia(const std::string poolId, const std::string& id);
        int GetActiveMediaSession(const std::string &poolId, std::string &id);

        int MediaCtrl(const std::string& id, const std::string& jsonParams);
        int SetMediaLayout(const std::string& id, const std::list<Region>& layout, const std::string& jsonParams);

        std::list<CodecInst> GetSupportAudioCodecs();
        std::list<CodecInst> GetSupportVideoCodecs();

        void SetAudioCodecPriority(const std::list<CodecInst>& codecs, const std::string& id = "");
        void SetVideoCodecPriority(const std::list<CodecInst>& codecs, const std::string& id = "");

        int StartPSTN(const std::string& id);
        int StopPSTN(const std::string& id);
        int AddVideoRender(const std::string& id, const FramePipeline::Ptr& ptr);
        int RemoveVideoRender(const std::string& id);

        void SetRTPStatisticsListener(const std::string& id, const std::string& jsonParam, const RTPStatisticsNotifyCallback& cb, bool use_async);
        int UpdateAudioCapturer(const std::string &id, const std::string &jsonParams);
        int UpdateAudioRender(const std::string &id, const std::string &jsonParams);
        int UpdateAudioMixer(const std::string &id, const std::string &jsonParams);
        bool GetConnectorStatus(const std::string& jsonParams);
        bool GetCameraStatus(const std::string& jsonParams);

        int StartPlayFile(const std::string& jsonParams);
        int StopPlayFile(const std::string& jsonParams);

        int LoopbackTest(const std::string &id, const std::string& jsonParm);
        int StopLoopbackTest(const std::string &id);
        
        int OpenPip(const std::string &id, const std::string& jsonParm);
        int ClosePip(const std::string &id);
        int OpenCaption(const std::string &id, const std::string& jsonParm);
        int CloseCaption(const std::string &id);

        // 创建音频采集器
        // @param[in] 设备id，用于标识一个设备
        // @param[in] jsonParam: 配置参数json字符串
        int CreateAudioCapturer(const int &id, const std::string& jsonParm);
        int DestroyAudioCapturer(const int &id);
        // 创建音频渲染器
        // @param[in] id: 设备id，用于标识一个设备
        // @param[in] jsonParam: 配置参数json字符串
        int CreateAudioRenderer(const int &id, const std::string& jsonParm);
        int DestroyAudioRenderer(const int &id);
        int GetAudioCapturerStatus(const int id, std::string &jsonParam);

        int SetAudioCapturerNotifyCallback(const int &id, const std::string& jsonParam, const AudioCapturerNotifyCallback& callback);
        int SetAudioRendererNotifyCallback(const int &id, const std::string& jsonParam, const AudioRendererNotifyCallback& callback);

#ifdef USE_ALSA
        std::vector<AlsaHWPcmDevice> ListAudioHWDeviceInfos();
        std::vector<AlsaPcmDeviceInfo> ListAudioDeviceInfos();
#endif        
    private:
        int createSession(const std::string& id, const std::string& jsonParams);
        int startSession(const std::string& id);
        int pauseSession(const std::string& id);
        int stopSession(const std::string& id);
        int releaseSession(const std::string& id);
        int releaseSession(const std::string& id, bool isVideo);
        int clearSessions();

        int getLocalSDP(const std::string& id, std::string& sdp, bool isOffer, const std::string& jsonParams = "");
        int setRemoteSDP(const std::string& id, const std::string& sdp, std::string& localSdp, const std::string& jsonParams = "", bool startSession = true);

        int startUplinkAudioFile(const std::string& id, const std::string& path, const std::string& jsonParams = "");
        int stopUplinkAudioFile(const std::string& id);

        int startUplinkVideoFile(const std::string& id, const std::string& path, const std::string& jsonParams = "");
        int stopUplinkVideoFile(const std::string& id);

        int startDownlinkAudioFile(const std::string& id, const std::string& path, const std::string& jsonParams = "");
        int stopDownlinkAudioFile(const std::string& id);

        int startDownlinkVideoFile(const std::string& id, const std::string& path, const std::string& jsonParams = "");
        int stopDownlinkVideoFile(const std::string& id);

        int getLocalSDP(const std::string& id, bool isOffer, const std::string& jsonParams, const SDPNotifyCallback& cb);
        int setRemoteSDP(const std::string& id, const std::string& sdp, const std::string& jsonParams, const SDPNotifyCallback& cb);

        int setRemoteMediaTimeoutListener(const std::string& id, int timeoutMS, const MediaTimeoutCallback& cb);
        int setRemoteMediaTimeoutListener2(const std::string& id, int timeoutMS, const MediaTimeoutCallback2& cb);
        void setRemoteMediaTimeoutCallback(const std::string& id);

        // 双流和断流检测新方案，不使用渲染器进行监控，采用在rtp模块后接一个monitor模块监控
        void setStreamMonitorTimeoutCallback(const std::string& id);
        void setMediaPoolStreamMonitorCallback(const std::string poolId, const std::string& id);

        int addToMediaPool(const std::string poolId, const std::string& id, const WorkingMediaCallback& cb);
        int addToMediaPool2(const std::string poolId, const std::string& id, const WorkingMediaCallback2& cb);
        int removeFromMediaPool(const std::string poolId, const std::string& id);
        int clearMediaPool(const std::string poolId);
        int setActiveMedia(const std::string poolId, const std::string& id);
        void setMediaPoolStreamCallback(const std::string poolId, const std::string& id);
        int getActiveMediaSession(const std::string& poolId, std::string &id);

        int mediaCtrl(const std::string& id, const std::string& jsonParams);
        int setMediaLayout(const std::string& id, const std::list<Region>& layout, const std::string& jsonParams);

        std::list<CodecInst> getSupportAudioCodecs();
        std::list<CodecInst> getSupportVideoCodecs();

        void setAudioCodecPriority(const std::list<CodecInst>& codecs, const std::string& id = "");
        void setVideoCodecPriority(const std::list<CodecInst>& codecs, const std::string& id = "");

        int startPSTN(const std::string& id);
        int stopPSTN(const std::string& id);
        int addVideoRender(const std::string& id, const FramePipeline::Ptr& ptr);
        int removeVideoRender(const std::string& id);

        void setRTPStatisticsListener(const std::string& id, const std::string& jsonParam, const RTPStatisticsNotifyCallback& cb, bool use_async);
        void setRTPStatisticsCallback(const std::string& id);

        int startPlayFile(const std::string& jsonParams);
        int stopPlayFile(const std::string& jsonParams);

        int updateAudioMixer(const std::string &id, const std::string &jsonParams);
    private:
        bool isOffer(const std::string& id);
        bool isActiveStream(const std::string& id);
        std::string getWorkingIdBySessionId(const std::string& id) const;
        bool isInPool(std::string& poolId, const std::string& id);
        bool isWorkingId(std::string& poolId, const std::string& id);
        void LoadConfig(const std::string& path);
        std::shared_ptr<uint16_t> getPort();
        std::string getIP(const std::string& ifdev);
        CodecInst codecInstFromJson(const nlohmann::json& rtp, int ptime = 0);
        CodecInst codecInstFromJsonWithFmtp(const nlohmann::json& rtp, const nlohmann::json& fmtp, std::string& selected, int ptime = 0);
        std::list<CodecInst> GetSupportCodecs(CodecType type);
        bool isSupportCodec(const CodecInst& codec, CodecType type);
        int getLocalSDP(const std::string& id, std::string& sdp, bool isOffer, const std::string& jsonParams, const std::list<CodecInst>& audioCodecs, const std::list<CodecInst>& videoCodecs, bool dtls);
        int processAnswer(const std::string& id, const std::string& remotesdp, const std::string& jsonParams);
        int processOffer(const std::string& id, const std::string& remotesdp, std::string& localSdp, const std::string& jsonParams);
        int processOffer(const std::string& id, const std::string& remotesdp, const std::string& jsonParams, const SDPNotifyCallback& cb);
        void makeStunSdp(const std::string& id, const std::string& sdp, bool useVideo, const std::string& jsonParams, const SDPNotifyCallback& cb);
        std::list<CodecInst> findMatchCodec(const std::list<CodecInst>& codecs, CodecType type);
        void makeLocalSession(const std::string& id, nlohmann::json& media, const std::string& ip, const std::string& dir, const std::list<CodecInst>& codecs, CodecType type, bool dtls, const std::string &bind_ip="");
        void makeRemoteSession(const std::string& id, const CodecInst& codec, const std::string& ip, int port, const std::string& dir, CodecType type, bool found_codec=true);
        void getPriorityCodecs(const std::string& id, std::list<CodecInst>& audioCodecs, std::list<CodecInst>& videoCodecs);

        int doMediaPipeline(const std::string& id);

        int addAudioSendPipeline(const std::string& id);
        int addAudioRecvPipeline(const std::string& id);

        int updateAudioSendPipeline(const std::string& id);
        int updateAudioRecvPipeline(const std::string& id);
#ifdef ENABLE_VIDEO        
        int addVideoSendPipeline(const std::string& id);
        int addVideoRecvPipeline(const std::string& id);

        int updateVideoSendPipeline(const std::string& id);
        int updateVideoRecvPipeline(const std::string& id);
#endif
        int inactivePipeline(CodecType type, const std::string& id);
        int sendOnlyPipeline(CodecType type, const std::string& id);
        int recvOnlyPipeline(CodecType type, const std::string& id);
        int sendrecvPipeline(CodecType type, const std::string& id);

        std::shared_ptr<AudioFrameCapturer> createAudioCapture(const std::string& id);
        std::shared_ptr<AudioFrameCapturer> createAudioCapturer(const std::string& id, const int dev);
        bool createAudioCapturers(const std::string& id);   // 创建后直接存到session中，返回是否成功就行
        int createAudioCapturer(const int & deviceId, const std::string& jsonParam);
        int destroyAudioCapturer(const int &deviceId);

        int createAudioUplinkProcesser(const std::string& id, std::vector<std::shared_ptr<AudioFrameProcesser>>& processers);

        std::shared_ptr<AudioFrameEncoder> createAudioEncoder(const CodecInst& codec);

        std::shared_ptr<AudioFrameRender> createAudioRender(const std::string& id);
        std::shared_ptr<AudioFrameRender> createAudioRender(const std::string& id, const int dev);
        bool createAudioRenders(const std::string& id);
        std::shared_ptr<AudioFrameRender> createLocalAudioRender(const int dev);
        std::shared_ptr<AudioFrameRender> createDefaultAudioRender(const int dev, const std::string &name);
        int createAudioRenderer(const int & deviceId, const std::string& jsonParam);
        int destroyAudioRenderer(const int & deviceId);

        int createAudioDownlinkProcesser(const std::string& id, std::vector<std::shared_ptr<AudioFrameProcesser>>& processers);

        std::shared_ptr<AudioFrameDecoder> createAudioDecoder(const CodecInst& codec);
        std::shared_ptr<AudioFrameDecoder> createAudioDecoder(const std::string& id);
        std::shared_ptr<AudioFrameDecoder> createAudioDecoderWrapper(const std::string &id);

        std::shared_ptr<AudioFrameMixer> createAudioMixer(const std::string& id, bool remote=false);
        
        std::shared_ptr<RtpEngine> createAudioRtp(const std::string& id, bool uplink);

#ifdef ENABLE_VIDEO  
        std::shared_ptr<VideoFrameCapturer> createVideoCapturer(int dev, int gpuIndex);
        std::shared_ptr<VideoFrameEncoder> createVideoEncoder(const CodecInst& codec, const std::string& id, int gpuIndex);

        std::shared_ptr<VideoFrameRender> createVideoRender(int dev, const std::string& id, int gpuIndex);
        std::shared_ptr<VideoFrameRender> createVideoRender(int dev, const std::string& id, int gpuIndex, int x, int y, int width, int height);
        std::shared_ptr<VideoFrameDecoder> createVideoDecoder(const CodecInst& codec, const std::string& decodeType, int gpuIndex);

        std::shared_ptr<RtpEngine> createVideoRtp(const std::string& id, bool uplink, std::shared_ptr<FramePipeline>& rtpTransport, std::shared_ptr<FramePipeline>& rtcpTransport);
        std::shared_ptr<RtpEngine> createVideoRtp(const std::string& id, bool uplink);

        std::shared_ptr<VideoFrameProcesser> createVideoPreprocessor(int dev, const std::string& id);
        std::shared_ptr<VideoFrameMixer> createVideoMixer(const std::string& id);

        bool getCameraStatus(const std::string& jsonParams);

        int openPip(const std::string &id, const std::string& jsonParm);
        int closePip(const std::string &id);
        int openCaption(const std::string &id, const std::string& jsonParm);
        int closeCaption(const std::string &id);
#endif
        void switchAudio(const std::string& oldId, const std::string& newId);
        void switchVideo(const std::string& oldId, const std::string& newId);
        void setWorkingId(const std::string poolId, const std::string& id, const std::string& oldId="");
        std::string  getWorkingId(const std::string &poolId);
        void generateRtpMapFromPayloads(nlohmann::json& rtp, std::string payloads);
        nlohmann::json generateRtpMapSortByPayloads(nlohmann::json& rtp, std::string payloads);

        bool hasCodecName(nlohmann::json& rtp, const std::string &name, int &payloadType);

        int loopbackTest(const std::string &id, const std::string& jsonParm);
        int stopLoopbackTest(const std::string &id);

        int updateGain(const std::string &id, const std::string& jsonParams);
        int getExtIdFromMediaJson(const nlohmann::json& mediaPart, const std::string& uri);

        int getAudioCapturerStatus(const int id, std::string &jsonParam);
        int setAudioCapturerNotifyCallback(const int &id, const std::string& jsonParam, const AudioCapturerNotifyCallback& callback);
        int setAudioRendererNotifyCallback(const int &id, const std::string& jsonParam, const AudioRendererNotifyCallback& callback);

        int setAudioEncoderNotifyCallback(const std::string &id);
        int setAudioDecoderNotifyCallback(const std::string &id);
#ifdef ENABLE_VIDEO
        int setVideoEncoderNotifyCallback(const std::string &id);
        int setVideoDecoderNotifyCallback(const std::string &id);
#endif
    protected:
        void CheckRpcTimeout(void);
    private:
        using MediaSessions = std::vector<std::unordered_map<std::string, MediaSession>>;
        // TODO: using MediaSessions = std::vector<std::unordered_map<std::string, std::shared_ptr<MediaSession>>>;
        MediaSessions sessions_;
        
        using JsonTemplate = std::vector<nlohmann::json>;
        JsonTemplate templates_;

        nlohmann::json config_;

        using MediaCodecs = std::vector<std::list<CodecInst>>;
        MediaCodecs codecs_;

        std::unordered_map<std::string, MediaDevWorker> devWorkers_;
        std::mutex mutex_;
        std::set<uint16_t> ports_;
        
        struct MediaPoolPilelines
        {
            // FramePipeline::Ptr render;
            FramePipeline::Ptr capturer;
            int capturer_ref = 0; 
            // std::unordered_map<int, FramePipeline::Ptr> capturers;
            // std::unordered_map<int, FramePipeline::Ptr> renders;
            // FramePipeline::Ptr mixer;
            // FramePipeline::Ptr stream_monitor;
        };
        
        struct MediaPoolInfo
        {
            MediaPoolInfo()
                : workingId("") {
#ifdef ENABLE_VIDEO
                pipelines.resize(2);
#else
                pipelines.resize(1);
#endif
            }
            std::string workingId;
            std::list<std::string> poolIds; //备选id
            WorkingMediaCallback cb;
            WorkingMediaCallback2 cb2;
            std::vector<MediaPoolPilelines> pipelines;
        };
        
        std::unordered_map<std::string, MediaPoolInfo> mediaPools_;
#ifdef USE_RPC
        std::shared_ptr<hv::EventLoopThread> thread_;
        std::shared_ptr<PRPC::WsRpcChnl> ws_;
#endif
        hv::EventLoopThread workThread_;
        hv::EventLoopThread cbThread_;

        std::vector<std::unordered_map<int, std::set<FramePipeline::Ptr>>> file_pipelines_;

        struct LoopbackPipelines
        {
            FramePipeline::Ptr capturer;
            FramePipeline::Ptr renderer;
        };
        
        std::unordered_map<std::string, LoopbackPipelines> local_loopback_;

        int32_t default_audio_timeout_ms_;
        int32_t default_video_timeout_ms_;

        struct PipelineRef {
            int32_t ref;
            FramePipeline::Ptr pipeline;
        };
        std::unordered_map<int, PipelineRef> audio_capturers_; 
        std::unordered_map<int, PipelineRef> audio_renderers_;
#ifdef ENABLE_VIDEO
        std::unordered_map<int, PipelineRef> video_capturers_;
        std::unordered_map<int, PipelineRef> video_renderers_;
#endif
    };
}

#endif