{"name": "template", "audio": {"capturer": {"name": "Sdl2AudioFrameCapturer", "samplerate": 16000, "channel": 1, "dev": -1}, "render": {"name": "Sdl2AudioFrameRender", "samplerate": 16000, "channel": 1, "dev": -1}, "encoder": {"name": "native", "codec": [{"name": "opus", "samplerate": 16000, "channel": 1, "fec": 1, "rc": "vbr"}, {"name": "g711", "samplerate": 8000, "channel": 1}]}, "decoder": {"name": "native", "codec": [{"name": "opus", "samplerate": 16000, "channel": 1, "fec": 1, "rc": "vbr"}, {"name": "g711", "samplerate": 8000, "channel": 1}]}, "uplink": {"processer": [{"name": "AudioFramePacer", "ptime": 20}, {"name": "RtcAudioFrameAec", "samplerate": 16000, "channel": 1}, {"name": "RnnAudioFrameNS", "samplerate": 48000, "channel": 1}], "rtp": "JRtpEngine"}, "downlink": {"processer": [{"name": "AudioFrameAsFarEnd", "samplerate": 16000, "channel": 1, "bind": "RtcAudioFrameAec"}], "rtp": "JRtpEngine"}}, "video": {"capturer": {"name": "V4L2VideoFrameCapturer", "width": 1920, "height": 1080, "fps": 30, "format": "mjpeg", "dev": -1, "decoder": "CuVideoDecoder"}, "render": {"name": "CuIPCFrameRender", "shm": "cuIPC", "frameCount": 8}, "encoder": {"name": "CuVideoEncoder", "codec": [{"name": "h264", "width": 1920, "height": 1080, "fps": 30, "gop": 30, "rc": "AVBR", "bitrate": 2000000, "max-qp": 35, "min-qp": 15}, {"name": "h265", "width": 1920, "height": 1080, "fps": 30, "gop": 30, "rc": "AVBR", "bitrate": 2000000, "max-qp": 35, "min-qp": 15}]}, "decoder": {"name": "CuVideoDecoder", "codec": [{"name": "h264", "width": 1920, "height": 1080}, {"name": "h265", "width": 1920, "height": 1080}, {"name": "mjpeg", "width": 1920, "height": 1080}]}, "uplink": {"processer": [], "rtp": "RtcRtpVideoFrameDestination", "rtp-transport": "UdpFramePipeline", "rtcp-transport": "UdpFramePipeline"}, "downlink": {"processer": [], "rtp": "RtcRtpVideoFrameSource", "rtp-transport": "UdpFramePipeline", "rtcp-transport": "UdpFramePipeline"}}}