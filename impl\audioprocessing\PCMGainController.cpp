#include "PCMGainController.h"
#include <cmath>

#include "ljcore/jlog.h"
#include "json.hpp"

#include "Utils.h"
namespace panocom
{

PCMGainController::PCMGainController(const std::string &jsonParams)
    : sample_rate_(16000)
    , number_of_channels_(1)
    , gain_(0)
    , gain_min_(-12)
    , gain_max_(12)
    , use_default_gain_(false) {
    name_ = "PCMGainController";
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    if (j.contains("samplerate"))
        sample_rate_ = j["samplerate"];
    if (j.contains("channel"))
        number_of_channels_ = j["channel"];
    if (j.contains("use-default-gain")) {
        use_default_gain_ = j["use-default-gain"];
    }
    fmt_ = Frame::getPCMFormat(number_of_channels_, sample_rate_);  // 没什么用，用的是源数据格式
    for (int gain = gain_min_; gain <= gain_max_; gain++) {
        gain_table_[gain] = std::pow(10.0f, gain / 20.0f);
    }
}

PCMGainController::~PCMGainController() {

}

void PCMGainController::onFrame(const std::shared_ptr<Frame> &frame) {
    if (muted_) {
        int16_t *in_data = (int16_t *)frame->getFrameBuffer();
        auto out_frame = std::make_shared<Frame>(frame->getFrameFormat());
        auto in_len = frame->getFrameBufferSize() / sizeof(int16_t);
        out_frame->createFrameBuffer(frame->getFrameBufferSize());      
        memset(out_frame->getFrameBuffer(), 0, frame->getFrameBufferSize());
        deliverFrame(out_frame);
    } else if (((frame->getGain() != 0 && !use_default_gain_) || (use_default_gain_ && gain_ != 0)) && Frame::isAudioFrame(frame)) {
        int16_t *in_data = (int16_t *)frame->getFrameBuffer();
        auto out_frame = std::make_shared<Frame>(frame->getFrameFormat());
        auto in_len = frame->getFrameBufferSize() / sizeof(int16_t);
        // out_frame->createFrameBuffer(samples_per_channel_ * number_of_channels_ * 2);
        out_frame->createFrameBuffer(frame->getFrameBufferSize());
        int16_t *out_data = (int16_t*)out_frame->getFrameBuffer();
        // int gain = frame->getGain();
        // AdjustGain(out_data, in_len, in_data, in_len, (float)use_default_gain_ ? gain_ : frame->getGain());
        int gain = (use_default_gain_ ? gain_ : frame->getGain());
        adjust_gain(out_data, in_len, in_data, in_len, gain);
        deliverFrame(out_frame);
    } else {
        deliverFrame(frame);
    }
}

void PCMGainController::AdjustGain(int16_t* out_data, size_t out_len, int16_t* in_data, size_t in_len, int db) {
    db = std::max(-12, std::min(12, db));
    float gain = gain_table_[db];
    size_t size = std::min(out_len, in_len);
    for (size_t i = 0; i < size; i++) {
        int32_t sample = static_cast<int32_t>(in_data[i]) * gain;
        out_data[i] = static_cast<int16_t>(std::max(-32768, std::min(32767, sample)));
    }    
}

int PCMGainController::updateParam(const std::string& jsonParams) {
    jinfo("PCMGainController::updateParam %s",jsonParams.c_str());
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    if (j.contains("gain")) gain_ = j["gain"];
    if (j.contains("muted")) muted_ = j["muted"];
    return 0;
}
} // namespace panocom
