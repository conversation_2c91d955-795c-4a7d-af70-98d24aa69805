#include "VideoFrameProcesser.h"
#ifdef USE_CUDA
#include "cuda/CuFrameScale.h"
#endif
#ifdef RK_PLATFORM
#include "rk/RKVideoFrameScaler.h"
#endif
#include <algorithm>

using namespace panocom;

bool VideoFrameProcesser::isRegistered = false;

std::vector<std::string> VideoFrameProcesser::processers_;

void VideoFrameProcesser::RegistProcessers()
{
    if (!isRegistered)
    {
#ifdef USE_CUDA
        processers_.push_back("CuFrameScale");
#endif
#ifdef RK_PLATFORM
        processers_.push_back("RKVideoFrameScaler");
#endif
        isRegistered = true;
    }
}

std::vector<std::string>& VideoFrameProcesser::GetSupportProcessers()
{
    RegistProcessers();
    return processers_;
}

bool VideoFrameProcesser::IsSupportProcesser(const std::string &processerName)
{
    RegistProcessers();
    return std::find(processers_.begin(), processers_.end(), processerName) != processers_.end();
}

std::shared_ptr<VideoFrameProcesser> VideoFrameProcesser::CreateVideoProcesser(const std::string &processerName, const std::string &jsonParams)
{
    std::shared_ptr<VideoFrameProcesser> ret;
#ifdef USE_CUDA
    if (processerName == "CuFrameScale")
    {
        ret = std::make_shared<CuFrameScale>(jsonParams);
    }
#endif
#ifdef RK_PLATFORM
    if (processerName == "RKVideoFrameScaler")
    {
        ret = std::make_shared<RKVideoFrameScaler>(jsonParams);
    }
#endif
    return ret;
}