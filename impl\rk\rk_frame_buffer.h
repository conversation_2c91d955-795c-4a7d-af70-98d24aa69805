#ifndef RK_RKFRAMEBUFFER_H_
#define RK_RKFRAMEBUFFER_H_
#include <stdint.h>
#include <chrono>
#include "Frame.h"
#include "rockchip/rk_type.h"
#define MPP_ALIGN(x, a)         (((x)+(a)-1)&~((a)-1))
namespace panocom {

class RKVideoFrameBuffer{
public:
	explicit RKVideoFrameBuffer(MppFrame frame);
	virtual ~RKVideoFrameBuffer();
	static std::shared_ptr<RKVideoFrameBuffer> ConvertFrom(const std::shared_ptr<Frame> &f);
	bool CopyFrom(std::shared_ptr<RKVideoFrameBuffer> input, int32_t x, int32_t y, int32_t width, int32_t height);
	int8_t* RawData() const { return data_; }
	uint32_t size() const { return size_; }
	uint32_t width() const { return width_; }
	uint32_t height() const { return height_; }
	uint32_t hor_stride() const { return hor_stride_; }
	uint32_t ver_stride() const { return ver_stride_; }
	MppFrame InnerFrame() { return frame_; }
	void setGroupId(int groupId) { groupId_ = groupId; }
	int getGroupId() { return groupId_; }
	int getFD();
private:
	MppFrame frame_;
	int8_t* data_;
	uint32_t size_;
	uint32_t width_;
	uint32_t height_;
	uint32_t hor_stride_;
	uint32_t ver_stride_;
	int groupId_;   //用于渲染、合屏位置选择
};

class RKBufferManager {

public:
    RKBufferManager(uint32_t maxFrames);
	RKBufferManager(uint32_t maxFrames, int width, int height);
    ~RKBufferManager();

    std::shared_ptr<RKVideoFrameBuffer> getFreeBuffer(uint32_t width, uint32_t height);
	MppFrame getFreeFrame(uint32_t width, uint32_t height);
	int getFreeFrame(uint32_t width, uint32_t height, MppFrame *frame_ptr);
	static int PrepareFrameWithFd(const int& fd, const int& size, const uint32_t& width, const uint32_t& height, const std::string &format_str, MppFrame &frame);
private:
    MppBufferGroup m_buf_grp;
    uint32_t m_width;
    uint32_t m_height;
    uint32_t m_maxFrames;
};

class WrapRKMppFrame : public Frame {
public:
	WrapRKMppFrame();
	// TODO: 直接使用frame_进行初始化和mpp_frame操作
	explicit WrapRKMppFrame(MppFrame frame);
	virtual ~WrapRKMppFrame();
	MppFrame InnerFrame() const { return frame_; }
	void setFormatStr(const std::string &str) { format_str_ = str; }
	std::string getFormatStr() const { return format_str_; }
public:
	MppFrame frame_;
	std::string format_str_;

};


struct CachedFrame
{
	std::shared_ptr<Frame> frame;
	std::chrono::steady_clock::time_point timestamp;
};
}
#endif