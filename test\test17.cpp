#include "MediaManagerInterface.h"
#include <hv/EventLoop.h>
#include <ljcore/jlog.h>

static std::string sdpTemplate = R"(v=0
o=- 0 0 IN IP4 ************
s=
t=0 0
m=audio 10000 RTP/AVPF 8 111
c=IN IP4 ************
a=rtpmap:8 PCMA/8000
a=rtpmap:111 opus/48000
a=ptime:20
a=sendrecv
m=video 10002 RTP/AVPF 96 98
c=IN IP4 ************
a=rtpmap:96 H264/90000
a=rtpmap:98 H265/90000
a=sendrecv
)";

using namespace panocom;

int main(int argc, char *argv[])
{
    auto loop = std::make_shared<hv::EventLoop>();

    jlog_init(nullptr);
    MediaManagerInterface mmi;
    mmi.CreateSession("0");
    std::string sdp;
    int ret = mmi.GetLocalSDP("0", sdp, "{\"video\": true}");
    loop->setTimeout(3000, [&mmi](hv::TimerID id) {
        std::string sdp;
        int ret = mmi.SetRemoteSDP("0", sdpTemplate, sdp);
        jinfo("SetRemoteSDP done %d", ret);
    });
    loop->run();
    return 0;
}