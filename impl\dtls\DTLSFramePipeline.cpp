#include "DTLSFramePipeline.h"
#ifdef USE_DTLS
#include <DTLSTool/dtls.h>
#include <ljcore/jlog.h>
#include <json.hpp>
#include <Util/base64.h>

using namespace panocom;

bool DTLSFramePipeline::dtlsInited_ = false;
std::mutex DTLSFramePipeline::initMutex_;

void DTLSFramePipeline::initDTLSEnv()
{
    std::unique_lock<std::mutex> locker(initMutex_);
    if (!dtlsInited_)
    {
        dtlsInited_ = true;
        InitDTLSEnv();
    }
}

int DTLSFramePipeline::send(char* buf, int size, void* param)
{
    DTLSFramePipeline* p = (DTLSFramePipeline*)param;
    return p->send(buf, size);
}

void DTLSFramePipeline::notify(int result, char* err, void* param)
{
    DTLSFramePipeline* p = (DTLSFramePipeline*)param;
    return p->notify(result, err);
}

DTLSFramePipeline::DTLSFramePipeline(const std::string& jsonParams)
{
    initDTLSEnv();
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    if (j.contains("isServer"))
    {
        isServer_ = j["isServer"];
    }
    if (j.contains("isRtcp"))
    {
        isRtcp_ = j["isRtcp"];
    }
}

DTLSFramePipeline::~DTLSFramePipeline()
{
    stop();
}

int DTLSFramePipeline::updateParam(const std::string& jsonParams)
{
    if (!dtlsCtx_)
    {
        return -1;
    }
    int ret = 0;
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    if (j.contains("fingerprint"))
    {
        std::string fingerprint = j["fingerprint"];
        ret = SetRemoteFingerPrint(dtlsCtx_, (char*)fingerprint.data(), fingerprint.size());
        jinfo("SetRemoteFingerPrint %s ret %d", fingerprint.c_str(), ret);
    }
    return ret;
}
    
void DTLSFramePipeline::onFrame(const std::shared_ptr<Frame>& frame)
{
    auto f = frame;
    uint8_t *buffer = f->getFrameBuffer();
    int length = f->getFrameSize();
    if(frame->getFrameFormat() == FRAME_FORMAT_DATA)
    {
        // from socket
        if (active_)
        {
            if (isRtcp_)
            {
                f->setFrameFormat(FRAME_FORMAT_RTCP);
                srtp_err_status_t result = srtp_unprotect_rtcp(receive_session_, (void*)buffer, &length);
                if (result != srtp_err_status_ok)
                {
                    jerror("srtp_unprotect_rtcp fail");
                }
            }
            else
            {
                f->setFrameFormat(FRAME_FORMAT_RTP);
                srtp_err_status_t result = srtp_unprotect(receive_session_, (void*)buffer, &length);
                if (result != srtp_err_status_ok)
                {
                    jerror("srtp_unprotect fail");
                }
            }
            deliverFrame(f);
        }
        else
        {
            if (dtlsCtx_)
            {
                OnRecvDTLS(dtlsCtx_, frame->getFrameBuffer(), frame->getFrameBufferSize());
            }
        }
    }
    else
    {
        // from business
        if (active_)
        {
            f->setFrameFormat(FRAME_FORMAT_DATA);
            if (isRtcp_)
            {
                srtp_err_status_t result = srtp_protect_rtcp(send_session_, (void *)buffer, &length);
                if (result != srtp_err_status_ok)
                {
                    jerror("srtp_protect_rtcp fail");
                }
            }
            else
            {
                srtp_err_status_t result = srtp_protect(send_session_, (void *)buffer, &length);
                if (result != srtp_err_status_ok)
                {
                    jerror("srtp_protect fail");
                }
            }
            deliverFrame(f);
        }
        else
        {
            jerror("DTLSFramePipeline::onFrame not active");
        }
    }
}

void DTLSFramePipeline::start()
{
    if (started_.exchange(true))
    {
        return;
    }
    dtlsCtx_ = InitDTLS();
    char buf[1024];
    int len = GetMyFingerPrint(dtlsCtx_, buf, 1024);
    if (len > 0)
    {
        if (notifycallbacks_.find("fingerprint") != notifycallbacks_.end() && notifycallbacks_["fingerprint"].cb)
        {
            notifycallbacks_["fingerprint"].cb("fingerprint", std::string(buf, len), notifycallbacks_["fingerprint"].param);
        }
    }
}

void DTLSFramePipeline::stop()
{
    if (dtlsCtx_)
    {
        FreeDTLS(dtlsCtx_);
        dtlsCtx_ = nullptr;
    }
}

int DTLSFramePipeline::send(char* buf, int size)
{
    auto f = Frame::CreateFrame(FRAME_FORMAT_DATA);
    if (f)
    {
        f->createFrameBuffer(size);
        memcpy(f->getFrameBuffer(), buf, size);
        deliverFrame(f);
    }
}

void DTLSFramePipeline::notify(int result, char* err)
{
    if (result == 0)
    {
        std::string clientKey;
        std::string serverKey;
        char buf[100];
        int len = 0;

        memset(buf, 0, 100);
        len = GetClientKey(dtlsCtx_, buf, 100);
        clientKey = std::string(buf, len);
        memset(buf, 0, 100);
        len = GetServerKey(dtlsCtx_, buf, 100);
        serverKey = std::string(buf, len);
        if (isServer_) 
        {  
            clientKey.swap(serverKey);
        }
        if (configureSrtpSession(&send_session_, clientKey, SENDING) &&
            configureSrtpSession(&receive_session_, serverKey, RECEIVING))
        {
        }
        active_ = true;
    }
}

bool DTLSFramePipeline::configureSrtpSession(srtp_t *session, const std::string &key, enum TransmissionType type)
{
    srtp_policy_t policy;
    memset(&policy, 0, sizeof(policy));
    srtp_crypto_policy_set_aes_cm_128_hmac_sha1_80(&policy.rtp);
    srtp_crypto_policy_set_aes_cm_128_hmac_sha1_80(&policy.rtcp);
    if (type == SENDING)
    {
        policy.ssrc.type = ssrc_any_outbound;
    }
    else
    {
        policy.ssrc.type = ssrc_any_inbound;
    }

    policy.ssrc.value = 0;
    policy.window_size = 1024;
    policy.allow_repeat_tx = 1;
    policy.next = NULL;

    std::string akey = decodeBase64(key);
    policy.key = (uint8_t *)akey.data();
    int res = srtp_create(session, &policy);
    return res != 0? false:true;
}
#endif