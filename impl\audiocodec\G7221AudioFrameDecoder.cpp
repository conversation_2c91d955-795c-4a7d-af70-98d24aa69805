#include "G7221AudioFrameDecoder.h"
#include <json.hpp>

namespace panocom {
G7221AudioFrameDecoder::G7221AudioFrameDecoder(const std::string &jsonParams)
    : decode_state_(nullptr)
    , fmt_(FRAME_FORMAT_G7221_16000_32000_1)
    , out_fmt_(FRAME_FORMAT_PCM_16000_1)
    , bit_rate_(32000)
    , samplerate_(16000) {
    name_ = "G7221AudioFrameDecoder";
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    if (j.contains("bitrate"))
        bit_rate_ = j["bitrate"];
    if (j.contains("samplerate"))
        samplerate_ = j["samplerate"];
    out_fmt_ = Frame::getPCMFormat(1, samplerate_);
    jinfo("G7221AudioFrameDecoder %d %d", bit_rate_, samplerate_);
    decoded_buf_ = new int16_t[1280];
    decode_state_ = g722_1_decode_init(decode_state_, bit_rate_, samplerate_);
    if (decode_state_) {
        running_ = true;
        decode_future_ = std::async([this]() -> bool {
            while (running_) {
                std::unique_lock<std::mutex> lock(mutex_);
                queue_available_.wait(lock, [this] { return !frame_queue_.empty() || !running_; });
                if (!running_) {
                    break;
                }
                auto frame = std::move(frame_queue_.front());
                frame_queue_.pop();
                lock.unlock();
                if (decode_state_) {
                    size_t ret = g722_1_decode(decode_state_, decoded_buf_, frame->getFrameBuffer(), frame->getFrameBufferSize());
                    if (ret > 0) {
                        // if (!ofs_.is_open()) ofs_.open("decoded.pcm", std::ios::binary | std::ios::out);
                        // ofs_.write((char*)decoded_buf_, ret * 2);
                        std::shared_ptr<Frame> out_frame = Frame::CreateFrame(out_fmt_);
                        out_frame->createFrameBuffer(ret * 2);
                        memcpy(out_frame->getFrameBuffer(), (char *)decoded_buf_, ret * 2);
                        out_frame->setGain(frame->getGain());
                        deliverFrame(out_frame);
                    } else
                        jerror("g7221 decode failed");
                }
            }
            return true;
        });
    }
}
G7221AudioFrameDecoder::~G7221AudioFrameDecoder() {
    running_ = false;
    queue_available_.notify_all();
	if (decode_future_.valid()) {
        auto status = decode_future_.wait_for(std::chrono::milliseconds(500));
		if (status == std::future_status::timeout) {
			 jerror("g7221 decoder stop timeout");
		} else {
			bool res = decode_future_.get();
            jinfo("g7221 decoder stop status: %d", res);
		}
	}    
    if (decode_state_) {
        g722_1_decode_release(decode_state_);
        decode_state_ = nullptr;
    }
    if (decoded_buf_) delete[] decoded_buf_;
    decoded_buf_ = nullptr;
    // if (ofs_.is_open()) ofs_.close();
}
void G7221AudioFrameDecoder::onFrame(const std::shared_ptr<Frame> &frame) {
    if (frame->getFrameSize() == 0 || !decode_state_ || !running_)
        return;
    {
        std::lock_guard<std::mutex> lock(mutex_);
        if (frame_queue_.size() > 20) {
            frame_queue_.pop();
        }
        frame_queue_.emplace(frame);
    }
    queue_available_.notify_one();
}
} // namespace panocom
