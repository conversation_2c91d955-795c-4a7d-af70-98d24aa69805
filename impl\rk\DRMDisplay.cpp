#include "DRMDisplay.h"

#include <sys/mman.h>

#include <json.hpp>
#include <hv/hlog.h>
#include <drm/drm_fourcc.h>
#include <rockchip/rk_type.h>
#include <rockchip/rk_mpi.h>
#include <rockchip/mpp_log.h>
// #include <osal/mpp_mem.h>
// #include <osal/mpp_env.h>
// #include <osal/mpp_time.h>
// #include <osal/mpp_common.h>

#include "Frame.h"
#include "rk/rk_frame_buffer.h"
namespace panocom {
int32_t DRMDisplay::modeset_create_fb2(int fd, struct buffer_object *bo, uint32_t color) {
	int32_t ret = -1;
    drm_mode_create_dumb create {};
    drm_mode_map_dumb map {};
	uint32_t handles[4] = {0}, pitches[4] = {0}, offsets[4] = {0};
    // create a dumb-buffer, the pixel format is XRGB888
    create.width = bo->width;
    create.height = bo->height;
    create.bpp = 32;
    ret = drmIoctl(fd, DRM_IOCTL_MODE_CREATE_DUMB, &create);
    if (ret != 0) {
        LOGE("drmIoctl failed, ret = %d", ret);
        return ret;
    }

    // bind the dumb-buffer to an FB object
    bo->pitch = create.pitch;
    bo->size = create.size;
    bo->handle = create.handle;
    // ret = drmModeAddFB(fd, bo->width, bo->height, 24, 32, bo->pitch, bo->handle, &bo->fb_id);
    // if (ret != 0) {
    //     LOGE("drmModeAddFB failed, ret = %d", ret);
    //     return ret;
    // }
	handles[0] = bo->handle;
	pitches[0] = bo->pitch;
	ret = drmModeAddFB2(fd, bo->width, bo->height, DRM_FORMAT_XRGB8888, handles,
						pitches, offsets, &bo->fb_id, 0);
    if (ret != 0) {
        LOGE("drmModeAddFB2 failed, ret = %d", ret);
        return ret;
    }
    // map the dumb-buffer to userspace
    map.handle = create.handle;
    ret = drmIoctl(fd, DRM_IOCTL_MODE_MAP_DUMB, &map);
    if (ret != 0) {
        LOGE("drmIoctl failed, ret = %d", ret);
        return ret;
    }
    bo->vaddr = (uint32_t *)mmap(0, create.size, PROT_READ | PROT_WRITE, MAP_SHARED, fd, map.offset);
    // initialize the dumb-buffer with white-color
    for (int i = 0; i < (bo->size / 4); ++i)
		bo->vaddr[i] = color;
    return 0;
}

DRMDisplay::DRMDisplay(int fd, const std::string &jsonParams)
    : fd_(fd)
	, inited_(false)
	, crtc_index_(-1)
	, crtc_id_(0)
	, con_id_(0)
	, encoder_id_(0)
	, plane_id_(0)
	, format_(DRM_FORMAT_NV12)
	, width_(1920)
	, height_(1080)
	, running_(false) {
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
	if (j.contains("crtc_id")) crtc_id_ = j["crtc_id"];
	if (j.contains("encoder_id")) encoder_id_ = j["encoder_id"];
	if (j.contains("crtc_top")) crtc_rect_.top = j["crtc_top"];
	if (j.contains("crtc_left")) crtc_rect_.left = j["crtc_left"];
	if (j.contains("crtc_width")) crtc_rect_.width = j["crtc_width"];
	if (j.contains("crtc_height")) crtc_rect_.height = j["crtc_height"];
}

DRMDisplay::~DRMDisplay() {
	running_ = false;
	auto status = display_future_.wait_for(std::chrono::milliseconds(200));
	if (status == std::future_status::timeout) {

	} else {
		display_future_.get();
	}
}

uint32_t DRMDisplay::get_property_id(int fd, drmModeObjectProperties *props, const char *name) {
	drmModePropertyPtr property;
	uint32_t i, id = 0;
	
	// find property according to the name
	for (i = 0; i < props->count_props; i++) {
		property = drmModeGetProperty(fd, props->props[i]);
		if (!strcmp(property->name, name))
			id = property->prop_id;
		drmModeFreeProperty(property);
		if (id) break;
	}
	return id;
}
	// TODO: 使用property
bool DRMDisplay::InitRender() {
	drmModeRes *resource = drmModeGetResources(fd_);
	if (resource) {
		LOGD("count_connectors = %d, count_crtcs = %d\n", resource->count_connectors, resource->count_crtcs);
	} else {
		LOGE( "failed to query Drm Mode resources: %s", strerror(errno));
		return false;
	}

	int s32ret = drmSetClientCap(fd_, DRM_CLIENT_CAP_ATOMIC, 1);
	if (s32ret != 0)
		LOGE("drmSetClientCap failed, ret = %d", s32ret);
	s32ret = drmSetClientCap(fd_, DRM_CLIENT_CAP_UNIVERSAL_PLANES, 1);
	if (s32ret != 0)
		LOGE("drmSetClientCap failed, ret = %d", s32ret);

	bool ret = GetConnector(resource);
	if (!ret) {
		LOGE("failed to get connector %s", strerror(errno));
		return false;
	}
	// jc平台通过encoder_id获取crtc_id不靠谱？直接用con_id找
	// ret = GetCrtc(resource);
	// if (!ret) {
	// 	LOGE("failed to get CRTC %s", strerror(errno));
	// 	return false;
	// }
	ret = GetPlane();
	if (!ret) {
		LOGE("failed to get plane with required format %s", strerror(errno));
		return false;
	}

	display_future_ = std::async([this]() -> bool {
		while (running_)
		{
			/* code */
		}
		
		return true;
	});

	drmModeObjectProperties *props;
	drmModeAtomicReq *req;
	uint32_t blob_id;
	// uint32_t property_crtc_id;
	uint32_t property_mode_id;
	uint32_t property_active;

	// get connector properties
	props = drmModeObjectGetProperties(fd_, con_id_, DRM_MODE_OBJECT_CONNECTOR);
	property_.crtc_id = get_property_id(fd_, props, "CRTC_ID");
	drmModeFreeObjectProperties(props);
	// get crtc properties
	props = drmModeObjectGetProperties(fd_, crtc_id_, DRM_MODE_OBJECT_CRTC);
	property_active = get_property_id(fd_, props, "ACTIVE");
	property_mode_id = get_property_id(fd_, props, "MODE_ID");
	drmModeFreeObjectProperties(props);
	LOGI("property_crtc_id = %d, property_active = %d, property_mode_id = %d", property_.crtc_id, property_active, property_mode_id);
	// create blob to store current mode, and return the blob id
	drmModeCreatePropertyBlob(fd_, &mode_, sizeof(mode_), &blob_id);
	LOGI("conn->modes[0] = %s", mode_.name);
	// start modeseting
	req = drmModeAtomicAlloc();
	drmModeAtomicAddProperty(req, crtc_id_, property_active, 1);
	drmModeAtomicAddProperty(req, crtc_id_, property_mode_id, blob_id);
	drmModeAtomicAddProperty(req, con_id_, property_.crtc_id, crtc_id_);
	drmModeAtomicCommit(fd_, req, DRM_MODE_ATOMIC_ALLOW_MODESET, NULL);
	drmModeDestroyPropertyBlob(fd_, blob_id);
	drmModeAtomicFree(req);
	// get plane properties
	props = drmModeObjectGetProperties(fd_, plane_id_, DRM_MODE_OBJECT_PLANE);
	property_.crtc_id = get_property_id(fd_, props, "CRTC_ID");
	property_.fb_id = get_property_id(fd_, props, "FB_ID");
	property_.crtc_x = get_property_id(fd_, props, "CRTC_X");
	property_.crtc_y = get_property_id(fd_, props, "CRTC_Y");
	property_.crtc_w = get_property_id(fd_, props, "CRTC_W");
	property_.crtc_h = get_property_id(fd_, props, "CRTC_H");
	property_.src_x = get_property_id(fd_, props, "SRC_X");
	property_.src_y = get_property_id(fd_, props, "SRC_Y");
	property_.src_w = get_property_id(fd_, props, "SRC_W");
	property_.src_h = get_property_id(fd_, props, "SRC_H");
	drmModeFreeObjectProperties(props);

	return true;
}

bool DRMDisplay::GetCrtc(drmModeRes *res) {
	crtc_index_ = -1;
	drmModeEncoderPtr encoder = drmModeGetEncoder(fd_, encoder_id_);
	if (!encoder) {
		LOGE("drmModeGetEncoder failed: %s", strerror(errno));
		return false;
	}
    crtc_id_ = encoder->crtc_id;
    drmModeFreeEncoder(encoder);
    for (int i = 0; i < res->count_crtcs; i++) {
        if (crtc_id_ == res->crtcs[i]) {
            crtc_index_ = i;
            break;
        }
    }
	if (crtc_index_ == -1) {
		LOGE("CRTC %d not found encoder_id %d", crtc_id_, encoder_id_);
		return false;
	}
	return true;
}
bool DRMDisplay::GetConnector(drmModeRes *res) {
	if (res && res->count_connectors <= 0) {
		LOGE("No connector found");
		return false;
	}
	bool has_connector = false;
	
    for(int i = 0; i < res->count_connectors; ++i) {
        drmModeConnectorPtr connector = drmModeGetConnector(fd_, res->connectors[i]);
        if(connector && connector->connection == DRM_MODE_CONNECTED) {
            con_id_ = res->connectors[i];
            encoder_id_ = res->encoders[i];
			crtc_id_ = res->crtcs[i];
			crtc_index_ = i;
            mode_ = *connector->modes;
			has_connector = true;
			LOGI("con_id: %d; crtc_id(used): %d; encoder_id: %d, crtc_index_: %d", con_id_, crtc_id_, encoder_id_, i);
        }
		
        drmModeFreeConnector(connector);
    }
	if (!has_connector) {
		LOGE("drmModeGetConnector failed: %s", strerror(errno));
		return false;
	}
	return true;
}

bool DRMDisplay::GetPlane() {
    drmModePlaneResPtr planes = drmModeGetPlaneResources(fd_);
    if (!planes) {
        LOGE("failed to query planes: %s", strerror(errno));
        return false;
    }
    drmModePlanePtr plane = nullptr;
    for (int i = 0; i < planes->count_planes; i++) {
        if (plane) {
            drmModeFreePlane(plane);
            plane = nullptr;
        }
        plane = drmModeGetPlane(fd_, planes->planes[i]);
        if (!plane) {
            LOGE("failed to query plane %d: %s", i, strerror(errno));
            continue;
        }
		
        if (plane->crtc_id || !(plane->possible_crtcs & (1 << crtc_index_))) {
            continue;
        }
        for (uint32_t j = 0; j < plane->count_formats; j++) {
            // found a plane matching the requested format
            if (plane->formats[j] == format_) {
                plane_id_ = plane->plane_id;
                drmModeFreePlane(plane);
                drmModeFreePlaneResources(planes);
				LOGI("plane_id = %d, crtc_id(not used) = %d, i = %d", plane->plane_id, plane->crtc_id, i);
                return true;
            }
        }
    }

    if (plane)
        drmModeFreePlane(plane);
    drmModeFreePlaneResources(planes);
    return false;
}

bool DRMDisplay::SetCrtc(const FB &fb) {
    uint32_t fb_handle = fb.fb_handle;
    if (!inited_) {
        int32_t ret = drmModeSetCrtc(fd_, crtc_id_, fb_handle, 0, 0, &con_id_, 1, &mode_);
        if (ret != 0) {
            LOGE("failed to set crct via drm: %s", strerror(errno));
            return false;
        }
        inited_ = true;
    }
    return true;
}
bool DRMDisplay::SetPlane(const FB &fb) {
	uint32_t fb_handle = fb.fb_handle;
	int32_t ret = drmModeSetPlane(fd_, plane_id_, crtc_id_, 
								  fb_handle, 0, 
								  crtc_rect_.left, crtc_rect_.top, 
								  crtc_rect_.width, crtc_rect_.height, 
								  0, 0, width_ << 16, height_ << 16);
	if (ret != 0) {
		LOGE("failed to set plane via drm: %s", strerror(errno));
		return false;
	}
#if 0
	uint32_t index = fb.index;
    drmVBlank vblank;
    vblank.request.type = (drmVBlankSeqType) (DRM_VBLANK_EVENT | DRM_VBLANK_RELATIVE);
    vblank.request.sequence = 1;
    vblank.request.signal = (unsigned long) index;
    ret =  drmWaitVBlank(fd_, &vblank);
	if (ret != 0) {
    	LOGE("failed to wait vblank: %s", strerror(errno));
		return false
	}
#endif
	return true;
}

bool DRMDisplay::PageFlip(const FB &fb) {
	uint32_t fb_handle = fb.fb_handle;
	uint32_t index = fb.index;
	int32_t ret = drmModePageFlip(fd_, crtc_id_, fb_handle, 
								  DRM_MODE_PAGE_FLIP_EVENT,
								  (void*)(uint64_t)&index);
	if (ret != 0) {
		LOGE("failed on page flip: %s", strerror(errno));
		return false;
	}
	drmEventContext evctx;
    struct timeval timeout = { .tv_sec = 3, .tv_usec = 0 };
    fd_set fds;
    memset(&evctx, 0, sizeof evctx);	
    evctx.version = DRM_EVENT_CONTEXT_VERSION;
    evctx.vblank_handler = NULL;
	evctx.page_flip_handler = nullptr;
    FD_ZERO(&fds);
    FD_SET(fd_, &fds);
    select(fd_ + 1, &fds, NULL, NULL, &timeout);
    drmHandleEvent(fd_, &evctx);
	return true;
}
// TODO: 显示应该在另一线程执行
void DRMDisplay::onFrame(const std::shared_ptr<Frame> &f) {
	auto frame = RKVideoFrameBuffer::ConvertFrom(f);
	auto buffer = mpp_frame_get_buffer(frame->InnerFrame());
	int width = mpp_frame_get_width(frame->InnerFrame());
	int height = mpp_frame_get_height(frame->InnerFrame());
	int buf_fd = mpp_buffer_get_fd(buffer);
	uint32_t handle;
	int ret = drmPrimeFDToHandle(fd_, buf_fd, &handle);
	if (ret) {
		printf("failed to drmPrimeFDToHandle, ret = %d\n", ret);
	}
	uint32_t handles[4] = {0}, pitches[4] = {0}, offsets[4] = {0};
	for (int i = 0; i < 4; i++)
		handles[i] = handle;
	pitches[0] = frame->hor_stride();
	offsets[0] = 0;
	pitches[1] = pitches[0];
	offsets[1] = pitches[0] * frame->ver_stride();
	ret = drmModeAddFB2(fd_, frame->hor_stride(), frame->ver_stride(), format_, handles, pitches, offsets, &fb_id_, 0);
	if (ret) {
		LOGE("drmModeAddFB2 failed: %d (%s)", ret, strerror(errno));
		return;
	}
	FB fb;
	fb.fb_handle = fb_id_;
	auto ptr = drmModeGetPlane(fd_, plane_id_);
	printf("fb_id: %d, ptr->fb_id: %d\n", fb_id_, ptr->fb_id);
	if (fb_id_ != ptr->fb_id) {
	// 	// bool res = SetCrtc(fb);
	// 	// bool res = SetPlane(fb);
		auto req = drmModeAtomicAlloc();
		drmModeAtomicAddProperty(req, plane_id_, property_.fb_id, fb_id_);
		drmModeAtomicAddProperty(req, plane_id_, property_.crtc_id, crtc_id_);
		drmModeAtomicAddProperty(req, plane_id_, property_.crtc_x, crtc_rect_.left);
		drmModeAtomicAddProperty(req, plane_id_, property_.crtc_y, crtc_rect_.top);
		drmModeAtomicAddProperty(req, plane_id_, property_.crtc_w, crtc_rect_.width);
		drmModeAtomicAddProperty(req, plane_id_, property_.crtc_h, crtc_rect_.height);
		drmModeAtomicAddProperty(req, plane_id_, property_.src_w, width << 16);
		drmModeAtomicAddProperty(req, plane_id_, property_.src_h, height << 16);
		drmModeAtomicAddProperty(req, plane_id_, property_.src_x, 0);
		drmModeAtomicAddProperty(req, plane_id_, property_.src_y, 0);
		drmModeAtomicCommit(fd_, req, 0, NULL);
		drmModeAtomicFree(req);
	// 	// if (!res) {
	// 	// 	drmModeRmFB(fd_, fb_id_);
	// 	// 	LOGE("failed to SetPlane (%s)", strerror(errno));
	// 	// 	return;
	// 	// }
		drmModeRmFB(fd_, ptr->fb_id);
	// 	// drm_gem_close gem_close{0};
	// 	// gem_close.handle = ptr->fb_id;
	// 	// if (drmIoctl(fd_, DRM_IOCTL_GEM_CLOSE, &gem_close) < 0)
	// 	// 	LOGE("failed to close gem: %s", strerror(errno));
	}
	drmModeFreePlane(ptr);
}
}