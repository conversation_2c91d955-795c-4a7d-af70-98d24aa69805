#include "VideoFrameCapturer.h"
#ifdef USE_FFMPEG
#include "ffmpeg/FfVideoCapturer.h"
#endif
#ifdef USE_V4L2
#include "v4l2/V4L2VideoFrameCapturer.h"
#endif
#ifdef USE_CUDA
#include "cuda/CuIPCFrameCapturer.h"
#endif
#ifdef RK_PLATFORM
#include "rk/V4L2DMAVideoFrameCapturer.h"
#endif
#include <algorithm>
#include <json.hpp>

using namespace panocom;

std::vector<std::string> VideoFrameCapturer::capturers_;

bool VideoFrameCapturer::isRegistered = false;

std::unordered_map<VideoFrameCapturer::CapturerInfo, VideoFrameCapturer::CapturerRef, VideoFrameCapturer::InfoHash> VideoFrameCapturer::existed_capturers_;
// std::unordered_map<std::shared_ptr<VideoFrameCapturer>, int> VideoFrameCapturer::capturer_refs_;

void VideoFrameCapturer::RegistCapturers()
{
    if (!isRegistered)
    {
#ifdef USE_FFMPEG
        capturers_.push_back("FfVideoCapturer");
#endif
#ifdef USE_V4L2
        capturers_.push_back("V4L2VideoFrameCapturer");
#endif
#ifdef USE_CUDA
        capturers_.push_back("CuIPCFrameCapturer");
#endif
#ifdef RK_PLATFORM
        capturers_.push_back("V4L2DMAVideoFrameCapturer");
#endif
        isRegistered = true;
    }
}

std::vector<std::string> &VideoFrameCapturer::GetSupportCapturers()
{
    RegistCapturers();
    return capturers_;
}

bool VideoFrameCapturer::IsSupportCapturer(const std::string &captureName)
{
    RegistCapturers();
    return std::find(capturers_.begin(), capturers_.end(), captureName) != capturers_.end();
}

std::shared_ptr<VideoFrameCapturer> VideoFrameCapturer::CreateVideoCapturer(const std::string &captureName, const std::string &jsonParams)
{
    RegistCapturers();
    std::shared_ptr<VideoFrameCapturer> ret;
    // nlohmann::json j;
    // if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    // int dev = -1;
    // if (j.contains("dev")) dev = j["dev"];
    // int gpu_index = 0;
    // if (j.contains("gpuIndex")) gpu_index = j["gpuIndex"];
    // CapturerInfo temp { .name = captureName, .dev = dev, .gpu_index = gpu_index };
    // if (existed_capturers_.count(temp) != 0) {
    //     existed_capturers_[temp].ref++;
    //     return existed_capturers_[temp].capturer;
    // }
#ifdef USE_FFMPEG
    if (captureName == "FfVideoCapturer")
    {
        ret = std::shared_ptr<VideoFrameCapturer>(new FfVideoCapturer(jsonParams));
    }
#endif
#ifdef USE_V4L2
    if (captureName == "V4L2VideoFrameCapturer")
    {
        ret = std::shared_ptr<VideoFrameCapturer>(new V4L2VideoFrameCapturer(jsonParams));
    }
#endif
#ifdef RK_PLATFORM
    if (captureName == "V4L2DMAVideoFrameCapturer") {
        ret = std::make_shared<V4L2DMAVideoFrameCapturer>(jsonParams);
    }
#endif
#ifdef USE_CUDA
    if (captureName == "CuIPCFrameCapturer")
    {
        ret = std::shared_ptr<VideoFrameCapturer>(new CuIPCFrameCapturer(jsonParams));
    }
#endif
    // if (ret) {
    //     existed_capturers_[temp].capturer = ret;
    //     existed_capturers_[temp].ref = 1;
    // }
    return ret;
}

void VideoFrameCapturer::ReleaseVideoCapturer(const std::string &capturerName, int dev, int gpu_index) {
    CapturerInfo temp { .name = capturerName, .dev = dev, .gpu_index = gpu_index };
    if (existed_capturers_.count(temp) != 0) {
        existed_capturers_[temp].ref--;
        if (existed_capturers_[temp].ref <= 0) {
            if (existed_capturers_[temp].capturer) existed_capturers_[temp].capturer->stop();
            existed_capturers_.erase(temp);
        }
    }    
}

void VideoFrameCapturer::ReleaseVideoCapturer(const FramePipeline::Ptr& ptr) {
    for (auto &kv: existed_capturers_) {
        kv.second.ref--;
        if (kv.second.ref <= 0) {
            if (kv.second.capturer) kv.second.capturer->stop();
            existed_capturers_.erase(kv.first);
        }
        break;
    }        
}

bool VideoFrameCapturer::IsVideoFrameCapturer(const FramePipeline::Ptr& ptr) {
#ifdef USE_FFMPEG
    if (ptr->name() == "FfVideoCapturer")
    {
        return true;
    }
#endif
#ifdef USE_V4L2
    if (ptr->name() == "V4L2VideoFrameCapturer")
    {
        return true;
    }
#endif
#ifdef RK_PLATFORM
	if (ptr->name() == "V4L2DMAVideoFrameCapturer") 
    {
		return true;
	}
#endif
#ifdef USE_CUDA
    if (ptr->name() == "CuIPCFrameCapturer")
    {
        return true;
    }
#endif
    return false;
}