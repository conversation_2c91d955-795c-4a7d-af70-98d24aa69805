// created by gyj 2024-2-20
#ifndef P_FfVideoDecoder_h
#define P_FfVideoDecoder_h

#include "VideoFrameDecoder.h"
#include <queue>
#include <future>
#include <mutex>
#include <chrono>
#include <condition_variable>

extern "C" {
#include <libavutil/opt.h>
#include <libavutil/imgutils.h>
#include <libavcodec/avcodec.h>
#include <libswscale/swscale.h>
}

namespace panocom
{
    class FfVideoDecoder : public VideoFrameDecoder
    {
    public:
        FfVideoDecoder(const std::string& jsonParams);
        virtual ~FfVideoDecoder();

        void onFrame(const std::shared_ptr<Frame>& frame) override;
        void start() override;
        void stop() override;
        void cleanQueueIfNeeded();
        void decode(const std::shared_ptr<Frame>& frame);
    private:
        AVCodec *avcodec_ = nullptr;
        AVCodecContext *ctx_ = nullptr;
        SwsContext *swsCtx_ = nullptr;
        AVCodecParserContext* parser_ = nullptr;
        std::string codecName_;
        int fps_;
        int kbps_;
        int gop_;
        int width_;
        int height_;

        std::future<bool> decode_future_;
        std::atomic<bool> running_;
        std::mutex mutex_;
        std::condition_variable cond_;
        uint16_t max_queue_size_;
        std::deque<std::shared_ptr<Frame>> packet_queue_;

        //FILE* pf_ = nullptr;
    };
} // namespace panocom


#endif