//created by gyj 2024-03-21
#ifndef P_OpenGLRender_h
#define P_OpenGLRender_h

#include "video/VideoFrameRender.h"

namespace panocom
{
    class OpenGLRender: public VideoFrameRender
    {
    public:
        static std::shared_ptr<OpenGLRender> CreateOpenGLRender(const std::string& name, const std::string& jsonParam = "");
        OpenGLRender() = default;
        ~OpenGLRender() override = default;
        virtual int addFrameRect(int videoId, int areaId, int x, int y, int z, int w, int h, int cudaId, int gpuIndex) { return 0; }
        virtual int changeFrameRect(int videoId, int areaId, int x, int y, int z, int w, int h) { return 0; }
        virtual int removeFrameRect(int videoId, int areaId) { return 0; }
        virtual int maskFrameRect(int videoId, int areaId, bool mask, int r = 0, int g = 0, int b = 0) { return 0; }
        virtual int setSrcRectROI(int videoId, int areaId, int x, int y, int w, int h) { return 0; }
        virtual void setPBO(int fd, int width, int height) = 0;
        virtual bool prepareFrame() = 0;

        virtual int width() = 0;
        virtual int height() = 0;
    };
}
#endif