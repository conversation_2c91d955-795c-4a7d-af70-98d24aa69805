#DEPS(mk.deps) media-manager(): ?jthread ?jrtplib json-modern-cpp ljcore libopus librnnoise libaec libhv ptoolkit ?libPNPcm ?libnice ?glib ?libffi ?pcre2 ?dtlstool ?zlib ?libwebrtc ?libwebrtc-neteq ?libwebrtc-ulpfec ?sdl2 ?tinyalsa g729 fdk-aac 
PACKAGE_NAME = media-manager

include $(ENV_MAKE_DIR)/inc.env.mk
-include $(ENV_CFG_ROOT)/.config

#FETCH_METHOD     = git
#VERSION          = 
#SRC_DIR          = $(PACKAGE_NAME)
#SRC_NAME         = $(PACKAGE_NAME)
#SRC_URL          = *******************:guoyj/mediamanager.git
#SRC_REV          = $(VERSION)
#SRC_BRANCH       = cbuild_dtls_stun

CACHE_BUILD      = y
CACHE_DEPENDS    =
COMPILE_TOOL     = cmake
ifneq ($(CONFIG_MM_CMAKE_FLAGS), )
CMAKE_FLAGS      = $(subst ", , $(CONFIG_MM_CMAKE_FLAGS))
endif

include $(ENV_MAKE_DIR)/inc.rule.mk
