
#ifndef P_VideoFrameDecoder_h
#define P_VideoFrameDecoder_h

#include "../FramePipeline.h"

namespace panocom
{
    class VideoFrameDecoder : public FramePipeline
    {
    public:
        static void Regist();
        static std::list<std::string>& GetSupportDecoderTypes();
        static bool IsSupportDecoderType(const std::string &decoderType);
        static std::shared_ptr<VideoFrameDecoder> CreateVideoDecoder(const std::string &decoderType, const std::string& jsonParams = "");
        static bool IsVideoFrameDecoder(const FramePipeline::Ptr& ptr);

        virtual ~VideoFrameDecoder() = default;
    protected:
        static bool isRegistered;
        static std::list<std::string> decoderTypes_;
    };
}

#endif

