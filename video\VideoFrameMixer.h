#ifndef P_VideoFrameMixer_h
#define P_VideoFrameMixer_h

#include "../FramePipeline.h"

namespace panocom
{
    class VideoFrameMixer : public FramePipeline
    {
    public:
        static void RegistMixers();
        static std::vector<std::string>& GetSupportMixers();
        static bool IsSupportMixer(const std::string &mixerName);
        static std::shared_ptr<VideoFrameMixer> CreateVideoMixer(const std::string &mixerName, const std::string &jsonParams = "");

        virtual ~VideoFrameMixer() = default;
    protected:
        static bool isRegistered;
        static std::vector<std::string> mixers_;
    };
}

#endif