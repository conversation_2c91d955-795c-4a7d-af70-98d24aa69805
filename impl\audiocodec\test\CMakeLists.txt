cmake_minimum_required(VERSION 3.4.1)
project(AudioCodecTest)

#link_libraries(MediaPipeline
#				ljcore pthread rockchip_mpp hv jrtp
#				jthread yuv ZLToolKit drm rga sdptransform
#				opus g729 fdk-aac PNPcm asound)
#
#add_executable(g722_test g722_test.cpp)
#
#add_executable(g729_test g729_test.cpp)
#
#add_executable(aac_test aac_test.cpp)



link_libraries(MediaPipeline
		ljcore pthread hv_static
		avcodec swscale avutil avdevice avfilter avformat  SDL2
		jrtp
		jthread  ZLToolKit drm
		opus g729 fdk-aac PNPcm asound
		aec rnnoise ptoolkit)

add_executable(audio_codecs_test audio_codecs_test.cpp)

