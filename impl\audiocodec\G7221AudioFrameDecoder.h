#ifndef IMPL_AUDIO_G7221AUDIOFRAMEDECODER_H_
#define IMPL_AUDIO_G7221AUDIOFRAMEDECODER_H_
#include <future>
#include <mutex>
#include <queue>
#include <condition_variable>
#include "AudioFrameDecoder.h"
#include "Frame.h"
#ifdef __cplusplus
extern "C" {
#endif
#include <g722_1.h>
#ifdef __cplusplus
}
#endif
// #include <fstream>
namespace panocom
{
class G7221AudioFrameDecoder : public AudioFrameDecoder
{
public:
	G7221AudioFrameDecoder(const std::string& jsonParams);
	~G7221AudioFrameDecoder();
	void onFrame(const std::shared_ptr<Frame> &f) override;
private:
	g722_1_decode_state_t* decode_state_;
	FrameFormat fmt_;
	FrameFormat out_fmt_;
	int bit_rate_;	// bps
    int samplerate_;
	int16_t* decoded_buf_;
	// std::ofstream ofs_;

	bool running_;
    std::queue<std::shared_ptr<Frame>> frame_queue_;
    std::mutex mutex_;
	std::future<bool> decode_future_;
	std::condition_variable queue_available_;
};



} // namespace panocom

#endif