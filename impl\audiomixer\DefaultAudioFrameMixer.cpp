#include "DefaultAuidoFrameMixer.h"

#include <json.hpp>

#include "CAudioMix.h"
#include <thread>

namespace panocom {
namespace {
constexpr int kTypicalMaxNumberOfMixedStreams = 3;
constexpr int kFrameDurationInMs = 20;
}

DefaultAudioFrameMixer::DefaultAudioFrameMixer(const std::string &jsonParams)
    : receive_thread_(4)
    , sample_rate_(16000)
    , number_of_channels_(1)
    , ptime_(20) {
    name_ = "DefaultAuidoFrameMixer";
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    if (j.contains("samplerate")) sample_rate_ = j["samplerate"];
    if (j.contains("channel")) number_of_channels_ = j["channel"];
    if (j.contains("ptime")) ptime_ = j["ptime"];
    fmt_ = Frame::getPCMFormat(number_of_channels_, sample_rate_);
    samples_per_channel_ = static_cast<size_t>(sample_rate_ * ptime_ / 1000);
    max_buffer_size_ = samples_per_channel_ * number_of_channels_ * 2 * 4;
    loop_thread_.start();
    loop_thread_.loop()->setInterval(ptime_, [this](hv::TimerID id) {
        auto out_frame = std::make_shared<Frame>(fmt_);
        out_frame->createFrameBuffer(samples_per_channel_ * number_of_channels_ * 2);
        int16_t* input_data = nullptr;
        int AmountOfMixedAudioSources = 0;
        {
            std::lock_guard<std::mutex> lock(mutex_);
            input_data = new int16_t[audio_sources_.size() * samples_per_channel_ * number_of_channels_];
            for (auto &kv: audio_sources_) {
                if (kv.second.Length() >= samples_per_channel_ * number_of_channels_ * 2) {
                    memcpy(
                        (char *)input_data + AmountOfMixedAudioSources * samples_per_channel_ * number_of_channels_ * 2, kv.second.Data(),
                        samples_per_channel_ * number_of_channels_ * 2);
                    kv.second.Consume(samples_per_channel_ * number_of_channels_ * 2);
                    ++AmountOfMixedAudioSources;
                }
            }
        }
        if (!input_data) return;
        if (AmountOfMixedAudioSources == 0) {
            delete[] input_data;
            input_data = nullptr;
            return;
        }
        CAudioMix::AddAndNormalization(input_data, AmountOfMixedAudioSources, samples_per_channel_ * number_of_channels_, (int16_t*)out_frame->getFrameBuffer());
        deliverFrame(out_frame);
        if (input_data) delete[] input_data;
    });
    receive_thread_.start(true);
}

DefaultAudioFrameMixer::~DefaultAudioFrameMixer() {
    if (loop_thread_.isRunning()) loop_thread_.stop(true);
    if (receive_thread_.isRunning()) receive_thread_.stop(true);
    loop_thread_.join();
    receive_thread_.join();
    audio_sources_.clear();
    jinfo("~DefaultAudioFrameMixer success");
    // if (ofs_.is_open()) ofs_.close();
}

void DefaultAudioFrameMixer::onFrame(const std::shared_ptr<Frame> &frame) {
    if (!Frame::isAudioFrame(frame) || !loop_thread_.isRunning() || !receive_thread_.isRunning())
        return;
    // receive_thread_.loop()->runInLoop([this, frame] {
        auto *source = frame->getSource();
        int sample_rate = 0;
        int channels = 0;
        int group_id = frame->getGroupId();
        auto f = frame;
        if (Frame::getSamplerate(frame->getFrameFormat(), sample_rate, channels)) {
            if (sample_rate_ != sample_rate || number_of_channels_ != channels) {
                auto frame = Frame::CreateFrame((FrameFormat)fmt_);
                frame->createFrameBuffer(f->getFrameSize() * sample_rate_ / sample_rate * number_of_channels_ / channels);
                memset(frame->getFrameBuffer(), 0, frame->getFrameSize());
                if (!initResampler_ || sample_rate != source_samplerate_) {
                    source_samplerate_ = sample_rate;
                    initResampler_ = true;
#ifdef WEBRTC_RESAMPLE_ANOTHER
                    resampler_.InitializeIfNeeded(sample_rate, sample_rate_, 1);
#else
                    resampler_.ResetIfNeeded(sample_rate, sample_rate_, 1);
#endif
                }
#ifdef WEBRTC_RESAMPLE_ANOTHER
                resampler_.Resample(
                    (int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(),
                    frame->getFrameSize() / 2 / number_of_channels_ * channels);
#else
                size_t outlen = 0;
                resampler_.Push(
                    (int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(),
                    frame->getFrameSize() / 2 / number_of_channels_ * channels, outlen);
#endif
                // TODO:
                if (number_of_channels_ == 2 && channels == 1) {
                    uint16_t *data = (int16_t *)frame->getFrameBuffer();
                    uint32_t frame_size = frame->getFrameBufferSize() / number_of_channels_ / 2;
                    if (number_of_channels_ == 2 && channels == 1)
                        mono_to_stereo(data, frame_size);
                }
                f = frame;
            }
        }
        // if (group_id == 1) {
        //     if (!ofs_.is_open()) ofs_.open("tomix_1.pcm", std::ios::binary | std::ios::out);
        //     ofs_.write(f->getFrameBuffer(), f->getFrameBufferSize());
        // }
        std::lock_guard<std::mutex> lock(mutex_);
        if (audio_sources_.count(source) != 0) {
            audio_sources_[source].WriteBytes(f->getFrameBuffer(), f->getFrameBufferSize());
            if (audio_sources_[source].Length() > max_buffer_size_) {
                size_t size = audio_sources_[source].Length() - max_buffer_size_ / 2;
                audio_sources_[source].Consume(size);
                jwarn("drop frames %dbytes", size);
            }
        } else {
            jwarn("Source not present in mixer");
            return;
        }
    // });
}


void DefaultAudioFrameMixer::addAudioSource(const FramePipeline::Ptr &source) {
    FramePipeline::addAudioSource(source);
    addSource(source);
}

void DefaultAudioFrameMixer::removeAudioSource(const FramePipeline::Ptr &source) {
    FramePipeline::removeAudioSource(source);
    removeSource(source);
}

void DefaultAudioFrameMixer::addSource(const std::shared_ptr<FramePipeline> &source) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (audio_sources_.count(source.get()) != 0) {
        jwarn("Source %p already added to mixer", source.get());
        return;
    }
    audio_sources_[source.get()] = ByteBuffer();
    jinfo("DefaultAuidoFrameMixer addSource %p", source.get());
}
void DefaultAudioFrameMixer::removeSource(const std::shared_ptr<FramePipeline> &source) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (audio_sources_.count(source.get()) == 0) {
        jwarn("Source %p not present in mixer", source.get());
        return;
    }
    audio_sources_.erase(source.get());
    jinfo("DefaultAuidoFrameMixer removeSource %p", source.get());
}

DefaultAudioFrameMixerManager::~DefaultAudioFrameMixerManager() {
    mixers_.clear();
}

DefaultAudioFrameMixerManager &DefaultAudioFrameMixerManager::instance() {
    static DefaultAudioFrameMixerManager obj;
    return obj;
}

std::shared_ptr<DefaultAudioFrameMixer> DefaultAudioFrameMixerManager::CreateMixer(const std::string& jsonParams) {
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    if (j.contains("remote") && j["remote"]) {
        return CreateRemoteMixer(jsonParams);
    }
    return CreateLocalMixer(jsonParams);
    // int dev = 0;
    // if (j.contains("dev")) dev = j["dev"];
    // if (dev < 0) return nullptr;
    // if (mixers_.count(dev) == 0)
    //     mixers_[dev] = std::make_shared<DefaultAudioFrameMixer>(jsonParams);
    // return mixers_[dev];
}

std::shared_ptr<DefaultAudioFrameMixer> DefaultAudioFrameMixerManager::CreateLocalMixer(const std::string &jsonParams) {
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);

    if (j.contains("id")) {
        std::string id = j["id"];
        if (local_mixers_.count(id) == 0)
            local_mixers_[id] = std::make_shared<DefaultAudioFrameMixer>(jsonParams);
        return local_mixers_[id];
    }
    return nullptr;
}

std::shared_ptr<DefaultAudioFrameMixer> DefaultAudioFrameMixerManager::CreateRemoteMixer(const std::string &jsonParams) {
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    int dev = 0;
    if (j.contains("dev")) dev = j["dev"];
    if (dev < 0) return nullptr;
    if (remote_mixers_.count(dev) == 0)
        remote_mixers_[dev] = std::make_shared<DefaultAudioFrameMixer>(jsonParams);
    return remote_mixers_[dev];
}
} // namespace panocom
