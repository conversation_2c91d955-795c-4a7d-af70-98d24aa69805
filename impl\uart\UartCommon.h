#ifndef UART_UARTCOMMON_H_
#define UART_UARTCOMMON_H_
#include <stdint.h>
struct UartData
{
    /* data */
    int32_t magic;
    int16_t codec;
    int16_t gain;
    int32_t len;
};

/// 通过串口发送消息
extern bool SendUartMsg(uint8_t chnl, uint8_t* data, uint32_t dataLen);
extern uint32_t GetUartMsg(uint8_t chnl, uint8_t* data, uint32_t dataLen);
/// 清除发送缓冲
extern bool ClearSendQue(uint8_t chnl);
/// 清除接收缓冲
extern bool ClearRecvQue(uint8_t chnl);
/// 获取串口通道的状态，0:未连接，1:已连接
extern uint32_t UartChnlSta(void);

#endif