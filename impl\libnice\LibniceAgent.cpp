#include "LibniceAgent.h"
#ifdef USE_LIBNICE
#include <ljcore/jlog.h>
#include <json.hpp>
#include <sstream>

using namespace panocom;

void LibniceAgent::cb_candidate_gathering_done(NiceAgent *agent, guint stream_id, gpointer data)
{
    // sdp
    LibniceAgent* pipeline = (LibniceAgent*)data;
    NiceCandidate* c1 = nice_agent_get_default_local_candidate(agent, pipeline->stream_id_, 1);
    NiceCandidate* c2 = nice_agent_get_default_local_candidate(agent, pipeline->stream_id_, 2);
    gchar *ufrag = nullptr;
    gchar *pwd = nullptr;
    nice_agent_get_local_credentials(agent, pipeline->stream_id_, &ufrag, &pwd);
    std::string sdp1 = nice_agent_generate_local_candidate_sdp (agent, c1);
    std::string sdp2 = nice_agent_generate_local_candidate_sdp (agent, c2);
    std::stringstream ss;
    ss << "a=ice-ufrag:" << ufrag << "\r\n";
    ss << "a=ice-pwd:" << pwd << "\r\n";
    ss << sdp1 << "\r\n";
    ss << sdp2;
    std::string sdp = ss.str();
    jinfo("cb_candidate_gathering_done:\n%s", sdp.c_str());
    if (pipeline->notifycallbacks_.find("sdp") != pipeline->notifycallbacks_.end() && pipeline->notifycallbacks_["sdp"].cb)
    {
        pipeline->notifycallbacks_["sdp"].cb("sdp", sdp, pipeline->notifycallbacks_["sdp"].param);
    }
}

void LibniceAgent::cb_component_state_changed(NiceAgent *agent, guint stream_id, guint component_id, guint state, gpointer data)
{
    // 协商状态
    LibniceAgent* pipeline = (LibniceAgent*)data;
    if (pipeline->notifycallbacks_.find("ice") != pipeline->notifycallbacks_.end() && pipeline->notifycallbacks_["ice"].cb)
    {
        pipeline->notifycallbacks_["ice"].cb("ice", std::to_string(state), pipeline->notifycallbacks_["ice"].param);
    }
    if (state == NICE_COMPONENT_STATE_READY)
    {
        pipeline->ready_ = true;
    }
    else if (state == NICE_COMPONENT_STATE_FAILED)
    {
        pipeline->ready_ = false;
    }
}

void LibniceAgent::cb_nice_recv(NiceAgent *agent, guint stream_id, guint component_id, guint len, gchar *buf, gpointer data)
{
    LibniceAgent* pipeline = (LibniceAgent*)data;
    std::shared_ptr<Frame> f = Frame::CreateFrame(pipeline->fmt_);
    f->createFrameBuffer(len);
    memcpy(f->getFrameBuffer(), buf, len);
    pipeline->deliverFrame(f);
}

LibniceAgent::LibniceAgent(const std::string &jsonParams): started_{false}, closed_{false}
{
    g_type_init();
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    stun_addr_ = "127.0.0.1";
    if (j.contains("stun-server"))
    {
        stun_addr_ = j["stun-server"];
    }
    stun_port_ = 3478;
    if (j.contains("stun-port"))
    {
        stun_port_ = j["stun-port"];
    }
    controlling_ = 0;
    if (j.contains("controlling"))
    {
        controlling_ = j["controlling"];
    }
}

LibniceAgent::~LibniceAgent()
{
    stop();
}

int LibniceAgent::updateParam(const std::string& jsonParams)
{
    int ret = 0;
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    if (j.contains("sdp"))
    {
        std::string sdp = j["sdp"];
        ret = nice_agent_parse_remote_sdp (agent_, sdp.c_str());
        if (ret > 0)
        {
            jinfo("nice_agent_parse_remote_sdp %s fail", sdp.c_str());
        }
    }
    return ret;
}

void LibniceAgent::onFrame(const std::shared_ptr<Frame> &frame)
{
    if (ready_)
    {
        nice_agent_send(agent_, stream_id_, 1, frame->getFrameBufferSize(), frame->getFrameBuffer());
    }
}

void LibniceAgent::start()
{
    if (started_.exchange(true))
    {
        return;
    }
    context_ = g_main_context_new();
    loop_ = g_main_loop_new(context_, FALSE);
    thread_ = std::unique_ptr<std::thread>(new std::thread([this] {
        if (!this->closed_ && this->loop_) 
        {
            g_main_loop_run(this->loop_);
        } 
    }));

    agent_ = nice_agent_new(g_main_loop_get_context(loop_),
                           NICE_COMPATIBILITY_RFC5245);
    if (agent_ == NULL)
    {
        jerror("Failed to create agent");
    }

    if (stun_addr_ != "")
    {
        g_object_set(G_OBJECT(agent_), "stun-server", stun_addr_.c_str(), NULL);
        g_object_set(G_OBJECT(agent_), "stun-server-port", stun_port_, NULL);
    }
    g_object_set(G_OBJECT(agent_), "controlling-mode", controlling_, NULL);
    g_object_set(G_OBJECT(agent_), "ice-tcp", 0, NULL);

    // Connect to the signals
    g_signal_connect(G_OBJECT(agent_), "candidate-gathering-done",
                     G_CALLBACK(LibniceAgent::cb_candidate_gathering_done), this);
    g_signal_connect(G_OBJECT(agent_), "component-state-changed",
                     G_CALLBACK(LibniceAgent::cb_component_state_changed), this);

    // Create a new stream with one component
    stream_id_ = nice_agent_add_stream(agent_, 2);
    if (stream_id_ == 0)
    {
        jerror("Failed to add stream");
    }
    nice_agent_set_stream_name(agent_, stream_id_, "text");

    // Attach to the component to receive the data
    // Without this call, candidates cannot be gathered
    nice_agent_attach_recv(agent_, stream_id_, 1,
                           g_main_loop_get_context(loop_), cb_nice_recv, NULL);

    // Start gathering local candidates
    if (!nice_agent_gather_candidates(agent_, stream_id_))
    {
        jerror("Failed to start candidate gathering");
    }
}

void LibniceAgent::stop()
{
    if (!closed_.exchange(true))
    {
        if (context_ && loop_)
        {
            g_main_loop_quit(loop_);
            g_main_loop_unref(loop_);
            g_main_context_unref(context_);
            loop_ = NULL;
            context_ = NULL;
        }
        if (thread_ != nullptr)
        {
            thread_->join();
            thread_ = nullptr;
        }
    }
}
#endif