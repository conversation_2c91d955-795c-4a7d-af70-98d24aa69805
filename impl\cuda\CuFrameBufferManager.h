// created by gyj 2024-3-6
#ifndef P_CuFrameBufferManager_h
#define P_CuFrameBufferManager_h

#include <memory>
#include <list>
#include <mutex>

namespace panocom
{
    class Frame;
    class CuFrameBufferManager
    {
    public:
        CuFrameBufferManager(int cudaId, int gpuIndex, int maxFrame, int width, int height);
        virtual ~CuFrameBufferManager();
        
        virtual std::shared_ptr<Frame> getFrame(const std::string& jsonParams = "");

    private:
        std::list<Frame*> frames_;
        std::mutex mutex_;
        int width_;
        int height_;
    };
}

#endif