#include <stdint.h>
#include "AudioFramePacer.h"
#include "Frame.h"
#include "Utils.h"
#include <json.hpp>
#include <ljcore/jlog.h>
#include <stdio.h>

using namespace panocom;

AudioFramePacer::AudioFramePacer(const std::string& jsonParams)
{
    FN_BEGIN;
    jinfo("AudioFramePacer %s", jsonParams.c_str());
    name_ = "AudioFramePacer";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    ptime_ = 20;
    if (j.contains("ptime"))
    {
        ptime_ = j["ptime"];
    }
    start();
    FN_END;
}

AudioFramePacer::~AudioFramePacer()
{
    FN_BEGIN;
    stop();
    FN_END;
}

void AudioFramePacer::start()
{
    if (thread_.isRunning()) return;
    FN_BEGIN;
    thread_.start();
    thread_.loop()->setInterval(ptime_, [this](hv::TimerID id){
        if (bbuf_.Length() >= ptimeDataLen_ && ptimeDataLen_ != 0)
        {
            std::shared_ptr<Frame> f = Frame::CreateFrame(Frame::getPCMFormat(chn_, samplerate_));
            f->createFrameBuffer(ptimeDataLen_);
            memcpy(f->getFrameBuffer(), bbuf_.Data(), ptimeDataLen_);
            bbuf_.Consume(ptimeDataLen_);
            deliverFrame(f);
            printOutputStatus("AudioFramePacer");
        }
    });
    FN_END;
}

void AudioFramePacer::stop()
{
    if (!thread_.isRunning()) return;
    FN_BEGIN;
    std::unique_lock<std::mutex> locker(frame_mutex_);
    thread_.stop(true);
    FN_END;
}

void AudioFramePacer::onFrame(const std::shared_ptr<Frame>& frame)
{
    if (Frame::isPCM(frame->getFrameFormat()))
    {
        std::unique_lock<std::mutex> locker(frame_mutex_);
        if (!thread_.isRunning()) return;
        thread_.loop()->runInLoop([this, frame]() {
            if (samplerate_ == 0)
            {
                Frame::getSamplerate(frame->getFrameFormat(), samplerate_, chn_);
                ptimeDataLen_ = samplerate_ / 1000 * ptime_ * chn_ * sizeof(int16_t);
                jinfo("AudioFramePacer(%d %d) ptime(%d) len = %d", samplerate_, chn_, ptime_, ptimeDataLen_);
            }
            bbuf_.WriteBytes((char*)frame->getFrameBuffer(), frame->getFrameSize()); 
        });
        printInputStatus("AudioFramePacer");
    }
}