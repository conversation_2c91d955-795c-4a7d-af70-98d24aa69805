#include "rk/DRMDisplay.h"
#include "MediaFramePipeline.h"
#include <json.hpp>
#include <unistd.h>
#include <ljcore/jlog.h>

#include <future>
#include <fstream>
// #include <osal/mpp_common.h>
#include <hv/hlog.h>

#include "rk/rk_frame_buffer.h"
#include "rk/DRMDisplayManager.h"

#define WIDTH 1280
#define HEIGHT 720
#define BUFFER_SIZE 4096

using namespace panocom;

class TestFileSource : public FramePipeline
{
private:
	/* data */
public:
	TestFileSource(const std::string& jsonParams);
	~TestFileSource();
private:
	std::future<bool> future_;
	std::ifstream input_;
	bool running_;
};

TestFileSource::TestFileSource(const std::string& jsonParams)
{
	nlohmann::json j;
	if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
	if (j.contains("path")) {
		input_.open(j["path"], std::ios::in | std::ios::binary);
		running_ = true;
	}
	future_ = std::async([this]() -> bool {
		uint32_t size = BUFFER_SIZE;
		while (running_)
		{
			if (!input_.eof()) {
				auto frame = Frame::CreateFrame(FRAME_FORMAT_H264);
				frame->createFrameBuffer(BUFFER_SIZE);
				input_.read((char*)frame->getFrameBuffer(), size);
				deliverFrame(frame);
			} else {
				input_.clear();
				input_.seekg(0, std::ios::beg);
			}
			std::this_thread::sleep_for(std::chrono::milliseconds(5));
		}
		return true;
	});
}

TestFileSource::~TestFileSource()
{
	running_ = false;
	auto status = future_.wait_for(std::chrono::milliseconds(200));
	if (status == std::future_status::timeout) {
		
	} else {
		future_.get();
	}
	if (input_ && input_.is_open()) {
		input_.close();
	}
}

class TestFileDestination : public FramePipeline
{
private:
	/* data */
public:
	TestFileDestination(const std::string& jsonParams);
	~TestFileDestination();
	virtual void onFrame(const std::shared_ptr<Frame> &f) override;
private:
	std::ofstream output_;
	bool running_;
	uint32_t frame_count_;
};

TestFileDestination::TestFileDestination(const std::string& jsonParams)
{
	nlohmann::json j;
	if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
	if (j.contains("path")) {
		output_.open(j["path"], std::ios::out | std::ios::binary);
		running_ = true;
	}
	frame_count_ = 0;
}

void TestFileDestination::onFrame(const std::shared_ptr<Frame> &f) {
	frame_count_++;
	auto frame = RKVideoFrameBuffer::ConvertFrom(f);
	if (output_.is_open() && frame->RawData()) {
		int32_t width = frame->width();
		int32_t height = frame->height();
		int32_t hor_stride = frame->hor_stride();
		int32_t ver_stride = frame->ver_stride();
		char *base = (char*)frame->RawData();
		char *base_y = base, *base_uv = base + hor_stride * ver_stride;
		for (int i = 0; i < height; i++, base_y += hor_stride)
			output_.write(base_y, width);
		for (int i = 0; i < height / 2; i++, base_uv += hor_stride)
			output_.write(base_uv, width);
	}
}

TestFileDestination::~TestFileDestination()
{
	running_ = false;
	if (output_ && output_.is_open()) {
		output_.close();
	}
}


int main() {
    jlog_init(nullptr);
    nlohmann::json j;
	nlohmann::json r;

	j.clear();
    j["codec"] = "h264";
    j["width"] = WIDTH;
    j["height"] = HEIGHT;
	auto decoder = VideoFrameDecoder::CreateVideoDecoder("RKVideoDecoder", j.dump());
	decoder->setGroupId(1);
	// j.clear();
    // j["path"] = "output.yuv";
    // TestFileDestination out_file(j.dump());

	j.clear();
	// j["path"] = "input.h264";
	j["path"] = "720p_usb.h264";
	auto file_source = std::make_shared<TestFileSource>(j.dump());

	// decoder->addVideoDestination(&out_file);
	file_source->addVideoDestination(decoder);

	j.clear();

	// auto display = DRMDisplayManager::instance().CreateDisplay(j.dump());
	j["dev"] = 1;
	auto display = VideoFrameRender::CreateVideoRender("RKVideoRender", j.dump());

	// if (!display) {
	// 	LOGE("failed to create display %s", strerror(errno));
	// 	return -1;
	// }
	// if (display->InitRender()) {

	// } else {
	// 	LOGE("failed to init render %s", strerror(errno));
	// 	return -1;
	// }
	
	decoder->addVideoDestination(display);
	printf("init success!!!!!!!\n");
    while (true)
    {
        usleep(1000);
    }
	return 0;
}