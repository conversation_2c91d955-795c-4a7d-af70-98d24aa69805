#!/bin/bash
CURDIR=$(pwd)
#rm build -rf
rm build/MediaManager -rf
rm build/MediaManagerTest -rf
# rm build/MediaManagerQMLTest -rf
# rm build/MediaManagerQWidgetTest -rf
mkdir build/MediaManager -p
mkdir build/MediaManagerTest -p
# mkdir build/MediaManagerQMLTest -p
# mkdir build/MediaManagerQWidgetTest -p
cd ${CURDIR}/build/MediaManager
cmake ${CURDIR} -DCMAKE_INSTALL_PREFIX=$CURDIR -DUSE_JRTP=1 -DUSE_RTSP=1 -DENABLE_VIDEO=1 -DUSE_WEBRTC=1 -DUSE_CUDA=1 && make -j4 && make install
cd ${CURDIR}/build/MediaManagerTest
cmake ${CURDIR}/test -DCMAKE_INSTALL_PREFIX=$CURDIR -DUSE_JRTP=1 -DENABLE_VIDEO=1 -DUSE_CUDA=1 -DUSE_WEBRTC=1  && make -j4 && make install
# cd ${CURDIR}/build/MediaManagerQMLTest
# cmake ${CURDIR}/test/CuQML -DCMAKE_INSTALL_PREFIX=$CURDIR -DUSE_JRTP=1 -DENABLE_VIDEO=1 -DUSE_CUDA=1 -DUSE_WEBRTC=1  && make -j4 && make install
# cd ${CURDIR}/build/MediaManagerQWidgetTest
# cmake ${CURDIR}/test/CuWidget -DCMAKE_INSTALL_PREFIX=$CURDIR -DUSE_JRTP=1 -DENABLE_VIDEO=1 -DUSE_CUDA=1 -DUSE_WEBRTC=1  && make -j4 && make install