#ifndef P_AudioFrameProcesser_h
#define P_AudioFrameProcesser_h

#include "../FramePipeline.h"

namespace panocom
{
    class AudioFrameProcesser : public FramePipeline
    {
    public:
        static void RegistProcessers();
        static std::vector<std::string>& GetSupportProcessers();
        static bool IsSupportProcesser(const std::string &processerName);
        static std::shared_ptr<AudioFrameProcesser> CreateAudioProcesser(const std::string &processerName, const std::string &jsonParams = "");

        virtual ~AudioFrameProcesser() = default;
    protected:
        static bool isRegistered;
        static std::vector<std::string> processers_;
    };
}

#endif