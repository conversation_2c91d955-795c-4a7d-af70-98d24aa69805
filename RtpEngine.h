#ifndef P_RtpEngine_h
#define P_RtpEngine_h

#include "FramePipeline.h"

namespace panocom
{
    class RtpEngine : public FramePipeline
    {
    public:
        static void RegistRTPEngine();
        static std::vector<std::string>& GetSupportRtpEngines();
        static bool IsSupportRtpEngine(const std::string &rtpEngineName);
        static std::shared_ptr<RtpEngine> CreateRTPEngine(const std::string &rtpEngineName, const std::string& jsonParams = "");
        static bool IsRTPEngine(const FramePipeline::Ptr& ptr);
        static std::string GetFingerPrint();
        virtual std::string GetStats2() { return ""; }
        virtual ~RtpEngine() = default;
    protected:
        static bool isRegistered;
        static std::vector<std::string> rtps_;

    };
}

#endif