#ifndef IMPL_RK_ALSAAUDIOFRAMECAPTURER_H_
#define IMPL_RK_ALSAAUDIOFRAMECAPTURER_H_
#include <future>
#include <alsa/asoundlib.h>
// #include <fstream>
#include <hv/EventLoopThread.h>
#include "Frame.h"
#include "AudioFrameCapturer.h"
#include "alsa_utils.h"
namespace panocom
{
class AlsaAudioFrameCapturer : public AudioFrameCapturer
{
private:
    /* data */
public:
    AlsaAudioFrameCapturer(const std::string& jsonParams);
    ~AlsaAudioFrameCapturer();
    int updateParam(const std::string& jsonParams) override;
private:
    bool Init();
    bool Release();
private:
    int32_t card_;
    int32_t device_;
    int32_t dev_;
    uint32_t sample_rate_;
    uint32_t channel_;
    uint32_t ptime_;
    std::string alsa_recorder_;
    int fmt_ = 0;

    snd_pcm_t *pcm_handle_;
    snd_pcm_uframes_t frames_;
    std::future<bool> capture_future_;
    bool running_;

    // std::fstream ofs_;
    AlsaHWPcmDevice capture_device;

    bool use_hw_;
    bool internal_gain_control_;    // 内置音量调节，默认为false
    int gain_;

    bool init_done_;

    // 是否开启音量检测，默认关闭
    bool enable_volume_ = false;
    // 音量[0, 100]，每20ms调整一次音量
    int volume_;
    hv::EventLoopThread notifycallbacksThread_;
};

} // namespace panocom

#endif