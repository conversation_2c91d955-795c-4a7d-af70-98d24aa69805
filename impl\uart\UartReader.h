#ifndef UART_UARTREADER_H_
#define UART_UARTREADER_H_
#include "Frame.h"
#include "AudioFrameCapturer.h"
#include <hv/EventLoopThread.h>
#include <ptoolkit/bytebuffer.h>

namespace panocom
{
class UartReader : public AudioFrameCapturer
{
private:
    /* data */
public:
    UartReader(const std::string &jsonParams);
    virtual ~UartReader();
    void start() override;
    void stop() override;
    int updateParam(const std::string& jsonParam) override;
private:
    int8_t dev_;
    int32_t capture_len_;
    int16_t gain_;
    hv::EventLoopThread readThread_;
    ByteBuffer bbuf_;
};
    
} // namespace panocom

#endif