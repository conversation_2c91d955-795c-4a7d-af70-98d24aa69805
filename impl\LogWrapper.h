﻿//#pragma once
#ifndef LOGWRAPPER_H
#define LOGWRAPPER_H
#ifdef USE_MLOG
#include "stdarg.h"
#include <iostream>
#include <string>
//#include <string_view>
#ifdef WIN32
//#include <Windows.h>
#else
typedef void *HANDLE;
#define HMODULE void*
#define NULL 0
#endif

#if !defined(__WINDOWS__) && (defined(WIN32) || defined(WIN64) || defined(_MSC_VER) || defined(_WIN32))
#define __WINDOWS__
#endif

#ifndef LOGER
#define LOGER mm_logger //每个软件自己改一下这个定义名
#endif

#ifdef __WINDOWS__
#define CPP_FILE_NAME  std::string("")
#else
#define CPP_FILE_NAME  std::string(__FILE__)+std::string(" ")
#endif // __WINDOWS__
//#define LogD(strLog) LOGER.writeDebug(strLog)
//#define LogI(strLog) LOGER.writeInfo(strLog)
//#define LogN(strLog) LOGER.writeNormal(strLog)
//#define LogW(strLog) LOGER.writeWarning(strLog)
//#define LogF(strLog) LOGER.writeFatal(strLog)
#define LogD(fmt, ...) LOGER.writeDebug(_format11(fmt, ##__VA_ARGS__))
#define LogI(fmt, ...) LOGER.writeInfo(_format11(fmt, ##__VA_ARGS__))
#define LogN(fmt, ...) LOGER.writeNormal(_format11(fmt, ##__VA_ARGS__))
#define LogW(fmt, ...) LOGER.writeWarning(_format11(fmt, ##__VA_ARGS__))
#define LogF(fmt, ...) LOGER.writeFatal(_format11(fmt, ##__VA_ARGS__))
#define LogD_F(fmt, ...) LOGER.writeDebug(CPP_FILE_NAME+__FUNCTION__+std::string(" ")+_format11(fmt, ##__VA_ARGS__))
#define LogI_F(fmt, ...) LOGER.writeInfo(CPP_FILE_NAME+__FUNCTION__+std::string(" ")+_format11(fmt, ##__VA_ARGS__))
#define LogN_F(fmt, ...) LOGER.writeNormal(CPP_FILE_NAME+__FUNCTION__+std::string(" ")+_format11(fmt, ##__VA_ARGS__))
#define LogW_F(fmt, ...) LOGER.writeWarning(CPP_FILE_NAME+__FUNCTION__+std::string(" ")+_format11(fmt, ##__VA_ARGS__))
#define LogF_F(fmt, ...) LOGER.writeFatal(CPP_FILE_NAME+__FUNCTION__+std::string(" ")+_format11(fmt, ##__VA_ARGS__))

//#define __FILENAME__ (strrchr(__FILE__, '/') ? (strrchr(__FILE__, '/') + 1):__FILE__)
//linux下用
//#define log(fmt, args...)  g_pLog->writeInfo(_format11(fmt, ##args))


//C++ 11
template<class... T>
std::string _format11(const char *fmt, const T&... t)
{
    const auto len = snprintf(nullptr, 0, fmt, t...);
    std::string r("");
    r.resize(static_cast<size_t>(len) + 1);
    snprintf(&r.front(), len + 1, fmt, t...);
    r.resize(static_cast<size_t>(len));
    return r;
}

////C++11以下
//std::string _format98(const char *fmt, ...)
//{
//	va_list args;
//	va_start(args, fmt);
//	const auto len = vsnprintf(nullptr, 0, fmt, args);
//	va_end(args);
//	std::string r("");
//	r.resize(static_cast<size_t>(len) + 1);
//	va_start(args, fmt);
//	vsnprintf(&r.front(), len + 1, fmt, args);
//	va_end(args);
//	r.resize(static_cast<size_t>(len));
//	return r;
//}

namespace MediaManagerSP
{
class LogWrapper
{
public:
	LogWrapper(void);
	virtual ~LogWrapper(void);

	enum
	{
		LOG_LEVEL_FATAL = 0,
		LOG_LEVEL_ERROR,
		LOG_LEVEL_INFO,
		LOG_LEVEL_DEBUG,
	};

public:
	void Open(const char *name);
	void Close();
    bool Write(int type, const char *format, ...);
    bool Write(int type, const std::string &msg);
    //bool WriteQString(int type, const QString &strText);
    bool WriteString(int type, const std::string &strText);
	bool WriteHex(int type, const char *data, unsigned short dataLen, const char *format, ...);

	//void write(int level, const QString& msg);
	//void writeFatal(const QString& msg);
	//void writeWarning(const QString& msg);
	//void writeNormal(const QString& msg);
	//void writeDebug(const QString& msg);
	//void writeInfo(const QString& msg);

    void write(int level, const std::string& msg);
    void writeFatal(const std::string& msg);
    void writeWarning(const std::string& msg);
    void writeNormal(const std::string& msg);
    void writeDebug(const std::string& msg);
    void writeInfo(const std::string& msg);

public:
	static void Load();
	static void Free();

private:
	//HANDLE m_hLog;
	void* m_hLog;
    static const int MAX_BUF_SIZE = 1024 * 4;
	char m_buf[MAX_BUF_SIZE];
};
}

extern MediaManagerSP::LogWrapper LOGER;
#endif

#endif // LOGWRAPPER_H
