#include "ShmPcm.h"
#include <algorithm>
#include "ljcore/jlog.h"

namespace panocom
{
namespace
{
unsigned int pcm_calc_full(unsigned int wr, unsigned int rd, unsigned int max)
{
    unsigned int full = 0;
    
    if(wr >= rd)
    {
        full = wr - rd;
    }
    else
    {
        full = (max - rd) + wr;
    }
    return full;
}

const char default_dev[] {"/dev/lp_pcm_dev0"};
} // namespace

ShmPcm::ShmPcm(/* args */) : fdDev_(-1) {

}

ShmPcm& ShmPcm::inst() {
    static ShmPcm obj;
    return obj;
}

int ShmPcm::init() {
    if (fdDev_ == -1) {
        fdDev_ = open(default_dev, O_RDWR);
    } else {
        jinfo("%s has been opened.", default_dev);
        return 0;
    }
    shmem = mmap(NULL, PCM_SHMEM_SIZE, PROT_READ | PROT_WRITE, MAP_SHARED, fdDev_, 0);
    if(shmem <= 0)
    {
        printf("Get share memory failed!!!");
        return -1;
    }
    return 0;
}
int ShmPcm::deinit() {
    if (fdDev_ != -1) {
        close(fdDev_);
    }
    fdDev_ = -1;
    munmap(shmem, PCM_SHMEM_SIZE);
    return 0;
}

int ShmPcm::ReadPcm(unsigned int nChn, unsigned char* buf, unsigned int size, bool sync) {
    if (fdDev_ < 0) return -1;
    int len = 0;
    unsigned int uiReadCnt = 0;
    unsigned char ioctl_buf[2048] {0};
    if (sync) {
        int ret = ioctl(fdDev_, IOCTL_CMD_PCM_IRQ_SYNC, ioctl_buf);
        if (ret < 0) {
            jinfo("ReadPcm ioctl IOCTL_CMD_PCM_IRQ_SYNC failed ret = %d", ret);
            return 0;
        }
    }
    auto rcv_rd = (unsigned int *)&shmem->shm_head.rcv_rd[nChn];
    auto rcv_wr = (unsigned int *)&shmem->shm_head.rcv_wr[nChn];
    auto rcv_buf = shmem->rcv_buf[nChn];
    unsigned int full = pcm_calc_full(*rcv_wr, *rcv_rd, shmem->shm_head.buff_max_size) & 0xFFFFFFFE; // 确保每次取两个字节 & 0xFFFFFFFE
    if(full > 0)
    {
        len = std::min(full, size);
        // 一次循环拷贝2个数据,因为16bit
        for(unsigned int i = 0; i < len / 2; i++)
        {
            buf[i * 2 + 0] = rcv_buf[*rcv_rd];
            buf[i * 2 + 1] = rcv_buf[*rcv_rd + 1];
            *rcv_rd += 2;
            if(*rcv_rd >= shmem->shm_head.buff_max_size)
                *rcv_rd = 0;
        }
    }
    return len;
}


int ShmPcm::WritePcm(unsigned int nChn, unsigned char* pPcm, unsigned int uiPcmSize, bool sync) {
    if (fdDev_ < 0) return -1;
    int len = 0; 
    unsigned char ioctl_buf[1024] {0};
    auto snd_rd = (unsigned int *)&shmem->shm_head.snd_rd[nChn];
    auto snd_wr = (unsigned int *)&shmem->shm_head.snd_wr[nChn];
    auto snd_buf = shmem->snd_buf[nChn];
    // len = std::min(shmem->shm_head.hw_frame_len, uiPcmSize);
    len = uiPcmSize;
    unsigned char* data = pPcm;
    unsigned int pos = *snd_wr;
    while (len > 0) {
        snd_buf[pos++] = *data;
        // jinfo("nChn %d pos %d", nChn, pos);
        // memcpy(&snd_buf[*snd_wr], data, shmem->shm_head.hw_frame_len);
        // *snd_wr += shmem->shm_head.hw_frame_len;
        if(pos >= shmem->shm_head.buff_max_size)
            pos = 0;  
        len--;
        data++;
        if (sync) {
            int ret = ioctl(fdDev_, IOCTL_CMD_PCM_IRQ_SYNC, ioctl_buf);
            if (ret < 0) {
                jinfo("ioctl IOCTL_CMD_PCM_IRQ_SYNC failed ret = %d", ret);
                return -1;
            }
        }
    }
    *snd_wr = pos;
    return uiPcmSize;
}

int ShmPcm::ReadPcmRef(unsigned int nChn, unsigned char* buf, unsigned int size, bool sync) {
    if (fdDev_ < 0) return -1;
    int len = 0;
    unsigned int uiReadCnt = 0;
    unsigned char ioctl_buf[2048] {0};
    if (sync) {
        int ret = ioctl(fdDev_, IOCTL_CMD_PCM_IRQ_SYNC, ioctl_buf);
        if (ret < 0) {
            jinfo("ReadPcm ioctl IOCTL_CMD_PCM_IRQ_SYNC failed ret = %d", ret);
            return 0;
        }
    }
    auto ref_rd = (unsigned int *)&shmem->shm_head.ref_rd[nChn];
    auto rcv_wr = (unsigned int *)&shmem->shm_head.rcv_wr[nChn];
    auto ref_buf = shmem->ref_buf[nChn];
    unsigned int full = pcm_calc_full(*rcv_wr, *ref_rd, shmem->shm_head.buff_max_size) & 0xFFFFFFFE; // 确保每次取两个字节 & 0xFFFFFFFE
    if(full > 0)
    {
        len = std::min(full, size);
        // 一次循环拷贝2个数据,因为16bit
        for(unsigned int i = 0; i < len / 2; i++)
        {
            buf[i * 2 + 0] = ref_buf[*ref_rd];
            buf[i * 2 + 1] = ref_buf[*ref_rd + 1];
            *ref_rd += 2;
            if(*ref_rd >= shmem->shm_head.buff_max_size)
                *ref_rd = 0;
        }
    }
    return len;
}

int ShmPcm::ClearReadBuf(unsigned int nChn) {
    if (fdDev_ < 0) return -1;
    unsigned char ioctl_buf[2048];
    auto rcv_rd = (unsigned int *)&shmem->shm_head.rcv_rd[nChn];
    auto rcv_wr = (unsigned int *)&shmem->shm_head.rcv_wr[nChn];
    *rcv_rd = *rcv_wr;
    return 0;
}

// int ShmPcm::ClearWriteBuf(unsigned int nChn) {
//     return 0;
// }

int ShmPcm::Sync() {
    if (fdDev_ < 0) return -1;
    unsigned char ioctl_buf[2048];
    ioctl_s * p_ioctl = (ioctl_s *)ioctl_buf;
    int ret = ioctl(fdDev_, IOCTL_CMD_PCM_IRQ_SYNC, ioctl_buf);
    if (ret < 0) {
        jinfo("ioctl IOCTL_CMD_PCM_IRQ_SYNC failed ret = %d", ret);
    }
    return p_ioctl->data[0];
}

int ShmPcm::GetTickFPGA() {
    if (fdDev_ < 0) return -1;
    unsigned char ioctl_buf[2048];
    ioctl_s * p_ioctl = (ioctl_s *)ioctl_buf;
    int ret = ioctl(fdDev_, IOCTL_CMD_PCM_GET_FPGA_TICK, ioctl_buf);
    if (ret < 0) {
        jinfo("ioctl IOCTL_CMD_PCM_GET_FPGA_TICK failed ret = %d", ret);
    }
    return p_ioctl->data[0];
}

unsigned int ShmPcm::Get_RecvLen(unsigned int nChn) {
    if (fdDev_ < 0) return -1;
    auto rcv_rd = (unsigned int *)&shmem->shm_head.rcv_rd[nChn];
    auto rcv_wr = (unsigned int *)&shmem->shm_head.rcv_wr[nChn];
    auto rcv_buf = shmem->rcv_buf[nChn];
    unsigned int full = pcm_calc_full(*rcv_wr, *rcv_rd, shmem->shm_head.buff_max_size) & 0xFFFFFFFE;
    return full;
}
unsigned int ShmPcm::Get_SendFreeLen(unsigned int nChn) {
    return 0;
}
} // namespace
