#include "OpusAudioFrameDecoder.h"
#include <json.hpp>
#include <ljcore/jlog.h>
#include <string>

extern int mmi_record_audio;

using namespace panocom;

OpusAudioFrameDecoder::OpusAudioFrameDecoder(const std::string& jsonParams)
{
    FN_BEGIN;
    name_ = "OpusAudioFrameDecoder";
    jinfo("OpusAudioFrameDecoder %s", jsonParams.c_str());
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    chn_ = 1;
    // if (j.contains("channel"))
    // {
    //     chn = j["channel"];
    // }
    samplerate_ = 16000;
    if (j.contains("samplerate"))
    {
        samplerate_ = j["samplerate"];
    }
    jinfo("OpusAudioFrameDecoder %d %d", chn_, samplerate_);
    fmt_ = Frame::getPCMFormat(chn_, samplerate_);
    start();
    FN_END;
}

OpusAudioFrameDecoder::~OpusAudioFrameDecoder()
{
    FN_BEGIN;
    stop();
    FN_END;
}

void OpusAudioFrameDecoder::start()
{
    if (thread_.isRunning()) return;
    FN_BEGIN;
    int err = 0;
    decoder_ = std::shared_ptr<OpusDecoder>(opus_decoder_create(samplerate_, chn_, &err), [](OpusDecoder* decoder){
        opus_decoder_destroy(decoder);
    });
    //std::string dfile = "decode" + std::to_string((long)this) + ".pcm";
    //pf_ = fopen(dfile.c_str(), "w+b");
    buffer_.resize(2048);
    thread_.start();
    FN_END;
}

void OpusAudioFrameDecoder::stop()
{
    if (!thread_.isRunning()) return;
    FN_BEGIN;
    std::unique_lock<std::mutex> locker(frame_mutex_);
    thread_.stop(true);
    decoder_.reset();
    if (pf_) {
        fclose(pf_);
        pf_ = nullptr;
    }
    FN_END;
}

void OpusAudioFrameDecoder::onFrame(const std::shared_ptr<Frame> &f)
{
    std::unique_lock<std::mutex> locker(frame_mutex_);
    if (!thread_.isRunning()) return;
    thread_.loop()->runInLoop([this, f](){
        // f->getFrameSize() == 0 相当于丢包了，启用plc
        int gain = f->getGain();
        int ret = opus_decode(decoder_.get(), f->getFrameSize() == 0 ? NULL : f->getFrameBuffer(), f->getFrameSize(), (int16_t*)buffer_.data(), buffer_.size() / sizeof(int16_t), 0);
        if (ret > 0)
        {
            //jinfo("%d %d", ret, f->getFrameSize());
            std::shared_ptr<Frame> f = Frame::CreateFrame(fmt_);
            if (f)
            {
                f->createFrameBuffer(ret * sizeof(int16_t));
                memcpy(f->getFrameBuffer(), buffer_.data(), ret * sizeof(int16_t));
                // if (!pf_)
                // {
                //     std::string fileName = name_ + std::to_string(samplerate_) + "-" + std::to_string((long)this) + ".pcm";
                //     pf_ = fopen(fileName.c_str(), "w+b");
                // }
                // fwrite(f->getFrameBuffer(), 1, f->getFrameSize(), pf_);
                f->setGain(gain);
                deliverFrame(f);
                printOutputStatus("OpusAudioFrameDecoder");
                if (mmi_record_audio)
                {
                    if (!pf_)
                    {
                        std::string fileName = name_ + std::to_string(samplerate_) + "-" + std::to_string((long)this) + ".pcm";
                        pf_ = fopen(fileName.c_str(), "w+b");
                    }
                    if (pf_)
                    {
                        fwrite(f->getFrameBuffer(), 1, f->getFrameSize(), pf_);
                    }
                }
            }
        }
    });
    printInputStatus("OpusAudioFrameDecoder");
}