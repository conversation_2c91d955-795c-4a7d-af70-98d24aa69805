#include "AudioSpecMulticasterMulti.h"
#include "Frame.h"
#include "Utils.h"
#include <json.hpp>
#include <ljcore/jlog.h>
#include "ShmPcm.h"

using namespace panocom;

extern int mmi_record_audio;
extern int mmi_print_state;

std::string AudioSpecMulticasterMulti::devName_;
int AudioSpecMulticasterMulti::pcmFd_ = -1;
int AudioSpecMulticasterMulti::maxChn_ = 4;
std::vector<int> AudioSpecMulticasterMulti::chnState_;
std::mutex AudioSpecMulticasterMulti::mutex_;

void AudioSpecMulticasterMulti::openDev(int dev)
{
    std::unique_lock<std::mutex> locker(mutex_);
    int fd = ShmPcm::inst().GetFd();
    if (fd < 0) {
        jinfo("AudioSpecMulticasterMulti init ShmPcm dev[%d]", dev);
        int ret = ShmPcm::inst().init();
        if (ret < 0) {
            jerror("%s init ShmPcm fail", __FUNCTION__, devName_.c_str());
            return;            
        } else
        {
            for (size_t i = 0; i < maxChn_; i++)
            {
                chnState_.push_back(0);
            }
        }
    }
    if (dev >= 0 && dev < chnState_.size())
    {
        chnState_[dev]++;
        jinfo("AudioSpecMulticasterMulti openDev start dev[%d] ref[%d]", dev, chnState_[dev]);
    }
}

void AudioSpecMulticasterMulti::closeDev(int dev)
{
    std::unique_lock<std::mutex> locker(mutex_);
    if (dev >= 0 && dev < chnState_.size())
    {
        if (chnState_[dev])
            chnState_[dev]--;
        jinfo("AudioSpecMulticasterMulti closeDev stop dev[%d] ref[%d]", dev, chnState_[dev]);
    }
    for (size_t i = 0; i < chnState_.size(); i++)
    {
        if (chnState_[i])
        {
            return;
        }
    }
    chnState_.clear();
    // int fd = ShmPcm::inst().GetFd();
    // if (fd >= 0)
    // {
    //     jinfo("AudioSpecMulticasterMulti closeDev closePCM");
    //     ShmPcm::inst().deinit();
    // }
}

AudioSpecMulticasterMulti::AudioSpecMulticasterMulti(const std::string &jsonParams)
    : debug_mode_(0)
    , ref_count(0)
    , capturing_(0)
    , local_gain_(0)
    , remote_gain_(0)
    , gain_min_(-12)
    , gain_max_(12) {
    FN_BEGIN;
    jinfo("AudioSpecMulticasterMulti %s", jsonParams.c_str());
    name_ = "AudioSpecMulticasterMulti";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    {
        std::unique_lock<std::mutex> locker(mutex_);
        if (devName_ == "")
        {
            devName_ = "/dev/lp_pcm_dev0";
            if (j.contains("devName"))
            {
                devName_ = j["devName"];
            }
            maxChn_ = 16;
            if (j.contains("maxChn"))
            {
                maxChn_ = j["maxChn"];
            }
        }
    }
    
    dev_ = 0;
    if (j.contains("dev"))
    {
        dev_ = j["dev"];
    }
    if (j.contains("ptime"))
    {
        ptime_ = j["ptime"];
    }
    samplerate_ = 16000;
    if (j.contains("samplerate"))
    {
        samplerate_ = j["samplerate"];
    }
    channel_ = 1;
    if (j.contains("channel")) {
        channel_ = j["channel"];
    }
    renderOnly_ = false;
    if (j.contains("RenderOnly"))
    {
        renderOnly_ = j["RenderOnly"];
    }
    if (j.contains("local-gain")) local_gain_ = j["gain"];
    if (j.contains("remote-gain")) remote_gain_ = j["gain"];
    if (j.contains("debug")) debug_mode_ = j["debug"];
    fmt_ = Frame::getPCMFormat(channel_, samplerate_);
    jinfo("create AudioSpecMulticasterMulti %s dev[%d]", devName_.c_str(), dev_);
    for (int gain = gain_min_; gain <= gain_max_; gain++) {
        gain_table_[gain] = std::pow(10.0f, gain / 20.0f);
    }
    start();
    FN_END;
}

AudioSpecMulticasterMulti::~AudioSpecMulticasterMulti()
{
    FN_BEGIN;
    stop();
    FN_END;
}

void AudioSpecMulticasterMulti::start()
{
    // ref_count++;
    // jinfo("AudioSpecMulticasterMulti::start ref %d", ref_count);
    if (readThread_.isRunning() || writeThread_.isRunning()) return;
    FN_BEGIN;
    openDev(dev_);
    write_buf_ = new int16_t[samplerate_ * 20 / 1000];
    if (!renderOnly_)
    {
        ShmPcm::inst().ClearReadBuf(dev_);
        capturing_ = 1;
        readThread_.loop()->runInLoop([this] (){
            if (!isSetReadThreadAffinity)
            {
                setThreadAffinity(7);
                isSetReadThreadAffinity = true;
            }
            const int BUF_LEN = samplerate_ / 1000 * sizeof(int16_t);
            const int MAX_SAMPLE_SIZE = samplerate_ * 20 / 1000;
            int sampleBytes = samplerate_ / 1000 * ptime_ * 2;
            int16_t inbuf[MAX_SAMPLE_SIZE] = { 0 };
            int count = 0;
            auto last = std::chrono::steady_clock::now();
            while (capturing_) {
                auto now = std::chrono::steady_clock::now();
                auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - last).count();
                if (duration >= 10000) {
                    jinfo("duration: %dms, frame captured: %dbytes", duration, count);
                    count = 0;
                    last = now;
                }
                int len = 0;
                int available = ShmPcm::inst().Get_RecvLen(dev_);
                if (available >= ptime_ * BUF_LEN) {
                    len = ShmPcm::inst().ReadPcm(dev_, (char *)(inbuf), ptime_ * BUF_LEN, false);
                
                    if (len > 0) {
                        // NOTE: 使用外部增益控制
                        // if (inner_gain_control_) {
                        //     AdjustGain((int16_t *)(inbuf), len / 2, (int16_t *)(inbuf), len / 2, local_gain_);
                        // }
                        bbuf_.WriteBytes((char *)inbuf, len);
                        count += len;
                        // if (!ofs_.is_open()) ofs_.open("capture.pcm", std::ios::out | std::ios::binary);
                        // ofs_.write((char *)inbuf, len);
                    }
                    while (bbuf_.Length() >= sampleBytes)
                    {
                        std::shared_ptr<Frame> f = Frame::CreateFrame((FrameFormat)fmt_);
                        f->createFrameBuffer(sampleBytes);
                        f->setGroupId(dev_);
                        memcpy(f->getFrameBuffer(), bbuf_.Data(), sampleBytes);
                        f->setGroupId(getGroupId());
                        f->setGain(local_gain_);
                        deliverFrame(f);
                        bbuf_.Consume(sampleBytes);
                        printOutputStatus("AudioSpecMulticasterMulti");
                    }
                } else {
                    ShmPcm::inst().Sync();
                }
            }
        });

        readThread_.start();
    }
    last_time_point_ = std::chrono::steady_clock::now();
    writeThread_.start();
    FN_END;
}

void AudioSpecMulticasterMulti::stop()
{
    // ref_count--;
    jinfo("AudioSpecMulticasterMulti [%p] dev %d stop begin", this, dev_);
    // if (ref_count > 0) return;
    if (!readThread_.isRunning() && !writeThread_.isRunning()) return;
    capturing_ = 0;
    ref_count = 0;
    readThread_.stop(true);
    writeThread_.stop(true);
    readThread_.join();
    writeThread_.join();
    closeDev(dev_);
    initResampler_ = false;
    isSetReadThreadAffinity = false;
    isSetWriteThreadAffinity = false;
    if (ofs_.is_open()) ofs_.close();
    if (write_buf_) {
        delete[] write_buf_;
        write_buf_ = nullptr;
    }
    frame_to_playout_ = 0;
    jinfo("AudioSpecMulticasterMulti [%p] dev %d stop end", this, dev_);
}

void AudioSpecMulticasterMulti::onFrame(const std::shared_ptr<Frame> &frame)
{
    int samplerate = 0;
    int chn = 0;
    if (Frame::getSamplerate(frame->getFrameFormat(), samplerate, chn))
    {
        if (!writeThread_.isRunning()) return;
        writeThread_.loop()->runInLoop([this, frame, samplerate](){
            auto f = frame;
            if (!isSetWriteThreadAffinity)
            {
                setThreadAffinity(6);
                isSetWriteThreadAffinity = true;
            }
            if (samplerate_ != samplerate)
            {
                if (!initResampler_ || source_samplerate_ != samplerate)
                {
                    source_samplerate_ = samplerate;
                    initResampler_ = true;
#ifdef WEBRTC_RESAMPLE_ANOTHER
                    resampler_.InitializeIfNeeded(samplerate, samplerate_, 1);
#else
                    resampler_.ResetIfNeeded(samplerate, samplerate_, 1);
#endif
                }
                auto frame = Frame::CreateFrame((FrameFormat)fmt_);
                frame->createFrameBuffer(f->getFrameSize() * samplerate_ / samplerate);
                memset(frame->getFrameBuffer(), 0, frame->getFrameSize());
#ifdef WEBRTC_RESAMPLE_ANOTHER
                resampler_.Resample((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2);
#else
                size_t outlen = 0;
                resampler_.Push((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2, outlen);
#endif
                f = frame;
            }
            // if (!ofs_.is_open()) {
            //     std::string filename = std::to_string(dev_) + ".pcm";
            //     ofs_.open(filename, std::ios_base::out | std::ios_base::binary);
            // }
            // ofs_.write((char *)f->getFrameBuffer(), f->getFrameSize());
            // write to shm
            frame_to_playout_ += f->getFrameSize();
            auto now_time_point = std::chrono::steady_clock::now();
            if (now_time_point - last_time_point_ > std::chrono::milliseconds(10000))
            {
                auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now_time_point - last_time_point_).count();
                jinfo("dev %d duration %d ms frame_to_playout_ %d", dev_, duration, frame_to_playout_);
                frame_to_playout_ = 0;
                last_time_point_ = now_time_point;
            }
            // NOTE: 使用外部增益控制
            if (inner_gain_control_ && write_buf_)
            {
                AdjustGain((int16_t *)write_buf_, f->getFrameSize() / 2, (int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, remote_gain_);
                // write to shm
                int len = ShmPcm::inst().WritePcm(dev_, (int8_t *)write_buf_, f->getFrameSize(), false);
            } else
            {
                int len = ShmPcm::inst().WritePcm(dev_, f->getFrameBuffer(), f->getFrameSize(), false);
            }
            printInputStatus("AudioSpecMulticasterMulti");
        });
    }
    else
    {
        jerror("AudioSpecMulticasterMulti::onFrame error format[%d]", frame->getFrameFormat());
    }
}
// TODO
int AudioSpecMulticasterMulti::updateParam(const std::string& jsonParams) {
    jinfo("AudioSpecMulticasterMulti::updateParam %s",jsonParams.c_str());
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    int dev = -1;
    if (j.contains("dev")) {
        dev = j["dev"];
    }
    if (j.contains("remote-gain")) {
        remote_gain_ = j["remote-gain"];
    }
    if (j.contains("local-gain")) {
        local_gain_ = j["local-gain"];
    }
    if (dev != -1 && dev != dev_) {
        closeDev(dev_);
        jinfo("AudioSpecMulticasterMulti updateParams %s", jsonParams.c_str());
        jinfo("UpdateParam AudioSpecMulticasterMulti %s close dev[%d] and open dev[%d]", devName_.c_str(), dev_, dev);
        openDev(dev);
        dev_ = dev;
        if (!renderOnly_) {
            ShmPcm::inst().ClearReadBuf(dev);
        }
    }
    return 0;
}

void AudioSpecMulticasterMulti::AdjustGain(int16_t* out_data, size_t out_len, int16_t* in_data, size_t in_len, int db) {
    db = std::max(gain_min_, std::min(gain_max_, db));
    float gain = gain_table_[db];
    size_t size = std::min(out_len, in_len);
    for (size_t i = 0; i < size; i++) {
        int32_t sample = static_cast<int32_t>(in_data[i]) * gain;
        out_data[i] = static_cast<int16_t>(std::max(-32768, std::min(32767, sample)));
    }    
}