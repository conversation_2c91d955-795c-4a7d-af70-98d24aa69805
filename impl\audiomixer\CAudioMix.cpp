#include "CAudioMix.h"
#include <cmath>
#include <cstdio>

namespace panocom
{
CAudioMix::CAudioMix(/* args */)
{
}

CAudioMix::~CAudioMix()
{
}


void CAudioMix::TimeSliceByPoint(std::vector<AUDIO_DATA_TYPE*> &allMixingSounds, 
                                uint16_t raw_data_cnt, AUDIO_DATA_TYPE *raw_data_buffer) {
    int size = allMixingSounds.size();
    for (int idxInEachSound = 0; idxInEachSound < raw_data_cnt; idxInEachSound += size) {
        for (int soundIdx = 0; soundIdx < size; ++soundIdx) {
            if (idxInEachSound + soundIdx < raw_data_cnt) {
                raw_data_buffer[idxInEachSound + soundIdx] = allMixingSounds[soundIdx][idxInEachSound + soundIdx];
            }
        }
    }
}

void CAudioMix::TimeSliceBySection(std::vector<AUDIO_DATA_TYPE*> &allMixingSounds, 
             uint16_t raw_data_cnt, AUDIO_DATA_TYPE *raw_data_buffer) {
    const int SECTION_LEN = allMixingSounds.size();
    int size = allMixingSounds.size();
    int idxInEachSound = 0;
    while (idxInEachSound < raw_data_cnt)
    {
        for (int soundIdx = 0; soundIdx < size; ++soundIdx) {
            for (int idxInEachSection = 0; idxInEachSection < SECTION_LEN; ++idxInEachSection) {
                if (idxInEachSound + idxInEachSection < raw_data_cnt) {
                    raw_data_buffer[idxInEachSound + idxInEachSection] = allMixingSounds[soundIdx][idxInEachSound + idxInEachSection];
                }
            }
            idxInEachSound += SECTION_LEN;
        }
    }
      
}
void CAudioMix::CombinePointsToOneWay1(std::vector<AUDIO_DATA_TYPE*> &allMixingSounds, 
             uint16_t raw_data_cnt, AUDIO_DATA_TYPE *raw_data_buffer) {
    WIDEN_TEMP_TYPE temp_mul = 1;
    WIDEN_TEMP_TYPE temp_sum = 0;
    int size = allMixingSounds.size();
    for (int i = 0; i < raw_data_cnt; ++i) {
        temp_mul = 1;
        temp_sum = 0;
        for (int idx = 0; idx < size; ++idx) {
            temp_mul *= allMixingSounds[idx][i];
            temp_sum += allMixingSounds[idx][i];
        }

        WIDEN_TEMP_TYPE mixed_temp_data = temp_sum - (temp_mul >> 0x10);
        if (mixed_temp_data > AUDIO_DATA_TYPE_MAX) mixed_temp_data = AUDIO_DATA_TYPE_MAX;
        else if (mixed_temp_data < AUDIO_DATA_TYPE_MIN) mixed_temp_data = AUDIO_DATA_TYPE_MIN;

        AUDIO_DATA_TYPE mixed_data = static_cast<AUDIO_DATA_TYPE>(mixed_temp_data);
        raw_data_buffer[i] = mixed_data;
    }  
}
void CAudioMix::CombinePointsToOneNewLC(std::vector<AUDIO_DATA_TYPE*> &allMixingSounds, 
             uint16_t raw_data_cnt, AUDIO_DATA_TYPE *raw_data_buffer) {
    int size = allMixingSounds.size();
    WIDEN_TEMP_TYPE temp_mul = 1;
    WIDEN_TEMP_TYPE temp_sum = 0;
    WIDEN_TEMP_TYPE mixed_temp_data;
    int how_many_points_Are_Pos = 0; 

    for (int i = 0; i < raw_data_cnt; i++) {
        temp_mul = 1;
        temp_sum = 0;
        how_many_points_Are_Pos = 0;

        for (int idx = 0; idx < size; ++idx) {
            temp_mul *= allMixingSounds[idx][i];
            temp_sum += allMixingSounds[idx][i];
            if (allMixingSounds[idx][i] < 0) ++how_many_points_Are_Pos;            
        }

        if (how_many_points_Are_Pos == size) {
            mixed_temp_data = temp_sum - (temp_mul / -(pow(2, 16 - 1) - 1));
        } else {
            mixed_temp_data = temp_sum - (temp_mul / (pow(2, 16 - 1) - 1));
        }

        if (mixed_temp_data > AUDIO_DATA_TYPE_MAX) mixed_temp_data = AUDIO_DATA_TYPE_MAX;
        else if (mixed_temp_data < AUDIO_DATA_TYPE_MIN) mixed_temp_data = AUDIO_DATA_TYPE_MIN;

        AUDIO_DATA_TYPE mixed_data = static_cast<AUDIO_DATA_TYPE>(mixed_temp_data);
        raw_data_buffer[i] = mixed_data;
    }
}
         
void CAudioMix::MixSoundsBySimplyAdd(std::vector<AUDIO_DATA_TYPE*> &allMixingSounds, 
             uint16_t raw_data_cnt, AUDIO_DATA_TYPE *raw_data_buffer) {
    int size = allMixingSounds.size();
    WIDEN_TEMP_TYPE temp_sum = 0;
    for (int i = 0; i < raw_data_cnt; ++i) {
        temp_sum = 0;
        for (int idx = 0; idx < size; ++idx) {
            temp_sum += allMixingSounds[idx][i];
        }
        if (temp_sum > AUDIO_DATA_TYPE_MAX) temp_sum = AUDIO_DATA_TYPE_MAX;
        else if (temp_sum < AUDIO_DATA_TYPE_MIN) temp_sum = AUDIO_DATA_TYPE_MIN;
        raw_data_buffer[i] = static_cast<AUDIO_DATA_TYPE>(temp_sum);
    }
}

void CAudioMix::MixSoundsBySimplyAdd(
    AUDIO_DATA_TYPE *allMixingSounds, uint16_t AmountOfMixedAudioSources, uint16_t raw_data_cnt, AUDIO_DATA_TYPE *raw_data_buffer) {
    WIDEN_TEMP_TYPE temp_sum = 0;
    for (int i = 0; i < raw_data_cnt; ++i) {
        temp_sum = 0;
        for (int idx = 0; idx < AmountOfMixedAudioSources; ++idx) {
            temp_sum += allMixingSounds[idx * raw_data_cnt + i];
        }
        if (temp_sum > AUDIO_DATA_TYPE_MAX) temp_sum = AUDIO_DATA_TYPE_MAX;
        else if (temp_sum < AUDIO_DATA_TYPE_MIN) temp_sum = AUDIO_DATA_TYPE_MIN;
        raw_data_buffer[i] = static_cast<AUDIO_DATA_TYPE>(temp_sum);
    }
}

void CAudioMix::MixSoundsByMean(std::vector<AUDIO_DATA_TYPE*> &allMixingSounds, 
             uint16_t raw_data_cnt, AUDIO_DATA_TYPE *raw_data_buffer) {
    int size = allMixingSounds.size();
    WIDEN_TEMP_TYPE temp_sum = 0;
    WIDEN_TEMP_TYPE mean = 0;

    for (int i = 0; i < raw_data_cnt; ++i) {
        temp_sum = 0;
        mean = 0;
        for (int idx = 0; idx < size; ++idx) {
            temp_sum += allMixingSounds[idx][i];
        }
        mean = temp_sum / size;
        if (mean > AUDIO_DATA_TYPE_MAX) mean = AUDIO_DATA_TYPE_MAX;
        else if (mean < AUDIO_DATA_TYPE_MIN) mean = AUDIO_DATA_TYPE_MIN;
        raw_data_buffer[i] = static_cast<AUDIO_DATA_TYPE>(mean);
    }            
}
void CAudioMix::AddAndNormalization(std::vector<AUDIO_DATA_TYPE*> &allMixingSounds, 
                        uint16_t raw_data_cnt, AUDIO_DATA_TYPE *raw_data_buffer) {
    int size = allMixingSounds.size();
    WIDEN_TEMP_TYPE temp_sum = 0;
    double decay_factor = 1;

    for (int i = 0; i < raw_data_cnt; ++i) {
        temp_sum = 0;
        for (int idx = 0; idx < size; ++idx) {
            temp_sum += allMixingSounds[idx][i];
        }
        temp_sum *= decay_factor;

        if (temp_sum > AUDIO_DATA_TYPE_MAX) {
            decay_factor = static_cast<double>(AUDIO_DATA_TYPE_MAX) / temp_sum;
            temp_sum = AUDIO_DATA_TYPE_MAX;
        } else if (temp_sum < AUDIO_DATA_TYPE_MIN) {
            decay_factor = static_cast<double>(AUDIO_DATA_TYPE_MIN) / temp_sum;
            temp_sum = AUDIO_DATA_TYPE_MIN;
        }

        if (decay_factor < 1) {
            decay_factor += static_cast<double>(1 - decay_factor) / 32.0;
        }

        raw_data_buffer[i] = static_cast<AUDIO_DATA_TYPE>(temp_sum);
    }                
}

void CAudioMix::AddAndNormalization(AUDIO_DATA_TYPE *allMixingSounds, uint16_t AmountOfMixedAudioSources,
                        uint16_t raw_data_cnt, AUDIO_DATA_TYPE *raw_data_buffer) {
    WIDEN_TEMP_TYPE temp_sum = 0;
    double decay_factor = 1;

    for (int i = 0; i < raw_data_cnt; ++i) {
        temp_sum = 0;
        for (int idx = 0; idx < AmountOfMixedAudioSources; ++idx) {
            temp_sum += allMixingSounds[idx * raw_data_cnt + i];
        }
        temp_sum *= decay_factor;

        if (temp_sum > AUDIO_DATA_TYPE_MAX) {
            decay_factor = static_cast<double>(AUDIO_DATA_TYPE_MAX) / temp_sum;
            temp_sum = AUDIO_DATA_TYPE_MAX;
        } else if (temp_sum < AUDIO_DATA_TYPE_MIN) {
            decay_factor = static_cast<double>(AUDIO_DATA_TYPE_MIN) / temp_sum;
            temp_sum = AUDIO_DATA_TYPE_MIN;
        }

        if (decay_factor < 1) {
            decay_factor += static_cast<double>(1 - decay_factor) / 32.0;
        }

        raw_data_buffer[i] = static_cast<AUDIO_DATA_TYPE>(temp_sum);
    }      
}

void CAudioMix::AddAndNormalization(AUDIO_DATA_TYPE *allMixingSounds, uint16_t AmountOfMixedAudioSources,
                        uint16_t raw_data_cnt, uint16_t stride, AUDIO_DATA_TYPE *raw_data_buffer) {
    WIDEN_TEMP_TYPE temp_sum = 0;
    double decay_factor = 1;

    for (int i = 0; i < raw_data_cnt; ++i) {
        temp_sum = 0;
        for (int idx = 0; idx < AmountOfMixedAudioSources; ++idx) {
            temp_sum += allMixingSounds[idx * stride + i];
        }
        temp_sum *= decay_factor;

        if (temp_sum > AUDIO_DATA_TYPE_MAX) {
            decay_factor = static_cast<double>(AUDIO_DATA_TYPE_MAX) / temp_sum;
            temp_sum = AUDIO_DATA_TYPE_MAX;
        } else if (temp_sum < AUDIO_DATA_TYPE_MIN) {
            decay_factor = static_cast<double>(AUDIO_DATA_TYPE_MIN) / temp_sum;
            temp_sum = AUDIO_DATA_TYPE_MIN;
        }

        if (decay_factor < 1) {
            decay_factor += static_cast<double>(1 - decay_factor) / 32.0;
        }

        raw_data_buffer[i] = static_cast<AUDIO_DATA_TYPE>(temp_sum);
    }      
}
} // namespace panocom
