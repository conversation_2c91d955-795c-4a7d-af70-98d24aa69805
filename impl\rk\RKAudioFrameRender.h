#ifndef IMPL_RK_RKAUDIOFRAMERENDER_H_
#define IMPL_RK_RKAUDIOFRAMERENDER_H_
#include <future>

#include <hv/EventLoopThread.h>

#include "AudioFrameRender.h"
#include "Frame.h"
#include <ptoolkit/bytebuffer.h>

namespace panocom
{
class RKAudioFrameRender : public AudioFrameRender
{
private:
    /* data */
public:
    RKAudioFrameRender(const std::string& jsonParams);
    ~RKAudioFrameRender();
    void onFrame(const std::shared_ptr<Frame>& frame) override;
private:
    uint8_t nChn_;   // 采集通道序号
    uint8_t channel_count_;   // 声道数

    bool running_;
    ByteBuffer audio_buffer_;
    std::mutex mutex_;
    
	std::shared_ptr<hv::EventLoopThread> loop_thread_;
	hv::EventLoopPtr loop_;
};



} // namespace panocom


#endif