#include "FfVideoDecoder.h"
#include "Frame.h"
#include <json.hpp>
#include <ljcore/jlog.h>
#include "FfUtils.h"
using namespace panocom;
/**
 *
 * codecName_:mjpeg 一般输出    pix_format:yuvj422p
 * codecName_:h264、h265 一般输出    pix_format:yuv420p
 *
 * @param jsonParams
 */
FfVideoDecoder::FfVideoDecoder(const std::string& jsonParams)
: running_(false)
, max_queue_size_(60)
{
	FN_BEGIN;
	name_ = "FfVideoDecoder";
	nlohmann::json j;
	if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
	fps_ = 30;
	if (j.contains("fps"))
	{
		fps_ = j["fps"];
	}
	codecName_ = "libx264";
	if (j.contains("codec"))
	{
		codecName_ = j["codec"];
	}
	kbps_ = 2000;
	if (j.contains("kbps"))
	{
		kbps_ = j["kbps"];
	}
	gop_ = 30;
	if (j.contains("gop"))
	{
		gop_ = j["gop"];
	}
	width_ = 1280;
	if (j.contains("width"))
	{
		width_ = j["width"];
	}
	height_ = 720;
	if (j.contains("height"))
	{
		height_ = j["height"];
	}
    max_queue_size_ =  gop_ * 3;
    if(max_queue_size_<60)
        max_queue_size_ = 60;

	jinfo("FfVideoDecoder %s %dx%d@%d gop=%d kbps=%d max_queue_size_:%d", codecName_.c_str(), width_, height_, fps_, gop_, kbps_,max_queue_size_);
    inputFrameCount_ =0;
    outputFrameCount_ =0;
	start();
	FN_END;
}

FfVideoDecoder::~FfVideoDecoder()
{
	FN_BEGIN;
	stop();
	FN_END;
}

void FfVideoDecoder::start()
{
    if(running_.load())
        return;
    running_.store(true);

	FN_BEGIN;
	avcodec_ = avcodec_find_decoder_by_name(codecName_.c_str());
	if (!avcodec_)
	{
		jerror("!avcodec");
		return;
	}
	parser_ = av_parser_init(avcodec_->id);
	ctx_ = avcodec_alloc_context3(avcodec_);
	if (ctx_)
	{
		/* put sample parameters */
		if (codecName_ == "libx264")
			ctx_->bit_rate = kbps_;
		else
			ctx_->bit_rate = kbps_ * 1000;
		/* resolution must be a multiple of two */
		ctx_->width = width_;
		ctx_->height = height_;
		/* frames per second */
		ctx_->time_base.num = 1;
		ctx_->time_base.den = fps_;
		ctx_->framerate.num = fps_;
		ctx_->framerate.den = 1;
		ctx_->gop_size = gop_;
		ctx_->max_b_frames = 0;
//      解码器的输出格式是有解码器自动决定，取决于编码时的图像格式和解码器支持的格式
//		ctx_->pix_fmt = AV_PIX_FMT_NV12;无须设置
        ctx_->thread_count =  6;
        ctx_->thread_type = FF_THREAD_FRAME;

		int ret = avcodec_open2(ctx_, avcodec_, NULL);
		if (ret < 0)
		{
			jerror("ret < 0");
		}
		else
		{
			jinfo("FfVideoDecoder start");
            decode_future_ = std::async([this]() -> bool {
                while (running_) {
                    std::shared_ptr<Frame> packet;
                    {
                        std::unique_lock<std::mutex> lock(mutex_);
                        if (running_ && packet_queue_.empty()) {
                            cond_.wait(lock, [this] { return !running_ || !packet_queue_.empty(); });
                        }
                        if (!running_)
                            return true;
                        packet = packet_queue_.front();
                        packet_queue_.pop_front();
                    }
                    decode(packet);
                }
                return true;
            });
		}
	}
	else
	{
		jerror("!ctx_");
	}
	FN_END;
}

void FfVideoDecoder::stop()
{
    if(!running_)
        return;
    running_ =false;
    cond_.notify_all();

    {
        std::unique_lock<std::mutex> lock(mutex_);
        packet_queue_.clear();
    }

    if (decode_future_.valid()) {
        auto status = decode_future_.wait_for(std::chrono::milliseconds(2000));
        if (std::future_status::timeout == status) {
            // TODO: 释放超时处理
            jerror("FfVideoDecoder stop timeout");
        } else if (std::future_status::ready == status) {
            bool flag = decode_future_.get();
            jerror("FfVideoDecoder stop sucess: %d", flag);
        }
    }

	if (ctx_)
	{
		avcodec_close(ctx_);
		avcodec_free_context(&ctx_);
	}
}

void FfVideoDecoder::onFrame(const std::shared_ptr<Frame>& frame) {

    if(!running_)
        return;
    inputFrameCount_++;
//    jinfo("FfVideoDecoder onFrame inputFrameCount_:%d outputFrameCount_:%d", inputFrameCount_, outputFrameCount_);
    printInputStatus("ffdecoder");

    cleanQueueIfNeeded();
    {
        std::unique_lock<std::mutex> lock(mutex_);
        packet_queue_.push_back(frame);
    }
    cond_.notify_one();
}

void FfVideoDecoder::cleanQueueIfNeeded() {
    std::unique_lock<std::mutex> lock(mutex_);
    if (packet_queue_.size() < max_queue_size_)
        return;
    auto it = packet_queue_.begin();
    while(it!= packet_queue_.end()){
        if(Frame::isKeyFrame((*it))){
            //找到第一个I帧位置
            break;
        }
        ++it;
    }
    if(it == packet_queue_.end()){
        //没有I帧，不清除
        jinfo("cleanQueueIfNeeded  no i frame,not clean。 queue size: %d , max_queue_size_: %d", packet_queue_.size(), max_queue_size_);
        return;
    }
    int size = packet_queue_.size();
    packet_queue_.erase(packet_queue_.begin(),it);
    jinfo("cleanQueueIfNeeded  queue size: %d , cleanSize: %d", packet_queue_.size(), size -packet_queue_.size());
}

void FfVideoDecoder::decode(const std::shared_ptr<Frame>& frame) {

    AVPacket* pkt = NULL;
    bool needFreePacket = true;
    if (frame->getFrameFormat() == FRAME_FORMAT_FFAVPACKET)
    {
        auto p = std::dynamic_pointer_cast<FFAVPacket>(frame);
        pkt = (AVPacket*)p->getAVPacket();
        needFreePacket = false;
    }
    else
    {
        pkt = av_packet_alloc();
        av_init_packet(pkt);
        if (parser_)
        {
            int ret = 0;
            uint8_t* buf = frame->getFrameBuffer();
            int len = frame->getFrameSize();
            //jinfo("FfVideoDecoder::onFrame %d", len);
            do {
                ret = av_parser_parse2(parser_, ctx_, &pkt->data, &pkt->size, buf, len, AV_NOPTS_VALUE, AV_NOPTS_VALUE, 0);
                if (ret > 0 && len - ret > 0)
                {
                    buf += ret;
                    len -= ret;
                }
                else
                {
                    break;
                }
            } while (1);
            if (pkt->size == 0)
            {
                av_packet_free(&pkt);
                return;
            }
        }
        else
        {
            pkt->data = frame->getFrameBuffer();
            pkt->size = frame->getFrameSize();
        }
    }

    bool pkt_done = false;
    do{
        int ret = avcodec_send_packet(ctx_, pkt);
        if (ret < 0)
        {
            jinfo("avcodec_send_packet<0 err:%s",FfUtils::ff_err2str(ret));
        }else{
            pkt_done = true;
        }

        while (ret >= 0)
        {
            AVFrame* frame = av_frame_alloc();
            if (!frame)
            {
            }
            ret = avcodec_receive_frame(ctx_, frame);
            if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF)
            {
                av_frame_free(&frame);
                break;
            }
            else if (ret < 0)
            {
                av_frame_free(&frame);
            }
            else {
                static bool logFormat = true;
                if(logFormat){
                    logFormat = false;
                    AVPixelFormat fmt = (AVPixelFormat)(frame->format);
                    jinfo("FfVideoDecoder output pix_fmt:%d %s color_range:%d AVCOL_RANGE_JPEG:%d",fmt,FfUtils::ff_get_pix_fmt_name( fmt),frame->color_range,AVCOL_RANGE_JPEG);
                }
                outputFrameCount_++;
                std::shared_ptr<Frame> f = std::make_shared<FFAVFrame>(frame);
                f->setGroupId(getGroupId());
                deliverFrame(f);
                printOutputStatus("ffdecoder");
            }
        }


        if (needFreePacket)
        {
            av_packet_free(&pkt);
        }

        if (pkt_done)
            break;
    } while (running_);
}