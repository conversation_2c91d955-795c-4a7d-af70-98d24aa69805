// created by gyj 2024-2-20
#ifndef P_FfVideoCapturer_h
#define P_FfVideoCapturer_h

#include "VideoFrameCapturer.h"
#include "FrameBufferManager.h"
#include <thread>
#include <mutex>
#include <condition_variable>
#include <memory>

extern "C" {
#include <libavutil/opt.h>
#include <libavcodec/avcodec.h>
#include <libavdevice/avdevice.h>
#include <libavformat/avformat.h>
#include <libswscale/swscale.h>
}

namespace panocom
{
    class FfVideoCapturer : public VideoFrameCapturer
    {
    public:
        FfVideoCapturer(const std::string& jsonParams);
        ~FfVideoCapturer() override;
        void start() override;
        void stop() override;
    private:
        std::unique_ptr<std::thread> thread_;
        bool running_ = false;

        std::string deviceType_;
        std::string deviceName_;
        int fmt_;
        int width_;
        int height_;
        int fps_ = 0;

        AVFormatContext* fmtCtx_ = NULL;
        AVInputFormat* fmtInput_ = NULL;
        int index_ = -1;
    };
}

#endif