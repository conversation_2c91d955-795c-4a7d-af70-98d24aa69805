#include "PCMFileFrameSource.h"
#include "Frame.h"
#include <ljcore/jlog.h>
#include <json.hpp>

using namespace panocom;


void PCMFileFrameSource::readFile(const std::string& path)
{
    FILE* pf = fopen(path.c_str(), "rb");
    if (pf)
    {
        fseek(pf, 0, SEEK_END);
        int ret = ftell(pf);
        jinfo("fopen %s %d", path.c_str(), ret);
        buffer_.resize(ret);
        fseek(pf, 0, SEEK_SET);
        fread(buffer_.data(), 1, buffer_.size(), pf);
        fclose(pf);
    } else {
        jerror("fopen %s failed", path.c_str());
    }
    index_ = 0;
}

PCMFileFrameSource::PCMFileFrameSource(const std::string& jsonParams) : FileFramePipeline(jsonParams)
{
    FN_BEGIN;
    name_ = "PCMFileFrameSource";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    path_ = "test.pcm";
    if (j.contains("path"))
    {
        path_ = j["path"];
    }
    chn_ = 1;
    if (j.contains("chn"))
    {
        chn_ = j["chn"];
    }
    samplerate_ = 16000;
    if (j.contains("samplerate"))
    {
        samplerate_ = j["samplerate"];
    }
    ptime_ = 20;
    if (j.contains("ptime"))
    {
        ptime_ = j["ptime"];
    }
    loop_ = false;
    if (j.contains("loop"))
    {
        loop_ = j["loop"];
    }
    ptimeDataLen_ = samplerate_ / 1000 * ptime_ * chn_ * sizeof(int16_t);
    fmt_ = Frame::getPCMFormat(chn_, samplerate_);
    jinfo("PCMFileFrameSource %s %d %d %d", path_.c_str(), samplerate_, chn_, fmt_);
    start();
    
    FN_END;
}

PCMFileFrameSource::~PCMFileFrameSource()
{
    FN_BEGIN;
    stop();
    FN_END;
}

void PCMFileFrameSource::start()
{
    if (thread_.isRunning()) return;
    FN_BEGIN;
    readFile(path_);
    thread_.loop()->setInterval(ptime_, [this](hv::TimerID id) {
        if (index_ + ptimeDataLen_ <= buffer_.size())
        {
            std::shared_ptr<Frame> f = Frame::CreateFrame((FrameFormat)fmt_);
            f->createFrameBuffer(ptimeDataLen_);
            memcpy(f->getFrameBuffer(), buffer_.data() + index_, ptimeDataLen_);
            f->setGroupId(getGroupId());
            deliverFrame(f);
            index_ += ptimeDataLen_;
            if (index_  + ptimeDataLen_ > buffer_.size())
            {
                if (loop_)
                {
                    index_ = 0;
                }
                else
                {
                    thread_.loop()->killTimer(id);
                }
            }
        }
        else
        {
            index_ = 0;
        }
    });

    thread_.start();
    FN_END;
}

void PCMFileFrameSource::stop()
{
    FN_BEGIN;
    thread_.stop(true);
    buffer_.clear();
    FN_END;
}