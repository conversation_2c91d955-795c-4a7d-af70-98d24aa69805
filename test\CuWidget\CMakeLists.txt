cmake_minimum_required(VERSION 3.5)

project(CuWidget LANGUAGES CXX)

set(CMAKE_INCLUDE_CURRENT_DIR ON)

set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)

set(CMAKE_CXX_STANDARD 17)

find_package(QT NAMES Qt6 Qt5 COMPONENTS Widgets REQUIRED)
find_package(Qt${QT_VERSION_MAJOR} COMPONENTS Widgets REQUIRED)

if (ENABLE_VIDEO)
  add_definitions(-DENABLE_VIDEO)
endif ()
if (USE_CUDA)
  add_definitions(-DUSE_CUDA)
endif ()
if (USE_JRTP)
  add_definitions(-DUSE_JRTP)
endif ()
if (USE_WEBRTC)
  add_definitions(-DWEBRTC_POSIX -DWEBRTC_LINUX)
endif ()

if (ENABLE_VIDEO)
    if (USE_CUDA)
        find_library(CUVID_LIB nvcuvid)
        find_library(NVENCODEAPI_LIB nvidia-encode)
        find_package(CUDA)

        find_library(FREEGLUT_LIB glut)
        find_library(GLEW32_LIB GLEW)
        find_library(X11_LIB X11)
        find_library(GL_LIB GL)
        find_library(CUDART_LIB cudart HINTS ${CUDA_TOOLKIT_ROOT_DIR}/lib64)
    endif()
endif()

include_directories(${CMAKE_CURRENT_SOURCE_DIR}
${CMAKE_CURRENT_SOURCE_DIR}/../../
${CMAKE_CURRENT_SOURCE_DIR}/../../audio
${CMAKE_CURRENT_SOURCE_DIR}/../../video
${CMAKE_CURRENT_SOURCE_DIR}/../../3rdpart/include 
${CMAKE_CURRENT_SOURCE_DIR}/../../3rdpart/include/abseil-cpp-new
${CMAKE_CURRENT_SOURCE_DIR}/../../3rdpart/include/zltoolkit 
${CUDA_INCLUDE_DIRS})
link_directories(../../3rdpart/lib)
link_directories(../../release/lib)

set(PROJECT_SOURCES
        main.cpp
        ../QCuRender.h
        ../QCuRender.cpp
        QCuVideoWidget.cpp
        widget.cpp
        widget.h
        widget.ui
)

add_executable(CuWidget
            ${PROJECT_SOURCES}
        )

if (ENABLE_VIDEO)
    target_link_libraries(${PROJECT_NAME} PRIVATE Qt${QT_VERSION_MAJOR}::Widgets MediaPipeline libv4l2cpp avcodec swscale avutil SDL2 jrtp jthread ljcore yuv hv_static ZLToolKit ssl crypto webrtc sdptransform aec rnnoise pthread dl nppicc nppig ${CUDA_CUDA_LIBRARY} ${CUDART_LIB} ${CMAKE_DL_LIBS} ${NVENCODEAPI_LIB} ${CUVID_LIB} ${FREEGLUT_LIB} ${GLEW32_LIB} ${X11_LIB} ${GL_LIB})
else ()
    target_link_libraries(${PROJECT_NAME} PRIVATE Qt${QT_VERSION_MAJOR}::Widgets MediaPipeline SDL2 jrtp jthread ljcore hv_static sdptransform aec rnnoise opus ZLToolKit pthread dl)
endif ()

install(TARGETS ${PROJECT_NAME} RUNTIME DESTINATION ${CMAKE_INSTALL_PREFIX}/release/)