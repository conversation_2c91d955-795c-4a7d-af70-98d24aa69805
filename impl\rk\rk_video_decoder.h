/*
 * @Author: luo <EMAIL>
 * @Date: 2024-03-04 10:54:25
 * @LastEditors: luo <EMAIL>
 * @LastEditTime: 2025-05-13 17:18:31
 * @FilePath: /mediapipeline/rk/RKVideoDecoder.h
 * @Description: rk视频解码
 */
#ifndef RK_RK_VIDEO_DECODER_H_
#define RK_RK_VIDEO_DECODER_H_
#include <queue>
#include <future>
#include <mutex>
#include <chrono>
#include <condition_variable>

#include "VideoFrameDecoder.h"
#include "rk_frame_buffer.h"

#include <rockchip/rk_type.h>
#include <rockchip/rk_mpi.h>
#include <rockchip/mpp_log.h>
#include <hv/EventLoopThread.h>
// #include <osal/mpp_mem.h>
// #include <osal/mpp_env.h>
// #include <osal/mpp_time.h>
// #include <osal/mpp_common.h>

#include <fstream>

namespace panocom {
class RKVideoDecoder: public VideoFrameDecoder
{
private:
	/* data */
public:
	RKVideoDecoder(const std::string& jsonParams="");
	virtual ~RKVideoDecoder();
	virtual void onFrame(const std::shared_ptr<Frame> &f) override;

private:
	int32_t InitDecode();
	void Release();
	void ResetStatistics();
	void LogStatistics();
	void SendFeedbackFrame();

	std::future<bool> decode_future_;
	bool running_;
	
	MppCtx mpp_ctx_;
	MppApi* mpp_api_;
	MppPacket mpp_packet_;
	MppBufferGroup buf_group_;

	size_t packet_size_;
	MppCodingType codec_type_;

	RK_S32 width_;
	RK_S32 height_;
	RK_S32 hor_stride_;
	RK_S32 ver_stride_;
	RK_S32 group_buffer_size_;

	std::mutex mutex_;
	std::condition_variable cond_;
	
	uint16_t max_queue_size_;
	uint32_t max_gop_count_;
	uint32_t gop_count_;
	std::queue<std::shared_ptr<Frame>> packet_queue_;

	uint32_t frame_count_;
	std::chrono::steady_clock::time_point last_;

	std::ofstream ofs_;
	std::chrono::steady_clock::time_point statistics_start_time_;
	uint32_t frames_recieved_;
	uint32_t frames_to_decode_;
	uint32_t frames_decoded_;

	// 实际解码信息
	RK_S32 decode_width_ = 0;
    RK_S32 decode_height_ = 0;
	uint32_t fps_out_ = 0;

	hv::EventLoopThread notifycallbacks_thread_;
	std::chrono::steady_clock::time_point last_send_feedback_time_;
};

}
#endif