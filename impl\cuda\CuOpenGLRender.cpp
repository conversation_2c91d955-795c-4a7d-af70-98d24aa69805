#include "CuOpenGLRender.h"
#include <json.hpp>
#include <ljcore/jlog.h>
#include <npp.h>
#include "CuCommon.h"
#include "Frame.h"

extern int mmi_record_video;

using namespace panocom;
#define UPALIGNTO(value, align) ((value + align - 1) & (~(align - 1)))
#define UPALIGNTO16(value) UPALIGNTO(value, 16)
CuOpenGLRender::CuOpenGLRender(const std::string& jsonParams)
{
    FN_BEGIN;
    name_ = "CuOpenGLRender";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    dev_ = 0;
    if (j.contains("dev"))
    {
        dev_ = j["dev"];
    }
    cudaId_ = 0;
    if (j.contains("cudaId"))
    {
        cudaId_ = j["cudaId"];
    }
    gpuIndex_ = 0;
    if (j.contains("gpuIndex"))
    {
        gpuIndex_ = j["gpuIndex"];
    }
    maxWidth_ = 3840;
    if (j.contains("maxWidth"))
    {
        maxWidth_ = j["maxWidth"];
    }
    maxHeight_ = 2160;
    if (j.contains("maxHeight"))
    {
        maxHeight_ = j["maxHeight"];
    }
    FN_END;
}

CuOpenGLRender::~CuOpenGLRender()
{
    FN_BEGIN;
    // for (auto it = renderArea_.begin(); it != renderArea_.end(); ++it)
    // {
    //     //
    // }
    
    FN_END;
}

int CuOpenGLRender::addFrameRect(int videoId, int areaId, int x, int y, int z, int w, int h, int cudaId, int gpuIndex)
{
    jinfo("addFrameRect %d %d (%d, %d, %d) %d x %d (%d %d)", videoId, areaId, x, y, z, w, h, cudaId, gpuIndex);
    std::unique_lock<std::mutex> lock(mutex_);
    if (renderArea_.find(areaId) == renderArea_.end())
    {
        RenderArea area;
        area.newArea.x = x;
        area.newArea.y = y;
        area.newArea.z = z;
        area.newArea.w = w;
        area.newArea.h = h;
        area.x = x;
        area.y = y;
        area.z = z;
        area.w = w;
        area.h = h;
        area.cudaId = cudaId;
        area.gpuIndex = gpuIndex;
        area.videoId = videoId;
        area.mask = false;
        area.id = areaId;
        renderArea_[areaId] = area;
        renderAreaPriority_[z].push_back(&renderArea_[areaId]);
    } else {
        renderArea_[areaId].newArea.x = x;
        renderArea_[areaId].newArea.y = y;
        renderArea_[areaId].newArea.z = z;
        renderArea_[areaId].newArea.w = w;
        renderArea_[areaId].newArea.h = h;
        // renderArea_[areaId].x = x;
        // renderArea_[areaId].y = y;
        // renderArea_[areaId].z = z;
        // renderArea_[areaId].w = w;
        // renderArea_[areaId].h = h;
        renderArea_[areaId].cudaId = cudaId;
        renderArea_[areaId].gpuIndex = gpuIndex;
        renderArea_[areaId].videoId = videoId;
        renderArea_[areaId].mask = false;    
        renderArea_[areaId].changed = true;    
    }
    jinfo("renderAreaPriority_[%d] size: %d", z, renderAreaPriority_[z].size());
    return 0;
}

int CuOpenGLRender::changeFrameRect(int videoId, int areaId, int x, int y, int z, int w, int h)
{
    std::unique_lock<std::mutex> lock(mutex_);
    if (renderArea_.count(areaId) == 0)
    {
        jerror("changeFrameRect invalid areaId(%d)", areaId);
        return -1;
    }
    jinfo("changeFrameRect %d (%d, %d, %d) %d x %d", areaId, x, y, z, w, h);
    renderArea_[areaId].newArea.x = x;
    renderArea_[areaId].newArea.y = y;
    renderArea_[areaId].newArea.z = z;
    renderArea_[areaId].newArea.w = w;
    renderArea_[areaId].newArea.h = h;
    renderArea_[areaId].changed = true;
    return 0;
}

int CuOpenGLRender::removeFrameRect(int videoId, int areaId)
{
    std::unique_lock<std::mutex> lock(mutex_);
    if (renderArea_.count(areaId) == 0)
    {
        jerror("removeFrameRect invalid areaId(%d)", areaId);
        return -1;
    }
    else
    {
        jinfo("removeFrameRect %d", areaId);
        renderArea_[areaId].frame.reset();
        renderArea_[areaId].mask = true;
        renderArea_[areaId].r = 0;
        renderArea_[areaId].g = 0;
        renderArea_[areaId].b = 0;
        // // TODO
        // renderArea_[areaId].newArea.x = 0;
        // renderArea_[areaId].newArea.y = 0;
        // renderArea_[areaId].newArea.z = 1;
        // renderArea_[areaId].newArea.w = 128;
        // renderArea_[areaId].newArea.h = 128;
        // renderArea_[areaId].changed = true;
        renderArea_[areaId].newArea.roi_x_ratio = 0.0;
        renderArea_[areaId].newArea.roi_y_ratio = 0.0;
        renderArea_[areaId].newArea.roi_w_ratio = 1.0;
        renderArea_[areaId].newArea.roi_h_ratio = 1.0;
        renderArea_[areaId].roi_x_ratio = 0.0;
        renderArea_[areaId].roi_y_ratio = 0.0;
        renderArea_[areaId].roi_w_ratio = 1.0;
        renderArea_[areaId].roi_h_ratio = 1.0;
    }
    return 0;
}

int CuOpenGLRender::maskFrameRect(int videoId, int areaId, bool mask, int r, int g, int b)
{
    std::unique_lock<std::mutex> lock(mutex_);
    if (renderArea_.count(areaId) == 0)
    {
        jerror("muteFrameRect invalid areaId(%d)", areaId);
        return -1;
    }
    else
    {
        jinfo("muteFrameRect areaId(%d)", areaId);
        renderArea_[areaId].mask = mask;
        renderArea_[areaId].r = r;
        renderArea_[areaId].g = g;
        renderArea_[areaId].b = b;
    }
    return 0;
}

int CuOpenGLRender::setSrcRectROI(int videoId, int areaId, int x, int y, int w, int h) {
    std::unique_lock<std::mutex> lock(mutex_);
    if (renderArea_.count(areaId) == 0)
    {
        jerror("setSrcRectROI invalid areaId(%d)", areaId);
        return -1;
    } else {
        auto &area = renderArea_[areaId];
        jinfo("setSrcRectROI areaId %d (%d, %d) %d x %d", areaId, x, y, w, h);
        if (x < area.x || y < area.y || w > area.w || h > area.h) {
            jinfo("setSrcRectROI areaId %d failed: invalid params");
            return -1;
        }
        area.newArea.x = x;
        area.newArea.y = y;
        area.newArea.w = w;
        area.newArea.h = h;

        area.newArea.roi_x_ratio = area.roi_x_ratio + (x - area.x) * area.roi_w_ratio / area.w;
        area.newArea.roi_y_ratio = area.roi_y_ratio + (y - area.y) * area.roi_h_ratio / area.h;
        area.newArea.roi_w_ratio = area.roi_w_ratio * ((double)w / area.w);
        area.newArea.roi_h_ratio = area.roi_h_ratio * ((double)h / area.h);
        jinfo("setSrcRectROI areaId %d (%lf, %lf) %lf x %lf", areaId, area.newArea.roi_x_ratio, area.newArea.roi_y_ratio, area.newArea.roi_w_ratio, area.newArea.roi_h_ratio);
        area.changed = true;
    }
    return 0;
}

bool CuOpenGLRender::registerTex()
{
    for (size_t i = 0; i < 2; i++)
    {
        cuCtxPushCurrent(*CuCommon::instance().getCudaContext(0, i));
        if (fd_ > 0)
        {
            CUresult ret = cuGraphicsGLRegisterBuffer(&cuda_tex_resource_[i], fd_, CU_GRAPHICS_REGISTER_FLAGS_WRITE_DISCARD);
            if (ret != CUDA_SUCCESS)
            {
                jinfo("cuGraphicsGLRegisterBuffer fail ret=%d", ret);
            }
        }
        cuCtxPopCurrent(NULL);
    }
    return true;
}

void CuOpenGLRender::unRegisterTex()
{
    for (size_t i = 0; i < 2; i++)
    {
        cuCtxPushCurrent(*CuCommon::instance().getCudaContext(0, i));
        if (fd_ > 0 && cuda_tex_resource_[i] != nullptr)
        {
            CUresult ret = cuGraphicsUnregisterResource(cuda_tex_resource_[i]);
            if (ret != CUDA_SUCCESS)
            {
                jinfo("cuGraphicsUnregisterResource fail ret=%d", ret);
            }
            else
            {
                cuda_tex_resource_[i] = 0;
            }
        }
        cuCtxPopCurrent(NULL);
    }
}

void CuOpenGLRender::onFrame(const std::shared_ptr<Frame> &f)
{
    printInputStatus("CuOpenGLRender" + std::to_string(dev_));
    int width = f->width();
    int height = f->height();
    int id = f->getGroupId();
    {
        std::unique_lock<std::mutex> lock(mutex_);
        if (!stopped)
        {
            for (auto &area: renderArea_) {
                if (!area.second.mask && area.second.videoId == id)
                    area.second.frame = f;
            }
        }
    }
    
    if (frameNotifyCB_)
    {
        frameNotifyCB_(width, height, 0, shared_from_this());
    }
}

void CuOpenGLRender::setPBO(int fd, int width, int height)
{
    jinfo("setPBO %d %d x %d", fd, width, height);
    fd_ = fd;
    width_ = width;
    height_ = height;
    registerTex();
}

CUdeviceptr CuOpenGLRender::getTexBuffer(int cudaId, int gpuIndex)
{
    if (texResource_.find(gpuIndex) == texResource_.end() && fd_ > 0)
    {
        // CUresult ret = cuGraphicsGLRegisterBuffer(&texResource_[gpuIndex].cuda_tex_resource, fd_, CU_GRAPHICS_REGISTER_FLAGS_NONE);
        // if (ret != CUDA_SUCCESS)
        // {
        //     jinfo("cuGraphicsGLRegisterBuffer fail ret=%d", ret);
        // }
        // else
        {
            CUresult ret = cuGraphicsMapResources(1, &cuda_tex_resource_[gpuIndex], 0);
            if (ret != CUDA_SUCCESS)
            {
                jinfo("prepareFrame cuGraphicsMapResources(%p) fail ret=%d", cuda_tex_resource_[gpuIndex], ret);
            }
            else
            {
                size_t d_tex_size;
                ret = cuGraphicsResourceGetMappedPointer(&texResource_[gpuIndex].ptr, &d_tex_size, cuda_tex_resource_[gpuIndex]);
                if (ret != CUDA_SUCCESS)
                {
                    jinfo("cuGraphicsResourceGetMappedPointer fail ret=%d", ret);
                }
                else
                {
                    //jinfo("cuGraphicsResourceGetMappedPointer gpuIndex=%d resource=%p ptr=%p", gpuIndex, cuda_tex_resource_[gpuIndex], texResource_[gpuIndex].ptr);
                }
            }
        }
    }
    return texResource_[gpuIndex].ptr;
}

void CuOpenGLRender::releaseTexBuffer(int cudaId, int gpuIndex)
{
    if (texResource_.find(gpuIndex) != texResource_.end())
    {
        cuGraphicsUnmapResources(1, &cuda_tex_resource_[gpuIndex], 0);
        //cuGraphicsUnregisterResource(texResource_[gpuIndex].cuda_tex_resource);
        texResource_.erase(gpuIndex);
    }
}

bool CuOpenGLRender::prepareFrame()
{
    std::unique_lock<std::mutex> lock(mutex_);
    int cudaId = -1;
    int gpuIndex = -1; 
    CUdeviceptr d_tex_buffer = 0;
    
    for (auto it = renderAreaPriority_.rbegin(); it != renderAreaPriority_.rend(); it++)
    {
        for (auto it2 = it->second.begin(); it2 != it->second.end(); it2++)
        {
            RenderArea* area = *it2;
            if (area->frame)
            {
                cudaId = area->cudaId;
                gpuIndex = area->gpuIndex;
                cuCtxPushCurrent(*CuCommon::instance().getCudaContext(cudaId, gpuIndex));
                d_tex_buffer = getTexBuffer(cudaId, gpuIndex);
                if (!d_tex_buffer) {
                    jinfo("%d: d_tex_buffer is nullptr", __LINE__);
                    cuCtxPopCurrent(NULL);
                    continue;
                }
                int rgbSize = area->frame->width() * area->frame->height() * 3;
                if (rgbSize > 0 && area->rgbsize != rgbSize)
                {
                    if (area->rgb)
                    {
                        jinfo("%d: change buf from %d to %d", __LINE__, area->rgbsize, rgbSize);
                        cuMemFree(area->rgb);
                        area->rgb = 0;
                    }
                    CUresult ret = cuMemAlloc(&area->rgb, rgbSize);
                    if (ret != CUDA_SUCCESS)
                    {
                        jerror("cuMemAlloc(%d) %d x %d fail ret %d", rgbSize, area->frame->width(), area->frame->height(), ret);
                        cuCtxPopCurrent(NULL);
                        return false;
                    }
                    area->rgbsize = rgbSize;
                    area->rgb_width = area->frame->width();
                    area->rgb_height = area->frame->height();
                }
                if (area->changed)
                {
                    area->changed = false;

                    // 视频源改变重新分配内存才能用
                    {
                        if (area->rgb)
                        {
                            jinfo("%d: change buf from %d to %d", __LINE__, area->rgbsize, rgbSize);
                            cuMemFree(area->rgb);
                            area->rgb = 0;
                        }
                        CUresult ret = cuMemAlloc(&area->rgb, rgbSize);
                        if (ret != CUDA_SUCCESS)
                        {
                            jerror("cuMemAlloc(%d) %d x %d fail ret %d", rgbSize, area->frame->width(), area->frame->height(), ret);
                            cuCtxPopCurrent(NULL);
                            return false;
                        }
                        area->rgbsize = rgbSize;
                        area->rgb_width = area->frame->width();
                        area->rgb_height = area->frame->height();
                    }

                    int x = area->frame->width() * area->roi_x_ratio;
                    int y = area->frame->height() * area->roi_y_ratio;
                    int w = area->frame->width() * area->roi_w_ratio;
                    int h = area->frame->height() * area->roi_h_ratio;
                    jinfo("change roi (%d, %d) %d x %d", x, y, w, h);

                    int flag = NPPI_INTER_SUPER;
                    if (w <= area->w || h < area->h)
                    {
                        jerror("%d: %d %d x %d ==> %d x %d", __LINE__, area->rgb, area->frame->width(), area->frame->height(), area->w, area->h);
                        flag = NPPI_INTER_LINEAR;
                    }
                    CUresult ret = cuMemsetD8(area->rgb, 0, area->rgbsize);
                    if (ret != CUDA_SUCCESS)
                    {
                        jerror("%d: cuMemsetD8[%p](%d) %d x %d %d fail ret %d", __LINE__, area->rgb, area->rgbsize, area->frame->width(), area->frame->height(), area->frame->getGroupId(), ret);
                        cuCtxPopCurrent(NULL);
                        continue;
                    }

                    NppStatus status = nppiResize_8u_C3R((Npp8u *)area->rgb, 3 * area->frame->width(), NppiSize{area->frame->width(), area->frame->height()}, NppiRect{x, y, w, h},
                                        (Npp8u *)d_tex_buffer, 3 * width_, NppiSize{width_, height_}, NppiRect{area->x, area->y, area->w, area->h}, flag);
                    if (NPP_SUCCESS != status)
                        jinfo("%d: nppiResize_8u_C3R %d", __LINE__, status);

                    area->x = area->newArea.x;
                    area->y = area->newArea.y;
                    area->w = area->newArea.w;
                    area->h = area->newArea.h;

                    area->roi_x_ratio = area->newArea.roi_x_ratio;
                    area->roi_y_ratio = area->newArea.roi_y_ratio;
                    area->roi_w_ratio = area->newArea.roi_w_ratio;
                    area->roi_h_ratio = area->newArea.roi_h_ratio;

                    if (area->z != area->newArea.z)
                    {
                        it->second.erase(it2);
                        jinfo("%d: area[%d] zpos %d => %d", __LINE__, area->id, area->z, area->newArea.z);
                        area->z = area->newArea.z;
                        renderAreaPriority_[area->z].push_back(area);
                        cuCtxPopCurrent(NULL);
                        return true; //FIXME
                    }
                }
                Npp8u *src[2];
                src[0] = area->frame->getFrameBuffer();
                src[1] = area->frame->getFrameBuffer() + area->frame->linesize(0) * area->frame->linesize(1);
                NppStatus status = nppiNV12ToRGB_8u_P2C3R(src, area->frame->linesize(0), (Npp8u *)area->rgb, 3 * area->frame->width(), NppiSize{area->frame->width(), area->frame->height()});
                if (NPP_SUCCESS != status)
                    jinfo("nppiNV12ToRGB_8u_P2C3R %d", status);
                    
                //jinfo("resize (%d, %d) (%d x %d)", area->x, area->y, area->w, area->h);

                int x = area->frame->width() * area->roi_x_ratio;
                int y = area->frame->height() * area->roi_y_ratio;
                int w = area->frame->width() * area->roi_w_ratio;
                int h = area->frame->height() * area->roi_h_ratio;

                int flag = NPPI_INTER_SUPER;
                if (w <= area->w || h < area->h)
                {
                    flag = NPPI_INTER_LINEAR;
                }
                status = nppiResize_8u_C3R((Npp8u *)area->rgb, 3 * area->frame->width(), NppiSize{area->frame->width(), area->frame->height()}, NppiRect{x, y, w, h},
                                        (Npp8u *)d_tex_buffer, 3 * width_, NppiSize{width_, height_}, NppiRect{area->x, area->y, area->w, area->h}, flag);
                if (NPP_SUCCESS != status) 
                {
                    jinfo("nppiResize_8u_C3R(%d, %d) %d", cudaId, gpuIndex, status);
                }
                releaseTexBuffer(cudaId, gpuIndex);
                cuCtxPopCurrent(NULL);
            }
            else
            {
                if (area->mask)
                {
                    cudaId = area->cudaId;
                    gpuIndex = area->gpuIndex;
                    cuCtxPushCurrent(*CuCommon::instance().getCudaContext(cudaId, gpuIndex));
                    if (area->changed)
                    {
                        if (area->rgb)
                        {
                            d_tex_buffer = getTexBuffer(cudaId, gpuIndex);
                            if (!d_tex_buffer) {
                                jinfo("%d: d_tex_buffer is nullptr", __LINE__);
                                cuCtxPopCurrent(NULL);
                                continue;
                            }
                            CUresult ret = cuMemsetD8(area->rgb, 0, area->rgbsize);
                            if (ret != CUDA_SUCCESS)
                            {
                                jerror("%d: cuMemsetD8(%d) %d x %d fail ret %d", __LINE__, area->rgbsize, area->w, area->h, ret);
                                cuCtxPopCurrent(NULL);
                                continue;
                            }
                            NppStatus status = nppiResize_8u_C3R((Npp8u *)area->rgb, 3 * area->rgb_width, NppiSize{area->rgb_width, area->rgb_height}, NppiRect{0, 0, area->rgb_width, area->rgb_height},
                                                (Npp8u *)d_tex_buffer, 3 * width_, NppiSize{width_, height_}, NppiRect{area->x, area->y, area->w, area->h}, NPPI_INTER_SUPER);
                            if (NPP_SUCCESS != status)
                                jinfo("nppiResize_8u_C3R failed %d %dx%d", status, width_, height_);
                            releaseTexBuffer(cudaId, gpuIndex);
                        }
                        area->changed = false;
                        area->x = area->newArea.x;
                        area->y = area->newArea.y;
                        area->w = area->newArea.w;
                        area->h = area->newArea.h;
                        if (area->z != area->newArea.z)
                        {
                            it->second.erase(it2);
                            jinfo("%d: area[%d] zpos %d => %d", __LINE__, area->id, area->z, area->newArea.z);
                            area->z = area->newArea.z;
                            renderAreaPriority_[area->z].push_back(area);
                            cuCtxPopCurrent(NULL);
                            return true; //FIXME
                        }
						cuCtxPopCurrent(NULL);
                        continue;
                    }
                    int rgbSize = (area->w + 16) * (area->h + 16) * 3;
                    // int rgbSize = UPALIGNTO16(area->w) * UPALIGNTO16(area->h) * 3;
                    if (rgbSize > 0 && area->rgbsize != rgbSize)
                    {
                        if (area->rgb)
                        {
                            jinfo("%d: change buf from %d to %d", __LINE__, area->rgbsize, rgbSize);
                            cuMemFree(area->rgb);
                            area->rgb = 0;
                        }
                        CUresult ret = cuMemAlloc(&area->rgb, rgbSize);
                        if (ret != CUDA_SUCCESS)
                        {
                            jerror("%d: areaId[%d] cuMemAlloc(%d) %d x %d fail ret %d", __LINE__, area->id, rgbSize, area->w, area->h, ret);
                            cuCtxPopCurrent(NULL);
                            return false;
                        }
                        area->rgbsize = rgbSize;
                        area->rgb_width = area->w + 16;
                        area->rgb_height = area->h + 16;
                    }
                    if (area->rgb)
                    {
                        d_tex_buffer = getTexBuffer(cudaId, gpuIndex);
                        if (!d_tex_buffer) {
                            jinfo("%d: d_tex_buffer is nullptr", __LINE__);
                            cuCtxPopCurrent(NULL);
                            continue;
                        }
                        CUresult ret = cuMemsetD8(area->rgb, 0, area->rgbsize);
                        if (ret != CUDA_SUCCESS)
                        {
                            jerror("%d: cuMemsetD8(%d) %d x %d fail ret %d", __LINE__, area->rgbsize, area->w, area->h, ret);
                            cuCtxPopCurrent(NULL);
                            continue;
                        }
                        NppStatus status = nppiResize_8u_C3R((Npp8u *)area->rgb, 3 * (area->w + 16), NppiSize{area->w + 16, area->h + 16}, NppiRect{0, 0, area->w + 16, area->h + 16},
                                            (Npp8u *)d_tex_buffer, 3 * width_, NppiSize{width_, height_}, NppiRect{area->x, area->y, area->w, area->h}, NPPI_INTER_SUPER);
                        if (NPP_SUCCESS != status)
                            jinfo("nppiResize_8u_C3R failed %d %dx%d", status, width_, height_);
                        releaseTexBuffer(cudaId, gpuIndex);
                    }
                    cuCtxPopCurrent(NULL);
                }
            }
        }
    }
    //jinfo("prepareFrame success");
    return true;
}

void CuOpenGLRender::start()
{
    jinfo("CuOpenGLRender::start");
    stopped = false;
}

void CuOpenGLRender::stop()
{
    jinfo("CuOpenGLRender::stop");
    std::unique_lock<std::mutex> lock(mutex_);
    stopped = true;
    for (auto &area: renderArea_) {
        area.second.frame.reset();
    }
}