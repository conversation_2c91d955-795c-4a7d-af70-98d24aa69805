#include "Utils.h"
#include "Frame.h"
#include <assert.h>
#include <cmath>
#include <ljcore/jlog.h>
#include <string.h>

#ifdef USE_NEON
#include <arm_neno.h>
#endif
namespace panocom
{
#ifndef WIN32
#include <sys/times.h>
long GetTickCount()
{
    struct tms tm;
    return times(&tm) * 10;
}
#endif

namespace
{
constexpr int32_t default_db_max = 12;
constexpr int32_t default_db_min = -12;
constexpr float dp_gain_table[25] = { 0.25f,  0.2818f, 0.3162f, 0.3548f, 0.3981f, 0.4467f, 0.5012f, 0.5623f, 0.6309f, 0.7079f, 0.7943f, 0.8913f, 1.0f,
                                      1.122f, 1.259f,  1.413f,  1.585f,  1.778f,  2.0f,    2.239f,  2.512f,  2.818f,  3.162f,  3.548f,  4.0f };
} // namespace


int findNALU(uint8_t *buf, int size, int *nal_start, int *nal_end, int *sc_len)
{
    int i = 0;
    *nal_start = 0;
    *nal_end = 0;
    *sc_len = 0;

    while (true)
    {
        if (size < i + 3)
            return -1; /* Did not find NAL start */

        /* ( next_bits( 24 ) == {0, 0, 1} ) */
        if (buf[i] == 0 && buf[i + 1] == 0 && buf[i + 2] == 1)
        {
            i += 3;
            *sc_len = 3;
            break;
        }

        /* ( next_bits( 32 ) == {0, 0, 0, 1} ) */
        if (size > i + 3 && buf[i] == 0 && buf[i + 1] == 0 && buf[i + 2] == 0 && buf[i + 3] == 1)
        {
            i += 4;
            *sc_len = 4;
            break;
        }

        ++i;
    }

    // assert(buf[i - 1] == 1);
    *nal_start = i;

    /*( next_bits( 24 ) != {0, 0, 1} )*/
    while ((size > i + 2) &&
           (buf[i] != 0 || buf[i + 1] != 0 || buf[i + 2] != 1))
        ++i;

    if (size <= i + 2)
        *nal_end = size;
    else if (buf[i - 1] == 0)
        *nal_end = i - 1;
    else
        *nal_end = i;

    return (*nal_end - *nal_start);
}

static char prefix3[3] = { 0, 0, 1 };
static char prefix4[4] = { 0, 0, 0, 1 };

bool isIDRNALU(uint8_t *buf, int size, int fmt)
{
    if (fmt == FRAME_FORMAT_H264)
    {
        uint8_t type = 0;
        if (memcmp(buf, prefix3, 3) == 0)
        {
            type = buf[3] & 0x1F;
        }
        else if (memcmp(buf, prefix4, 4) == 0)
        {
            type = buf[4] & 0x1F;
        }
        switch (type)
        {
        case H264_NAL_IDR:
            return true;
        default:
            return false;
        }
    }
    else if (fmt == FRAME_FORMAT_H265)
    {
        uint8_t type = 0;
        if (memcmp(buf, prefix3, 3) == 0)
        {
            type = (buf[3]>> 1) & 0x3F;
        }
        else if (memcmp(buf, prefix4, 4) == 0)
        {
            type = (buf[4]>> 1) & 0x3F;
        }
        switch (type)
        {
        case H265_NAL_RSV_IRAP:
            return true;
        default:
            return false;
        }
    }
    return false;
}

bool setThreadAffinity(int i) {
#if (defined(__linux) || defined(__linux__)) && !defined(ANDROID)
    jinfo("setThreadAffinity %d", i);
    cpu_set_t mask;
    CPU_ZERO(&mask);
    CPU_SET(i, &mask);
    if (!pthread_setaffinity_np(pthread_self(), sizeof(mask), &mask)) {
        jerror("pthread_setaffinity_np success");
        return true;
    }
#endif
    return false;
}

std::string hexStr(uint8_t* buf, int size)
{
    std::string ret;
    char* strbuf = new char[size * 5 + 1];
    char* temp = strbuf;
    memset(strbuf, 0, size * 5 + 1);
    for (int i = 0; i < size; ++i)
    {
        sprintf(temp, "0x%02x ", buf[i]);
        temp += 5;
    }
    ret = strbuf;
    delete[] strbuf;
    return ret;
}

// TODO: too simple
void stereo_to_mono(int16_t *stereo_buffer, short *mono_buffer, size_t frames) {
    for (size_t i = 0; i < frames; i++) {
        int32_t left = stereo_buffer[2 * i];
        int32_t right = stereo_buffer[2 * i + 1];
        mono_buffer[i] = (left + right) / 2;
    }
}

void mono_to_stereo(int16_t *mono_buffer, int16_t *stereo_buffer, size_t frames) {
    if (frames <= 0) return;
    for (size_t i = frames - 1; i >= 0; i--) {
        stereo_buffer[2 * i] = mono_buffer[i];      // left
        stereo_buffer[2 * i + 1] = mono_buffer[i];  // right
    }
}

void mono_to_stereo(int16_t *buffer, size_t frames) {
    for (int i = frames - 1; i >= 0; i--) {
        buffer[2 * i + 1] = buffer[i];
        buffer[2 * i] = buffer[i];
    }
}


uint64_t simple_resample_s16(const int16_t *input, int16_t *output, int inSampleRate, int outSampleRate, uint64_t inputSize) 
{
    if (input == NULL)
        return 0;
    uint64_t outputSize = (uint64_t) (inputSize * (double) outSampleRate / (double) inSampleRate);
    if (output == NULL)
        return outputSize;
    for (uint32_t i = 0; i < outputSize; i += 1) {
        double src_index = i * static_cast<double>(inSampleRate) / outSampleRate;
        size_t index = static_cast<size_t>(src_index);
        double frac = src_index - index;
        if (index + 1 < inputSize) output[i] = input[index] * (1.0 - frac) + input[index + 1] * frac;
        else output[i] = input[index];
    }
    return outputSize;
}

#if defined(__linux__)
pid_t getThreadLWP() {
    return syscall(SYS_gettid);
}
#endif

static std::string limitString(const char *name, size_t max_size) {
    std::string str = name;
    if (str.size() + 1 > max_size) {
        auto erased = str.size() + 1 - max_size + 3;
        str.replace(5, erased, "...");
    }
    return str;
}

void setThreadName(const char *name) {
    assert(name);
#if defined(__linux) || defined(__linux__) || defined(__MINGW32__)
    pthread_setname_np(pthread_self(), limitString(name, 16).data());
#elif defined(__MACH__) || defined(__APPLE__)
    pthread_setname_np(limitString(name, 32).data());
#elif defined(_MSC_VER)
    // SetThreadDescription was added in 1607 (aka RS1). Since we can't guarantee the user is running 1607 or later, we need to ask for the function from the kernel.
    using SetThreadDescriptionFunc = HRESULT(WINAPI * )(_In_ HANDLE hThread, _In_ PCWSTR lpThreadDescription);
    static auto setThreadDescription = reinterpret_cast<SetThreadDescriptionFunc>(::GetProcAddress(::GetModuleHandle("Kernel32.dll"), "SetThreadDescription"));
    if (setThreadDescription) {
        // Convert the thread name to Unicode
        wchar_t threadNameW[MAX_PATH];
        size_t numCharsConverted;
        errno_t wcharResult = mbstowcs_s(&numCharsConverted, threadNameW, name, MAX_PATH - 1);
        if (wcharResult == 0) {
            HRESULT hr = setThreadDescription(::GetCurrentThread(), threadNameW);
            if (!SUCCEEDED(hr)) {
                int i = 0;
                i++;
            }
        }
    } else {
        // For understanding the types and values used here, please see:
        // https://docs.microsoft.com/en-us/visualstudio/debugger/how-to-set-a-thread-name-in-native-code

        const DWORD MS_VC_EXCEPTION = 0x406D1388;
#pragma pack(push, 8)
        struct THREADNAME_INFO {
            DWORD dwType = 0x1000; // Must be 0x1000
            LPCSTR szName;         // Pointer to name (in user address space)
            DWORD dwThreadID;      // Thread ID (-1 for caller thread)
            DWORD dwFlags = 0;     // Reserved for future use; must be zero
        };
#pragma pack(pop)

        THREADNAME_INFO info;
        info.szName = name;
        info.dwThreadID = (DWORD) - 1;

        __try{
                RaiseException(MS_VC_EXCEPTION, 0, sizeof(info) / sizeof(ULONG_PTR), reinterpret_cast<const ULONG_PTR *>(&info));
        } __except(GetExceptionCode() == MS_VC_EXCEPTION ? EXCEPTION_CONTINUE_EXECUTION : EXCEPTION_EXECUTE_HANDLER) {
        }
    }
#else
    thread_name = name ? name : "";
#endif
}

bool StartsWith(const std::string& text, const std::string& prefix) {
    return prefix.empty() || (text.size() >= prefix.size() && memcmp(text.data(), prefix.data(), prefix.size()) == 0);
}

double caculateRMS(int16_t *buffer, size_t frames) {
    assert(buffer != nullptr && "buffer is null");
    assert(frames > 0 && "frames is zero");
    double sum = 0;
    for (size_t i = 0; i < frames; ++i)
        sum += buffer[i] * buffer[i];
    return sqrt(sum / frames);
}

double rms_to_db(double rms) {
    return 20.0f * log10(rms / 32767.0); // 32767是int16_t的最大值
}

int quantize_volume(double rms) {
    const double maxRMS = 32767; // int16_t最大值，即满音量时的RMS值
    return static_cast<int>(rms / maxRMS * 100);
}

int GetPcmLevel(const uint8_t* data, int len, int sampleSize, int channelCount) {
	static const uint32_t maxAmplitude = 32767;
	const int channelBytes = sampleSize / 8;
	const int sampleBytes = channelCount * channelBytes;
	const int numSamples = len / sampleBytes;

	uint32_t maxValue = 0;
	for (int i = 0; i < numSamples; ++i)
	{
		for (int j = 0; j < channelCount; ++j)
		{
			int16_t temp = *(int16_t*)data;
			uint32_t value = abs(temp);
			maxValue = std::max(value, maxValue);
			data += channelBytes;
		}
	}
	maxValue = std::min(maxValue, maxAmplitude);
	double flevel = double(maxValue) / maxAmplitude;
	int level = 100 * flevel;
	return level;    
}

float caculateJitter(uint32_t last_ts, uint32_t ts, uint32_t last_rtp_time, uint32_t rtp_time, uint32_t clock_rate, float previous_jitter) {
    // 计算当前的抖动值
    if (last_ts == 0 || last_rtp_time == 0 || clock_rate == 0)
        return 0.f;
    double jitter = std::abs((double)(rtp_time - last_rtp_time) * 1000.0 / clock_rate - ts + last_ts);
    previous_jitter += ((jitter / 16.0f) - (previous_jitter / 16.0f));
    // 返回当前的抖动值和上一个抖动值的平均值
    return previous_jitter;
}

float caculateJitter(uint32_t last_ts, uint32_t ts, uint32_t rtp_ts_inc, uint32_t clock_rate, float previous_jitter = 0.0f) {
    // 计算当前的抖动值
    if (last_ts == 0 || clock_rate == 0)
        return 0.f;
    double jitter = std::abs((double)(rtp_ts_inc) * 1000.0 / clock_rate - ts + last_ts);
    previous_jitter += ((jitter / 16.0f) - (previous_jitter / 16.0f));
    // 返回当前的抖动值和上一个抖动值的平均值
    return previous_jitter;    
}

void adjust_gain(int16_t* out_data, size_t out_len, int16_t* in_data, size_t in_len, int db) {
    db = std::max(default_db_min, std::min(default_db_max, db));
    float gain = dp_gain_table[db + 12];
    size_t size = std::min(out_len, in_len);
#ifdef USE_NEON
    size_t i = 0;
    float32x4_t gain_vec = vdupq_n_f32(gain);
    for (; i + 3 < size; i += 4) {
        int16x4_t s16 = vld1_s16(in_data + i);
        int32x4_t s32 = vmovl_s16(s16);
        float32x4_t f32 = vcvtq_f32_s32(s32);
        f32 = vmulq_f32(f32, gain_vec);
        f32 = vmaxq_f32(f32, vdupq_n_f32(-32768.0f));
        f32 = vminq_f32(f32, vdupq_n_f32(32767.0f));
        int32x4_t s32_out = vcvtq_s32_f32(f32);
        int16x4_t s16_out = vqmovn_s32(s32_out);
        vst1_s16(out_data + i, s16_out);
    }
    for (; i < size; i++) {
        float amplified = static_cast<float>(input[i]) * gain();
        if (amplified > 32767.0f) amplified = 32767.0f;
        else if (amplified < -32768.0f) amplified = -32768.0f;
        out_data[i] = static_cast<int16_t>(amplified);
    }
#else
    for (size_t i = 0; i < size; i++) {
        int32_t sample = static_cast<int32_t>(in_data[i]) * gain;
        out_data[i] = static_cast<int16_t>(std::max(-32768, std::min(32767, sample)));
    }        
#endif
}

}