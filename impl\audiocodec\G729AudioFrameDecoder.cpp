#include "G729AudioFrameDecoder.h"
#include <json.hpp>

namespace panocom
{
namespace
{
const size_t kSampleRateHz = 8000;
} // namespace


G729AudioFrameDecoder::G729AudioFrameDecoder(const std::string &jsonParams)
    : fmt_(FRAME_FORMAT_G729) {
	name_ = "G729AudioFrameDecoder";
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
	ctx_ = initBcg729DecoderChannel();
	if (!ctx_) printf("Create g729 encoder failed\n");
}
G729AudioFrameDecoder::~G729AudioFrameDecoder() {
	if (ctx_) 
		closeBcg729DecoderChannel(ctx_);
	ctx_ = nullptr;
}
// 16bits, 8k
void G729AudioFrameDecoder::onFrame(const std::shared_ptr<Frame> &frame) {
	if (frame->getFrameSize() == 0)
		return;
    int16_t total = 0;
    int n = frame->getFrameBufferSize() / 10;
	uint8_t *encoded = (uint8_t *)frame->getFrameBuffer();
	std::shared_ptr<Frame> out_frame = Frame::CreateFrame(FRAME_FORMAT_PCM_8000_1);
	out_frame->createFrameBuffer(frame->getFrameBufferSize() << 4);
	int16_t *decoded = (int16_t*)out_frame->getFrameBuffer();
	out_frame->setGain(frame->getGain());
	if (ctx_) {
		for (int i = 0; i < n; i++) {
			bcg729Decoder(ctx_, encoded + 10 * i, 10, 0, 0, 0, decoded + L_FRAME * i);
			total += L_FRAME;
		}
		if (total > 0)
			deliverFrame(out_frame);
		else
			printf("g729 decode failed\n");
	}
}
} // namespace panocom
