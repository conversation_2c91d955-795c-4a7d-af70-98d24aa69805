#include "UlpFecEncoder.h"
#include "RtpHeader.h"
#include <json.hpp>

using namespace panocom;

UlpFecEncoder::UlpFecEncoder(const std::string& jsonParams)
{
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    red_payload_type_ = 127;
    if (j.contains("redPayloadType"))
    {
        red_payload_type_ = j["redPayloadType"];
    }
    ulpfec_payload_type_ = 125;
    if (j.contains("ulpfecPayloadType"))
    {
        ulpfec_payload_type_ = j["ulpfecPayloadType"];
    }
    payload_type_ = 96;
    if (j.contains("payloadType"))
    {
        payload_type_ = j["payloadType"];
    }
}

UlpFecEncoder::~UlpFecEncoder()
{

}

void UlpFecEncoder::onFrame(const std::shared_ptr<Frame> &f)
{
    if (f->getFrameFormat() == FRAME_FORMAT_RTP)
    {
        std::unique_ptr<RtpPacket> red_packet = buildRedPayload(f->getFrameBuffer(), f->getFrameSize());
        red_packet->SetPayloadType(red_payload_type_);

        std::vector<std::unique_ptr<RedPacket>> fec_packets;
        RTPHeader* header = (RTPHeader*)f->getFrameBuffer();
        int headerLen = header->getHeaderLength();
        ulpfec_generator_.AddRtpPacketAndGenerateFec(f->getFrameBuffer(), f->getFrameSize() - headerLen, headerLen);
        size_t num_fec_packets = ulpfec_generator_.NumAvailableFecPackets();
        if (num_fec_packets > 0) 
        {
            printf("num_fec_packets:%d\n", num_fec_packets);
            fec_packets = ulpfec_generator_.GetUlpfecPacketsAsRed(red_payload_type_, ulpfec_payload_type_, header->getSeqNumber());
        }

        auto f = Frame::CreateFrame(FRAME_FORMAT_RED);
        f->createFrameBuffer(red_packet->length());
        memcpy(f->getFrameBuffer(), red_packet->data(), red_packet->length());
        deliverFrame(f);

        for (size_t i = 0; i < fec_packets.size(); i++)
        {
            auto f = Frame::CreateFrame(FRAME_FORMAT_ULPFEC);
            f->createFrameBuffer(fec_packets[i]->length());
            memcpy(f->getFrameBuffer(), fec_packets[i]->data(), fec_packets[i]->length());
            deliverFrame(f);
        }
    }
}

std::unique_ptr<RtpPacket> UlpFecEncoder::buildRedPayload(const uint8_t* data, int len)
{
    RTPHeader* header = (RTPHeader*)data;
    int headerLen = header->getHeaderLength();
    std::unique_ptr<RtpPacket> packet(new RtpPacket(len + 1));
    memcpy(packet->data(), data, headerLen);
    packet->data()[headerLen] = payload_type_;
    memcpy(packet->data() + payload_type_ + 1, data + headerLen, len - headerLen);
    packet->SetLength(len + 1);
    return std::move(packet);
}