#include "VideoFrameRender.h"
#ifdef USE_FFMPEG
#include "ffmpeg/FfIPCFrameRender.h"
#endif


#ifdef USE_SDL2
#include "sdl2/Sdl2VideoFrameRender.h"
#endif
#ifdef RK_PLATFORM
#include "rk/DRMDisplayManager.h"
#endif
#ifdef USE_CUDA
#include "cuda/CuIPCFrameRender.h"
#endif

#include <algorithm>
#include <ljcore/jlog.h>
#include <json.hpp>

using namespace panocom;

bool VideoFrameRender::isRegistered = false;

std::vector<std::string> VideoFrameRender::renders_;

std::unordered_map<VideoFrameRender::RendererInfo, VideoFrameRender::RendererRef, VideoFrameRender::InfoHash> VideoFrameRender::existed_renderers_;
// std::unordered_map<std::shared_ptr<VideoFrameRender>, int> VideoFrameRender::renderer_refs_;

void VideoFrameRender::RegistRenders()
{
    if (!isRegistered)
    {
#ifdef USE_FFMPEG
    renders_.push_back("FfIPCFrameRender");
#endif

#ifdef USE_SDL2
        renders_.push_back("Sdl2VideoFrameRender");
#endif
#ifdef RK_PLATFORM
		renders_.push_back("RKVideoRender");
#endif
#ifdef USE_CUDA
        renders_.push_back("CuIPCFrameRender");
#endif
        isRegistered = true;
    }
}

std::vector<std::string>& VideoFrameRender::GetSupportRenders()
{
    RegistRenders();
    return renders_;
}

bool VideoFrameRender::IsSupportRender(const std::string &renderName)
{
    RegistRenders();
    return std::find(renders_.begin(), renders_.end(), renderName) != renders_.end();
}

std::shared_ptr<VideoFrameRender> VideoFrameRender::CreateVideoRender(const std::string &renderName, const std::string &jsonParams)
{
    std::shared_ptr<VideoFrameRender> ret;
    // nlohmann::json j;
    // if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    // int dev = -1;
    // if (j.contains("dev")) dev = j["dev"];
    // int gpu_index = 0;
    // if (j.contains("gpuIndex")) gpu_index = j["gpuIndex"];
    // RendererInfo temp { .name = renderName, .dev = dev, .gpu_index = gpu_index };
    // if (existed_renderers_.count(temp) != 0) {
    //     existed_renderers_[temp].ref++;
    //     return existed_renderers_[temp].renderer;
    // }
    jinfo("CreateVideoRender 111  renderName:%s",renderName.c_str());
#ifdef USE_FFMPEG
    if (renderName == "FfIPCFrameRender")
    {
        ret = std::make_shared<FfIPCFrameRender>(jsonParams);
    }
#endif

#ifdef USE_SDL2
    if (renderName == "Sdl2VideoFrameRender")
    {
        ret = std::make_shared<Sdl2VideoFrameRender>(jsonParams);
    }
#endif
#ifdef RK_PLATFORM
	if (renderName == "RKVideoRender") 
    {
		ret = DRMDisplayManager::instance().CreateRender(jsonParams);
	}
#endif
#ifdef USE_CUDA
    if (renderName == "CuIPCFrameRender")
    {
        ret = std::make_shared<CuIPCFrameRender>(jsonParams);
    }
#endif
    // if (ret) {
    //     existed_renderers_[temp].renderer = ret;
    //     existed_renderers_[temp].ref = 1;
    // }
    return ret;
}

void VideoFrameRender::ReleaseVideoRender(const std::string &renderName, int dev, int gpu_index) {
    RendererInfo info { .name = renderName, .dev = dev, .gpu_index = gpu_index };
    if (existed_renderers_.count(info) != 0) {
        auto &renderer_ref = existed_renderers_[info];
        renderer_ref.ref--;
        if (renderer_ref.ref <= 0) {
            if (renderer_ref.renderer) renderer_ref.renderer->stop();
            existed_renderers_.erase(info);
        }
    }   
}

void VideoFrameRender::ReleaseVideoRender(const FramePipeline::Ptr& ptr) {
    for (auto &kv: existed_renderers_) {
        if (kv.second.renderer == ptr) {
            kv.second.ref--;
            if (kv.second.ref <= 0) {
                if (kv.second.renderer) kv.second.renderer->stop();
                existed_renderers_.erase(kv.first);
            }
            break;
        }
    }
}

bool VideoFrameRender::isVideoFrameRender(const FramePipeline::Ptr& ptr)
{

#ifdef USE_FFMPEG
    if (ptr->name() == "FfIPCFrameRender")
    {
        return true;
    }
#endif

#ifdef USE_SDL2
    if (ptr->name() == "Sdl2VideoFrameRender")
    {
        return true;
    }
#endif
#ifdef RK_PLATFORM
	if (ptr->name() == "RKVideoRender") 
    {
		return true;
	}
#endif
#ifdef USE_CUDA
    if (ptr->name() == "CuIPCFrameRender")
    {
        return true;
    }
#endif
    return false;
}

bool VideoFrameRender::GetConnectorStatus(int dev, int card) {
#ifdef RK_PLATFORM
		return DRMDisplayManager::instance().GetConnectorStatus(dev, card);
#endif
    return false;
}