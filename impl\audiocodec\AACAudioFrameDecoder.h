/*
 * @Author: luo <EMAIL>
 * @Date: 2024-04-18 14:07:39
 * @LastEditors: luo <EMAIL>
 * @LastEditTime: 2025-03-10 20:15:17
 * @FilePath: /mediamanager/impl/audiocodec/AACAudioFrameDecoder.h
 * @Description: 音频aac解码模块
 */
#ifndef IMPL_AUDIO_AACAUDIOFRAMEDECODER_H_
#define IMPL_AUDIO_AACAUDIOFRAMEDECODER_H_
#include "AudioFrameDecoder.h"
#include "Frame.h"
#include <ptoolkit/bytebuffer.h>

#include <fdk-aac/aacdecoder_lib.h>
#include <hv/EventLoopThread.h>

#include <fstream>
namespace panocom {
class AACAudioFrameDecoder : public AudioFrameDecoder {
public:
    AACAudioFrameDecoder(const std::string &jsonParams);
    ~AACAudioFrameDecoder();
    void onFrame(const std::shared_ptr<Frame> &f) override;

private:
    bool init();

private:
    FrameFormat fmt_;
    FrameFormat out_fmt_;
    int channels_;
    uint32_t sample_rate_;
    uint32_t bitrate_;
    uint32_t ptime_;
    uint32_t samples_per_frame_;
    
    HANDLE_AACDECODER decoder_;
    uint8_t *outbuf_;
    uint32_t outbuf_size_;
	ByteBuffer input_buf_;
    ByteBuffer output_buf_;

	hv::EventLoopThread loop_thread_;
    std::mutex output_mutex_;
    std::ofstream ofs_;

    int16_t gain_;
};

} // namespace panocom

#endif