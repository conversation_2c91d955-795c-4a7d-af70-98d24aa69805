#include <stdio.h>
#include <future>
#include <fstream>
#include <json.hpp>
#include <unistd.h>

#include <ljcore/jlog.h>

#include "AudioFrameCapturer.h"
#include "MediaPipeline.h"

using namespace panocom;

namespace
{
constexpr uint32_t sample_rate = 16000;
constexpr uint16_t ptime = 20;	// ms
constexpr uint16_t count_bytes = 2;
constexpr uint16_t channel = 1;
constexpr int16_t dev = 4;
} // namespace

class TestFileSource : public FramePipeline
{
private:
	/* data */
public:
	TestFileSource(const std::string& jsonParams);
	~TestFileSource();
private:
	std::future<bool> future_;
	std::ifstream input_;
	int sample_rate_;
	bool running_;
};

TestFileSource::TestFileSource(const std::string& jsonParams)
{
	nlohmann::json j;
	if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
	if (j.contains("path")) {
		input_.open(j["path"], std::ios::in | std::ios::binary);
		running_ = true;
	}
	sample_rate_ = 16000;
	future_ = std::async([this]() -> bool {
		int frames = sample_rate_ * ptime / 1000;
		uint32_t size = frames * channel * count_bytes;
		double sleep_time = (double)frames / sample_rate_;
		sleep_time *= 1e6;
		auto last = std::chrono::steady_clock::now();
		while (running_)
		{
			auto now = std::chrono::steady_clock::now();
			auto duration = std::chrono::duration_cast<std::chrono::microseconds>(now - last).count();
			// last = now;
			FrameFormat fmt = Frame::getPCMFormat(channel, sample_rate_);
			if (duration < 10000) continue;
			last = now;

			// std::cout << "duration = " << duration << std::endl;
			if (!input_.eof()) {
				auto frame = Frame::CreateFrame(fmt);
				frame->createFrameBuffer(size);
				input_.read((char*)frame->getFrameBuffer(), size);
				deliverFrame(frame);
			} else {
				input_.clear();
				input_.seekg(0, std::ios::beg);
			}
			// std::this_thread::sleep_for(std::chrono::microseconds(9600));
			// usleep(sleep_time);

		}
		return true;
	});
}

TestFileSource::~TestFileSource()
{
	running_ = false;
	auto status = future_.wait_for(std::chrono::milliseconds(200));
	if (status == std::future_status::timeout) {
		
	} else {
		future_.get();
	}
	if (input_ && input_.is_open()) {
		input_.close();
	}
}
class TestFileDestination : public FramePipeline {
private:
    /* data */
public:
    TestFileDestination(const std::string &jsonParams);
    ~TestFileDestination();
    virtual void onFrame(const std::shared_ptr<Frame> &f) override;

private:
    std::ofstream output_;
};

TestFileDestination::TestFileDestination(const std::string &jsonParams) {
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    if (j.contains("path")) {
        output_.open(j["path"], std::ios::out | std::ios::binary);
    }
}

void TestFileDestination::onFrame(const std::shared_ptr<Frame> &f) {
    output_.write((char *)f->getFrameBuffer(), f->getFrameBufferSize());
}

TestFileDestination::~TestFileDestination() {
    if (output_ && output_.is_open()) {
        output_.close();
    }
}

int main() {
    jlog_init(nullptr);
    nlohmann::json j;

    j.clear();
    // j["path"] = "output.pcm";
    // auto file_destination = std::make_shared<TestFileDestination>(j.dump());
    j["path"] = "wfcwx_hifi.pcm";
    auto file_source = std::make_shared<TestFileSource>(j.dump());
    j.clear();
    j["maxChn"] = 8;
    j["dev"] = dev;
    // auto audio_capturer = AudioFrameCapturer::CreateAudioCapturer("RKAudioFrameCapturer");
    auto audio_capturer = AudioFrameCapturer::CreateAudioCapturer("AudioSpec", j.dump());
    // audio_capturer->addAudioDestination(file_destination);
    file_source->addAudioDestination(audio_capturer);

    while (true)
    {
        usleep(10000);
    }
    
    return 0;
}