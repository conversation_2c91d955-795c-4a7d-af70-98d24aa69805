#ifndef ALSA_UTILS_H_
#define ALSA_UTILS_H_
#include <string>
#include <vector>
#include <alsa/asoundlib.h>

namespace panocom {
struct AlsaHWPcmDevice {
    int index;          // 业务索引
    int card;
    int device;
    std::string id;
    std::string name;
    std::string type;
    std::string hw_string;
    std::vector<unsigned int> sample_rates;
    std::vector<unsigned int> channels;
    std::vector<std::string> formats;
};

struct AlsaPcmDeviceInfo {
    int index;          // 业务索引
    std::string name;
    std::string desc;
    bool can_capture = false;
    bool can_playback = false;
};

class AlsaUtils {
public:
    static void printCardInfo(snd_ctl_card_info_t *info);
    static bool isDeviceAvailable(int cardId, const char *deviceName);
    static int openAlsaHandle(const std::string &name, snd_pcm_stream_t streamType, snd_pcm_t **handle);
    static void listDevices(snd_pcm_stream_t type);
    static void listAllDevices();
    static void listPcmDevices(snd_pcm_stream_t stream_type);
    static std::vector<AlsaHWPcmDevice> ListAlsaPcmDevices();
    static std::vector<AlsaHWPcmDevice> ListAlsaPcmDevices(snd_pcm_stream_t stream_type);
    static bool GetAlsaHWPcmDevice(snd_pcm_stream_t stream_type, AlsaHWPcmDevice &alsa_device);
    static std::vector<AlsaPcmDeviceInfo> GetAllAlsaPcmDevices();
    static bool supports_playback(const std::string &name);
    static bool supports_capture(const std::string &name);
};
} // namespace panocom
#endif