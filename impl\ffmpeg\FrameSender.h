#pragma once
#include <vector>
#include <thread>
#include <mutex>
#include <string>
#include "ShmVideoProc.h"
extern "C" {
#include <libavcodec/avcodec.h>
#ifdef WIN32
#else
#include <fcntl.h>
#include <unistd.h>
#include <drm/drm.h>
#include <va/va.h>
#include <va/va_drm.h>
#include <va/va_drmcommon.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <sys/msg.h>
#include <libavutil/hwcontext_vaapi.h>
#include <libavutil/hwcontext.h>
#endif // WIN 32
}
#ifdef WIN32
#else
class UnixDomainSocketProc
{
public:
	static UnixDomainSocketProc* Inst();
	static void Free();
	int GetSendSocket(uint8_t channel);
	void init(uint8_t maxChn, char* appName);
private:
	UnixDomainSocketProc() = default;
	~UnixDomainSocketProc();
	uint8_t m_maxChn = 0;
	bool m_isInit = false;
	std::vector<int> m_serverSocket;
	std::vector<int> m_clientSocket;
	std::mutex m_socketMutex;
	static UnixDomainSocketProc* m_obj;
};
#endif
class FrameSender
{
public:
	static FrameSender* Inst();
	static void Free();
	void init(uint8_t maxChn, uint32_t shmStartId, char* appName);
	bool sendFrame(const AVFrame* frame,uint8_t chn);

private:
	bool writeVideoToShm(const AVFrame* frame, uint8_t chn);
	ShmVideoProc* m_shmProc = nullptr;
	FrameSender();
	~FrameSender();
	static FrameSender* m_obj;
#ifdef WIN32
#else
	bool sendDmaBuf(const AVFrame* frame, uint8_t chn);
	bool exportVAAPIFrameToDMABuf(const AVFrame* frame, VADRMPRIMESurfaceDescriptor& prime_desc);
	bool sendFrameInfo(int socket, const VADRMPRIMESurfaceDescriptor& prime_desc, const AVFrame* frame);
	UnixDomainSocketProc* m_socketProc = nullptr;
#endif
};