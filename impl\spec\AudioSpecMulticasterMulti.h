// created by gyj 2024-6-19
#ifndef P_AudioSpecMulticasterMulti_h
#define P_AudioSpecMulticasterMulti_h

#include "FramePipeline.h"
#include <ptoolkit/bytebuffer.h>
#include <hv/EventLoopThread.h>
#include <malloc.h>
#include <stdio.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <arpa/inet.h>
#include <net/if.h>
#include <string.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <pthread.h>
#include <fcntl.h>
#include <stdint.h>
#include <unordered_map>
#include <fstream>
#include <chrono>
// #include <PNPcm/HDPcm.h>
#include <resampler/resampler.h>
#include <resampler/push_resampler.h>

namespace panocom
{
    // NOTE: SIP-AL使用
    class AudioSpecMulticasterMulti : public FramePipeline
    {
    public:
        AudioSpecMulticasterMulti(const std::string& jsonParams);
        ~AudioSpecMulticasterMulti() override;

        void onFrame(const std::shared_ptr<Frame>& frame) override;
        void start() override;
        void stop() override;
        int updateParam(const std::string& jsonParam) override;
    private:
        static void openDev(int dev);
        static void closeDev(int dev);
        void AdjustGain(int16_t* out_data, size_t out_len, int16_t* in_data, size_t in_len, int db);
    private:
        static std::string devName_;
        static int pcmFd_;
        static int maxChn_;
        static std::vector<int> chnState_;
        static std::mutex mutex_;
        int ptime_ = 20;
        bool block_ = false;
        int samplerate_ = 16000;
        int codec_;
        int dev_;
        bool renderOnly_ = false;
        int fmt_;
        int channel_;

        hv::EventLoopThread readThread_;
        hv::EventLoopThread writeThread_;
        static std::shared_ptr<AudioSpecMulticasterMulti> inst_;
        ByteBuffer bbuf_;
        FILE* sendf = nullptr;
        FILE* recvf = nullptr;

        bool isSetReadThreadAffinity = false;
        bool isSetWriteThreadAffinity = false;

#ifdef WEBRTC_RESAMPLE_ANOTHER
        nswebrtc::PushResampler<int16_t> resampler_;
#else
        nswebrtc::Resampler resampler_;
#endif       
        bool initResampler_ = false;
        int source_samplerate_;
        hv::EventLoopThread check_timeout_thread_;

        int debug_mode_;
        int ref_count;
        int capturing_;

        std::ofstream ofs_;
        int local_gain_;
        int remote_gain_;
        int gain_min_;
        int gain_max_;
        std::unordered_map<int, float> gain_table_;
        bool inner_gain_control_ = false;
        int16_t *write_buf_;
        uint32_t frame_to_playout_ = 0;
        std::chrono::steady_clock::time_point last_time_point_;
    };
}

#endif