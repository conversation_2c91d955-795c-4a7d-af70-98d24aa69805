#include "RKVideoFrameRender.h"

#include <sys/mman.h>
#include <unistd.h>

#include <drm/drm_fourcc.h>
#include <hv/hlog.h>

#include <rga/im2d.hpp>
#include <rga/rga.h>
#include <rockchip/mpp_frame.h>
// #include <osal/mpp_common.h>
#include <json.hpp>

#include "rk/rk_frame_buffer.h"
#include "Utils.h"
namespace panocom
{

namespace {
// 打表算了
std::unordered_map<std::string, RgaSURF_FORMAT> tab_s_rgapixel {
    { "nv12", RK_FORMAT_YCbCr_420_SP }, 
	{ "nv16", RK_FORMAT_YCbCr_422_SP }, 
	// {"nv24", RK_FORMAT_YCbCr_444_SP}, 
	{ "bgr3", RK_FORMAT_BGR_888 }
};

std::unordered_map<std::string, uint32_t> tab_s_drmpixel {
	{"nv12", DRM_FORMAT_NV12},
	{"nv16", DRM_FORMAT_NV16},
	{"nv24", DRM_FORMAT_NV24},
	{"bgr3", DRM_FORMAT_BGR888}  
};

std::vector<std::map<int, LayoutRect>> preset_layouts {
    {{0, LayoutRect(0.0f, 0.0f, 1.0f, 1.0f)}},
    {{0, LayoutRect(0.0f, 1.0f / 4, 1.0f / 2, 1.0f / 2)}, {1, LayoutRect(1.0f / 2, 1.0f / 4, 1.0f / 2, 1.0f / 2)}},
    {{0, LayoutRect(0.0f, 0.0f, 1.0f, 1.0f)}, {1, LayoutRect(0.0f / 4, 0.0f / 4, 1.0f / 4, 1.0f / 4)}},
    {{0, LayoutRect(0.0f, 0.0f, 1.0f, 1.0f)}, {1, LayoutRect(3.0f / 4, 0.0f / 4, 1.0f / 4, 1.0f / 4)}},
    {{0, LayoutRect(0.0f, 0.0f, 1.0f, 1.0f)}, {1, LayoutRect(3.0f / 4, 3.0f / 4, 1.0f / 4, 1.0f / 4)}},
    {{0, LayoutRect(0.0f, 0.0f, 1.0f, 1.0f)}, {1, LayoutRect(0.0f / 4, 3.0f / 4, 1.0f / 4, 1.0f / 4)}},
};

// int get_rga_format(OSDPixelFormat f) {
//   static std::unordered_map<OSDPixelFormat, int> rga_format_map = {
//       {OSD_PIX_FMT_YUV420P, RK_FORMAT_YCbCr_420_P},
//       {OSD_PIX_FMT_NV12, RK_FORMAT_YCbCr_420_SP},
//       {OSD_PIX_FMT_NV21, RK_FORMAT_YCrCb_420_SP},
//       {OSD_PIX_FMT_YUV422P, RK_FORMAT_YCbCr_422_P},
//       {OSD_PIX_FMT_NV16, RK_FORMAT_YCbCr_422_SP},
//       {OSD_PIX_FMT_NV61, RK_FORMAT_YCrCb_422_SP},
//       {OSD_PIX_FMT_YUYV422, RK_FORMAT_YUYV_422},
//       {OSD_PIX_FMT_UYVY422, RK_FORMAT_UYVY_422},
//       {OSD_PIX_FMT_RGB565, RK_FORMAT_RGB_565},
//       {OSD_PIX_FMT_BGR565, -1},
//       {OSD_PIX_FMT_RGB888, RK_FORMAT_BGR_888},
//       {OSD_PIX_FMT_BGR888, RK_FORMAT_RGB_888},
//       {OSD_PIX_FMT_ARGB8888, RK_FORMAT_BGRA_8888},
//       {OSD_PIX_FMT_ABGR8888, RK_FORMAT_RGBA_8888},
//       {OSD_PIX_FMT_BGRA8888, RK_FORMAT_BGRA_8888}};
//   auto it = rga_format_map.find(f);
//   if (it != rga_format_map.end())
//     return it->second;
//   return -1;
// }

// std::vector<OsdInfo> s_osds;

// IM_STATUS RGA_Rect_Fill(rga_buffer_t buf, RK_U32 x, RK_U32 y,
//                                RK_U32 width, RK_U32 height) {
//   im_rect rect_up = {x, y, width, height / 2};
//   im_rect rect_all = {x, y, width, height};
//   IM_STATUS STATUS = imfill(buf, rect_all, 0x00000000);
//   STATUS |= imfill(buf, rect_up, 0xff0000ff);
//   return STATUS;
// }


// TODO: DRY
bool CopyFrom(struct buffer_object &bo, MppFrame input, int32_t x, int32_t y, int32_t width, int32_t height, const std::string &src_format_str = "nv12", const std::string &dst_format_str="nv12") {
	MppBuffer srcbuffer = mpp_frame_get_buffer(input);
	if (!srcbuffer) return false;
    RK_U32 src_width = mpp_frame_get_width(input);
    RK_U32 src_height = mpp_frame_get_height(input);
    RK_U32 src_hor_stride = mpp_frame_get_hor_stride(input);
    RK_U32 src_ver_stride = mpp_frame_get_ver_stride(input);

    RK_U32 dst_width = bo.width;
    RK_U32 dst_height = bo.height;
    RK_U32 dst_hor_stride = MPP_ALIGN(dst_width, 16);
    RK_U32 dst_ver_stride = MPP_ALIGN(dst_height, 16);

    im_rect src_rect;
    im_rect dst_rect;
    rga_buffer_t src;
    rga_buffer_t dst;

    int src_fd = mpp_buffer_get_fd(srcbuffer);
	int dst_fd = bo.dst_fd;
	auto format = RK_FORMAT_YCbCr_420_SP;
	if (tab_s_rgapixel.count(src_format_str) != 0) format = tab_s_rgapixel[src_format_str];
    src = wrapbuffer_fd(src_fd, src_width, src_height, format, src_hor_stride, src_ver_stride);
	if (tab_s_rgapixel.count(dst_format_str) != 0) format = tab_s_rgapixel[dst_format_str];
	dst = wrapbuffer_fd(dst_fd, dst_width, dst_height, format, dst_hor_stride, dst_ver_stride);

    int usage = 0;
    IM_STATUS ret = IM_STATUS_NOERROR;

    im_opt_t opt { 0 };
    rga_buffer_t pat { 0 };
    im_rect srect { 0 };
    im_rect drect { 0 };
    im_rect prect { 0 };

    usage |= IM_SYNC;

    srect.width = src_width & ~1;
    srect.height = src_height & ~1;
    drect.x = x & ~1;
    drect.y = y & ~1;
    drect.width = width & ~1;
    drect.height = height & ~1;

    // jinfo("CopyFrom size(%d x %d)(%d x %d) to pos(%d, %d) size(%d x %d)(%d x %d)", src_hor_stride, src_ver_stride, srect.width, srect.height, drect.x, drect.y, drect.width, drect.height, dst_hor_stride, dst_ver_stride);

    ret = improcess(src, dst, pat, srect, drect, prect, -1, NULL, &opt, usage);
    // if (!s_osds.empty()) {
    //     rga_buffer_t temp { 0 };
    //     im_rect temp_rect { 0 };
    //     const uint32_t pat_width = 1920;
    //     const uint32_t pat_height = 1080;
    //     char *data = new char[pat_width*pat_height*4];
    //     memset(data, 128, pat_width*pat_height*4);

    //     pat = wrapbuffer_virtualaddr(data, pat_width, pat_height, RK_FORMAT_RGBA_8888);
    //     prect.x = 0;
    //     prect.y = 0;
    //     prect.width = 1280;
    //     prect.height = 720;
    //     // usage |= (IM_ALPHA_BLEND_DST_OVER);
    //     ret = improcess(pat, dst, temp, prect, drect, temp_rect, usage);

    //     // ret = imblend(dst, pat, usage);
    //     // ret = imcomposite(dst, pat, dst, usage);
    //     if (ret != IM_STATUS_SUCCESS) {
    //         jerror("improcess failed %d x %d, ret = %d", s_osds[0].w, s_osds[0].h, ret);
    //     }
    //     delete[] data;
    // }

    if (ret == IM_STATUS_SUCCESS)
        return true;
    jerror("RENDER: Failed to CopyFrom size(%d x %d)(%d x %d) to pos(%d, %d) size(%d x %d)(%d x %d)", src_hor_stride, src_ver_stride, srect.width, srect.height,  drect.x, drect.y, drect.width, drect.height, dst_hor_stride, dst_ver_stride);
	jerror("ret = %d", ret);
    return false;
}

// TODO: DRY
bool CopyFrom(struct buffer_object &bo, MppFrame input, const LayoutRect &src_rect, const std::string &src_format_str = "nv12", const std::string &dst_format_str="nv12") {
	MppBuffer srcbuffer = mpp_frame_get_buffer(input);
	if (!srcbuffer) return false;
    RK_U32 src_width = mpp_frame_get_width(input);
    RK_U32 src_height = mpp_frame_get_height(input);
    RK_U32 src_hor_stride = mpp_frame_get_hor_stride(input);
    RK_U32 src_ver_stride = mpp_frame_get_ver_stride(input);

    RK_U32 dst_width = bo.width;
    RK_U32 dst_height = bo.height;
    RK_U32 dst_hor_stride = MPP_ALIGN(dst_width, 16);
    RK_U32 dst_ver_stride = MPP_ALIGN(dst_height, 16);

    rga_buffer_t src;
    rga_buffer_t dst;

    int src_fd = mpp_buffer_get_fd(srcbuffer);
	int dst_fd = bo.dst_fd;
	auto format = RK_FORMAT_YCbCr_420_SP;
	if (tab_s_rgapixel.count(src_format_str) != 0) format = tab_s_rgapixel[src_format_str];
    src = wrapbuffer_fd(src_fd, src_width, src_height, format, src_hor_stride, src_ver_stride);
	if (tab_s_rgapixel.count(dst_format_str) != 0) format = tab_s_rgapixel[dst_format_str];
	dst = wrapbuffer_fd(dst_fd, dst_width, dst_height, format, dst_hor_stride, dst_ver_stride);

    int usage = 0;
    IM_STATUS ret = IM_STATUS_NOERROR;

    im_opt_t opt { 0 };
    rga_buffer_t pat { 0 };
    im_rect srect { 0 };
    im_rect drect { 0 };
    im_rect prect { 0 };

    usage |= IM_SYNC;

    if (src_rect.isCrop && src_rect.srcW > 0.0f && src_rect.srcH > 0.0f) {
        srect.x = (int)(src_width * src_rect.srcX) & ~1;
        srect.y = (int)(src_height * src_rect.srcY) & ~1;
        srect.width = (int)(src_width * src_rect.srcW) & ~1;
        srect.height = (int)(src_height * src_rect.srcH) & ~1;
        if (srect.x + srect.width > src_width || srect.y + srect.height > src_height) {
            jerror("RENDER: src rect(%dx%d), pos(%d,%d) is out of src size(%d x %d)", srect.width, srect.height, srect.x, srect.y, src_width, src_height);
            return false;
        }
    }
    else {
        srect.x = 0;
        srect.y = 0;
        srect.width = src_width & ~1;
        srect.height = src_height & ~1;
    }
    drect.x = (int)(dst_width * src_rect.x) & ~1;
    drect.y = (int)(dst_height * src_rect.y) & ~1;
    drect.width = (int)(dst_width * src_rect.w) & ~1;
    drect.height = (int)(dst_height * src_rect.h) & ~1;
    if (drect.x + drect.width > dst_width || drect.y + drect.height > dst_height) {
        jerror("RENDER: dst rect(%dx%d), pos(%d,%d) is out of dst size(%d x %d)", drect.width, drect.height, drect.x, drect.y, dst_width, dst_height);
        return false;
    }

    ret = improcess(src, dst, pat, srect, drect, prect, -1, NULL, &opt, usage);
    if (ret == IM_STATUS_SUCCESS)
        return true;
    jerror("RENDER: Failed to CopyFrom size(%d x %d)(%d x %d) to pos(%d, %d) size(%d x %d)(%d x %d)", src_hor_stride, src_ver_stride, srect.width, srect.height,  drect.x, drect.y, drect.width, drect.height, dst_hor_stride, dst_ver_stride);
	jerror("ret = %d", ret);
    return false;
}

std::mutex s_plane_ids_mutex;
std::unordered_map<int, int> s_plane_ids; // 存储正在使用的plane_id
std::unordered_map<int, bool> s_running;
// TODO: 多fd
struct CustomRes
{
    int connector_id;
    int crtc_id;
    int encoder_id;
    int crtc_index;
    int width;
    int height;
    int fps;
    int64_t renderGap;
    bool interlace;
    int ref;
    drmModeModeInfo mode;
};
std::unordered_map<int, CustomRes> s_custom_res;    // 正在使用的设备计数
std::mutex s_res_mutex;

bool GetCustomRes(int fd, int dev, int width, int height, int fps, bool interlace) {
    if (s_custom_res.count(dev) != 0) {
        return true;
    }
    drmModeRes *res = drmModeGetResources(fd);
    if (res) {
        jinfo("count_connectors = %d, count_crtcs = %d\n", res->count_connectors, res->count_crtcs);
    } else {
        jerror("failed to query Drm Mode resources: %s", strerror(errno));
        return false;
    }
    if (res && res->count_connectors <= 0) {
        jerror("No connector found");
        drmModeFreeResources(res);
        return false;
    }
    bool has_connector = false;
    bool connector_found = false;
    uint32_t refresh_hz = fps;
    // uint32_t refresh_hz = fps_;
    int con_id, encoder_id, crtc_id, crtc_index;
    drmModeModeInfo applied_mode;
    for (int i = 0; i < res->count_connectors; ++i) {
        drmModeConnectorPtr connector = drmModeGetConnector(fd, res->connectors[i]);
        if (connector && connector->connection == DRM_MODE_CONNECTED) {
            con_id = res->connectors[i];
            encoder_id = res->encoders[i];
            crtc_id = res->crtcs[i];
            crtc_index = i;
            // mode_ = *connector->modes;
            applied_mode = connector->modes[0];
            for (int j = 0; j < connector->count_modes; j++) {
                const auto &mode = connector->modes[j];
                if (std::string(mode.name).back() == 'i' && !interlace)
                    continue;
                if (std::string(mode.name).back() != 'i' && interlace)
                    continue;
                if (mode.hdisplay != width || mode.vdisplay != height || mode.vrefresh != refresh_hz)
                    continue;
                jinfo(
                    "modes[%d]: %s, refresh: %d, h: %d, v: %d, htotal: %d, vtotal: %d, hskew: %d, vscan: %d, flags: %d, type: %d", j, mode.name, mode.vrefresh,
                    mode.hdisplay, mode.vdisplay, mode.htotal, mode.vtotal, mode.hskew, mode.vscan, mode.flags, mode.type);
                applied_mode = mode;
                break;
            }
            has_connector = true;
            jinfo("con_id: %d; crtc_id(used): %d; encoder_id: %d, crtc_index_: %d; mode: %s, dev: %d", con_id, crtc_id, encoder_id, i, applied_mode.name, dev);
            if (i == dev) {
                connector_found = true;
                drmModeFreeConnector(connector);
                break; //  选择connector
            }
        }
        drmModeFreeConnector(connector);
    }
    if (has_connector && (applied_mode.hdisplay != width || applied_mode.vdisplay != height)) {
        jinfo("resolution changed: %dx%d ==> %dx%d", width, height, applied_mode.hdisplay, applied_mode.vdisplay);
        // width = mode.hdisplay;
        // height = mode.vdisplay;
    }
    refresh_hz = applied_mode.vrefresh;
    if (!connector_found) {
        jerror("GetConnector has no connector %d", dev);
        drmModeFreeResources(res);
        return false;
    }
    if (!has_connector) {
        // jerror("drmModeGetConnector failed: %s", strerror(errno));
        drmModeFreeResources(res);
        return false;
    }
    s_custom_res[dev].connector_id = con_id;
    s_custom_res[dev].crtc_id = crtc_id;
    s_custom_res[dev].encoder_id = encoder_id;
    s_custom_res[dev].crtc_index = crtc_index;
    s_custom_res[dev].width = applied_mode.hdisplay;
    s_custom_res[dev].height = applied_mode.vdisplay;
    s_custom_res[dev].fps = refresh_hz;
    s_custom_res[dev].renderGap = 1000000 / refresh_hz;
    s_custom_res[dev].interlace = interlace;
    s_custom_res[dev].mode = applied_mode;
    drmModeFreeResources(res);
    return true;
}

bool TryInitResources(int fd, int dev, int width, int height, int fps, bool interlace) {
    {
        std::lock_guard<std::mutex> lock(s_res_mutex);
        if (s_custom_res.count(dev) != 0) {
            s_custom_res[dev].ref++;
            return true;
        }
        if (GetCustomRes(fd, dev, width, height, fps, interlace)) {
            s_custom_res[dev].ref++;
        } else {
            return false;
        }
        int s32ret = drmSetClientCap(fd, DRM_CLIENT_CAP_ATOMIC, 1);
        if (s32ret != 0)
            jinfo("drmSetClientCap failed, ret = %d", s32ret);
        s32ret = drmSetClientCap(fd, DRM_CLIENT_CAP_UNIVERSAL_PLANES, 1);
        if (s32ret != 0)
            jerror("drmSetClientCap failed, ret = %d", s32ret);

        drmModeObjectProperties *props;
        drmModeAtomicReq *req;
        uint32_t blob_id;
        uint32_t property_mode_id;
        uint32_t property_active;
        uint32_t property_crtc_id;
        uint32_t property_bus_format;

        // get connector properties
        props = drmModeObjectGetProperties(fd, s_custom_res[dev].connector_id, DRM_MODE_OBJECT_CONNECTOR);
        property_crtc_id = RKVideoFrameRender::get_property_id(fd, props, "CRTC_ID");
        property_bus_format = RKVideoFrameRender::get_property_id(fd, props, "color_format");
        drmModeFreeObjectProperties(props);
        // get crtc properties
        props = drmModeObjectGetProperties(fd, s_custom_res[dev].crtc_id, DRM_MODE_OBJECT_CRTC);
        property_active = RKVideoFrameRender::get_property_id(fd, props, "ACTIVE");
        property_mode_id = RKVideoFrameRender::get_property_id(fd, props, "MODE_ID");
        drmModeFreeObjectProperties(props);
        jinfo(
            "property_crtc_id = %d, property_active = %d, property_mode_id = %d property_bus_format = %d", property_crtc_id, property_active, property_mode_id,
            property_bus_format);
        // create blob to store current mode, and return the blob id
        drmModeCreatePropertyBlob(fd, &s_custom_res[dev].mode, sizeof(drmModeModeInfo), &blob_id);
        jinfo("conn->modes[0] = %s", s_custom_res[dev].mode.name);

        // start modeseting
        printf("------------------------------%d\n", __LINE__);
        req = drmModeAtomicAlloc();
        drmModeAtomicAddProperty(req, s_custom_res[dev].crtc_id, property_active, 1);
        drmModeAtomicAddProperty(req, s_custom_res[dev].crtc_id, property_mode_id, blob_id);
        drmModeAtomicAddProperty(req, s_custom_res[dev].connector_id, property_crtc_id, s_custom_res[dev].crtc_id);
        drmModeAtomicCommit(fd, req, DRM_MODE_ATOMIC_ALLOW_MODESET, NULL);
        drmModeDestroyPropertyBlob(fd, blob_id);
        drmModeAtomicFree(req);
        printf("------------------------------%d\n", __LINE__);
    }
    return true;
}

bool TryReleaseResources(int fd, int dev) {
    {
        std::lock_guard<std::mutex> lock(s_res_mutex);
        if (s_custom_res.count(dev) != 0) {
            s_custom_res[dev].ref--;
            if (s_custom_res[dev].ref <= 0) s_custom_res.erase(dev);
        } else {
            jerror("dev[%d] has not been inited!");
            return false;
        }
    }
    return true;
}

}

RKVideoFrameRender::RKVideoFrameRender(int fd, const std::string &jsonParams)
    : running_(false)
    , fd_(fd)
    , crtc_index_(0)
    , dev_(0)
    , crtc_id_(0)
    , con_id_(0)
    , encoder_id_(0)
    , plane_id_(0)
    , format_str_("nv12")
    , width_(1920)
    , height_(1080)
    , fps_(30)
    , interlace_(false)
    , first_frame_(true)
    // , library_(nullptr)
    // , face_(nullptr)
    , pip_(false) {
    name_ = "RKVideoRender";
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    if (j.contains("dev")) {
        dev_ = j["dev"];
    }

    if (j.contains("crtc_id")) {
        crtc_id_ = j["crtc_id"];
    }
    if (j.contains("encoder_id")) {
        encoder_id_ = j["encoder_id"];
    }

    if (j.contains("fps")) {
        fps_ = j["fps"];
    }
    if (j.contains("format")) {
        format_str_ = j["format"];
    }

    if (j.contains("width")) {
        width_ = j["width"];
    }
    if (j.contains("height")) {
        height_ = j["height"];
    }
    if (j.contains("pip")) {
        pip_ = j["pip"];
    }
    crtc_rect_.width = width_;
    crtc_rect_.height = height_;
	crtc_rect_.left = 0;
	crtc_rect_.top = 0;
	if (j.contains("y")) crtc_rect_.top = j["y"];
	if (j.contains("x")) crtc_rect_.left = j["x"];
	if (j.contains("plane_width")) crtc_rect_.width = j["plane_width"];
	if (j.contains("plane_height")) crtc_rect_.height = j["plane_height"];
    if (j.contains("z")) crtc_rect_.zpos = j["z"];
	format_ = DRM_FORMAT_NV12;
	if (tab_s_drmpixel.count(format_str_) != 0) format_ = tab_s_drmpixel[format_str_];

	render_time_ = std::chrono::steady_clock::now();

	// TODO：合屏布局
	// {
	// 	Region region;
	// 	region.id = 0;
	// 	region.rect.x = 0;
	// 	region.rect.y = 0;
	// 	region.rect.w = width_;
	// 	region.rect.h = height_;
	// 	video_layout_.push_back(region);
	// }

    // if (j.contains("videoLayout") && j["videoLayout"].is_array())
    // {
	// 	video_layout_.clear();
    //     for (uint32_t i = 0; i < j["videoLayout"].size(); ++i)
    //     {
    //         if (j["videoLayout"][i].contains("id") &&
    //             j["videoLayout"][i].contains("rect") &&
    //             j["videoLayout"][i]["rect"].contains("x") &&
    //             j["videoLayout"][i]["rect"].contains("y") &&
    //             j["videoLayout"][i]["rect"].contains("w") &&
    //             j["videoLayout"][i]["rect"].contains("h"))
    //         {
    //             Region region;
    //             region.id = j["videoLayout"][i]["id"];
    //             region.rect.x = j["videoLayout"][i]["rect"]["x"];
    //             region.rect.y = j["videoLayout"][i]["rect"]["y"];
    //             region.rect.w = j["videoLayout"][i]["rect"]["w"];
    //             region.rect.h = j["videoLayout"][i]["rect"]["h"];
    //             LOGI("video layout id = %d x = %d y= %d w = %d h = %d", region.id, region.rect.x, region.rect.y, region.rect.w, region.rect.h);
    //             video_layout_.push_back(region);
    //         }
    //     }
    // }

#if USE_MULTI_PLANES
    InitMulti();
#elif USE_SINGLE_PLANE
    updateLayout(preset_layouts[0]);
	InitSingle();
#else
	Init();
#endif
}

RKVideoFrameRender::~RKVideoFrameRender()
{
	jinfo("RKVideoFrameRender release!");
	running_ = false;
	displaying_ = false;
	cv_.notify_all();
	if (display_future_.valid()) {
		auto status = display_future_.wait_for(std::chrono::milliseconds(3000));
		if (status == std::future_status::timeout) {
			 jerror("RK video render stop timeout");
		} else {
			bool res = display_future_.get();
            jinfo("RK video render stop status: %d", res);
		}
	}
	while (!frame_queue_.empty())
	{
		frame_queue_.pop();
	}
    if (check_timeout_thread_.isRunning()) {
        check_timeout_thread_.stop(true);
        check_timeout_thread_.join();
    }
#if USE_MULTI_PLANES
    for (auto &kv: plane_layouts_) {
        auto &plane_layout = kv.second;
        if (plane_layout.plane_id >= 0) {
            std::lock_guard<std::mutex> lock(s_plane_ids_mutex);
            if (s_plane_ids.count(plane_layout.plane_id) > 0) {
                s_plane_ids.erase(plane_layout.plane_id);
            }
        }
    }
    plane_layouts_.clear();
#else
    if (s_plane_ids.count(plane_id_) > 0) {
        s_plane_ids.erase(plane_id_);
    }
#endif
    // DestoryFont();
	jinfo("RKVideoFrameRender release success!");
}

bool RKVideoFrameRender::Init() {
    // CreateFont("/usr/share/fonts/dejavu/DejaVuSans-Bold.ttf", 48);
	running_ = true;
    display_future_ = std::async([this]() -> bool {
        drmModeRes *resource = drmModeGetResources(fd_);
        if (resource) {
            jinfo("count_connectors = %d, count_crtcs = %d\n", resource->count_connectors, resource->count_crtcs);
        } else {
            jerror("failed to query Drm Mode resources: %s", strerror(errno));
            return false;
        }

        int s32ret = drmSetClientCap(fd_, DRM_CLIENT_CAP_ATOMIC, 1);
        if (s32ret != 0)
            jinfo("drmSetClientCap failed, ret = %d", s32ret);
        s32ret = drmSetClientCap(fd_, DRM_CLIENT_CAP_UNIVERSAL_PLANES, 1);
        if (s32ret != 0)
            jerror("drmSetClientCap failed, ret = %d", s32ret);
        bool ret = GetConnector(resource);
        if (!ret) {
			bool has_connector = false;
            // TODO: 检测connector连接
			while (!has_connector && fd_ != -1 && running_) {
				has_connector = GetConnector(resource);
				if (!has_connector) usleep(2000000);
			}
            if (!has_connector || fd_ == -1 || !running_) {
                jerror("Failed to get connector.");
                drmModeFreeResources(resource);
                return false;
            }
        }
        crtc_rect_.width = width_;
        crtc_rect_.height = height_;
        // TODO：布局
        {
            Region region;
            region.id = 0;
            region.rect.x = 0;
            region.rect.y = 0;
            region.rect.w = width_;
            region.rect.h = height_;
            video_layout_.push_back(region);
        }

        drmModeFreeResources(resource);
        ret = GetPlane();
        if (!ret) {
            jerror("failed to get plane with required format %s", strerror(errno));
            return false;
        }

        for (int i = 0; i < 2; i++) {
            bo_[i].width = width_;
            bo_[i].height = height_;
            bo_[i].format = format_;
            DrmUtils::modeset_create_fb2(fd_, &bo_[i], true);
        }
        if (pip_) {
            ret = GetPipPlane();
            if (!ret) {
                jerror("failed to get pip plane with required format %s", strerror(errno));
                // return false;
            }
            for (int i = 0; i < 2; i++) {
                pip_bo_[i].width = width_;
                pip_bo_[i].height = height_;
                pip_bo_[i].format = format_;
                DrmUtils::modeset_create_fb2(fd_, &pip_bo_[i], true);
            }            
        }
        drmModeObjectProperties *props;
        drmModeAtomicReq *req;
        uint32_t blob_id;
        // uint32_t property_crtc_id;
        uint32_t property_mode_id;
        uint32_t property_active;
        uint32_t property_crtc_id;
        uint32_t property_bus_format;
        uint32_t property_fb_id;
        uint32_t property_pip_fb_id;
        uint32_t property_crtc_x;
        uint32_t property_crtc_y;
        uint32_t property_crtc_w;
        uint32_t property_crtc_h;
        uint32_t property_src_x;
        uint32_t property_src_y;
        uint32_t property_src_w;
        uint32_t property_src_h;
        uint32_t property_zpos;
        // get connector properties
        props = drmModeObjectGetProperties(fd_, con_id_, DRM_MODE_OBJECT_CONNECTOR);
        property_crtc_id = get_property_id(fd_, props, "CRTC_ID");
        property_bus_format = get_property_id(fd_, props, "color_format");
        drmModeFreeObjectProperties(props);
        // get crtc properties
        props = drmModeObjectGetProperties(fd_, crtc_id_, DRM_MODE_OBJECT_CRTC);
        property_active = get_property_id(fd_, props, "ACTIVE");
        property_mode_id = get_property_id(fd_, props, "MODE_ID");
        drmModeFreeObjectProperties(props);
        jinfo(
            "property_crtc_id = %d, property_active = %d, property_mode_id = %d property_bus_format = %d", property_crtc_id, property_active, property_mode_id,
            property_bus_format);
        // create blob to store current mode, and return the blob id
        drmModeCreatePropertyBlob(fd_, &mode_, sizeof(mode_), &blob_id);
        jinfo("conn->modes[0] = %s", mode_.name);

        // start modeseting
        printf("------------------------------%d\n", __LINE__);
        req = drmModeAtomicAlloc();
        drmModeAtomicAddProperty(req, crtc_id_, property_active, 1);
        drmModeAtomicAddProperty(req, crtc_id_, property_mode_id, blob_id);
        drmModeAtomicAddProperty(req, con_id_, property_crtc_id, crtc_id_);
        drmModeAtomicCommit(fd_, req, DRM_MODE_ATOMIC_ALLOW_MODESET, NULL);
        drmModeDestroyPropertyBlob(fd_, blob_id);
        drmModeAtomicFree(req);
        printf("------------------------------%d\n", __LINE__);

        req = drmModeAtomicAlloc();
        // rgb=0 ycbcr444=1 ycbcr422=2 ycbcr420=3 ycbcr_high_subsampling=4 ycbcr_low_subsampling=5 invalid_output=6
        drmModeAtomicAddProperty(req, con_id_, property_bus_format, 2);
        drmModeAtomicCommit(fd_, req, DRM_MODE_ATOMIC_ALLOW_MODESET, NULL);
        drmModeAtomicFree(req);
        printf("------------------------------%d\n", __LINE__);

        // get plane properties
        props = drmModeObjectGetProperties(fd_, plane_id_, DRM_MODE_OBJECT_PLANE);
        property_crtc_id = get_property_id(fd_, props, "CRTC_ID");
        property_fb_id = get_property_id(fd_, props, "FB_ID");
        property_crtc_x = get_property_id(fd_, props, "CRTC_X");
        property_crtc_y = get_property_id(fd_, props, "CRTC_Y");
        property_crtc_w = get_property_id(fd_, props, "CRTC_W");
        property_crtc_h = get_property_id(fd_, props, "CRTC_H");
        property_src_x = get_property_id(fd_, props, "SRC_X");
        property_src_y = get_property_id(fd_, props, "SRC_Y");
        property_src_w = get_property_id(fd_, props, "SRC_W");
        property_src_h = get_property_id(fd_, props, "SRC_H");
        property_zpos = get_property_id(fd_, props, "zpos");
        jinfo("type = %d zpos %d", get_property_value(fd_, props, "type"), get_property_value(fd_, props, "zpos"));
        drmModeFreeObjectProperties(props);

        req = drmModeAtomicAlloc();
        drmModeAtomicAddProperty(req, plane_id_, property_fb_id, bo_[0].fb_id);
        drmModeAtomicAddProperty(req, plane_id_, property_crtc_id, crtc_id_);
        drmModeAtomicAddProperty(req, plane_id_, property_crtc_x, crtc_rect_.left);
        drmModeAtomicAddProperty(req, plane_id_, property_crtc_y, crtc_rect_.top);
        drmModeAtomicAddProperty(req, plane_id_, property_crtc_w, crtc_rect_.width);
        drmModeAtomicAddProperty(req, plane_id_, property_crtc_h, crtc_rect_.height);
        drmModeAtomicAddProperty(req, plane_id_, property_src_w, width_ << 16);
        drmModeAtomicAddProperty(req, plane_id_, property_src_h, height_ << 16);
        drmModeAtomicAddProperty(req, plane_id_, property_src_x, 0);
        drmModeAtomicAddProperty(req, plane_id_, property_src_y, 0);
        drmModeAtomicCommit(fd_, req, 0, NULL);
        drmModeAtomicFree(req);
        if (pip_) {
            // get plane properties
            props = drmModeObjectGetProperties(fd_, pip_plane_id_, DRM_MODE_OBJECT_PLANE);
            property_crtc_id = get_property_id(fd_, props, "CRTC_ID");
            property_pip_fb_id = get_property_id(fd_, props, "FB_ID");
            property_crtc_x = get_property_id(fd_, props, "CRTC_X");
            property_crtc_y = get_property_id(fd_, props, "CRTC_Y");
            property_crtc_w = get_property_id(fd_, props, "CRTC_W");
            property_crtc_h = get_property_id(fd_, props, "CRTC_H");
            property_src_x = get_property_id(fd_, props, "SRC_X");
            property_src_y = get_property_id(fd_, props, "SRC_Y");
            property_src_w = get_property_id(fd_, props, "SRC_W");
            property_src_h = get_property_id(fd_, props, "SRC_H");
            property_zpos = get_property_id(fd_, props, "zpos");
            jinfo("type = %d zpos %d", get_property_value(fd_, props, "type"), get_property_value(fd_, props, "zpos"));
            drmModeFreeObjectProperties(props);

            req = drmModeAtomicAlloc();
            drmModeAtomicAddProperty(req, pip_plane_id_, property_pip_fb_id, pip_bo_[0].fb_id);
            drmModeAtomicAddProperty(req, pip_plane_id_, property_crtc_id, crtc_id_);
            drmModeAtomicAddProperty(req, pip_plane_id_, property_crtc_x, crtc_rect_.width * 3 / 4);
            drmModeAtomicAddProperty(req, pip_plane_id_, property_crtc_y, crtc_rect_.height * 3 / 4);
            drmModeAtomicAddProperty(req, pip_plane_id_, property_crtc_w, crtc_rect_.width >> 2);
            drmModeAtomicAddProperty(req, pip_plane_id_, property_crtc_h, crtc_rect_.height >> 2);
            drmModeAtomicAddProperty(req, pip_plane_id_, property_src_w, width_ << 16);
            drmModeAtomicAddProperty(req, pip_plane_id_, property_src_h, height_ << 16);
            drmModeAtomicAddProperty(req, pip_plane_id_, property_src_x, 0);
            drmModeAtomicAddProperty(req, pip_plane_id_, property_src_y, 0);
            drmModeAtomicAddProperty(req, pip_plane_id_, property_zpos, 1);
            drmModeAtomicCommit(fd_, req, 0, NULL);
            drmModeAtomicFree(req);            
        }

        /* Configure the current thread to use only RGA3_core0 or RGA3_core1. */
        imconfig(IM_CONFIG_SCHEDULER_CORE, IM_SCHEDULER_RGA3_CORE0 | IM_SCHEDULER_RGA3_CORE1);

        displaying_ = true;
        auto pid = getThreadLWP();
        jinfo("[%d]Init RKVideoFrameRender success!", pid);
        ResetStatistics();
        int index = 0, pip_index = 0;
        auto last = std::chrono::steady_clock::now();
        bool first = true;
        while (running_) {
            // auto& bo = bo_[index ^ 1];
            auto now = std::chrono::steady_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(now - last).count();
            last = now;
            if (duration > renderGap_ && !first) continue;
            first = false;
            std::shared_ptr<Frame> frame;
            std::shared_ptr<Frame> local_frame;
            // if (!pip_)
            // {
            //     std::unique_lock<std::mutex> lock(mutex_);
            //     if (running_ && frame_queue_.empty()) {
            //         cv_.wait(lock, [this] { return !running_ || !frame_queue_.empty(); });
            //     }
            //     if (!running_)
            //         break;
            //     frame = frame_queue_.front();
            //     frame_queue_.pop();
            //     frames_to_render_++;
            // } else {
            //     std::unique_lock<std::mutex> lock(mutex_);
            //     if (running_ && frame_queue_.empty()) {
            //         cv_.wait(lock, [this] { return !running_ || !frame_queue_.empty(); });
            //     }
            //     if (!running_)
            //         break;
            //     if (!frame_queue_.empty()) {
            //         frame = frame_queue_.front();
            //         frame_queue_.pop();
            //         frames_to_render_++;
            //     }
            //     if (!pip_frame_queue_.empty()) {
            //         local_frame = pip_frame_queue_.front();
            //         pip_frame_queue_.pop();
            //         local_frames_to_render_++;
            //     }
            // }
            {
                std::unique_lock<std::mutex> lock(mutex_);
                if (!frame_queue_.empty()) {
                    frame = frame_queue_.front();
                    frame_queue_.pop();
                    frames_to_render_++;
                }
                if (!pip_frame_queue_.empty()) {
                    local_frame = pip_frame_queue_.front();
                    pip_frame_queue_.pop();
                    local_frames_to_render_++;
                }
            }

            LogStatistics();
            std::shared_ptr<WrapRKMppFrame> wrap_frame;
            std::shared_ptr<WrapRKMppFrame> wrap_local_frame;
            if (frame && frame->getFrameFormat() == FRAME_FORMAT_RK) {
                wrap_frame = std::dynamic_pointer_cast<WrapRKMppFrame>(frame);
            }
            if (local_frame && local_frame->getFrameFormat() == FRAME_FORMAT_RK) {
                wrap_local_frame = std::dynamic_pointer_cast<WrapRKMppFrame>(local_frame);
            }
            // int groupId = frame->getGroupId();
            bool res1 = false;
            bool res2 = false;
            {
                std::lock_guard<std::mutex> lock(layout_mutex_);
                for (const auto &region : video_layout_) {
                    // MppFrame rk_frame = wrap_frame->InnerFrame();
                    // bool res = CopyFrom(bo_[index], rk_frame, region.rect.x, region.rect.y, region.rect.w, region.rect.h, format_str_);
                    // if (!res)
                    //     jerror("Failed to copy frame");
                    auto req = drmModeAtomicAlloc();
                    drmModeAtomicAddProperty(req, plane_id_, property_fb_id, bo_[index].fb_id);
                    if (pip_)
                        drmModeAtomicAddProperty(req, pip_plane_id_, property_pip_fb_id, pip_bo_[pip_index].fb_id);
                    drmModeAtomicCommit(fd_, req, 0, NULL);
                    drmModeAtomicFree(req);
                    if (wrap_frame) {
                        MppFrame rk_frame = wrap_frame->InnerFrame();
                        bool res = CopyFrom(bo_[index ^ 1], rk_frame, region.rect.x, region.rect.y, region.rect.w, region.rect.h, wrap_frame->format_str_, format_str_);
                        if (!res) 
                            jerror("Failed to copy frame");
                        else res1 = true;
                    }
                    if (pip_ && wrap_local_frame) {
                        MppFrame rk_frame = wrap_local_frame->InnerFrame();
                        bool res = CopyFrom(pip_bo_[pip_index ^ 1], rk_frame, region.rect.x, region.rect.y, region.rect.w, region.rect.h, wrap_local_frame->format_str_, format_str_);
                        if (!res)
                            jerror("Failed to copy frame [PIP]");
                        else res2 = true;
                    }
                }
            }
            if (wrap_frame && res1) index ^= 1;
            if (wrap_local_frame && res2) pip_index ^= 1;
        }
        for (int i = 0; i < 2; ++i)
            DrmUtils::modeset_destroy_fb(fd_, &bo_[i]);
        if (pip_) {
            for (int i = 0; i < 2; ++i)
                DrmUtils::modeset_destroy_fb(fd_, &pip_bo_[i]);
        }
		return true;
    });
    return true;
}

bool RKVideoFrameRender::GetCrtc(drmModeRes *res) {
	crtc_index_ = -1;
	drmModeEncoderPtr encoder = drmModeGetEncoder(fd_, encoder_id_);
	if (!encoder) {
		jerror("drmModeGetEncoder failed: %s", strerror(errno));
		return false;
	}
    crtc_id_ = encoder->crtc_id;
    drmModeFreeEncoder(encoder);
    for (int i = 0; i < res->count_crtcs; i++) {
        if (crtc_id_ == res->crtcs[i]) {
            crtc_index_ = i;
            break;
        }
    }
	if (crtc_index_ == -1) {
		jerror("CRTC %d not found encoder_id %d", crtc_id_, encoder_id_);
		return false;
	}
	return true;
}
bool RKVideoFrameRender::GetConnector(drmModeRes *res) {
	if (res && res->count_connectors <= 0) {
		jerror("No connector found");
		return false;
	}
	bool has_connector = false;
    bool connector_found = false;
	// uint32_t refresh_hz = 60;
	uint32_t refresh_hz = fps_;
    for(int i = 0; i < res->count_connectors; ++i) {
        drmModeConnectorPtr connector = drmModeGetConnector(fd_, res->connectors[i]);
        if(connector && connector->connection == DRM_MODE_CONNECTED) {
            con_id_ = res->connectors[i];
            encoder_id_ = res->encoders[i];
			crtc_id_ = res->crtcs[i];
			crtc_index_ = i;
            // mode_ = *connector->modes;
			mode_ = connector->modes[0];
            first_mode_ = connector->modes[0];
            for (int j = 0; j < connector->count_modes; j++) {
				const auto& mode = connector->modes[j];
				if (std::string(mode.name).back() == 'i' && !interlace_) continue;
				if (std::string(mode.name).back() != 'i' && interlace_) continue;
				if (mode.hdisplay != width_ || mode.vdisplay != height_ || mode.vrefresh != refresh_hz) continue;
                jinfo(
                    "modes[%d]: %s, refresh: %d, h: %d, v: %d, htotal: %d, vtotal: %d, hskew: %d, vscan: %d, flags: %d, type: %d", j, mode.name, mode.vrefresh,
                    mode.hdisplay, mode.vdisplay, mode.htotal, mode.vtotal, mode.hskew, mode.vscan, mode.flags, mode.type);
				mode_ = mode;
				break;
            }
			has_connector = true;
			jinfo("con_id: %d; crtc_id(used): %d; encoder_id: %d, crtc_index_: %d; mode: %s, dev: %d", con_id_, crtc_id_, encoder_id_, i, mode_.name, dev_);
			if (i == dev_) {
                connector_found = true;
				drmModeFreeConnector(connector);
				break;		//  选择connector
			}
        }
        drmModeFreeConnector(connector);
    }
    if (has_connector && (mode_.hdisplay != width_ || mode_.vdisplay != height_)) {
        jinfo("resolution changed: %dx%d ==> %dx%d", width_, height_, mode_.hdisplay, mode_.vdisplay);
        width_ = mode_.hdisplay;
        height_ = mode_.vdisplay;
    }
    refresh_hz = mode_.vrefresh;
	renderGap_ = 1000000 / refresh_hz;	// us
    if (!connector_found) {
        jerror("GetConnector has no connector %d", dev_);
        return false;
    }
	if (!has_connector) {
		// jerror("drmModeGetConnector failed: %s", strerror(errno));
		return false;
	}
	return true;
}

bool RKVideoFrameRender::GetPlane() {
    std::lock_guard<std::mutex> lock(s_plane_ids_mutex);
    drmModePlaneResPtr planes = drmModeGetPlaneResources(fd_);
    if (!planes) {
        jerror("failed to query planes: %s", strerror(errno));
        return false;
    }
    drmModePlanePtr plane = nullptr;
	// 优先使用对应crtc已占用的plane
	// TODO: 适配多个图层
	for (uint32_t i = 0; i < planes->count_planes; i++) {
		plane = drmModeGetPlane(fd_, planes->planes[i]);
        if (!plane) {
            jerror("failed to query plane %d: %s", i, strerror(errno));
            continue;
        }
        if (s_plane_ids.count(plane->plane_id) != 0) {
            if (plane) {
                drmModeFreePlane(plane);
                plane = nullptr;
            }
            continue;            
        }
		if (plane->crtc_id == crtc_id_) {
			for (uint32_t j = 0; j < plane->count_formats; j++) {
				jinfo("---------------------%d<-->%d plane_id_ = %d\n", plane->formats[j], format_, plane->plane_id);
				// found a plane matching the requested format
				if (plane->formats[j] == format_) {
					plane_id_ = plane->plane_id;
					jinfo("plane_id = %d, crtc_id(not used) = %d, i = %d", plane->plane_id, plane->crtc_id, i);
					drmModeFreePlane(plane);
					drmModeFreePlaneResources(planes);
					plane = nullptr;
                    s_plane_ids[plane_id_] = dev_;
					return true;
				}
			}
		}
		if (plane) {
            drmModeFreePlane(plane);
            plane = nullptr;
        }
	}

    for (uint32_t i = 0; i < planes->count_planes; i++) {
        if (plane) {
            drmModeFreePlane(plane);
            plane = nullptr;
        }
        plane = drmModeGetPlane(fd_, planes->planes[i]);
        if (!plane) {
            jerror("failed to query plane %d: %s", i, strerror(errno));
            continue;
        }

        if (!(plane->possible_crtcs & (1 << crtc_index_)) || 
            (plane->crtc_id != 0 && plane->crtc_id != crtc_id_) || 
            s_plane_ids.count(plane->plane_id) != 0) {
            continue;
        }

        if (plane->crtc_id != 0 && plane->crtc_id != crtc_id_)
            continue;
        if (s_plane_ids.count(plane->plane_id) != 0) continue;
        for (uint32_t j = 0; j < plane->count_formats; j++) {
			jinfo("---------------------%d<-->%d plane_id_ = %d\n", plane->formats[j], format_, plane->plane_id);
            // found a plane matching the requested format
            if (plane->formats[j] == format_) {
                plane_id_ = plane->plane_id;
                jinfo("plane_id = %d, crtc_id(not used) = %d, i = %d", plane->plane_id, plane->crtc_id, i);
                drmModeFreePlane(plane);
                drmModeFreePlaneResources(planes);
                plane = nullptr;
                s_plane_ids[plane_id_] = dev_;
                return true;
            }
        }
    }

    if (plane) {
        drmModeFreePlane(plane);
        plane = nullptr;
    }

    drmModeFreePlaneResources(planes);
    return false;
}

bool RKVideoFrameRender::GetPipPlane() {
    drmModePlaneResPtr planes = drmModeGetPlaneResources(fd_);
    if (!planes) {
        jerror("failed to query planes: %s", strerror(errno));
        return false;
    }
    drmModePlanePtr plane = nullptr;
    for (uint32_t i = 0; i < planes->count_planes; i++) {
        if (plane) {
            drmModeFreePlane(plane);
            plane = nullptr;
        }
        plane = drmModeGetPlane(fd_, planes->planes[i]);
        if (!plane) {
            jerror("failed to query plane %d: %s", i, strerror(errno));
            continue;
        }
		
        if (!(plane->possible_crtcs & (1 << crtc_index_))) continue;
		if (plane->crtc_id != 0 || plane->plane_id == plane_id_) continue;
        for (uint32_t j = 0; j < plane->count_formats; j++) {
			jinfo("---------------------%d<-->%d pip_plane_id_ = %d\n", plane->formats[j], format_, plane->plane_id);
            // found a plane matching the requested format
            if (plane->formats[j] == format_) {
                pip_plane_id_ = plane->plane_id;
				jinfo("pip_plane_id = %d, crtc_id(not used) = %d, i = %d", plane->plane_id, plane->crtc_id, i);
                drmModeFreePlane(plane);
                drmModeFreePlaneResources(planes);
				plane = nullptr;
                return true;
            }
        }
    }

    if (plane)
        drmModeFreePlane(plane);
    drmModeFreePlaneResources(planes);
    return false;    
}

bool RKVideoFrameRender::SetCrtc(const FB &fb) {
    uint32_t fb_handle = fb.fb_handle;
    if (!running_) {
        int32_t ret = drmModeSetCrtc(fd_, crtc_id_, fb_handle, 0, 0, &con_id_, 1, &mode_);
        if (ret != 0) {
            jerror("failed to set crct via drm: %s", strerror(errno));
            return false;
        }
        running_ = true;
    }
    return true;
}
bool RKVideoFrameRender::SetPlane(const FB &fb) {
	uint32_t fb_handle = fb.fb_handle;
	int32_t ret = drmModeSetPlane(fd_, plane_id_, crtc_id_, 
								  fb_handle, 0, 
								  crtc_rect_.left, crtc_rect_.top, 
								  crtc_rect_.width, crtc_rect_.height, 
								  0, 0, width_ << 16, height_ << 16);
	if (ret != 0) {
		jerror("failed to set plane via drm: %s", strerror(errno));
		return false;
	}
#if 0
	uint32_t index = fb.index;
    drmVBlank vblank;
    vblank.request.type = (drmVBlankSeqType) (DRM_VBLANK_EVENT | DRM_VBLANK_RELATIVE);
    vblank.request.sequence = 1;
    vblank.request.signal = (unsigned long) index;
    ret =  drmWaitVBlank(fd_, &vblank);
	if (ret != 0) {
    	jerror("failed to wait vblank: %s", strerror(errno));
		return false
	}
#endif
	return true;
}

bool RKVideoFrameRender::PageFlip(const FB &fb) {
	uint32_t fb_handle = fb.fb_handle;
	uint32_t index = fb.index;
	int32_t ret = drmModePageFlip(fd_, crtc_id_, fb_handle, 
								  DRM_MODE_PAGE_FLIP_EVENT,
								  (void*)(uint64_t)&index);
	if (ret != 0) {
		jerror("failed on page flip: %s", strerror(errno));
		return false;
	}
	drmEventContext evctx;
    struct timeval timeout = { .tv_sec = 3, .tv_usec = 0 };
    fd_set fds;
    memset(&evctx, 0, sizeof evctx);	
    evctx.version = DRM_EVENT_CONTEXT_VERSION;
    evctx.vblank_handler = NULL;
	evctx.page_flip_handler = nullptr;
    FD_ZERO(&fds);
    FD_SET(fd_, &fds);
    select(fd_ + 1, &fds, NULL, NULL, &timeout);
    drmHandleEvent(fd_, &evctx);
	return true;
}

uint32_t RKVideoFrameRender::get_property_id(int fd, drmModeObjectProperties *props, const char *name) {
	drmModePropertyPtr property;
	uint32_t i, id = 0;
	
	// find property according to the name
	for (i = 0; i < props->count_props; i++) {
		property = drmModeGetProperty(fd, props->props[i]);
		if (property && !strcmp(property->name, name))
			id = property->prop_id;
		drmModeFreeProperty(property);
		if (id) break;
	}
	return id;
}

int32_t RKVideoFrameRender::get_property_value(int fd, drmModeObjectProperties *props, const char *name) {
	drmModePropertyPtr property;
	int value = -1;
	// find property according to the name
	for (uint32_t i = 0; i < props->count_props; i++) {
		property = drmModeGetProperty(fd, props->props[i]);
		if (property && !strcmp(property->name, name))
			value = property->values[i];
		drmModeFreeProperty(property);
	}
	return value;
}

void RKVideoFrameRender::onFrame(const std::shared_ptr<Frame>& frame) {
	if (!displaying_ || !running_) return;
	// auto now_time = std::chrono::steady_clock::now();
	// auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now_time - render_time_).count();
	// if (duration < renderGap_ && renderGap_ - duration > 5 && !first_frame_) return;
	// render_time_ = now_time;
	// first_frame_ = false;

#if USE_MULTI_PLANES
    int id = frame->getGroupId();
    std::unique_lock<std::mutex> lock(mutex_);
    if (plane_layouts_.count(id) == 0)
        return;
    if (id == 0)
        frames_recieved_++;
    if (plane_layouts_[id].frame_queue.size() > 3) {
        plane_layouts_[id].frame_queue.pop();
    }
    plane_layouts_[id].frame_queue.emplace(frame);
#elif USE_SINGLE_PLANE
    frames_recieved_++;
    if (running_ && frame && frame->getFrameFormat() == FRAME_FORMAT_RK) {
        int id = frame->getGroupId();
        void *source = frame->getSource();
        if (source_id_map_.count(source) > 0) {
            id = source_id_map_[source];
        }
        auto &channel = input_channels_[id];
        std::lock_guard<std::mutex> lock(channel.mtx);
        if (channel.cache.size() >= 3) {
            jinfo("RKVideoFrameRender id %d. Too many frames, drop one", id);
            channel.cache.pop_front();
        }
        channel.cache.push_back({frame, std::chrono::steady_clock::now()});
    }
#else
	std::lock_guard<std::mutex> lock(mutex_);
    if (pip_ && frame->getGroupId() == -1) {
        local_frames_recieved_++;
        if (pip_frame_queue_.size() > 3) {
            pip_frame_queue_.pop();
        }
        pip_frame_queue_.emplace(frame);
    } else {
        frames_recieved_++;
        if (frame_queue_.size() > 3) {
            // TODO: 缓存过多处理
            // jinfo("[RKVideoFrameRender] Too much frame in queue\n");
            frame_queue_.pop();
            // return;
        }
        frame_queue_.emplace(frame);
        // cv_.notify_one();
    }
#endif
}

void RKVideoFrameRender::ResetStatistics() {
	statistics_start_time_ = std::chrono::steady_clock::now();
	frames_recieved_ = 0;
	frames_to_render_ = 0;
	local_frames_recieved_ = 0;
	local_frames_to_render_ = 0;
}

void RKVideoFrameRender::LogStatistics() {
	auto current_time = std::chrono::steady_clock::now();
	auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - statistics_start_time_).count();
	if (duration >= 10000) {
		jinfo("[RKVideoFrameRender] Duartion: %dms. Frame received: %d. Frame to render: %d.", duration, frames_recieved_, frames_to_render_);
        if (pip_)
            jinfo("[RKVideoFrameRender] Duartion: %dms. Local frame received: %d. local frame to render: %d.", duration, local_frames_recieved_, local_frames_to_render_);
		ResetStatistics();
	}
}

bool RKVideoFrameRender::GetConnectorStatus(int fd, int dev) {
    drmModeRes *res = drmModeGetResources(fd);
    if (!res) return false;
    bool connected = true;
	if (res->count_connectors <= 0) {
        drmModeFreeResources(res);
        return false;
    }
    int count = res->count_connectors;
    if (dev < 0 || dev >= count) {
        drmModeFreeResources(res);
        return false;
    }
    drmModeConnectorPtr connector = drmModeGetConnector(fd, res->connectors[dev]);
    if (!connector || connector->connection == DRM_MODE_DISCONNECTED || connector->count_modes == 0)
        connected = false;
    jinfo(
        "connection %d count_modes %d dev %d count_connectors %d connected %d", connector->connection, connector->count_modes, dev, res->count_connectors,
        connected);
    if (connector)
        drmModeFreeConnector(connector);
    drmModeFreeResources(res);
    return connected;
}

int RKVideoFrameRender::updateParams(const std::string &jsonParams) {
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    if (j.contains("videoLayout") && j["videoLayout"].is_array())
    {
        std::lock_guard<std::mutex> lock(layout_mutex_);
		video_layout_.clear();
        for (uint32_t i = 0; i < j["videoLayout"].size(); ++i)
        {
            if (j["videoLayout"][i].contains("id") &&
                j["videoLayout"][i].contains("rect") &&
                j["videoLayout"][i]["rect"].contains("x") &&
                j["videoLayout"][i]["rect"].contains("y") &&
                j["videoLayout"][i]["rect"].contains("w") &&
                j["videoLayout"][i]["rect"].contains("h"))
            {
                Region region;
                region.id = j["videoLayout"][i]["id"];
                region.rect.x = j["videoLayout"][i]["rect"]["x"];
                region.rect.y = j["videoLayout"][i]["rect"]["y"];
                region.rect.w = j["videoLayout"][i]["rect"]["w"];
                region.rect.h = j["videoLayout"][i]["rect"]["h"];
                LOGI("video layout id = %d x = %d y= %d w = %d h = %d", region.id, region.rect.x, region.rect.y, region.rect.w, region.rect.h);
                video_layout_.push_back(region);
            }
        }
    }
    return 0;
}

void RKVideoFrameRender::setFrameTimeoutNotify(int ms, void* param, const TimeoutNotifyCallback& cb) {
    if (ms <= 0) {
        jinfo("RKVideoFrameRender setFrameTimeoutNotify timeout must > 0");
        return;
    }
    VideoFrameRender::setFrameTimeoutNotify(ms, param, cb);
    if (!check_timeout_thread_.isRunning()) {
        check_timeout_thread_.loop()->setInterval(30, [this](hv::TimerID id) {
            checkFrameTimeout();
        });
        check_timeout_thread_.start(true);
    }
}

#if USE_MULTI_PLANES
bool RKVideoFrameRender::InitMulti() {
    running_ = true;
    display_future_ = std::async([this]() -> bool {
        bool has_connector = false;
        while (!has_connector && fd_ != -1 && running_) {
            if (!TryInitResources(fd_, dev_, width_, height_, fps_, interlace_)) {
                usleep(2000000);
			} else {
                has_connector = true;
            }
        }
        if (!has_connector || !running_ || fd_ == -1) {
            jerror("Failed to get connector.");
            return false;
        }
        connecotr_connected_ = true;
        {
            std::unique_lock<std::mutex> lock(mutex_); 
            for (auto &kv : plane_layouts_) {
                auto id = kv.first;
                auto &plane_layout = plane_layouts_[id];
                if (plane_layout.plane_id >= 0) continue;
                if (plane_layout.plane_id < 0 && !GetPlane(id)) continue;
                for (int i = 0; i < 2; i++) {
                    plane_layout.index = 0;
                    plane_layout.bo[i].width = width_;
                    plane_layout.bo[i].height = height_;
                    plane_layout.bo[i].format = format_;
                    DrmUtils::modeset_create_fb2(fd_, &plane_layout.bo[i], true);
                }
                drmModeObjectProperties *props;
                drmModeAtomicReq *req;
                uint32_t property_crtc_id;
                uint32_t property_fb_id;
                uint32_t property_crtc_x;
                uint32_t property_crtc_y;
                uint32_t property_crtc_w;
                uint32_t property_crtc_h;
                uint32_t property_src_x;
                uint32_t property_src_y;
                uint32_t property_src_w;
                uint32_t property_src_h;
                uint32_t property_zpos;
                // get plane properties
                props = drmModeObjectGetProperties(fd_, plane_layout.plane_id, DRM_MODE_OBJECT_PLANE);
                property_crtc_id = get_property_id(fd_, props, "CRTC_ID");
                property_fb_id = get_property_id(fd_, props, "FB_ID");
                property_crtc_x = get_property_id(fd_, props, "CRTC_X");
                property_crtc_y = get_property_id(fd_, props, "CRTC_Y");
                property_crtc_w = get_property_id(fd_, props, "CRTC_W");
                property_crtc_h = get_property_id(fd_, props, "CRTC_H");
                property_src_x = get_property_id(fd_, props, "SRC_X");
                property_src_y = get_property_id(fd_, props, "SRC_Y");
                property_src_w = get_property_id(fd_, props, "SRC_W");
                property_src_h = get_property_id(fd_, props, "SRC_H");
                property_zpos = get_property_id(fd_, props, "zpos");
                drmModeFreeObjectProperties(props);

                req = drmModeAtomicAlloc();
                drmModeAtomicAddProperty(req, plane_layout.plane_id, property_fb_id, plane_layout.bo[0].fb_id);
                drmModeAtomicAddProperty(req, plane_layout.plane_id, property_crtc_id, s_custom_res[dev_].crtc_id);
                drmModeAtomicAddProperty(req, plane_layout.plane_id, property_crtc_x, (int)(width_ * plane_layout.left));
                drmModeAtomicAddProperty(req, plane_layout.plane_id, property_crtc_y, (int)(height_ * plane_layout.top));
                drmModeAtomicAddProperty(req, plane_layout.plane_id, property_crtc_w, (int)(width_ * plane_layout.width));
                drmModeAtomicAddProperty(req, plane_layout.plane_id, property_crtc_h, (int)(height_ * plane_layout.height));
                drmModeAtomicAddProperty(req, plane_layout.plane_id, property_zpos, plane_layout.zpos);
                drmModeAtomicAddProperty(req, plane_layout.plane_id, property_src_w, width_ << 16);
                drmModeAtomicAddProperty(req, plane_layout.plane_id, property_src_h, height_ << 16);
                drmModeAtomicAddProperty(req, plane_layout.plane_id, property_src_x, 0);
                drmModeAtomicAddProperty(req, plane_layout.plane_id, property_src_y, 0);
                drmModeAtomicCommit(fd_, req, 0, NULL);
                jinfo("type = %d zpos %d expected %d", get_property_value(fd_, props, "type"), get_property_value(fd_, props, "zpos"), plane_layout.zpos);
                drmModeAtomicFree(req);
                plane_layout.property_fb_id = property_fb_id;
            }
        }

        // crtc_rect_.width = width_;
        // crtc_rect_.height = height_;
        // TODO：布局
        {
            Region region;
            region.id = 0;
            region.rect.x = 0;
            region.rect.y = 0;
            region.rect.w = width_;
            region.rect.h = height_;
            video_layout_.push_back(region);
        }

        displaying_ = true;
        auto pid = getThreadLWP();
        jinfo("[%d]Init RKVideoFrameRender success!", pid);
        ResetStatistics();
        auto last = std::chrono::steady_clock::now();
        bool first = true;
        while (running_) {
            auto now = std::chrono::steady_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(now - last).count();
            last = now;
            if (duration > renderGap_ && !first)
                continue;
            first = false;
            std::unordered_map<uint32_t, std::shared_ptr<WrapRKMppFrame>> wrap_frames;
            {
                std::unique_lock<std::mutex> lock(mutex_);
                for (auto &kv: plane_layouts_) {
                    auto id = kv.first;
                    auto &plane_layout = kv.second;
                    if (!plane_layout.frame_queue.empty() && plane_layout.plane_id >= 0) {
                        if (id == 0) frames_to_render_++;
                        auto frame = plane_layout.frame_queue.front();
                        plane_layout.frame_queue.pop();
                        if (frame && frame->getFrameFormat() == FRAME_FORMAT_RK) {
                            wrap_frames[plane_layout.property_fb_id] = std::dynamic_pointer_cast<WrapRKMppFrame>(frame);
                        }
                    }
                }
            }

            LogStatistics();
            // int groupId = frame->getGroupId();
            {
                std::lock_guard<std::mutex> lock(mutex_);
                auto req = drmModeAtomicAlloc();
                for (auto &kv: plane_layouts_) {
                    auto &plane_layout = kv.second;
                    int index = plane_layout.index;
                    if (plane_layout.plane_id >= 0)
                        drmModeAtomicAddProperty(req, plane_layout.plane_id, plane_layout.property_fb_id, plane_layout.bo[index].fb_id);
                }
                drmModeAtomicCommit(fd_, req, 0, NULL);
                drmModeAtomicFree(req);
                for (auto &kv : plane_layouts_) {
                    auto &plane_layout = kv.second;
                    auto &index = plane_layout.index;
                    if (wrap_frames[plane_layout.property_fb_id]) {
                        MppFrame rk_frame = wrap_frames[plane_layout.property_fb_id]->InnerFrame();
                        bool res = CopyFrom(plane_layout.bo[index ^ 1], rk_frame, 0, 0, width_, height_, format_str_);
                        if (!res)
                            jerror("Failed to copy frame");
                        index ^= 1;
                    }
                }
            }
            // if (wrap_frame)
            //     index ^= 1;
        }
        // TryReleaseResources(fd_, dev_);
        // {
        //     std::lock_guard<std::mutex> lock(s_plane_ids_mutex);
        //     if (s_plane_ids.count(plane_id_) != 0) s_plane_ids.erase(plane_id_);
        //     if (s_plane_ids.empty()) {

        //     }
        // }

        for (auto &kv: plane_layouts_) {
            auto &plane_layout = kv.second;
            
            for (int i = 0; i < 2; ++i) {
                DrmUtils::modeset_destroy_fb(fd_, &plane_layout.bo[i]);
            }
        }
		return true;
    });
    return true;
}

// 自动分配图层
bool RKVideoFrameRender::setPlane(int32_t id, const std::string &jsonParams) {
    jinfo("SetPlane[%d]: %s", id, jsonParams.c_str());
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    std::unique_lock<std::mutex> lock(mutex_); 
    if (plane_layouts_.count(id) == 0) {
        plane_layouts_[id] = PlaneLayout();
        plane_layouts_[id].top = 0.0;
        if (j.contains("top"))
            plane_layouts_[id].top = j["top"];
        plane_layouts_[id].left = 0.0;
        if (j.contains("left"))
            plane_layouts_[id].left = j["left"];
        plane_layouts_[id].width = 1.0;
        if (j.contains("width"))
            plane_layouts_[id].width = j["width"];
        plane_layouts_[id].height = 1.0;
        if (j.contains("height"))
            plane_layouts_[id].height = j["height"];
        plane_layouts_[id].zpos = 0;
        if (j.contains("zpos"))
            plane_layouts_[id].zpos = j["zpos"];        
        if (connecotr_connected_) {
            auto &plane_layout = plane_layouts_[id];
            if (plane_layout.plane_id >= 0) return true;
            if (plane_layout.plane_id < 0 && !GetPlane(id)) return false;
            for (int i = 0; i < 2; i++) {
                plane_layout.index = 0;
                plane_layout.bo[i].width = width_;
                plane_layout.bo[i].height = height_;
                plane_layout.bo[i].format = format_;
                DrmUtils::modeset_create_fb2(fd_, &plane_layout.bo[i], true);
            }
            drmModeObjectProperties *props;
            drmModeAtomicReq *req;
            uint32_t property_crtc_id;
            uint32_t property_fb_id;
            uint32_t property_crtc_x;
            uint32_t property_crtc_y;
            uint32_t property_crtc_w;
            uint32_t property_crtc_h;
            uint32_t property_src_x;
            uint32_t property_src_y;
            uint32_t property_src_w;
            uint32_t property_src_h;
            uint32_t property_zpos;
            // get plane properties
            props = drmModeObjectGetProperties(fd_, plane_layout.plane_id, DRM_MODE_OBJECT_PLANE);
            property_crtc_id = get_property_id(fd_, props, "CRTC_ID");
            property_fb_id = get_property_id(fd_, props, "FB_ID");
            property_crtc_x = get_property_id(fd_, props, "CRTC_X");
            property_crtc_y = get_property_id(fd_, props, "CRTC_Y");
            property_crtc_w = get_property_id(fd_, props, "CRTC_W");
            property_crtc_h = get_property_id(fd_, props, "CRTC_H");
            property_src_x = get_property_id(fd_, props, "SRC_X");
            property_src_y = get_property_id(fd_, props, "SRC_Y");
            property_src_w = get_property_id(fd_, props, "SRC_W");
            property_src_h = get_property_id(fd_, props, "SRC_H");
            property_zpos = get_property_id(fd_, props, "zpos");
            drmModeFreeObjectProperties(props);

            req = drmModeAtomicAlloc();
            drmModeAtomicAddProperty(req, plane_layout.plane_id, property_fb_id, plane_layout.bo[0].fb_id);
            drmModeAtomicAddProperty(req, plane_layout.plane_id, property_crtc_id, s_custom_res[dev_].crtc_id);
            drmModeAtomicAddProperty(req, plane_layout.plane_id, property_crtc_x, (int)(width_ * plane_layout.left));
            drmModeAtomicAddProperty(req, plane_layout.plane_id, property_crtc_y, (int)(height_ * plane_layout.top));
            drmModeAtomicAddProperty(req, plane_layout.plane_id, property_crtc_w, (int)(width_ * plane_layout.width));
            drmModeAtomicAddProperty(req, plane_layout.plane_id, property_crtc_h, (int)(height_ * plane_layout.height));
            drmModeAtomicAddProperty(req, plane_layout.plane_id, property_zpos, plane_layout.zpos);
            drmModeAtomicAddProperty(req, plane_layout.plane_id, property_src_w, width_ << 16);
            drmModeAtomicAddProperty(req, plane_layout.plane_id, property_src_h, height_ << 16);
            drmModeAtomicAddProperty(req, plane_layout.plane_id, property_src_x, 0);
            drmModeAtomicAddProperty(req, plane_layout.plane_id, property_src_y, 0);
            drmModeAtomicCommit(fd_, req, 0, NULL);      
            jinfo("type = %d zpos %d expected %d", get_property_value(fd_, props, "type"), get_property_value(fd_, props, "zpos"), plane_layout.zpos);
            drmModeAtomicFree(req);
            plane_layout.property_fb_id = property_fb_id;
        }
    }
    return true;
}
bool RKVideoFrameRender::unsetPlane(int32_t id) {
    jinfo("unsetPlane[%d]", id);
    if (plane_layouts_.count(id) != 0) {
        if (plane_layouts_[id].plane_id >= 0) {
            std::lock_guard<std::mutex> lock(s_plane_ids_mutex);
            if (s_plane_ids.count(plane_layouts_[id].plane_id) > 0) {
                s_plane_ids.erase(plane_layouts_[id].plane_id);
            }
        }
        plane_layouts_.erase(id);
    }
    return true;
}

bool RKVideoFrameRender::GetPlane(int id) {
    if (plane_layouts_.count(id) == 0) return false;
    std::lock_guard<std::mutex> lock(s_plane_ids_mutex);
    drmModePlaneResPtr planes = drmModeGetPlaneResources(fd_);
    if (!planes) {
        jerror("failed to query planes: %s", strerror(errno));
        return false;
    }
    drmModePlanePtr plane = nullptr;
	// 优先使用对应crtc已占用的plane
	// TODO: 适配多个图层
	for (uint32_t i = 0; i < planes->count_planes; i++) {
		plane = drmModeGetPlane(fd_, planes->planes[i]);
        if (!plane) {
            jerror("failed to query plane %d: %s", i, strerror(errno));
            continue;
        }
        if (s_plane_ids.count(plane->plane_id) != 0) {
            if (plane) {
                drmModeFreePlane(plane);
                plane = nullptr;
            }
            continue;            
        }
		if (plane->crtc_id == crtc_id_) {
			for (uint32_t j = 0; j < plane->count_formats; j++) {
				jinfo("---------------------%d<-->%d plane_layouts_[%d].plane_id = %d\n", plane->formats[j], format_, id, plane->plane_id);
				// found a plane matching the requested format
				if (plane->formats[j] == format_) {
					plane_layouts_[id].plane_id = plane->plane_id;
					jinfo("plane_id = %d, crtc_id(not used) = %d, i = %d", plane->plane_id, plane->crtc_id, i);
					drmModeFreePlane(plane);
					drmModeFreePlaneResources(planes);
					plane = nullptr;
                    s_plane_ids[plane_layouts_[id].plane_id] = dev_;
					return true;
				}
			}
		}
		if (plane) {
            drmModeFreePlane(plane);
            plane = nullptr;
        }
	}

    for (uint32_t i = 0; i < planes->count_planes; i++) {
        if (plane) {
            drmModeFreePlane(plane);
            plane = nullptr;
        }
        plane = drmModeGetPlane(fd_, planes->planes[i]);
        if (!plane) {
            jerror("failed to query plane %d: %s", i, strerror(errno));
            continue;
        }

        if (!(plane->possible_crtcs & (1 << crtc_index_)) || 
            (plane->crtc_id != 0 && plane->crtc_id != crtc_id_) || 
            s_plane_ids.count(plane->plane_id) != 0) {
            continue;
        }

        if (plane->crtc_id != 0 && plane->crtc_id != crtc_id_)
            continue;
        if (s_plane_ids.count(plane->plane_id) != 0) continue;
        for (uint32_t j = 0; j < plane->count_formats; j++) {
			jinfo("---------------------%d<-->%d plane_layouts_[%d].plane_id = %d\n", plane->formats[j], format_, id, plane->plane_id);
            // found a plane matching the requested format
            if (plane->formats[j] == format_) {
                plane_layouts_[id].plane_id = plane->plane_id;
                jinfo("plane_id = %d, crtc_id(not used) = %d, i = %d", plane->plane_id, plane->crtc_id, i);
                drmModeFreePlane(plane);
                drmModeFreePlaneResources(planes);
                plane = nullptr;
                s_plane_ids[plane_layouts_[id].plane_id] = dev_;
                return true;
            }
        }
    }

    if (plane) {
        drmModeFreePlane(plane);
        plane = nullptr;
    }

    drmModeFreePlaneResources(planes);
    return false;
}
#elif USE_SINGLE_PLANE
bool RKVideoFrameRender::InitSingle() {
    // CreateFont("/usr/share/fonts/dejavu/DejaVuSans-Bold.ttf", 48);
	running_ = true;
    display_future_ = std::async([this]() -> bool {
        drmModeRes *resource = drmModeGetResources(fd_);
        if (resource) {
            jinfo("count_connectors = %d, count_crtcs = %d\n", resource->count_connectors, resource->count_crtcs);
        } else {
            jerror("failed to query Drm Mode resources: %s", strerror(errno));
            return false;
        }

        int s32ret = drmSetClientCap(fd_, DRM_CLIENT_CAP_ATOMIC, 1);
        if (s32ret != 0)
            jinfo("drmSetClientCap failed, ret = %d", s32ret);
        s32ret = drmSetClientCap(fd_, DRM_CLIENT_CAP_UNIVERSAL_PLANES, 1);
        if (s32ret != 0)
            jerror("drmSetClientCap failed, ret = %d", s32ret);
        bool ret = GetConnector(resource);
        if (!ret) {
			bool has_connector = false;
            // TODO: 检测connector连接
			while (!has_connector && fd_ != -1 && running_) {
				has_connector = GetConnector(resource);
				if (!has_connector) usleep(2000000);
			}
            if (!has_connector || fd_ == -1 || !running_) {
                jerror("Failed to get connector.");
                drmModeFreeResources(resource);
                return false;
            }
        }

        crtc_rect_.width = width_;
        crtc_rect_.height = height_;
        // TODO：布局
        {
            Region region;
            region.id = 0;
            region.rect.x = 0;
            region.rect.y = 0;
            region.rect.w = width_;
            region.rect.h = height_;
            video_layout_.push_back(region);
        }

        drmModeFreeResources(resource);
        ret = GetPlane();
        if (!ret) {
            jerror("failed to get plane with required format %s", strerror(errno));
            return false;
        }

        for (int i = 0; i < 2; i++) {
            bo_[i].width = width_;
            bo_[i].height = height_;
            bo_[i].format = format_;
            DrmUtils::modeset_create_fb2(fd_, &bo_[i], true);
        }
        drmModeObjectProperties *props;
        drmModeAtomicReq *req;
        uint32_t blob_id;
        // uint32_t property_crtc_id;
        uint32_t property_mode_id;
        uint32_t property_active;
        uint32_t property_crtc_id;
        uint32_t property_bus_format;
        uint32_t property_fb_id;
        uint32_t property_pip_fb_id;
        uint32_t property_crtc_x;
        uint32_t property_crtc_y;
        uint32_t property_crtc_w;
        uint32_t property_crtc_h;
        uint32_t property_src_x;
        uint32_t property_src_y;
        uint32_t property_src_w;
        uint32_t property_src_h;
        uint32_t property_zpos;
        // get connector properties
        props = drmModeObjectGetProperties(fd_, con_id_, DRM_MODE_OBJECT_CONNECTOR);
        property_crtc_id = get_property_id(fd_, props, "CRTC_ID");
        property_bus_format = get_property_id(fd_, props, "color_format");
        drmModeFreeObjectProperties(props);
        // get crtc properties
        props = drmModeObjectGetProperties(fd_, crtc_id_, DRM_MODE_OBJECT_CRTC);
        property_active = get_property_id(fd_, props, "ACTIVE");
        property_mode_id = get_property_id(fd_, props, "MODE_ID");
        drmModeFreeObjectProperties(props);
        jinfo(
            "property_crtc_id = %d, property_active = %d, property_mode_id = %d property_bus_format = %d", property_crtc_id, property_active, property_mode_id,
            property_bus_format);
        // create blob to store current mode, and return the blob id
        drmModeCreatePropertyBlob(fd_, &mode_, sizeof(mode_), &blob_id);
        jinfo("conn->modes[0] = %s", mode_.name);

        // start modeseting
        printf("------------------------------%d\n", __LINE__);
        req = drmModeAtomicAlloc();
        drmModeAtomicAddProperty(req, crtc_id_, property_active, 1);
        drmModeAtomicAddProperty(req, crtc_id_, property_mode_id, blob_id);
        drmModeAtomicAddProperty(req, con_id_, property_crtc_id, crtc_id_);
        drmModeAtomicCommit(fd_, req, DRM_MODE_ATOMIC_ALLOW_MODESET, NULL);
        drmModeDestroyPropertyBlob(fd_, blob_id);
        drmModeAtomicFree(req);
        printf("------------------------------%d\n", __LINE__);

        req = drmModeAtomicAlloc();
        // rgb=0 ycbcr444=1 ycbcr422=2 ycbcr420=3 ycbcr_high_subsampling=4 ycbcr_low_subsampling=5 invalid_output=6
        drmModeAtomicAddProperty(req, con_id_, property_bus_format, 2);
        drmModeAtomicCommit(fd_, req, DRM_MODE_ATOMIC_ALLOW_MODESET, NULL);
        drmModeAtomicFree(req);
        printf("------------------------------%d\n", __LINE__);

        // get plane properties
        props = drmModeObjectGetProperties(fd_, plane_id_, DRM_MODE_OBJECT_PLANE);
        property_crtc_id = get_property_id(fd_, props, "CRTC_ID");
        property_fb_id = get_property_id(fd_, props, "FB_ID");
        property_crtc_x = get_property_id(fd_, props, "CRTC_X");
        property_crtc_y = get_property_id(fd_, props, "CRTC_Y");
        property_crtc_w = get_property_id(fd_, props, "CRTC_W");
        property_crtc_h = get_property_id(fd_, props, "CRTC_H");
        property_src_x = get_property_id(fd_, props, "SRC_X");
        property_src_y = get_property_id(fd_, props, "SRC_Y");
        property_src_w = get_property_id(fd_, props, "SRC_W");
        property_src_h = get_property_id(fd_, props, "SRC_H");
        property_zpos = get_property_id(fd_, props, "zpos");
        jinfo("type = %d zpos %d", get_property_value(fd_, props, "type"), get_property_value(fd_, props, "zpos"));
        drmModeFreeObjectProperties(props);

        req = drmModeAtomicAlloc();
        drmModeAtomicAddProperty(req, plane_id_, property_fb_id, bo_[0].fb_id);
        drmModeAtomicAddProperty(req, plane_id_, property_crtc_id, crtc_id_);
        drmModeAtomicAddProperty(req, plane_id_, property_crtc_x, crtc_rect_.left);
        drmModeAtomicAddProperty(req, plane_id_, property_crtc_y, crtc_rect_.top);
        drmModeAtomicAddProperty(req, plane_id_, property_crtc_w, crtc_rect_.width);
        drmModeAtomicAddProperty(req, plane_id_, property_crtc_h, crtc_rect_.height);
        drmModeAtomicAddProperty(req, plane_id_, property_src_w, width_ << 16);
        drmModeAtomicAddProperty(req, plane_id_, property_src_h, height_ << 16);
        drmModeAtomicAddProperty(req, plane_id_, property_src_x, 0);
        drmModeAtomicAddProperty(req, plane_id_, property_src_y, 0);
        drmModeAtomicCommit(fd_, req, 0, NULL);
        drmModeAtomicFree(req);

        /* Configure the current thread to use only RGA3_core0 or RGA3_core1. */
        imconfig(IM_CONFIG_SCHEDULER_CORE, IM_SCHEDULER_RGA3_CORE0 | IM_SCHEDULER_RGA3_CORE1);

        displaying_ = true;
        auto pid = getThreadLWP();
        jinfo("[%d]InitSingle RKVideoFrameRender success!", pid);
        ResetStatistics();
        int index = 0;
        auto last = std::chrono::steady_clock::now();
        bool first = true;
        while (running_) {
            // auto& bo = bo_[index ^ 1];
            auto now = std::chrono::steady_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(now - last).count();
            last = now;
            // if (duration > renderGap_ && !first) continue;
            // first = false;
            std::map<int, LayoutRect> current_layout;
            bool need_clear = false;
            {
                std::lock_guard<std::mutex> lock(layout_mtx_);
                current_layout = layout_map_;
                need_clear = need_clear_screen_;
                need_clear_screen_ = false;
            }
            frames_to_render_++;
            LogStatistics();
            auto req = drmModeAtomicAlloc();
            drmModeAtomicAddProperty(req, plane_id_, property_fb_id, bo_[index].fb_id);
            drmModeAtomicCommit(fd_, req, 0, NULL);
            drmModeAtomicFree(req);
            bool res = false;
            std::shared_ptr<WrapRKMppFrame> wrap_frame;
            if (need_clear) {
                for (int i = 0; i < 2; i++) {
                    DrmUtils::set_black(&bo_[i]);
                }
            }
            for (const auto& kv: current_layout) {
                // jinfo("kv.first: %d", kv.first);
                // jinfo("[%d]frame format is %s", pid, frame->getFormatStr().c_str());
                auto frame = getNextValidFrame(kv.first);
                if (!frame) {
                    // jinfo("[%d]no valid frame", kv.first);
                    continue;
                }
                if (frame->getFrameFormat() == FRAME_FORMAT_RK) {
                    wrap_frame = std::dynamic_pointer_cast<WrapRKMppFrame>(frame);
                }
                if (wrap_frame) {
                    bool res = CopyFrom(bo_[index ^ 1], wrap_frame->InnerFrame(), kv.second, wrap_frame->getFormatStr());
                    if (!res)
                        jerror("failed to copy frame");
                }
                res = true;
            }
            if (res) index ^= 1;
        }

        drmModeCreatePropertyBlob(fd_, &first_mode_, sizeof(first_mode_), &blob_id);
        req = drmModeAtomicAlloc();
        drmModeAtomicAddProperty(req, crtc_id_, property_mode_id, blob_id);
        drmModeAtomicCommit(fd_, req, DRM_MODE_ATOMIC_ALLOW_MODESET, NULL);
        drmModeDestroyPropertyBlob(fd_, blob_id);
        drmModeAtomicFree(req);
        jinfo("###################first_mode_: %s", first_mode_.name);
        for (int i = 0; i < 2; ++i)
            DrmUtils::modeset_destroy_fb(fd_, &bo_[i]);
		return true;
    });
    return true;
}
std::shared_ptr<Frame> RKVideoFrameRender::getNextValidFrame(int inputId) {
    if (input_channels_.count(inputId) == 0) return nullptr;
    
    auto &channel = input_channels_[inputId];
    std::lock_guard<std::mutex> lock(channel.mtx);
    auto &cache = channel.cache;
    auto now = std::chrono::steady_clock::now();

    while (!cache.empty()) {
        auto &front = cache.front();
        auto frame = front.frame;
        input_channels_[inputId].last_frame = frame;
        int ageMs = std::chrono::duration_cast<std::chrono::milliseconds>(now - front.timestamp).count();
        cache.pop_front();
        if (ageMs <= timeoutMs_) {
            return frame;
        }
    }
    // return nullptr;
    return input_channels_[inputId].last_frame;
}

void RKVideoFrameRender::addInput(int inputId, const LayoutRect& layout) {
    std::lock_guard<std::mutex> lock(layout_mtx_);
    layout_map_[inputId] = layout;
    for (auto &it : layout_map_) {
        jinfo("addInput: %d", it.first);
        jinfo("addInput: %f,%f,%f,%f", it.second.x, it.second.y, it.second.w, it.second.h);
        jinfo("addInput: %f,%f,%f,%f", it.second.srcX, it.second.srcY, it.second.srcW, it.second.srcH);
    }
}

void RKVideoFrameRender::removeInput(int inputId) {
    {
        std::lock_guard<std::mutex> lock(layout_mtx_);
        if (layout_map_.count(inputId) != 0)
            layout_map_.erase(inputId);
    }
    if (input_channels_.erase(inputId) != 0)
        input_channels_.erase(inputId);
}

void RKVideoFrameRender::updateLayout(int inputId, const LayoutRect& layout) {
    std::lock_guard<std::mutex> lock(layout_mtx_);
    if (layout_map_.count(inputId) != 0)
        layout_map_[inputId] = layout;
}

void RKVideoFrameRender::updateLayout(std::map<int, LayoutRect> &layouts) {
    std::lock_guard<std::mutex> lock(layout_mtx_);
    for (auto &it : layouts) {
        layout_map_[it.first] = it.second;
    }
    for (auto &it : layout_map_) {
        jinfo("updateLayout: %d", it.first);
        jinfo("updateLayout: %f,%f,%f,%f", it.second.x, it.second.y, it.second.w, it.second.h);
        jinfo("updateLayout: %f,%f,%f,%f", it.second.srcX, it.second.srcY, it.second.srcW, it.second.srcH);
    }
    need_clear_screen_ = true;
}

void RKVideoFrameRender::loadPresetLayouts(int presetIndex, bool reverse) {
    std::lock_guard<std::mutex> lock(layout_mtx_);
    if (presetIndex >= preset_layouts.size())
        return;
    auto layout = preset_layouts[presetIndex];
    // if (reverse) {
    //     if (layout.size() > 1)
    //         std::swap(layout[0], layout[layout.size() - 1]);
    // }
    for (auto &it : layout) {
        layout_map_[it.first] = it.second;
    }
    for (auto &it : layout_map_) {
        jinfo("loadPresetLayouts: %d", it.first);
        jinfo("loadPresetLayouts: %f,%f,%f,%f", it.second.x, it.second.y, it.second.w, it.second.h);
        jinfo("loadPresetLayouts: %f,%f,%f,%f", it.second.srcX, it.second.srcY, it.second.srcW, it.second.srcH);
    }
    need_clear_screen_ = true;
}

void RKVideoFrameRender::setSourceId(void *source, int32_t id) {
    if (!source)
        return;
    std::lock_guard<std::mutex> lock(layout_mtx_);
    auto it = source_id_map_.find(source);
    if (it != source_id_map_.end()) {
        jinfo("setSourceId[%p]: %d", source, id);
        it->second = id;
    } else {
        jinfo("setSourceId[%p]: %d", source, id);
        source_id_map_[source] = id;
    }
}

void RKVideoFrameRender::unsetSourceId(void *source) {
    std::lock_guard<std::mutex> lock(layout_mtx_);
    auto it = source_id_map_.find(source);
    if (it != source_id_map_.end()) {
        jinfo("unsetSourceId: %d", it->second);
        source_id_map_.erase(it);
    }
}

bool RKVideoFrameRender::getSourceId(void *source, int32_t &id) {
    std::lock_guard<std::mutex> lock(layout_mtx_);
    auto it = source_id_map_.find(source);
    if (it != source_id_map_.end()) {
        id = it->second;
        return true;
    }
    return false;
}

#endif
// int RKVideoFrameRender::CreateFont(const char *font_path, int font_size) {
//     FT_Init_FreeType(&library_);
//     FT_New_Face(library_, font_path, 0, &face_);
//     if (!face_) {
//         // LOG_ERROR("please check font_path %s\n", font_path);
//         return -1;
//     }

//     FT_Set_Pixel_Sizes(face_, 64, 54);
//     font_size_ = font_size;
//     font_path_ = font_path;
//     slot_ = face_->glyph;

//     if (FT_Load_Char(face_, '0', FT_LOAD_RENDER)) {
//         jerror("ERROR::FREETYTPE: Failed to load Glyph");
//         return -1;
//     } else {
//         jinfo(
//             "face %d %d %d %d %p", face_->glyph->bitmap.width, face_->glyph->bitmap.rows, face_->glyph->bitmap.pixel_mode, face_->glyph->bitmap.pitch,
//             face_->glyph->bitmap.buffer);
//     }
//     OsdInfo info = {0, 0, face_->glyph->bitmap.width, face_->glyph->bitmap.rows, face_->glyph->bitmap.buffer, OSD_PIX_FMT_BGRA8888, 1};
//     osds[0] = info;
//     s_osds.emplace_back(info);
//     return 0;
// }

// int RKVideoFrameRender::DestoryFont() {
//     if (face_) {
//         FT_Done_Face(face_);
//         face_ = NULL;
//     }
//     if (library_) {
//         FT_Done_FreeType(library_);
//         library_ = NULL;
//     }
//     return 0;
// }
} // namespace panocom
