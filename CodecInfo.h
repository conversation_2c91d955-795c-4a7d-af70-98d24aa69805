#ifndef P_CodecInfo_h
#define P_CodecInfo_h

#include <stdint.h>
#include <string.h>
#include <list>
#include <map>
#include <unordered_map>
#include "Frame.h"

#define H264_90000_PT       96  // H264 BP Video Codec
#define H264_90000_MP_PT    101 // H264 MP Video Codec
#define H264_90000_HP_PT    102 // H264 HP Video Codec
#define H265_90000_PT       98  // H265 Video Codec
#define PCMU_8000_PT        0   // PCMU Audio Codec
#define PCMA_8000_PT        8   // PCMA Audio Codec
#define G722_16000_1_PT     9   // G722 Mono, 16k, 64000bps
#define G729_8000_PT        18  // G722 Mono Audio Codec
// #define L16_16000_1_PT      97
#define OPUS_48000_2_PT     111 // Opus Audio Codec
#define OPUS_16000_1_PT     110 // Opus Audio Codec

#define AAC_16000_1_PT        97  // AAC 16khz Mono
#define AAC_48000_2_PT        99  // AAC 48khz Stereo
#define AAC_48000_1_PT        100  // AAC 48khz Mono
#define G7221_16000_32000_1_PT     117   // G722.1 Mono, 32kHz, 32000bps
#define G7221_32000_48000_1_PT     118   // G722.1 Annex C Mono, 32kHz, 48000bps
#define G7221_16000_24000_1_PT     119   // G722.1 Mono, 32kHz, 24000bps

#define L16_16000_1_PT        84  // PCM 16khz Mono
#define L16_48000_2_PT        83  // PCM 48khz Stereo

#define RED_90000_PT          116  
#define ULPFEC_90000_PT       127
namespace panocom
{


namespace H264
{
    
// RFC 6184 RTP Payload Format for H.264 video
const char kH264FmtpProfileLevelId[] = "profile-level-id";
const char kH264FmtpLevelAsymmetryAllowed[] = "level-asymmetry-allowed";
const char kH264FmtpPacketizationMode[] = "packetization-mode";

enum Profile {
  kProfileConstrainedBaseline,
  kProfileBaseline,
  kProfileMain,
  kProfileConstrainedHigh,
  kProfileHigh,
};

// Map containting SDP codec parameters.
typedef std::map<std::string, std::string> CodecParameterMap;

// All values are equal to ten times the level number, except level 1b which is
// special.
enum Level {
  kLevel1_b = 0,
  kLevel1 = 10,
  kLevel1_1 = 11,
  kLevel1_2 = 12,
  kLevel1_3 = 13,
  kLevel2 = 20,
  kLevel2_1 = 21,
  kLevel2_2 = 22,
  kLevel3 = 30,
  kLevel3_1 = 31,
  kLevel3_2 = 32,
  kLevel4 = 40,
  kLevel4_1 = 41,
  kLevel4_2 = 42,
  kLevel5 = 50,
  kLevel5_1 = 51,
  kLevel5_2 = 52
};

struct ProfileLevelId {
  constexpr ProfileLevelId(Profile profile, Level level)
      : profile(profile), level(level) {}
  Profile profile;
  Level level;
};

Profile GetProfileEnum(int profile);

std::string ProfileLevelIdToString(const ProfileLevelId &profile_level_id);

std::string FmtpConfigToString(CodecParameterMap &params);
} // namespace H264

    struct CodecInst
    {
        FrameFormat fmt;
        char name[32];
        int pt;
        int rate;
        int chn;
        int tsinc;
        int bitrate;
        int profile;
        int ptime;
        // CodecInst()
        // {
        //     memset(this, 0, sizeof(CodecInst));
        // }
        // CodecInst(char* name, int pt, int chn, int bitrate, int profile)
        // {
        //     memcpy(this->name, name, sizeof(this->name));
        //     this->pt = pt;
        //     this->chn = chn;
        //     this->bitrate = bitrate;
        //     this->profile = profile;
        // }
        CodecInst& operator=(CodecInst& a)
        {
            memcpy(this->name, a.name, sizeof(this->name));
            this->fmt = a.fmt;
            this->pt = a.pt;
            this->rate = a.rate;
            this->chn = a.chn;
            this->tsinc = a.tsinc;
            this->bitrate = a.bitrate;
            this->profile = a.profile;
            this->ptime = a.ptime;
            return *this;
        }
        CodecInst& operator=(const CodecInst& a)
        {
            memcpy(this->name, a.name, sizeof(this->name));
            this->fmt = a.fmt;
            this->pt = a.pt;
            this->rate = a.rate;
            this->chn = a.chn;
            this->tsinc = a.tsinc;
            this->bitrate = a.bitrate;
            this->profile = a.profile;
            this->ptime = a.ptime;
            return *this;
        }
        // HACK: 另外判断g7221的情况
        bool operator==(CodecInst& a)
        {
            if (a.pt == 8 || a.pt == 18)
            {
                return this->pt == a.pt;
            }
            if (strcasecmp(this->name, "G7221") == 0) {
                return strcasecmp(this->name, a.name) == 0 && this->rate == a.rate && this->chn == a.chn && this->profile == a.profile && this->bitrate == a.bitrate;
            }
            return strcasecmp(this->name, a.name) == 0 && this->rate == a.rate && this->chn == a.chn && this->profile == a.profile;
        }
        bool operator==(const CodecInst& a)
        {
            if (a.pt == 8 || a.pt == 18)
            {
                return this->pt == a.pt;
            }
            if (strcasecmp(this->name, "G7221") == 0) {
                return strcasecmp(this->name, a.name) == 0 && this->rate == a.rate && this->chn == a.chn && this->profile == a.profile && this->bitrate == a.bitrate;
            }
            return strcasecmp(this->name, a.name) == 0 && this->rate == a.rate && this->chn == a.chn && this->profile == a.profile;
        }
        bool operator==(CodecInst& a) const 
        {
            if (a.pt == 8 || a.pt == 18)
            {
                return this->pt == a.pt;
            }
            if (strcasecmp(this->name, "G7221") == 0) {
                return strcasecmp(this->name, a.name) == 0 && this->rate == a.rate && this->chn == a.chn && this->profile == a.profile && this->bitrate == a.bitrate;
            }
            return strcasecmp(this->name, a.name) == 0 && this->rate == a.rate && this->chn == a.chn && this->profile == a.profile;
        }
        bool operator==(const CodecInst& a) const
        {
            if (a.pt == 8 || a.pt == 18)
            {
                return this->pt == a.pt;
            }
            if (strcasecmp(this->name, "G7221") == 0) {
                return strcasecmp(this->name, a.name) == 0 && this->rate == a.rate && this->chn == a.chn && this->profile == a.profile && this->bitrate == a.bitrate;
            }
            return strcasecmp(this->name, a.name) == 0 && this->rate == a.rate && this->chn == a.chn && this->profile == a.profile;
        }
    };

    FrameFormat getFrameFormat(int pt);
    bool getCodecInst(int pt, CodecInst& codec);
    bool getCodecInst(FrameFormat format, CodecInst& codec);
    bool getCodecInst(const char* name, std::list<CodecInst> &codecs);
    int getPT(FrameFormat format, int profile = 0);
    int getClockrate(int pt);
    int32_t getSampleRate(const FrameFormat format);
    uint32_t getChannels(const FrameFormat format);
    std::string getFormatName(const FrameFormat format);
}

#endif