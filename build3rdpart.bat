cd 3rdpart\source
rmdir /S /Q build

mkdir build\jthread
cd build\jthread
cmake -G "Visual Studio 16 2019" -A x64 -DCMAKE_INSTALL_PREFIX=../../../ ../../jthread
devenv jthread.sln /Build "Debug|x64"
devenv jthread.sln /Build "Debug|x64" /Project INSTALL.vcxproj
cd ..\..\

mkdir build\jrtplib
cd build\jrtplib
cmake -G "Visual Studio 16 2019" -A x64 -DCMAKE_INSTALL_PREFIX=../../../ ../../jrtplib
devenv jrtplib.sln /Build "Debug|x64"
devenv jrtplib.sln /Build "Debug|x64" /Project INSTALL.vcxproj
cd ..\..\

mkdir build\libhv
cd build\libhv
cmake -G "Visual Studio 16 2019" -A x64 -DWITHOUT_HTTP_CONTENT=1 -DCMAKE_INSTALL_PREFIX=../../../ ../../libhv
devenv hv.sln /Build "Debug|x64"
devenv hv.sln /Build "Debug|x64" /Project INSTALL.vcxproj
cd ..\..\

mkdir build\libsdptransform
cd build\libsdptransform
cmake -G "Visual Studio 16 2019" -A x64 -DCMAKE_INSTALL_PREFIX=../../../ ../../libsdptransform
devenv sdptransform.sln /Build "Debug|x64"
devenv sdptransform.sln /Build "Debug|x64" /Project INSTALL.vcxproj
cd ..\..\

mkdir build\ZLToolKit
cd build\ZLToolKit
cmake -G "Visual Studio 16 2019" -A x64 -DCMAKE_INSTALL_PREFIX=../../../ ../../ZLToolKit
devenv ZLToolKit.sln /Build "Debug|x64"
devenv ZLToolKit.sln /Build "Debug|x64" /Project INSTALL.vcxproj
cd ..\..\

mkdir build\rtc.aec
cd build\rtc.aec
cmake -G "Visual Studio 16 2019" -A x64 -DCMAKE_INSTALL_PREFIX=../../../ ../../rtc.aec
devenv aec.sln /Build "Debug|x64"
devenv aec.sln /Build "Debug|x64" /Project INSTALL.vcxproj
cd ..\..\

mkdir build\rnnoise
cd build\rnnoise
cmake -G "Visual Studio 16 2019" -A x64 -DCMAKE_INSTALL_PREFIX=../../../ ../../rnnoise
devenv rnnoise.sln /Build "Debug|x64"
devenv rnnoise.sln /Build "Debug|x64" /Project INSTALL.vcxproj
cd ..\..\

cd opus-1.1.2\win32\VS2010
devenv opus.sln /Build "Debug|x64"
cd ..\..\
xcopy /s /e /y opus-1.1.2\win32\VS2010\x64\Debug\opus.lib ..\3rdpart\lib
xcopy /s /e /y opus-1.1.2\win32\VS2010\x64\Debug\celt.lib ..\3rdpart\lib
xcopy /s /e /y opus-1.1.2\win32\VS2010\x64\Debug\silk_common.lib ..\3rdpart\lib
xcopy /s /e /y opus-1.1.2\win32\VS2010\x64\Debug\silk_fixed.lib ..\3rdpart\lib
xcopy /s /e /y opus-1.1.2\win32\VS2010\x64\Debug\silk_float.lib ..\3rdpart\lib