// created by gyj 2024-3-27
#ifndef P_Sdl2AudioFrameRender_h
#define P_Sdl2AudioFrameRender_h

#include "AudioFrameRender.h"
#include <SDL2/SDL.h>
#include <resampler/resampler.h>
#include <resampler/push_resampler.h>
namespace panocom
{
    class Sdl2AudioFrameRender : public AudioFrameRender
    {
    public:
        Sdl2AudioFrameRender(const std::string& jsonParams);
        ~Sdl2AudioFrameRender() override;

        void onFrame(const std::shared_ptr<Frame>& frame) override;

    private:
        SDL_AudioDeviceID devid_ = 0;
        FILE* pf_ = nullptr;
        int fmt_ = 0;
        int samplerate_ = 0;
        bool first_ = true;

#ifdef WEBRTC_RESAMPLE_ANOTHER
        nswebrtc::PushResampler<int16_t> resampler_;
#else
        nswebrtc::Resampler resampler_;
#endif       
        bool initResampler_ = false;
        int source_samplerate_;        
    };
}

#endif