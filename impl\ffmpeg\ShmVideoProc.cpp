#include "ShmVideoProc.h"
#include <ljcore/jlog.h>

ShmVideoProc::ShmVideoProc()
{
	
}

ShmVideoProc::~ShmVideoProc()
{
	for (int i = 0; i < m_maxChn; i++)
	{

#ifdef WIN32
		UnmapViewOfFile(m_shm[i]);
		CloseHandle(m_handle[i]);
#else
		shmdt(m_shm[i]);
#endif
	}
}

ShmVideoProc* ShmVideoProc::s_obj = NULL;
ShmVideoProc* ShmVideoProc::Inst()
{
	if (s_obj == NULL)
		s_obj = new ShmVideoProc();
	return s_obj;
}

void ShmVideoProc::Free()
{
	if (s_obj != NULL)
	{
		delete s_obj;
		s_obj = NULL;
	}
}

void ShmVideoProc::init(uint8_t maxChn, uint32_t shmStartId)
{
	m_maxChn = maxChn;
	m_shm.resize(maxChn);
	m_shms.resize(maxChn);
	m_id.resize(maxChn);
	m_currentYuv.resize(maxChn);
#ifdef WIN32
	m_handle.resize(maxChn);
#endif // WIN32
	for (int i = 0; i < maxChn; i++)
	{
#ifdef WIN32
		char shmId[100] = { 0 };
		sprintf(shmId, "%d", shmStartId + i + 1);
		m_handle[i] = CreateFileMappingA(INVALID_HANDLE_VALUE, NULL, PAGE_READWRITE, 0, sizeof(YUVShm) * MAX_FRAME_BUFFER, shmId);
		if (m_handle[i] == NULL)
		{
			//DEBUG_PRINT_LASTWORD("OpenFileMappingA fail", "channel", i);
			break;
		}

		m_shm[i] = (char*)MapViewOfFile(m_handle[i], FILE_MAP_ALL_ACCESS, 0, 0,
			sizeof(YUVShm) * MAX_FRAME_BUFFER);
		if (m_shm[i] == NULL)
		{
			//DEBUG_PRINT_LASTWORD("MapViewOfFile fail", "channel", i);
			break;
		}

		//memset(m_shm, 0, sizeof(YUVShm) * MAX_FRAME_BUFFER);
		m_id[i] = 0;
		for (int j = 0; j < MAX_FRAME_BUFFER; ++j)
		{
			YUVShm* data = (YUVShm*)(m_shm[i] + j * sizeof(YUVShm));
			//data->is_valid = 0;
			if (data->id > m_id[j])
				m_id[j] = data->id;
			m_shms[i].push_back(data);
		}
#else
		int keyid = shmStartId + i + 1;
		int shmid = shmget((key_t)keyid, sizeof(YUVShm) * MAX_FRAME_BUFFER, 0666 | IPC_CREAT);
        jinfo("init  keyid:%d shmid:%d ",keyid,shmid);
		if (shmid == -1)
		{
            jerror("shmget fail shmid == -1  channel:%d",i);
			break;
		}

		m_shm[i] = (char*)shmat(shmid, 0, 0);
		if (m_shm[i] == (void*)-1)
		{
            jerror("shmat fail m_shm[i] == (void*)-1  channel:%d",i);
			break;
		}

		//memset(m_shm[i], 0, sizeof(YUVShm) * MAX_FRAME_BUFFER);
		m_id[i] = 0;
		for (int j = 0; j < MAX_FRAME_BUFFER; ++j)
		{
			YUVShm* data = (YUVShm*)(m_shm[i] + j * sizeof(YUVShm));
			if (data->id > m_id[j])
				m_id[j] = data->id;
			m_shms[i].push_back(data);
		}
        jinfo("init  i:%d m_shm[i]:%d ",i,m_id[i] );
#endif
	}

}

//目前只支持一个通道只由一个线程调用
YUVShm* ShmVideoProc::GetWriteShm(uint8_t channel)
{
	if (channel >= m_maxChn)
	{
        jerror("channel >= MAX_CHN_CNTL channel:%d >= m_maxChn:%d",channel,m_maxChn);
		return NULL;
	}

	if (m_shms[channel].size() <= 0)
		return NULL;

	YUVShm* yuvShm = NULL;
	{
		//AutoLock lock(m_lock[channel]);
		uint32_t minId = 0xffffffff;
		auto minIndex = m_shms[channel].end();
		auto iter = m_shms[channel].begin();
		for (; iter != m_shms[channel].end(); ++iter)
		{
			if (minId > (*iter)->id)
			{
				minId = (*iter)->id;
				minIndex = iter;
			}

			if ((*iter)->is_valid == 0)
				break;
		}



		if (iter != m_shms[channel].end())
		{
			yuvShm = *iter;
	//		m_shms[channel].erase(iter);
	//		m_shms[channel].push_back(yuvShm);
		}
		else if (minIndex != m_shms[channel].end())
		{
			yuvShm = *minIndex;
	//		m_shms[channel].erase(minIndex);
	//		m_shms[channel].push_back(yuvShm);
		}
		else
		{
			return NULL;
		}
		yuvShm->is_valid = 0;	//���븳0֮���ٵ���ID�������֡�ᱻ��������ˢ�£��γ���Ӱ
		yuvShm->id = ++m_id[channel];
		m_currentYuv[channel] = yuvShm;
		//if (m_id[channel] == 0)
		//{
		//	for (int i = 0; i < m_shms[channel].size(); ++i)
		//	{
		//		m_shms[channel][i]->id += m_shms[channel].size();
		//	}
		//}
	}
	return yuvShm;
}

YUVShm * ShmVideoProc::GetCurrentShm(uint8_t channel)
{
	return m_currentYuv[channel];
}

void ShmVideoProc::ResetShmData(uint8_t channel)
{
	if (channel >= m_maxChn)
	{
        jerror("channel >= MAX_CHN_CNTL channel:%d >= m_maxChn:%d",channel,m_maxChn);
        return;
	}

	if (m_shms[channel].size() <= 0)
		return;

	auto iter = m_shms[channel].begin();
	for (; iter != m_shms[channel].end(); ++iter)
	{
		(*iter)->is_valid = 0;
	}
}