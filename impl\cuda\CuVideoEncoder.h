#ifndef P_CuVideoEncoder_h
#define P_CuVideoEncoder_h

#include <cuda.h>
#include <nvEncodeAPI.h>
#include <hv/EventLoopThread.h>
#include "VideoFrameEncoder.h"

namespace panocom
{
    class Frame;
    class CuEncoderInputNV12Frame;
    class CuVideoEncoder : public VideoFrameEncoder
    {
    public:
        CuVideoEncoder(const std::string &jsonParams);
        ~CuVideoEncoder() override;

        void onFrame(const std::shared_ptr<Frame> &frame) override;
        void start() override;
        void stop() override;
    private:
        void LoadNvEncApi();
        void CreateEncoder(NV_ENC_INITIALIZE_PARAMS *pEncodeParams);
        void DestroyEncoder();
        void CreateDefaultEncoderParams(NV_ENC_INITIALIZE_PARAMS *pIntializeParams, GUID codecGuid, GUID presetGuid, NV_ENC_TUNING_INFO tuningInfo = NV_ENC_TUNING_INFO_UNDEFINED);
        void GetSequenceParams(std::vector<uint8_t> &seqParams);
        NVENCSTATUS DoEncode(const std::shared_ptr<CuEncoderInputNV12Frame> &f);

    private:
        hv::EventLoopThread thread_;

        uint32_t width_;
        uint32_t height_;
        void *encoder_ = nullptr;
        NV_ENCODE_API_FUNCTION_LIST nvenc_;
        NV_ENC_CONFIG encodeConfig_ = {};
        bool encoderInitialized_ = false;
        NV_ENC_OUTPUT_PTR bitstreamOutputBuffer_;
        int fmt_;
        int gop_;
        int fps_;
        int cudaId_ = 0;
        int gpuIndex_ = 0;
    };
}

#endif