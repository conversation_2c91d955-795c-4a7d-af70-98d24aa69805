#include "AlsaAudioFrameRender.h"

#include <iostream>

#include <json.hpp>
#include "Utils.h"

namespace panocom {
namespace {
const std::string default_alsa_player { "hw:0,0" };
std::vector<int> cards { 0, 1 };
}
AlsaAudioFrameRender::AlsaAudioFrameRender(const std::string &jsonParams)
    : card_(0)
    , device_(0)
    , sample_rate_(48000)
    , channel_(2)
    , ptime_(20)
    , frames_(960)
    , running_(false)
    , pcm_handle_(nullptr)
    , use_hw_(true)
    , internal_gain_control_(false)
    , gain_(0)
    , init_done_(false) {
    name_ = "AlsaAudioFrameRender";
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    if (j.contains("dev")) {
        dev_ = j["dev"];
        if (dev_ < 0) dev_ = 0;
        if (dev_ < cards.size()) card_ = cards[dev_];
    } else {
        if (j.contains("card"))
            card_ = j["card"];
        if (j.contains("device"))
            device_ = j["device"];
    }
    if (j.contains("channel")) {
        channel_ = j["channel"];
    }
    if (j.contains("samplerate")) {
        sample_rate_ = j["samplerate"];
    }
    if (j.contains("ptime"))
        ptime_ = j["ptime"];
    if (card_ < 0 && device_ < 0) alsa_player_ = default_alsa_player;
    else {
        alsa_player_ = "hw:";
        alsa_player_ += std::to_string(card_) + "," + std::to_string(device_);
    }   
    
    playback_device.index = dev_;
    if (use_hw_ && AlsaUtils::GetAlsaHWPcmDevice(SND_PCM_STREAM_PLAYBACK, playback_device)) {
        jinfo("Playback Device: %s", playback_device.hw_string.c_str());
        alsa_player_ = playback_device.hw_string;
    } 
    std::cout << jsonParams << std::endl;
    // frames_ = sample_rate_ * ptime_ / 1000;
    fmt_ = Frame::getPCMFormat(channel_, sample_rate_);
    running_ = true;
    notifycallbacksThread_.start();
    playout_future_ = std::async([this]() -> bool {
        // if (!Init()) {
        //     jerror("ERROR: Failed to init AlsaAudioFrameRender.");
        //     return false;
        // }
        int16_t *out_buf = nullptr;
        while (running_) {
            if (!pcm_handle_) {
                if (!Init()) {
                    jerror("ERROR: Failed to init AlsaAudioFrameRender.");
                    Release();
                    std::this_thread::sleep_for(std::chrono::seconds(3));
                    continue;
                } else {
                    if (out_buf) {
                        delete[] out_buf;
                        out_buf = nullptr;
                    }
                    out_buf = new int16_t[frames_];
                }
            }
            std::unique_lock<std::mutex> lock(mutex_);
            queue_available_.wait(lock, [this] { return !frame_queue_.empty() || !running_; });
            if (!running_) {
                break;
            }
            auto frame = std::move(frame_queue_.front());
            frame_queue_.pop();
            lock.unlock();

            uint8_t *data = frame->getFrameBuffer();
            size_t data_size = frame->getFrameBufferSize();
            if (internal_gain_control_ && gain_ != 0 && out_buf) {
                adjust_gain(out_buf, data_size / 2, (int16_t*)data, data_size / 2, gain_);
                data = (int8_t*)out_buf;
            }
            uint8_t *origin_data = data;
            size_t origin_size = data_size;
            while (data_size > 0 && !muted_) {
                if (!pcm_handle_) {
                    if (!Init()) {
                        Release();
                        jerror("ERROR: Failed to init AlsaAudioFrameRender.");
                        std::this_thread::sleep_for(std::chrono::seconds(3));
                        return false;
                    }
                }
                snd_pcm_uframes_t frames_to_write = data_size / (channel_ * 2);
                snd_pcm_uframes_t size = std::min(frames_to_write, frames_);
                snd_pcm_sframes_t rc = 0;
                // if (internal_gain_control_ && gain_ != 0 && out_buf) {
                //     adjust_gain(out_buf, size, (int16_t*)data, size, gain_);
                //     rc = snd_pcm_writei(pcm_handle_, out_buf, size);
                // } else 
                {
                    rc = snd_pcm_writei(pcm_handle_, data, size);
                }

                if (rc == -EPIPE) {
                    snd_pcm_prepare(pcm_handle_);
                } else if (rc < 0) {
                    jerror("Error playing audio: %s", snd_strerror(rc));
                    Release();
                    std::this_thread::sleep_for(std::chrono::seconds(3));
                    continue;
                } else {
                    size_t written_size = rc * channel_ * 2;
                    data += written_size;
                    data_size -= written_size;
                }
            }
            if (notifycallbacks_.find("AudioRendererStatus") != notifycallbacks_.end() && notifycallbacks_["AudioRendererStatus"].cb) {
                volume_ = quantize_volume(caculateRMS((int16_t *)origin_data, origin_size / 2));
                if (notifycallbacksThread_.isRunning()) {
                    notifycallbacksThread_.loop()->runInLoop([this](){
                        nlohmann::json notify;
                        notify["deviceId"] = dev_;
                        notify["samplerate"] = sample_rate_;
                        notify["channel"] = channel_;
                        notify["fmt"] = fmt_;
                        notify["ptime"] = ptime_;
                        notify["volume"] = volume_;
                        if (notifycallbacks_.find("AudioRendererStatus") != notifycallbacks_.end() && notifycallbacks_["AudioRendererStatus"].cb) {
                            notifycallbacks_["AudioRendererStatus"].cb("AudioRendererStatus", notify.dump(), notifycallbacks_["AudioRendererStatus"].param);
                        }
                    });
                }  
            }
        }
        if (out_buf) {
            delete[] out_buf;
            out_buf = nullptr;
        }
        Release();
        return true;
    });
}

AlsaAudioFrameRender::~AlsaAudioFrameRender() {
    running_ = false;
    if (check_timeout_thread_.isRunning()) {
        check_timeout_thread_.stop(true);
        check_timeout_thread_.join();
    }
    queue_available_.notify_all();
    if (playout_future_.valid()) {
        auto status = playout_future_.wait_for(std::chrono::milliseconds(500));
        if (status == std::future_status::timeout) {
            jerror("audio render stop timeout");
        } else if (status == std::future_status::ready) {
            bool res = playout_future_.get();
            jinfo("audio render stop status: %d", res);
        }
    }
    if (notifycallbacksThread_.isRunning()) {
        notifycallbacksThread_.stop(true);
        notifycallbacksThread_.join();
    }
    // if (ofs_.is_open()) ofs_.close();
    // if (ofs2_.is_open()) ofs2_.close();
}

bool AlsaAudioFrameRender::Init() {
    // jinfo("Init alsa render success.");
    int32_t pcm = snd_pcm_open(&pcm_handle_, alsa_player_.c_str(), SND_PCM_STREAM_PLAYBACK, 0);
    if (pcm < 0) {
        jerror("ERROR: Can't open %s PCM device: %s", alsa_player_.c_str(), snd_strerror(pcm));
        return false;
    }
    int dir = 0;
    snd_pcm_hw_params_t *params = nullptr;
    snd_pcm_hw_params_alloca(&params);
    pcm = snd_pcm_hw_params_any(pcm_handle_, params);
    if (pcm < 0) {
        jerror("ERROR: snd_pcm_hw_params_any failed: %s", snd_strerror(pcm));
        return false;
    }
    pcm = snd_pcm_hw_params_set_access(pcm_handle_, params, SND_PCM_ACCESS_RW_INTERLEAVED);
    if (pcm < 0) {
        jerror("ERROR: Can't set interleaved mode: %s",snd_strerror(pcm));
        return false;
    }
    
    bool is_channel_supported = false;
    bool is_rate_supported = false;
    // acquire supported channel and rate
    playback_device.channels.clear();
    for (const uint32_t &channel : {1, 2}) {
        if (snd_pcm_hw_params_test_channels(pcm_handle_, params, channel) == 0) {
            jinfo("%s Supported channel number is %d", playback_device.hw_string.c_str(), channel);
            playback_device.channels.push_back(channel);
            if (channel == channel_) {
                is_channel_supported = true;
            }
        }
    }
    playback_device.sample_rates.clear();
    for (const uint32_t &rate : { 16000, 24000, 32000, 44100, 48000, 96000 }) {
        if (snd_pcm_hw_params_test_rate(pcm_handle_, params, rate, 0) == 0) {
            jinfo("%s Supported rate is %d", playback_device.hw_string.c_str(), rate);
            playback_device.sample_rates.push_back(rate);
            if (rate == sample_rate_) {
                is_rate_supported = true;
            }
        }
    }
    playback_device.formats.clear();
    for (const std::string &format : {"S16_LE", "S24_3LE"}) {
        if (snd_pcm_hw_params_test_format(pcm_handle_, params,
                                          snd_pcm_format_t(snd_pcm_format_value(format.c_str()))) == 0) {
            jinfo("%s Supported format is %s", playback_device.hw_string.c_str(), format.c_str());
            playback_device.formats.push_back(format);
        }
    }
    // Use the first supported rate and format if the specified rate or format are not supported
    if (!is_rate_supported && !playback_device.sample_rates.empty()) {
        sample_rate_ = playback_device.sample_rates[0];
        jinfo("%s Use default sample rate is %d", playback_device.hw_string.c_str(), sample_rate_);
    }
    if (!is_channel_supported && !playback_device.channels.empty()) {
        channel_ = playback_device.channels[0];
        jinfo("%s Use default channel is %d", playback_device.hw_string.c_str(), channel_);
    }

    pcm = snd_pcm_hw_params_set_format(pcm_handle_, params, SND_PCM_FORMAT_S16_LE);
    if (pcm < 0) {
        jerror("ERROR: Can't set format: %s", snd_strerror(pcm));
        return false;
    }
    pcm = snd_pcm_hw_params_set_channels(pcm_handle_, params, channel_);
    if (pcm < 0) {
        jerror("ERROR: Can't set channels number: %s", snd_strerror(pcm));
        return false;
    }
    pcm = snd_pcm_hw_params_set_rate(pcm_handle_, params, sample_rate_, dir);
    if (pcm < 0) {
        jerror("ERROR: Can't set rate %d: %s ", sample_rate_, snd_strerror(pcm));
        return false;
    }
    frames_ = sample_rate_ * ptime_ / 1000;
    if (use_hw_) {
        pcm = snd_pcm_hw_params_set_period_size(pcm_handle_, params, frames_, dir);
        if (pcm < 0) {
            jerror("ERROR: Can't set period size: %s", snd_strerror(pcm));
            return false;
        }
    } else {
        pcm = snd_pcm_hw_params_set_period_size_near(pcm_handle_, params, &frames_, &dir);
        if (pcm < 0) {
            jerror("ERROR: Can't set period size: %s", snd_strerror(pcm));
            return false;
        }
    }

    // uint32_t period_time = 10000;
    // pcm = snd_pcm_hw_params_set_period_time_near(pcm_handle_, params, &period_time, &dir);
    // if (pcm < 0) {
    //     std::cerr << "ERROR: Can't set period time: " << snd_strerror(pcm) << std::endl;
    //     return false;
    // }
    pcm = snd_pcm_hw_params(pcm_handle_, params);
    if (pcm < 0) {
        jerror("ERROR: Can't set hardware parameters. ", snd_strerror(pcm));
        return false;
    }
    snd_output_t *out;
    snd_output_stdio_attach(&out, stdout, 0);
    snd_pcm_hw_params_dump(params, out);
    snd_output_close(out);
    // pcm = snd_pcm_hw_params_get_period_size(params, &frames_, &dir);
    // if (pcm < 0) {
    //     std::cerr << "ERROR: Can't get period. " << snd_strerror(pcm) << std::endl;
    //     return false;
    // }
    // std::cerr << "frames = " << frames_ << std::endl;
    jinfo("sample rate = %d", sample_rate_);
    init_done_ = true;
    return true;
}

bool AlsaAudioFrameRender::Release() {
    if (pcm_handle_) {
        // snd_pcm_drain(pcm_handle_);
        snd_pcm_close(pcm_handle_);
        pcm_handle_ = nullptr;
    }
    return true;
}

void AlsaAudioFrameRender::onFrame(const std::shared_ptr<Frame> &frame) {
    if (!running_ || !pcm_handle_ || !init_done_)
        return;
    int sample_rate = 0;
    int channels = 0;
    auto f = frame;
    if (Frame::getSamplerate(frame->getFrameFormat(), sample_rate, channels)) {
        if (sample_rate_ != sample_rate || channel_ != channels) {

            // if (!ofs_.is_open()) ofs_.open("play.pcm", std::ios::binary | std::ios::out);
            // ofs_.write(f->getFrameBuffer(), f->getFrameBufferSize());
            
            auto frame = Frame::CreateFrame((FrameFormat)fmt_);
            frame->createFrameBuffer(f->getFrameSize() * sample_rate_ / sample_rate * channel_ / channels);
            memset(frame->getFrameBuffer(), 0, frame->getFrameSize());
            if (!initResampler_ || sample_rate != source_samplerate_)
            {
                source_samplerate_ = sample_rate;
                initResampler_ = true;
#ifdef WEBRTC_RESAMPLE_ANOTHER
                resampler_.InitializeIfNeeded(sample_rate, sample_rate_, 1);
#else
                resampler_.ResetIfNeeded(sample_rate, sample_rate_, 1);
#endif
            }
#ifdef WEBRTC_RESAMPLE_ANOTHER
            resampler_.Resample((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2 / channel_ * channels);
#else
            size_t outlen = 0;
            resampler_.Push((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2 / channel_ * channels, outlen);
#endif
            // if (!ofs2_.is_open()) ofs2_.open("play2.pcm", std::ios::binary | std::ios::out);
            // ofs2_.write(frame->getFrameBuffer(), frame->getFrameBufferSize() / 2);
            // TODO: 单双通道转换
            if (channel_ == 2 && channels == 1) {
                uint16_t *data = (int16_t*)frame->getFrameBuffer();
                uint32_t frame_size = frame->getFrameBufferSize() / channel_ / 2;
                if (channel_ == 2 && channels == 1)
                    mono_to_stereo(data, frame_size); 
            }
            f = frame;
        }
        {
            std::lock_guard<std::mutex> lock(mutex_);
            if (frame_queue_.size() > 20) {
                frame_queue_.pop();
            }
            frame_queue_.emplace(f);
        }
        queue_available_.notify_one();
    }
}

void AlsaAudioFrameRender::setFrameTimeoutNotify(int ms, void* param, const TimeoutNotifyCallback& cb) {
    if (ms <= 0) {
        jinfo("AlsaAudioFrameRender setFrameTimeoutNotify timeout must > 0");
        return;
    }
    FramePipeline::setFrameTimeoutNotify(ms, param, cb);
    if (!check_timeout_thread_.isRunning()) {
        check_timeout_thread_.loop()->setInterval(30, [this](hv::TimerID id) {
            checkFrameTimeout();
        });
        check_timeout_thread_.start(true);
    }
}

void AlsaAudioFrameRender::setFrameTimeoutNotify2(int ms, void* param, const TimeoutNotifyCallback& cb) {
	if (ms <= 0) {
		jinfo("AlsaAudioFrameRender setFrameTimeoutNotify timeout must > 0");
		return;
	}
	FramePipeline::setFrameTimeoutNotify2(ms, param, cb);
	if (!check_timeout_thread_.isRunning()) {
		check_timeout_thread_.loop()->setInterval(30, [this](hv::TimerID id) {
			checkFrameTimeout();
		});
		check_timeout_thread_.start(true);
	}
}

int AlsaAudioFrameRender::updateParam(const std::string& jsonParams) {
    jinfo("###AlsaAudioFrameRender::updateParam %s", jsonParams.c_str());
    int ret = 0;
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    if (j.contains("gain")) {
        gain_ = j["gain"];
    }
    return ret;
}
} // namespace panocom
