// created by gyj 2024-6-19
#ifndef P_MixedAudioSpec_h
#define P_MixedAudioSpec_h

#include "FramePipeline.h"
#include <ptoolkit/bytebuffer.h>
#include <hv/EventLoopThread.h>
#include <malloc.h>
#include <stdio.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <arpa/inet.h>
#include <net/if.h>
#include <string.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <pthread.h>
#include <fcntl.h>
#include <stdint.h>
#include <unordered_map>
#include <PNPcm/HDPcm.h>
#include <resampler/resampler.h>
#include <resampler/push_resampler.h>

namespace panocom
{
    class MixedAudioSpec : public FramePipeline
    {
    public:
        MixedAudioSpec(const std::string& jsonParams);
        ~MixedAudioSpec() override;

        void onFrame(const std::shared_ptr<Frame>& frame) override;
        void start() override;
        void stop() override;
        int updateParam(const std::string& jsonParam) override;
        void addAudioSource(const FramePipeline::Ptr &source) override;
        void removeAudioSource(const FramePipeline::Ptr &source) override;
    private:
        static void openDev(int dev);
        static void closeDev(int dev);
        void addSource(const std::shared_ptr<FramePipeline> &source);
        void removeSource(const std::shared_ptr<FramePipeline> &source);
        void fillMixSource(const std::shared_ptr<Frame>& frame);
    private:
        static std::string devName_;
        static int pcmFd_;
        static int maxChn_;
        static std::vector<int> chnState_;
        static std::mutex mutex_;
        int ptime_ = 20;
        bool block_ = false;
        int samplerate_ = 16000;
        int codec_;
        int dev_;
        bool renderOnly_ = false;
        
        hv::EventLoopThread readThread_;
        hv::EventLoopThread writeThread_;
        static std::shared_ptr<MixedAudioSpec> inst_;
        ByteBuffer bbuf_;
        FILE* sendf = nullptr;
        FILE* recvf = nullptr;

        bool isSetReadThreadAffinity = false;
        bool isSetWriteThreadAffinity = false;

#ifdef WEBRTC_RESAMPLE_ANOTHER
        nswebrtc::PushResampler<int16_t> resampler_;
#else
        nswebrtc::Resampler resampler_;
#endif       
        bool initResampler_ = false;
        int source_samplerate_;

        std::mutex sources_mutex_;
        std::unordered_map<void*, ByteBuffer> audio_sources_;
        bool need_mixer_ = true;
        int number_of_channels_= 1;
        size_t samples_per_channel_;
        size_t max_buffer_size_;
        int render_ref = 0;
    };

// for render
class MixedAudioSpecManager
{
public:
    /* data */
    static MixedAudioSpecManager &instance();
    std::shared_ptr<MixedAudioSpec> CreateMixedAudioSpec(const std::string& jsonParams);
private:
    MixedAudioSpecManager(/* args */) = default;
    ~MixedAudioSpecManager();

private:
    std::unordered_map<int, std::shared_ptr<MixedAudioSpec>> renders_;
};
}

#endif