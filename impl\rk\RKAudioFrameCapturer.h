#ifndef IMPL_RK_RKAUDIOFRAMECAPTURER_H_
#define IMPL_RK_RKAUDIOFRAMECAPTURER_H_
#include "AudioFrameCapturer.h"

#include <future>
#include <hv/EventLoopThread.h>
namespace panocom
{
class RKAudioFrameCapturer : public AudioFrameCapturer
{
private:
    /* data */
public:
    RKAudioFrameCapturer(const std::string& jsonParams);
    ~RKAudioFrameCapturer();
private:
    uint8_t nChn_;   // 采集通道序号
    uint8_t channel_count_;   // 声道数

    bool running_;
    std::future<bool> capture_future_;

	std::shared_ptr<hv::EventLoopThread> loop_thread_;
	hv::EventLoopPtr loop_;
};


} // namespace panocom


#endif