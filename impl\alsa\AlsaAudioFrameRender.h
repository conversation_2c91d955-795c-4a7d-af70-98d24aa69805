#ifndef IMPL_RK_ALSAAUDIOFRAMERENDER_H_
#define IMPL_RK_ALSAAUDIOFRAMERENDER_H_
#include <condition_variable>
#include <queue>
#include <future>
#include <alsa/asoundlib.h>

#include "AudioFrameRender.h"
#include <ptoolkit/bytebuffer.h>
#include "Frame.h"
#include <resampler/resampler.h>
#include <resampler/push_resampler.h>
#include <hv/EventLoopThread.h>

#include "alsa_utils.h"
// #include <fstream>

namespace panocom
{
class AlsaAudioFrameRender : public AudioFrameRender
{
private:
    /* data */
public:
    AlsaAudioFrameRender(const std::string& jsonParams);
    ~AlsaAudioFrameRender();
    void onFrame(const std::shared_ptr<Frame>& frame) override;
	void setFrameTimeoutNotify(int ms, void* param, const TimeoutNotifyCallback& cb) override;
	void setFrameTimeoutNotify2(int ms, void* param, const TimeoutNotifyCallback& cb) override;
    int updateParam(const std::string& jsonParams) override;
private:
    bool Init();
    bool Release();
private:
    int32_t card_;
    int32_t device_;
    int32_t dev_;
    uint32_t sample_rate_;
    uint32_t channel_;
    uint32_t ptime_;
    std::string alsa_player_;
    int fmt_ = 0;

    snd_pcm_t *pcm_handle_;
    snd_pcm_uframes_t frames_;
    std::queue<std::shared_ptr<Frame>> frame_queue_;
    std::mutex mutex_;
    std::condition_variable queue_available_;
    std::future<bool> playout_future_;
    bool running_;

    // std::ofstream ofs_;
    // std::ofstream ofs2_;
#ifdef WEBRTC_RESAMPLE_ANOTHER
    nswebrtc::PushResampler<int16_t> resampler_;
#else
    nswebrtc::Resampler resampler_;
#endif
    bool initResampler_ = false;
    int source_samplerate_;
    hv::EventLoopThread check_timeout_thread_;

    AlsaHWPcmDevice playback_device;
    bool use_hw_;
    bool internal_gain_control_;
    int gain_;

    bool init_done_;

    // 是否开启音量检测，默认关闭
    bool enable_volume_ = false;
    // 音量[0, 100]，每20ms调整一次音量
    int volume_;
    hv::EventLoopThread notifycallbacksThread_;
};


} // namespace panocom

#endif