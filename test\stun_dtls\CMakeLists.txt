cmake_minimum_required(VERSION 3.10)

project(Test LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 11)

add_definitions(-Wall -O3 -g -fexceptions -fpermissive)
if (ENABLE_VIDEO)
  add_definitions(-DENABLE_VIDEO)
endif ()
if (USE_CUDA)
  add_definitions(-DUSE_CUDA)
endif ()
if (USE_JRTP)
  add_definitions(-DUSE_JRTP)
endif ()
if (USE_WEBRTC)
  add_definitions(-DWEBRTC_POSIX -DWEBRTC_LINUX)
endif ()

if (ENABLE_VIDEO)
    if (USE_CUDA)
        find_library(CUVID_LIB nvcuvid)
        find_library(NVENCODEAPI_LIB nvidia-encode)
        find_package(CUDA)

        find_library(FREEGLUT_LIB glut)
        find_library(GLEW32_LIB GLEW)
        find_library(X11_LIB X11)
        find_library(GL_LIB GL)
        find_library(CUDART_LIB cudart HINTS ${CUDA_TOOLKIT_ROOT_DIR}/lib64)
    endif()
endif()

include_directories(${CMAKE_CURRENT_SOURCE_DIR}
${CMAKE_PREFIX_PATH}/include
${CUDA_INCLUDE_DIRS})
link_directories(${CMAKE_PREFIX_PATH}/lib)
link_directories(/usr/local/cuda-12.4/targets/x86_64-linux/lib)

set(PROJECT_SOURCES test.cpp)

add_executable(Test ${PROJECT_SOURCES})

target_link_libraries(${PROJECT_NAME} PRIVATE MediaPipeline ptoolkit jrtp jthread ljcore hv_static ZLToolKit aec rnnoise opus g729 fdk-aac DTLSTool nice glib-2.0 gio-2.0 gobject-2.0 gmodule-2.0 z ffi pcre2-8 srtp2 PNPcm ssl crypto pthread dl)
if (ENABLE_RK)
    target_link_libraries(${PROJECT_NAME} PRIVATE drm rockchip_mpp rga asound)
endif()

install(TARGETS ${PROJECT_NAME} RUNTIME DESTINATION ${CMAKE_INSTALL_PREFIX}/bin/)