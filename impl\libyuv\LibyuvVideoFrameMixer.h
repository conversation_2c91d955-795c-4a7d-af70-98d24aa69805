// created by gyj 2024-2-21
#ifndef P_LibyuvVideoFrameMixer_h
#define P_LibyuvVideoFrameMixer_h

#include <hv/EventLoopThread.h>

#include "VideoFrameMixer.h"
#include "VideoLayout.h"
#include "FrameBufferManager.h"

extern "C" {
#include <libavutil/imgutils.h>
#include <libavutil/opt.h>
#include <libavcodec/avcodec.h>
#include <libswscale/swscale.h>
}

namespace panocom
{
    class LibyuvVideoFrameMixer : public VideoFrameMixer
    {
    public:
        LibyuvVideoFrameMixer(const std::string& jsonParams);
        ~LibyuvVideoFrameMixer() override;

        void onFrame(const std::shared_ptr<Frame>& frame) override;
        void start() override;
        void stop() override;
    private:
        void generateOutputFrame();

    private:
        long outputFrameGap_;
        long outputFrameTick_;

        std::vector<Region> videoLayout_;

        int x_;
        int y_;
        int width_;
        int height_;

        std::shared_ptr<Frame> outputFrame_;
        std::unique_ptr<FrameBufferManager> frameBufferManager_;
        hv::EventLoopThread thread_;
    };
}

#endif