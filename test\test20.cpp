#include "MediaPipeline.h"
#include <json.hpp>
#include <stdio.h>
#include <ljcore/jlog.h>
#include <hv/EventLoop.h>

using namespace panocom;

#if 0
int main(int argc, char *argv[])
{
    jlog_init(nullptr);
    auto loop = std::make_shared<hv::EventLoop>();
    nlohmann::json j;
    j["dev"] = atoi(argv[1]);
    auto render = AudioFrameRender::CreateAudioRender("TinyALSARender", j.dump());
    j["dev"] = atoi(argv[2]);
    auto capture = AudioFrameCapturer::CreateAudioCapturer("TinyALSACapturer", j.dump());
    loop->run();
    return 0;
}
#else
int main(int argc, char *argv[])
{
    jlog_init(nullptr);
    auto loop = std::make_shared<hv::EventLoop>();
    nlohmann::json j;
    j["path"] = std::string(argv[1]);
    j["chn"] = 2;
    j["samplerate"] = 48000;
    auto filesource = FileFramePipeline::CreateFileSource("PCMFileFrameSource", j.dump());
    j["dev"] = atoi(argv[2]);
    auto render = AudioFrameRender::CreateAudioRender("TinyALSARender", j.dump());
    filesource->addAudioDestination(render);
    loop->run();
    return 0;
}
#endif