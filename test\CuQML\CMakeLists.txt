cmake_minimum_required(VERSION 3.10)

project(CuQML LANGUAGES CXX)

set(CMAKE_INCLUDE_CURRENT_DIR ON)

# set(CMAKE_PREFIX_PATH "/home/<USER>/Qt/5.15.2/gcc_64")
# set(Qt5_DIR  "/home/<USER>/Qt/5.15.2/gcc_64/lib/cmake/Qt5" 
#   CACHE STRING "qt5 config dir containing  Qt5Config.cmake"
# )

set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(QT_VERSION_MAJOR 5)

set(CMAKE_CXX_STANDARD 17)

find_package(Qt5 COMPONENTS Core Quick Widgets OpenGL REQUIRED)

add_definitions(-Wall -g -fexceptions -fpermissive)
if (ENABLE_VIDEO)
  add_definitions(-DENABLE_VIDEO)
endif ()
if (USE_CUDA)
  add_definitions(-DUSE_CUDA)
endif ()
if (USE_JRTP)
  add_definitions(-DUSE_JRTP)
endif ()
if (USE_WEBRTC)
  add_definitions(-DWEBRTC_POSIX -DWEBRTC_LINUX)
endif ()

add_definitions(-DUSE_CUSTOM_RENDER)

if (ENABLE_VIDEO)
    if (USE_CUDA)
        find_library(CUVID_LIB nvcuvid)
        find_library(NVENCODEAPI_LIB nvidia-encode)
        find_package(CUDA)

        find_library(FREEGLUT_LIB glut)
        find_library(GLEW32_LIB GLEW)
        find_library(X11_LIB X11)
        find_library(GL_LIB GL)
        find_library(CUDART_LIB cudart HINTS ${CUDA_TOOLKIT_ROOT_DIR}/lib64)
    endif()
endif()

include_directories(${CMAKE_CURRENT_SOURCE_DIR}
${CMAKE_PREFIX_PATH}/include
${CUDA_INCLUDE_DIRS})
link_directories(${CMAKE_PREFIX_PATH}/lib)
link_directories(${CUDA_TOOLKIT_ROOT_DIR}/targets/x86_64-linux/lib)

set(PROJECT_SOURCES
        ../QCuRender.h
        ../QCuRender.cpp
        QCuVideoItem.cpp
        main.cpp
        qml.qrc
)

set(QWIDGET_SOURCES
        ../CuWidget/main.cpp
        ../QCuRender.h
        ../QCuRender.cpp
        ../CuWidget/QCuVideoWidget.cpp
        ../CuWidget/widget.cpp
        ../CuWidget/widget.h
        ../CuWidget/widget.ui
)

set(SERVER_SOURCES
  ../test11.cpp
)


add_executable(CuQML
          ${PROJECT_SOURCES}
        )

#add_executable(CuWidget
#        ${QWIDGET_SOURCES}
#      )

add_executable(Test
          ${SERVER_SOURCES}
        )

target_compile_definitions(CuQML
  PRIVATE $<$<OR:$<CONFIG:Debug>,$<CONFIG:RelWithDebInfo>>:QT_QML_DEBUG>)

target_link_libraries(${PROJECT_NAME} PRIVATE Qt${QT_VERSION_MAJOR}::Core Qt${QT_VERSION_MAJOR}::Quick MediaPipeline ptoolkit jrtp jthread ljcore hv_static ZLToolKit ssl crypto aec rnnoise opus g729 fdk-aac DTLSTool nice glib-2.0 gio-2.0 gobject-2.0 gmodule-2.0 z ffi pcre2-8 srtp2 PNPcm SDL2 dum resip rutil ssl crypto pthread dl nppicc nppig nppif ${CUDA_CUDA_LIBRARY} ${CUDART_LIB} ${CMAKE_DL_LIBS} ${NVENCODEAPI_LIB} ${CUVID_LIB} ${FREEGLUT_LIB} ${GLEW32_LIB} ${X11_LIB} ${GL_LIB})

#target_link_libraries(CuWidget PRIVATE Qt${QT_VERSION_MAJOR}::Widgets MediaPipeline ptoolkit jrtp jthread ljcore hv_static ZLToolKit ssl crypto aec rnnoise opus g729 fdk-aac DTLSTool nice glib-2.0 gio-2.0 gobject-2.0 gmodule-2.0 z ffi pcre2-8 srtp2 PNPcm SDL2 pthread dl nppicc nppig nppif ${CUDA_CUDA_LIBRARY} ${CUDART_LIB} ${CMAKE_DL_LIBS} ${NVENCODEAPI_LIB} ${CUVID_LIB} ${FREEGLUT_LIB} ${GLEW32_LIB} ${X11_LIB} ${GL_LIB})

target_link_libraries(Test PRIVATE MediaPipeline ptoolkit jrtp jthread ljcore hv_static ZLToolKit ssl crypto aec rnnoise opus g729 fdk-aac DTLSTool nice glib-2.0 gio-2.0 gobject-2.0 gmodule-2.0 z ffi pcre2-8 srtp2 PNPcm SDL2 pthread dl nppicc nppig nppif ${CUDA_CUDA_LIBRARY} ${CUDART_LIB} ${CMAKE_DL_LIBS} ${NVENCODEAPI_LIB} ${CUVID_LIB} ${FREEGLUT_LIB} ${GLEW32_LIB} ${X11_LIB} ${GL_LIB})


install(TARGETS ${PROJECT_NAME} RUNTIME DESTINATION ${CMAKE_INSTALL_PREFIX}/bin/)