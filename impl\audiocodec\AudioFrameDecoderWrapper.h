#ifndef IMPL_AUDIOCODEC_AUDIODECODERWRAPPER_H_
#define IMPL_AUDIOCODEC_AUDIODECODERWRAPPER_H_
#include <unordered_set>
#include <list>

#include "AudioFrameDecoder.h"
#include "Frame.h"
#include <ptoolkit/bytebuffer.h>

namespace panocom
{
// TODO: DRY
class AudioFrameDecoderWrapper : public AudioFrameDecoder
{
private:
    /* data */
public:
    AudioFrameDecoderWrapper(const std::string& jsonParams);
    ~AudioFrameDecoderWrapper();
    FramePipeline::Ptr addAudioDestination(const FramePipeline::Ptr& dst) override;
    FramePipeline::Ptr removeAudioDestination(const FramePipeline::Ptr& dst) override;
    void onFrameTransfer(const std::shared_ptr<Frame> &f) override;
private:
    void ReconfigureDecoder();
    void ReconfigureDecoder(int pt);
private:
    std::shared_ptr<AudioFrameDecoder> decoder_;
    std::list<FramePipeline::WPtr> destinations_;
    FrameFormat fmt_;
    int setted_pt_;
    bool isNative_;
};

} // namespace panocom

#endif