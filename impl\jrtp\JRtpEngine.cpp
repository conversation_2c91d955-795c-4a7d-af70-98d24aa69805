#include "JRtpEngine.h"
#include "Frame.h"
#include "Utils.h"
#include "CPing.h"
#include <ptoolkit/byteorder.h>
#include <json.hpp>
#include <ljcore/jlog.h>
#include "sdptransform.hpp"
#include <jrtplib3/rtppacket.h>
#include <jrtplib3/rtpudpv4transmitter.h>
#include <jrtplib3/rtpsessionparams.h>
#include "ip/UdpFramePipeline.h"
//#ifdef USE_DTLS_RTP
#include <DTLSTool/dtlsrtpsession.h>
#endif
#include <sstream>

using namespace panocom;

namespace {
std::string GetAddressString(uint32_t ip,uint16_t port)
{
	char str[24];
	sprintf(str,"%d.%d.%d.%d:%d",(int)((ip>>24)&0xFF),(int)((ip>>16)&0xFF),(int)((ip>>8)&0xFF),
		(int)(ip&0xFF),(int)port);
	return std::string(str);
}
}

#ifndef __JRTPEngine_CPP__
#define __JRTPEngine_CPP__

// ============================================================================
// 文件名称：JRtpEngine.cpp
// 文件功能：实现RTP引擎，负责RTP/RTCP数据的收发、会话管理、SDP处理等。
// 适用场景：多媒体流媒体传输、实时音视频通信等。
// 作者：自动注释生成
// 日期：2025-09-26
// ============================================================================

//#ifdef USE_LIBNICE
void JRtpEngine::cb_new_selected_pair(NiceAgent *agent, guint _stream_id, guint component_id, gchar *lfoundation, gchar *rfoundation, gpointer data)
{
    jinfo("SIGNAL: selected pair %d %s %s", component_id, lfoundation, rfoundation);
}

void JRtpEngine::cb_candidate_gathering_done(NiceAgent *agent, guint stream_id, gpointer data)
{
    // sdp
    JRtpEngine* pipeline = (JRtpEngine*)data;
    NiceCandidate* c1 = nice_agent_get_default_local_candidate(agent, pipeline->stream_id_, 1);
    NiceCandidate* c2 = nice_agent_get_default_local_candidate(agent, pipeline->stream_id_, 2);
    gchar *ufrag = nullptr;
    gchar *pwd = nullptr;
    nice_agent_get_local_credentials(agent, pipeline->stream_id_, &ufrag, &pwd);
    std::string sdp1 = nice_agent_generate_local_candidate_sdp (agent, c1);
    std::string sdp2 = nice_agent_generate_local_candidate_sdp (agent, c2);
    std::stringstream ss;
    ss << "a=ice-ufrag:" << ufrag << "\r\n";
    ss << "a=ice-pwd:" << pwd << "\r\n";
    ss << sdp1 << "\r\n";
    ss << sdp2;
// DTLS相关头文件，支持安全RTP传输（如SRTP）
    jinfo("cb_candidate_gathering_done:\n%s", sdp.c_str());
    if (pipeline->notifycallbacks_.find("sdp") != pipeline->notifycallbacks_.end() && pipeline->notifycallbacks_["sdp"].cb)
    {
        pipeline->notifycallbacks_["sdp"].cb("sdp", sdp, pipeline->notifycallbacks_["sdp"].param);
    }

// 使用panocom命名空间，避免命名冲突
using namespace panocom;
    {

// ============================================================================
// 匿名命名空间，仅在本文件可见的工具函数
// 功能：将IP和端口转换为字符串格式，便于日志和调试
// 参数：ip - 32位整型IP地址，port - 端口号
// 返回：形如"***********:8000"的字符串
// ============================================================================
namespace {
std::string GetAddressString(uint32_t ip,uint16_t port)
{
    char str[24];
    // 将IP按字节拆分为点分十进制格式
    sprintf(str,"%d.%d.%d.%d:%d",(int)((ip>>24)&0xFF),(int)((ip>>16)&0xFF),(int)((ip>>8)&0xFF),
        (int)(ip&0xFF),(int)port);
    return std::string(str);
}
}
    if (pipeline->notifycallbacks_.find("ice") != pipeline->notifycallbacks_.end() && pipeline->notifycallbacks_["ice"].cb)
// ============================================================================
// 以下为LIBNICE相关回调函数，支持ICE穿透
// ============================================================================
        pipeline->notifycallbacks_["ice"].cb("ice", std::to_string(state), pipeline->notifycallbacks_["ice"].param);
    }
    else
    {
        jerror("ice cb not set");
    }
    if (state == NICE_COMPONENT_STATE_READY)
    {
        pipeline->ready_[component_id - 1] = true;
    }
    else if (state == NICE_COMPONENT_STATE_FAILED)
    {
        pipeline->ready_[component_id - 1] = false;
    }
}

void JRtpEngine::cb_nice_recv(NiceAgent *agent, guint stream_id, guint component_id, guint len, gchar *buf, gpointer data)
{
    JRtpEngine* pipeline = (JRtpEngine*)data;
    jtrace("cb_nice_recv [%s] [%d] [%d]", pipeline->id_.c_str(), component_id, len);
    if (pipeline)
    {
        pipeline->onData(buf, len, component_id == 1);
    }
}

#endif

static const uint8_t start_sequence[] = { 0, 0, 0, 1 };

static std::shared_ptr<Frame> ff_h264_handle_aggregated_packet(const uint8_t *buf, int len)
{
    jinfo("#####ff_h264_handle_aggregated_packet len = %d", len);
    int pass         = 0;
    int total_length = 0;
    uint8_t* dst = NULL;
    std::shared_ptr<Frame> f;

    // first we are going to figure out the total size
    for (pass = 0; pass < 2; pass++) {
        const uint8_t *src = buf;
        int src_len        = len;

        while (src_len > 2) {
            uint16_t nal_size = GetBE16(src);
            // consume the length of the aggregate
            src     += 2;
            src_len -= 2;

            if (nal_size <= src_len) {
                if (pass == 0) {
                    // counting
                    total_length += sizeof(start_sequence) + nal_size;
                } else {
                    // copying
                    // jinfo("#####aggregated nal[%x] nal_size = %d", src[0], nal_size);
                    memcpy(dst, start_sequence, sizeof(start_sequence));
                    dst += sizeof(start_sequence);
                    memcpy(dst, src, nal_size);
                    dst += nal_size;
                }
            } else {
                return nullptr;
            }

            // eat what we handled
            src     += nal_size;
            src_len -= nal_size;
        }
        if (pass == 0) {
            f = Frame::CreateFrame(FRAME_FORMAT_H264);
            f->createFrameBuffer(total_length);
            dst = f->getFrameBuffer();
        }
    }
    return f;
}

static std::shared_ptr<Frame> ff_h264_handle_frag_packet(const uint8_t *buf, int len,
                               int start_bit, const uint8_t *nal_header,
                               int nal_header_len, ByteBuffer& bbuf)
{
    std::shared_ptr<Frame> f;
    // FIXME: 应该参考结束标志
    if (start_bit) {
        if (bbuf.Length() > 0)
        {
            f = Frame::CreateFrame(FRAME_FORMAT_H264);
            f->createFrameBuffer(bbuf.Length());
            memcpy(f->getFrameBuffer(), bbuf.Data(), bbuf.Length());
            bbuf.Clear();
        }
        bbuf.WriteBytes((const char*)start_sequence, sizeof(start_sequence));
        bbuf.WriteBytes((const char*)nal_header, nal_header_len);
    }
    bbuf.WriteBytes((const char*)buf, len);
    return f;
}

static std::shared_ptr<Frame> ff_h264_handle_frag_packet(const uint8_t *buf, int len,
                               int start_bit, int end_bit, const uint8_t *nal_header,
                               int nal_header_len, ByteBuffer& bbuf)
{
    std::shared_ptr<Frame> f;
    // FIXME: 应该参考结束标志
    if (start_bit) {
        bbuf.Clear();
        bbuf.WriteBytes((const char*)start_sequence, sizeof(start_sequence));
        bbuf.WriteBytes((const char*)nal_header, nal_header_len);
    }
    if (bbuf.Length() > 0)
        bbuf.WriteBytes((const char*)buf, len);
    if (end_bit) {
        if (bbuf.Length() > 0)
        {
            f = Frame::CreateFrame(FRAME_FORMAT_H264);
            f->createFrameBuffer(bbuf.Length());
            memcpy(f->getFrameBuffer(), bbuf.Data(), bbuf.Length());
            bbuf.Clear();
        }
    }
    return f;
}

static std::shared_ptr<Frame> h264_handle_packet_fu_a(const uint8_t *buf, int len, ByteBuffer& bbuf)
{
    uint8_t fu_indicator, fu_header, start_bit, end_bit, nal_type, nal;

    if (len < 3) {
        return NULL;
    }

    fu_indicator = buf[0];
    fu_header    = buf[1];
    start_bit    = fu_header >> 7;
    end_bit      = (fu_header & 0x40) >> 6;
    nal_type     = fu_header & 0x1f;
    nal          = fu_indicator & 0xe0 | nal_type;

    // skip the fu_indicator and fu_header
    buf += 2;
    len -= 2;
    // return ff_h264_handle_frag_packet(buf, len, start_bit, &nal, 1, bbuf);
    return ff_h264_handle_frag_packet(buf, len, start_bit, end_bit, &nal, 1, bbuf);
}

static std::shared_ptr<Frame> ff_h265_handle_aggregated_packet(const uint8_t *buf, int len)
{
    jinfo("#####ff_h265_handle_aggregated_packet len = %d", len);
    int pass         = 0;
    int total_length = 0;
    uint8_t* dst = NULL;
    std::shared_ptr<Frame> f;
    /*
    0000   00 e1 bb 0c 14 cc f8 98 ef af 7d 89 08 00 45 88
    0010   00 79 36 94 00 00 3f 11 1c 6a c0 a8 52 ca c0 a8
    0020   53 d3 09 46 27 12 00 65 a8 7a 80 62 6f ff ba 47
    0030   13 16 0e 01 66 11 60 01 00 1c 40 01 0c 03 ff ff
    0040   01 40 00 00 03 00 00 03 00 00 03 00 00 03 00 7b
    0050   00 00 9b 36 04 80 00 23 42 01 03 01 40 00 00 03
    0060   00 00 03 00 00 03 00 00 03 00 7b 00 00 a0 03 c0
    0070   80 10 e5 8b 9b 37 24 8d 92 5f 48 00 0a 44 01 c0
    0080   60 c8 f0 9b f8 41 19
    */
    int pos = 0;
    while (pos < len - 4)
    {
        int nal_size = buf[pos] << 8 | buf[pos + 1];
        pos += 2;
        int type = (buf[pos] >> 1) & 0x3f;
        // if (type == H265_NAL_TSA_N)
        //     return nullptr;
        if (nal_size < 0 || nal_size + pos > len)
        {
            jerror("invalid nal_size[%d]", nal_size);
            return nullptr;
        }
        jinfo("nal_size[%d]", nal_size);
        total_length += sizeof(start_sequence) + nal_size;
        pos += nal_size;
    }
    pos = 0;
    f = Frame::CreateFrame(FRAME_FORMAT_H265);
    f->createFrameBuffer(total_length);
    dst = f->getFrameBuffer();
    while (pos < len - 4)
    {
        int nal_size = buf[pos] << 8 | buf[pos + 1];
        pos += 2;
        int type = (buf[pos] >> 1) & 0x3f; // ignore the first bit
        // copying
        jinfo("#####aggregated nal[%x] nal_size = %d", type, nal_size);
        memcpy(dst, start_sequence, sizeof(start_sequence));
        dst += sizeof(start_sequence);
        memcpy(dst, buf + pos, nal_size);
        dst += nal_size;
        // move to next NAL unit
        pos += nal_size;
    }
    return f;
}

// static void logHandler(const gchar* domain, GLogLevelFlags level, const gchar* msg, gpointer userData)
// {
//     printf("%s\n", msg);
// }

JRtpEngine::JRtpEngine(const std::string &jsonParams)
    : started_ { false }
    , closed_ { false } 
#ifdef USE_AUDIO_SIMULATION
    , rd_()
    , gen_(rd_())
    , uniform_dist_(0.0, 1.0)
#endif
    {
    FN_BEGIN;
    jinfo("JRtpEngine %s", jsonParams.c_str());
    name_ = "JRtpEngine";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    if (j.contains("bindIp"))
    {
        bindIp_ = j["bindIp"];
    }
    bindPort_ = 10000;
    if (j.contains("bindPort"))
    {
        bindPort_ = j["bindPort"];
    }
    payloadSize_ = 1200;
    if (j.contains("maxPayloadSize"))
    {
        payloadSize_ = j["maxPayloadSize"];
    }
    payload_.resize(payloadSize_);
    payloadType_ = 8;
    local_payload_type_ = 8;
    remote_payload_type_ = local_payload_type_;
    timestampInc_ = 160;
    defaultMark_ = true;
    hz_ = 8000.0;
    fmt_ = FRAME_FORMAT_OPUS_16000_1;
    if (j.contains("fmt"))
    {
        fmt_ = j["fmt"];
        jinfo("JRtpEngine fmt = %d", fmt_);
    }
    CodecInst inst;
    if (getCodecInst((FrameFormat)fmt_, inst))
    {
        payloadType_ = inst.pt;
        local_payload_type_ = inst.pt;
        timestampInc_ = inst.tsinc;
    }

    if (j.contains("payloadType"))
    {
        payloadType_ = j["payloadType"];
    }

    if (j.contains("local_payload_type")) {
        local_payload_type_ = j["local_payload_type"];
    }

    if (j.contains("remote_payload_type")) {
        remote_payload_type_ = j["remote_payload_type"];
    }

    jinfo("JRtpEngine pt = %d tsinc = %d", payloadType_, timestampInc_);
    jinfo("JRtpEngine local_payload_type = %d remote_payload_type = %d", local_payload_type_, remote_payload_type_);
    if (j.contains("timestampInc"))
    {
        timestampInc_ = j["timestampInc"];
    }
    
    if (j.contains("defaultMark"))
    {
        defaultMark_ = j["defaultMark"];
    }
    
    if (j.contains("hz"))
    {
        //video 90000;
        hz_ = j["hz"];
    }

    if (j.contains("rawRtp"))
    {
        outputRawRtp = j["rawRtp"];
    }
    if (j.contains("ptime"))
    {
        ptime_ = j["ptime"];
    }

    if (j.contains("dst") && j["dst"].is_array())
    {
        for (int i = 0; i < j["dst"].size(); ++i)
        {
            std::string ip = j["dst"][i]["ip"];
            int port = j["dst"][i]["port"];
            // jinfo("ip = %s:%d", ip.c_str(), port);
            RTPIPv4Address addr(ntohl(inet_addr(ip.c_str())), port);
            dsts_.push_back(addr);
            std::string ip_addr = ip + ":" + std::to_string(port);
            ip_addrs_.emplace(ip_addr);
            ips_.push_back(ip);
            if (servers_to_ping_.count(ip) == 0) {
                servers_to_ping_.insert(ip);
                CPing::instance().AddServer(ip);
            }
            jinfo("ip = %s", ip_addr.c_str());
	        //status = sess_->AddDestination(addr);
        }
    }

    dtls_ = false;
    if (j.contains("dtls"))
    {
        dtls_ = j["dtls"];
    }

    stun_ = false;
    if (j.contains("stun"))
    {
        stun_ = j["stun"];
    }

    if (j.contains("id"))
    {
        id_ = j["id"];
    }

    if (j.contains("is_h323"))
    {
        is_h323_ = j["is_h323"];
    }

    send_clock_rate_ = hz_;
    if (j.contains("clock_rate")) {
        send_clock_rate_ = j["clock_rate"];
    }
    if (send_clock_rate_ <= 0) send_clock_rate_ = 90000;
    recv_clock_rate_ = send_clock_rate_;
#ifdef USE_WEBRTC
    if (j.contains("rtpProxy"))
    {
        if (j["rtpProxy"].contains("source"))
        {
            std::string name = j["rtpProxy"]["source"];
            rtpSourceProxy_ = RtpEngine::CreateRTPEngine(name, j.dump());
            if (rtpSourceProxy_)
            {
                jinfo("use rtpSourceProxy");
                rtpSourceProxy_->setAfterDeliverFrameListener([this](const std::shared_ptr<Frame> &f, void *param) {
                    switch (f->getFrameFormat())
                    {
                    case FRAME_FORMAT_H264:
                    case FRAME_FORMAT_H265:
                        deliverFrame(f);
                        break;
                    case FRAME_FORMAT_RTCP:
                        //if (sess_)
                        //    sess_->SendRTCPData(f->getFrameBuffer(), f->getFrameSize()); //webrtc刚发的rtcp, 为什么jrtp session又要重复发一次
                        break;
                    default: break;
                    }
                }, this);
            }
        }
        if (j["rtpProxy"].contains("destination"))
        {
            std::string name = j["rtpProxy"]["destination"];
            rtpDestinationProxy_ = RtpEngine::CreateRTPEngine(name, j.dump());
            if (rtpDestinationProxy_)
            {
                jinfo("use rtpDestinationProxy");
                // 回调带宽预测码率
                rtpDestinationProxy_->setNotify("vidcodec-bitrate", "", NULL, [this](const std::string& key, const std::string& json, void* param){
                    std::shared_ptr<Frame> f = Frame::CreateFrame(FRAME_FORMAT_VIDEO_FEEDBACK);
                    if (f)
                    {
                        f->createFrameBuffer(json.size());
                        memcpy(f->getFrameBuffer(), json.data(), json.size());
                        deliverFeedbackFrame(f);
                    }
                });
            }
        }
        if (!stun_)
        {
            j["fmt"] = FRAME_FORMAT_RTP;
            rtpProxy_ = std::make_shared<UdpFramePipeline>(j.dump());
            j["bindPort"] = bindPort_ + 1;
            j["fmt"] = FRAME_FORMAT_RTCP;
            if (j.contains("dst") && j["dst"].is_array())
            {
                for (int i = 0; i < j["dst"].size(); ++i)
                {
                    int port = j["dst"][i]["port"];
                    j["dst"][i]["port"] = port + 1;
                }
            }
            rtcpProxy_ = std::make_shared<UdpFramePipeline>(j.dump());

            if (!dtls_)
            {
                if (rtpDestinationProxy_) {
                    rtpDestinationProxy_->addDataDestination(rtpProxy_);
                    rtpDestinationProxy_->addDataDestination(rtcpProxy_);
                }
                if (rtcpProxy_) {
                    rtcpProxy_->addDataDestination(rtpDestinationProxy_);
                    rtcpProxy_->addDataDestination(rtpSourceProxy_);    //rtpSourceProxy_是收rtp，发rtcp，这里是不是不用rtcpProxy_->addDataDestination?
                }
                if (rtpProxy_)
                    rtpProxy_->addDataDestination(rtpSourceProxy_);
                if (rtpSourceProxy_)
                    rtpSourceProxy_->addDataDestination(rtcpProxy_);
            }
            else
            {
                if (rtpProxy_) {
                    rtpProxy_->setAfterDeliverFrameListener([this](const std::shared_ptr<Frame> &f, void *param) {
                        onData(f->getFrameBuffer(), f->getFrameSize(), true);
                    }, this);

                    rtcpProxy_->setAfterDeliverFrameListener([this](const std::shared_ptr<Frame> &f, void *param) {
                        onData(f->getFrameBuffer(), f->getFrameSize(), false);
                    }, this);
                }
                if (rtpDestinationProxy_)
                {
                    rtpDestinationProxy_->setAfterDeliverFrameListener(
                        [this](const std::shared_ptr<Frame> &f, void *param) {
                            switch (f->getFrameFormat()) {
                                case FRAME_FORMAT_RTP: sess_->SendRTPData(f->getFrameBuffer(), f->getFrameSize()); break;
                                case FRAME_FORMAT_RTCP: sess_->SendRTCPData(f->getFrameBuffer(), f->getFrameSize()); break;
                                default: break;
                            }
                        },
                        this);
                }
            }
        }
        else
        {
            if (rtpDestinationProxy_)
            {
                rtpDestinationProxy_->setAfterDeliverFrameListener(
                    [this](const std::shared_ptr<Frame> &f, void *param) {
                        switch (f->getFrameFormat()) {
                            case FRAME_FORMAT_RTP: sess_->SendRTPData(f->getFrameBuffer(), f->getFrameSize()); break;
                            case FRAME_FORMAT_RTCP: sess_->SendRTCPData(f->getFrameBuffer(), f->getFrameSize()); break;
                            default: break;
                        }
                    },
                    this);
            }
        }
    }
#endif

#ifdef USE_LIBNICE
    //g_log_set_default_handler(logHandler, "123");
    //nice_debug_enable(1);
    stun_addr_ = "127.0.0.1";
    if (j.contains("stun-server"))
    {
        stun_addr_ = j["stun-server"];
    }
    stun_port_ = 3478;
    if (j.contains("stun-port"))
    {
        stun_port_ = j["stun-port"];
    }
    controlling_ = 0;
    if (j.contains("controlling"))
    {
        controlling_ = j["controlling"];
    }
    jinfo("stun [%s:%d]", stun_addr_.c_str(), stun_port_);
#endif

    jinfo("dtls [%d] stun [%d]", dtls_, stun_);
#ifdef USE_DTLS_RTP
    auto s = std::make_shared<DTLSRTPSession>(id_);
    s->SetDTLSEnabled(dtls_);
    s->SetStunEnabled(stun_);
    s->SetDTLSNotify([this](int ret, char*err, bool rtp, void*param) {
        if (ret == 0)
        {
            jinfo("###[%s] dtls %s ready", id_.c_str(), rtp?"rtp":"rtcp");
            dtlsReady_[rtp] = true;
        }
        else
        {
            dtlsReady_[rtp] = false;
        }
    }, this);
    DTLSRTPSession::FnTransporter receiver;
    if (rtpSourceProxy_) {
        receiver = [this](const char *data, int len, bool rtp, void *) -> int {
            auto f = Frame::CreateFrame(rtp ? FRAME_FORMAT_RTP : FRAME_FORMAT_RTCP);
            f->writeBytes(data, len);
            if (rtpSourceProxy_) {
                rtpSourceProxy_->onFrameTransfer(f);
            }
            if (!rtp && rtpDestinationProxy_) {
                rtpDestinationProxy_->onFrameTransfer(f);
            }
            return len;
        };
    }

    s->SetTransport(
        [this](const char *data, int len, bool rtp, void *) -> int {
            if (rtpProxy_ && rtcpProxy_) {
                auto f = Frame::CreateFrame(rtp ? FRAME_FORMAT_RTP : FRAME_FORMAT_RTCP);
                f->writeBytes(data, len);
                if (rtp) {
                    rtpProxy_->onFrameTransfer(f);
                } else {
                    rtcpProxy_->onFrameTransfer(f);
                }
            } else {
                if (ready_[0] && ready_[1]) {
                    int ret = nice_agent_send(agent_, stream_id_, rtp ? 1 : 2, len, data);
                    jtrace("nice_agent_send [%s][%d] [%d] ret=[%d]", id_.c_str(), rtp, len, ret);
                }
            }
            return len;
        },
        receiver, this);
    sess_ = s;
#else
    sess_ = std::make_shared<RTPSession>();
#endif
    FN_END;
}

JRtpEngine::~JRtpEngine()
{
    FN_BEGIN;
    stop();
#ifdef USE_WEBRTC
    if (rtpDestinationProxy_) {
        if (rtpProxy_)
            rtpDestinationProxy_->removeDataDestination(rtpProxy_);
        if (rtcpProxy_)
            rtpDestinationProxy_->removeDataDestination(rtcpProxy_);
    }
    if (rtcpProxy_) {
        if (rtpDestinationProxy_)
            rtcpProxy_->removeDataDestination(rtpDestinationProxy_);
        if (rtpSourceProxy_)
            rtcpProxy_->removeDataDestination(rtpSourceProxy_);    //rtpSourceProxy_是收rtp，发rtcp，这里是不是不用rtcpProxy_->addDataDestination?
    }
    if (rtpProxy_ && rtpSourceProxy_)
        rtpProxy_->removeDataDestination(rtpSourceProxy_);
    if (rtpSourceProxy_ && rtcpProxy_)
        rtpSourceProxy_->removeDataDestination(rtcpProxy_);
#endif
    FN_END;
}

void JRtpEngine::onData(const char* buf, int len, bool rtp)
{
    std::string ip;
    int port = 9;
    if (!ips_.empty())
    {
        ip = ips_[0];
    }
    if (sess_)
    {
        sess_->OnData(buf, len, rtp, ip, port);
#ifdef USE_WEBRTC
        if (!dtls_ && !rtp && (rtpSourceProxy_ || rtpDestinationProxy_))
        {
            auto f = Frame::CreateFrame(FRAME_FORMAT_RTCP);
            f->writeBytes(buf, len);
            if (rtpSourceProxy_)
            {
                rtpSourceProxy_->onFrameTransfer(f);
            }
            if (rtpDestinationProxy_)
            {
                rtpDestinationProxy_->onFrameTransfer(f);
            }
        }
#endif
    }
}

int JRtpEngine::updateParam(const std::string& jsonParams)
{
    if (lastUpdate_ == jsonParams)
        return 0;
    lastUpdate_ = jsonParams;
    jinfo("### %s JRtpEngine::updateParam %s", id_.c_str(), jsonParams.c_str());
    int ret = 0;
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
#ifdef USE_DTLS_RTP
    if (dtls_)
    {
        if (j.contains("rtp"))
        {
            if (j["rtp"].contains("fingerprint") && j["rtp"].contains("isServer"))
            {
                std::string fingerprint = j["rtp"]["fingerprint"];
                bool isServer = j["rtp"]["isServer"];
                std::shared_ptr<DTLSRTPSession> s = std::dynamic_pointer_cast<DTLSRTPSession>(sess_);
                if (s)
                {
                    s->SetFingerprint(true, fingerprint, isServer);
                    jinfo("rtp SetRemoteFingerPrint %s isServer %d", fingerprint.c_str(), isServer);
                }
            }
        }
        if (j.contains("rtcp"))
        {
            if (j["rtcp"].contains("fingerprint") && j["rtcp"].contains("isServer"))
            {
                std::string fingerprint = j["rtcp"]["fingerprint"];
                bool isServer = j["rtcp"]["isServer"];
                std::shared_ptr<DTLSRTPSession> s = std::dynamic_pointer_cast<DTLSRTPSession>(sess_);
                if (s)
                {
                    s->SetFingerprint(true, fingerprint, isServer);
                    jinfo("rtcp SetRemoteFingerPrint %s isServer %d", fingerprint.c_str(), isServer);
                }
            }
        }
    }
#endif
#ifdef USE_LIBNICE
    if (stun_)
    {
        if (j.contains("sdp"))
        {
            if (j["sdp"].contains("iceUfrag") && j["sdp"].contains("icePwd"))
            {
                std::string ufrag = j["sdp"]["iceUfrag"];
                std::string passwd = j["sdp"]["icePwd"];
                jinfo("nice_agent_set_remote_credentials [%s][%s]-[%s]", id_.c_str(), ufrag.c_str(), passwd.c_str());
                if (!nice_agent_set_remote_credentials(agent_, stream_id_, ufrag.c_str(), passwd.c_str()))
                {
                    jerror("nice_agent_set_remote_credentials fail");
                    return 0;
                }
            }
            if (j["sdp"].contains("candidates"))
            {
                for (size_t i = 0; i < j["sdp"]["candidates"].size(); i++)
                {
                    GSList *remote_candidates = NULL;
                    std::string foundation = j["sdp"]["candidates"][i]["foundation"];
                    std::string addr = j["sdp"]["candidates"][i]["ip"];
                    int port = j["sdp"]["candidates"][i]["port"];
                    NiceCandidate *cand = nice_candidate_new(NICE_CANDIDATE_TYPE_HOST);
                    cand->component_id = j["sdp"]["candidates"][i]["component"];
                    cand->stream_id = stream_id_;
                    cand->transport = NICE_CANDIDATE_TRANSPORT_UDP;
                    strcpy(cand->foundation, foundation.c_str());
                    cand->priority = j["sdp"]["candidates"][i]["priority"];
                    if (nice_address_set_from_string(&cand->addr, addr.c_str()))
                    {
                        nice_address_set_port(&cand->addr, port);
                        remote_candidates = g_slist_prepend(remote_candidates, cand);
                        nice_agent_set_selected_remote_candidate(agent_, stream_id_, cand->component_id, cand);
                        jinfo("nice_agent_set_remote_candidates [%s][%d][%s]", id_.c_str(), cand->component_id, j["sdp"]["candidates"][i].dump().c_str());
                        if (nice_agent_set_remote_candidates(agent_, stream_id_, cand->component_id, remote_candidates) < 1)
                        {
                            jerror("nice_agent_set_remote_candidates fail");
                        }

                        if (remote_candidates)
                        {
                            g_slist_free_full(remote_candidates, (GDestroyNotify)&nice_candidate_free);
                        }
                    }
                    else
                    {
                        nice_candidate_free(cand);
                        cand = NULL;
                        jerror("nice_address_set_from_string fail");
                    }
                }
            }
        }
    }
#endif
    if (j.contains("clear") && j["clear"] == true) {
        dsts_.clear();
        ips_.clear();
        for (auto &ip: ips_) {
            if (servers_to_ping_.count(ip) != 0) {
                servers_to_ping_.erase(ip);
                CPing::instance().RemoveServer(ip);
            }
        }
        ip_addrs_.clear();
        sess_->ClearDestinations();
    }
    if (j.contains("dst") && j["dst"].is_array())
    {
        for (int i = 0; i < j["dst"].size(); ++i)
        {
            std::string ip = j["dst"][i]["ip"];
            int port = j["dst"][i]["port"];
            // jinfo("add dst ip = %s:%d", ip.c_str(), port);
            RTPIPv4Address addr(ntohl(inet_addr(ip.c_str())), port);
            dsts_.push_back(addr);
            ips_.push_back(ip);
            if (servers_to_ping_.count(ip) == 0) {
                servers_to_ping_.insert(ip);
                CPing::instance().AddServer(ip);
            }
            std::string ip_addr = ip + ":" + std::to_string(port);
            ip_addrs_.emplace(ip_addr);
            jinfo("ip = %s", ip_addr.c_str());
	        sess_->AddDestination(addr);
        }
    }
    if (j.contains("payloadType"))
    {
        uint8_t payloadType = j["payloadType"];
        if (payloadType != payloadType_) {
            payloadType_ = payloadType;
            sess_->SetDefaultPayloadType(payloadType_);
        }
    } 
    if (j.contains("local_payload_type")) {
        local_payload_type_ = j["local_payload_type"];
    }
    if (j.contains("remote_payload_type"))
    {
        uint8_t payloadType = j["remote_payload_type"];
        if (payloadType != remote_payload_type_) {
            remote_payload_type_ = payloadType;
            sess_->SetDefaultPayloadType(remote_payload_type_);
        }
    } 
    if (j.contains("ptime"))
    {
        ptime_ = j["ptime"];
    }
    if (j.contains("is_h323"))
    {
        is_h323_ = j["is_h323"];
    }

    if (j.contains("clock_rate")) {
        send_clock_rate_ = j["clock_rate"];
    }
    if (send_clock_rate_ <= 0) send_clock_rate_ = 90000;
    return ret;
}

void JRtpEngine::onFrame(const std::shared_ptr<Frame> &f)
{
    if (rtp_send_thread_.isRunning()) {
        rtp_send_thread_.loop()->runInLoop([this, f]() mutable {
            std::unique_lock<std::mutex> l(stopMutex_);
            if (stopped_) {
                return;
            }
            printInputStatus("jrtp");
            rtpStatistics[RTP_SEND].fps_++;
            insertSend(f->getFrameSize() + 12, 0);
            switch (f->getFrameFormat()) {
                case FRAME_FORMAT_H264:
                    if (rtpDestinationProxy_) {
                        rtpDestinationProxy_->onFrameTransfer(f);
                    } else {
                        sendH264(f);
                    }
                    break;
                case FRAME_FORMAT_H265:
                    if (rtpDestinationProxy_) {
                        rtpDestinationProxy_->onFrameTransfer(f);
                    } else {
                        sendH265(f);
                    }
                    break;
                default: {
                    int ret = sess_->SendPacket(f->getFrameBuffer(), f->getFrameSize());
                    if (ret != 0) {
                        jerror("SendPacket ret = %d", ret);
                    }
                    prev_timestamp_inc_ = timestampInc_;
                } break;
            }
        });
    }
}

void JRtpEngine::onFeedbackFrame(const std::shared_ptr<Frame> &f) {
    if (thread_ && thread_->isRunning()) {
        thread_->loop()->runInLoop([this, f] {
            if (f->getFrameFormat() == FRAME_FORMAT_VIDEO_FEEDBACK) {
                jinfo("JRtpEngine onFeedbackFrame %s", f->getFrameBuffer());
                std::string jsonStr((char*)f->getFrameBuffer(), f->getFrameBufferSize());
                nlohmann::json j;
                if (jsonStr != "") {
                    j = nlohmann::json::parse(jsonStr);
                }
                if (j.contains("fir")) {
                    bool fir = j["fir"];
                    if (fir)
                        sendFIR(sess_->GetLocalSSRC(), remote_ssrc_);
                } 
                if (j.contains("pli")) {
                    bool pli = j["pli"];
                    if (pli)
                        sendPLI(sess_->GetLocalSSRC(), remote_ssrc_);
                }
            }
        });
    }
}

#ifdef USE_LIBNICE
void JRtpEngine::startNICE()
{
    if (started_.exchange(true))
    {
        return;
    }
    jinfo("startNICE");
    context_ = g_main_context_new();
    loop_ = g_main_loop_new(context_, FALSE);
    gthread_ = std::unique_ptr<std::thread>(new std::thread([this] {
        if (!this->closed_ && this->loop_) 
        {
            jinfo("g_main_loop_run start");
            g_main_loop_run(this->loop_);
        } 
    }));

    agent_ = nice_agent_new(g_main_loop_get_context(loop_),
                           NICE_COMPATIBILITY_RFC5245);
    if (agent_ == NULL)
    {
        jerror("Failed to create agent");
    }

    if (stun_addr_ != "")
    {
        g_object_set(G_OBJECT(agent_), "stun-server", stun_addr_.c_str(), NULL);
        g_object_set(G_OBJECT(agent_), "stun-server-port", stun_port_, NULL);
    }
    else
    {
        jerror("set stun_addr fail");
    }
    g_object_set(G_OBJECT(agent_), "controlling-mode", controlling_, NULL);
    g_object_set(G_OBJECT(agent_), "ice-tcp", 0, NULL);

    // Connect to the signals
    g_signal_connect(G_OBJECT(agent_), "new-selected-pair",
                     G_CALLBACK(JRtpEngine::cb_new_selected_pair), NULL);
    g_signal_connect(G_OBJECT(agent_), "candidate-gathering-done",
                     G_CALLBACK(JRtpEngine::cb_candidate_gathering_done), this);
    g_signal_connect(G_OBJECT(agent_), "component-state-changed",
                     G_CALLBACK(JRtpEngine::cb_component_state_changed), this);

    // Create a new stream with one component
    stream_id_ = nice_agent_add_stream(agent_, 2);
    if (stream_id_ == 0)
    {
        jerror("Failed to add stream");
    }
    nice_agent_set_stream_name(agent_, stream_id_, "text");

    // Attach to the component to receive the data
    // Without this call, candidates cannot be gathered
    nice_agent_attach_recv(agent_, stream_id_, 1,
                           g_main_loop_get_context(loop_), cb_nice_recv, this);
    nice_agent_attach_recv(agent_, stream_id_, 2,
                           g_main_loop_get_context(loop_), cb_nice_recv, this);

    // Start gathering local candidates
    if (!nice_agent_gather_candidates(agent_, stream_id_))
    {
        jerror("Failed to start candidate gathering");
    }
}

void JRtpEngine::stopNICE()
{
    if (!closed_.exchange(true))
    {
        jinfo("stopNICE");
        if (context_ && loop_)
        {
            g_main_loop_quit(loop_);
            g_main_loop_unref(loop_);
            g_main_context_unref(context_);
            loop_ = NULL;
            context_ = NULL;
        }
        if (gthread_ != nullptr)
        {
            gthread_->join();
            gthread_ = nullptr;
        }
    }
}
#endif

namespace
{
#define HEVC_NALU_TYPE_FU 49
typedef struct
{
    uint8_t forbindden_zero_bit;
    uint8_t nal_unit_type;
    uint8_t nuh_layer_id;
    uint8_t nuh_temporal_id_plus1;
} HEVCNALUHeader;

typedef struct {
    uint8_t s;
    uint8_t e;
    uint8_t type;
} HEVCFUHeader; 

void parseHEVCNALUHeader(uint16_t bytes, HEVCNALUHeader *header) {
    header->forbindden_zero_bit = (bytes >> 15) & 0x1;
    header->nal_unit_type = (bytes >> 9) & 0x3F;
    header->nuh_layer_id = (bytes >> 3) & 0x3F;
    header->nuh_temporal_id_plus1 = bytes & 0x7;
}

void parseHEVCFUHeader(uint8_t byte, HEVCFUHeader *header) {
    header->s = (byte >> 7) & 0x01;
    header->e = (byte >> 6) & 0x01;
    header->type = byte & 0x3F;
}

} // namespace

void JRtpEngine::insertRecv(uint16_t seq, uint32_t packetSize, uint32_t timestamp)
{
    if (avSeqSet_.size() == 0)
    {
        avStartSeq_ = seq;
        avSeq_ = avStartSeq_ + 1;
    }
    else
    {
        if (seq - avSeq_ > 100)
        {
            avSeqSet_.clear();
            avStartSeq_ = seq;
            avSeq_ = avStartSeq_ + 1;
        }
        else
        {
            if (avSeq_ != seq)
            {
                disorderCount_++;
            }
            else
            {
                if (avSeqSet_.size() >= kMaxSeqCheck / 2)
                {
                    uint16_t size = avSeqSet_.size() / 2;
                    for (int i = 0; i < size; i++) {
                        if (avSeqSet_.count(avStartSeq_) == 0) {
                            lostCount_++;
                            jinfo("bindIp_: %s, bindPort_: %d, lost seq: %d, seq: %d", bindIp_.c_str(), bindPort_, avStartSeq_, seq);
                        } else {
                            avSeqSet_.erase(avStartSeq_);
                        }
                        avStartSeq_++;
                    }
                    // for (uint16_t i = avStartSeq_ + 1; i < avEndseq; i++)
                    // {
                    //     if (!avSeqSet_.count(i))
                    //     {
                    //         lostCount_++;
                    //         jinfo("lost seq: %d, seq: %d", i, seq);
                    //     }
                    //     else
                    //     {
                    //         avSeqSet_.erase(avStartSeq_);
                    //     }
                    //     avStartSeq_ = i;
                    // }
                }

            }
            avSeq_ = seq + 1;
        }
    }
    avSeqSet_.emplace(seq);
    rtpStatistics[RTP_RECV].packetCount_++;
    rtpStatistics[RTP_RECV].bps_ += packetSize;
    rtpStatistics[RTP_RECV].packetRate_++;
    auto now = std::chrono::steady_clock::now();
    // 转化为时间戳
    auto local_timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
    // 计算jitter
    rtpStatistics[RTP_RECV].jitter = caculateJitter(
        rtpStatistics[RTP_RECV].last_local_ts, local_timestamp, rtpStatistics[RTP_RECV].last_rtp_ts, timestamp, recv_clock_rate_, rtpStatistics[RTP_RECV].jitter);
    // jinfo(
    //     "recv gap = %d %d %d %f", local_timestamp - rtpStatistics[RTP_RECV].last_local_ts, timestamp - rtpStatistics[RTP_RECV].last_rtp_ts, recv_clock_rate_,
    //     rtpStatistics[RTP_RECV].jitter);
    rtpStatistics[RTP_RECV].last_local_ts = local_timestamp;
    rtpStatistics[RTP_RECV].last_rtp_ts = timestamp;
}

void JRtpEngine::insertSend(uint32_t packetSize, uint32_t timestamp)
{
    rtpStatistics[RTP_SEND].packetCount_ += packetSize / payloadSize_ + (packetSize % payloadSize_ ? 1 : 0);
    rtpStatistics[RTP_SEND].bps_ += packetSize;
    rtpStatistics[RTP_SEND].packetRate_++;
    auto now = std::chrono::steady_clock::now();
    // 转化为时间戳
    auto local_timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
    rtpStatistics[RTP_SEND].jitter
        = caculateJitter(rtpStatistics[RTP_SEND].last_local_ts, local_timestamp, prev_timestamp_inc_, send_clock_rate_, rtpStatistics[RTP_SEND].jitter);
    // jinfo(
    //     "send gap = %d %d %d %f", local_timestamp - rtpStatistics[RTP_SEND].last_local_ts, prev_timestamp_inc_, send_clock_rate_,
    //     rtpStatistics[RTP_SEND].jitter);
    rtpStatistics[RTP_SEND].last_local_ts = local_timestamp;   
}

void JRtpEngine::start()
{
    if (thread_) return;
    thread_ = std::make_unique<hv::EventLoopThread>();
    if (!cb_thread_) cb_thread_ = std::make_unique<hv::EventLoopThread>();
    if (!rtp_send_thread_.isRunning()) {
        rtp_send_thread_.start();
    } 
    FN_BEGIN;
    seqValid_ = false;
    RTPUDPv4TransmissionParams transparams;
	RTPSessionParams sessparams;
	int status = 0;
	sessparams.SetOwnTimestampUnit(1.0/hz_);	
    sessparams.SetUsePollThread(false);	
	sessparams.SetAcceptOwnPackets(true);
    // sessparams.SetReceiveMode(RTPTransmitter::AcceptAll);
    transparams.SetBindIP(htonl(inet_addr(bindIp_.c_str())));
	transparams.SetPortbase(bindPort_);
    transparams.SetRTPReceiveBuffer(4 * 1024 * 1024);
    transparams.SetRTPSendBuffer(4 * 1024 * 1024);
    transparams.SetSendProxyEnabled(stun_);
    if (rtpProxy_)
    {
        transparams.SetSendProxyEnabled(true);
    }
	status = sess_->Create(sessparams,&transparams);
    if (status != 0)
    {
        jerror("Create session %s:%d failed %d", bindIp_.c_str(), bindPort_, status);
        return;
    }
    else
    {
        jinfo("Create session %s:%d", bindIp_.c_str(), bindPort_);
    }
    sess_->SetDefaultPayloadType(remote_payload_type_);
    sess_->SetDefaultMark(defaultMark_);
    sess_->SetMaximumPacketSize(1450);
    sess_->SetDefaultTimestampIncrement(timestampInc_);

    for (int i = 0; i < dsts_.size(); ++i)
    {
        status = sess_->AddDestination(dsts_[i]);
    }

#ifdef USE_WEBRTC
    if (status == 0) {
        sess_->SetRtcpReceiver([this](const char *data, int len, void *) -> int {
            if (rtpSourceProxy_ || rtpDestinationProxy_)
            {
                auto f = Frame::CreateFrame(FRAME_FORMAT_RTCP);
                f->writeBytes(data, len);
                if (rtpSourceProxy_)
                {
                    rtpSourceProxy_->onFrameTransfer(f);
                }
                if (rtpDestinationProxy_)
                {
                    rtpDestinationProxy_->onFrameTransfer(f);
                }
            }
            return len; 
        }, this);
    }
#endif

#ifdef USE_LIBNICE
    if (stun_)
    {
#ifdef USE_DTLS_RTP
        std::shared_ptr<DTLSRTPSession> s = std::dynamic_pointer_cast<DTLSRTPSession>(sess_);
        s->SetStunTransport(
            [this](const char *data, int len, bool rtp, void *) -> int {
                if (ready_[0] && ready_[1]) {
                    int ret = nice_agent_send(agent_, stream_id_, rtp ? 1 : 2, len, data);
                    jtrace("nice_agent_send [%s][%d] [%d] ret=[%d]", id_.c_str(), rtp, len, ret);
                }
                // else
                // {
                //     jerror("nice_agent_send not ready");
                // }
                return len;
            },
            this);
#endif
    }
#endif
    last_time_point_ = std::chrono::steady_clock::now();
    int timeout_ms = ptime_ / 2;
    timeout_ms = std::max(1, std::min(20, timeout_ms));
    thread_->loop()->setInterval(timeout_ms, [this](hv::TimerID id) {
        sess_->BeginDataAccess();
        // check incoming packets
        if (sess_->GotoFirstSourceWithData()) {
            do {
                RTPPacket *pack;
                // TODO:
                jrtplib::RTPSourceData *srcdat = sess_->GetCurrentSourceInfo();
                // jinfo("srcdat: %p", srcdat);
                if (srcdat) {
                    jrtplib::RTPAddress *rtp_addr = (jrtplib::RTPAddress *)srcdat->GetRTPDataAddress();
                    jrtplib::RTPIPv4Address *ipaddr = dynamic_cast<jrtplib::RTPIPv4Address *>(rtp_addr);
                    unsigned int srcIp = ipaddr->GetIP();
                    uint16_t srcPort = ipaddr->GetPort();
                    std::string srcUri = GetAddressString(srcIp, srcPort);
                    // jinfo("srcUri = %s, ssrc = %#x", srcUri.c_str(), srcdat->GetSSRC());
                    // for (auto &addr: ip_addrs_) jinfo("dstUri = %s", addr.c_str());
                    if (ip_addrs_.count(srcUri) == 0)
                        continue;
                    while ((pack = sess_->GetNextPacket()) != NULL) {
                        int pt = pack->GetPayloadType();
                        if (enable_pt_map_ && pt_map_.count(pt) > 0)
                            pt = pt_map_[pt];
                        // FIXME: h323的payload type不固定，需要特殊处理
                        if (pt >= 0 && setted_pt_ != pt && getCodecInst(pt, codec_inst_)) {
                            setted_pt_ = pt;
                            fmt_ = codec_inst_.fmt;
                            recv_clock_rate_ = getClockrate(pt);
                            if (recv_clock_rate_ == 0) recv_clock_rate_ = 90000;
                            jinfo("srcUri %s payload type = %d", srcUri.c_str(), pt);
                        } else if (is_h323_ && pt >= 0 && setted_pt_ != pt) {
                            setted_pt_ = pt;
                            if (pt < 96 && getCodecInst(pt, codec_inst_)) {
                                fmt_ = codec_inst_.fmt;
                                recv_clock_rate_ = getClockrate(pt);
                                if (recv_clock_rate_ == 0) recv_clock_rate_ = 90000;
                            }
                            jinfo("srcUri %s payload type = %d fmt %d", srcUri.c_str(), pt, fmt_);
                        }
                        // jinfo("remote payload = %d, local payload = %d", pack->GetPayloadType(), local_payload_type_);
                        if (setted_pt_ >= 0 || is_h323_) {
                            // if (pack->GetPayloadType() != local_payload_type_)
                            //     jwarn("packet_payload_type(%d) != local_payload_type_(%d)", pack->GetPayloadType(),  local_payload_type_);
                            insertRecv(pack->GetSequenceNumber(), pack->GetPacketLength(), pack->GetTimestamp());
                            //
                            switch (fmt_) {
                                case FRAME_FORMAT_H264: {
                                    uint8_t nal = pack->GetPayloadData()[0];
                                    uint8_t nal_type = nal & 0x1f;
                                    uint8_t type = nal_type;
                                    if (type >= 1 && type <= 23)
                                        type = 1;
                                    // SIP-S发过来的第一帧可能不是关键帧，关键帧请求
                                    // TODO: 发送FIR的条件
                                    if (first_ && type != 0x18 && (nal & 0x1f) != 0x7) {
                                        // first_ = false;
                                        auto now_time_point = std::chrono::steady_clock::now();
                                        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now_time_point - last_time_point_).count();
                                        if (duration >= 1000) {
                                            uint32_t ssrc = sess_->GetLocalSSRC();
                                            uint32_t media_ssrc = pack->GetSSRC();
                                            sendFIR(ssrc, media_ssrc);
                                            last_time_point_ = now_time_point;
                                        }
                                    }
                                    remote_ssrc_ = pack->GetSSRC();
                                    switch (type) {
                                        case 0:
                                        case 1: {
                                            // bbuf_.Clear();
                                            std::shared_ptr<Frame> f = Frame::CreateFrame(FRAME_FORMAT_H264);
                                            f->createFrameBuffer(sizeof(start_sequence) + pack->GetPayloadLength());
                                            memcpy(f->getFrameBuffer(), start_sequence, sizeof(start_sequence));
                                            memcpy(f->getFrameBuffer() + sizeof(start_sequence), pack->GetPayloadData(), pack->GetPayloadLength());
                                            if (7 == (nal & 0x1f) || 8 == (nal & 0x1f)) {
                                                first_ = false;
                                                // jinfo(
                                                //     "deliverFrame %x %x %x %x %x", f->getFrameBuffer()[0], f->getFrameBuffer()[1], f->getFrameBuffer()[2],
                                                //     f->getFrameBuffer()[3], f->getFrameBuffer()[4]);
                                            } else rtpStatistics[RTP_RECV].fps_++;
                                            deliverFrame(f);   
                                            break;
                                        }
                                        case 24: {
                                            first_ = false;
                                            auto f = ff_h264_handle_aggregated_packet(pack->GetPayloadData() + 1, pack->GetPayloadLength() - 1);
                                            if (f) {
                                                jinfo(
                                                    "deliverFrame %x %x %x %x %x", f->getFrameBuffer()[0], f->getFrameBuffer()[1], f->getFrameBuffer()[2],
                                                    f->getFrameBuffer()[3], f->getFrameBuffer()[4]);
                                                deliverFrame(f);
                                                // rtpStatistics[RTP_RECV].fps_++;
                                            }
                                            break;
                                        }
                                        case 28: {
                                            auto f = h264_handle_packet_fu_a(pack->GetPayloadData(), pack->GetPayloadLength(), bbuf_);
                                            if (f) {
                                                // jinfo("deliverFrame %x %x %x %x %x", f->getFrameBuffer()[0], f->getFrameBuffer()[1], f->getFrameBuffer()[2],
                                                // f->getFrameBuffer()[3], f->getFrameBuffer()[4]);
                                                deliverFrame(f);
                                                rtpStatistics[RTP_RECV].fps_++;
                                            }
                                            break;
                                        }
                                        default: jerror("nal type(%d) unknown", type); break;
                                    }
                                } break;
                                case FRAME_FORMAT_H265: {
                                    if (pack->GetPayloadType() != 98) break;
                                    const uint8_t *payload = pack->GetPayloadData();
                                    const uint32_t payload_size = pack->GetPayloadLength();
                                    HEVCNALUHeader fu_indicator;
                                    uint16_t fu_indicator_bytes = ((payload[0] << 8) | payload[1]);
                                    parseHEVCNALUHeader(fu_indicator_bytes, &fu_indicator);
                                    HEVCFUHeader fu_header;
                                    parseHEVCFUHeader(payload[2], &fu_header);

                                    // SIP-S发过来的第一帧可能不是关键帧，关键帧请求
                                    // TODO: 发送FIR的条件
                                    if (first_) {
                                        first_ = false;
                                        auto now_time_point = std::chrono::steady_clock::now();
                                        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now_time_point - last_time_point_).count();
                                        if (duration >= 1000) {
                                            uint32_t ssrc = sess_->GetLocalSSRC();
                                            uint32_t media_ssrc = pack->GetSSRC();
                                            sendFIR(ssrc, media_ssrc);
                                            last_time_point_ = now_time_point;
                                        }
                                    }

                                    switch (fu_indicator.nal_unit_type) {
                                        case 48: {
                                            first_ = false;
                                            auto f = ff_h265_handle_aggregated_packet(pack->GetPayloadData() + 2, pack->GetPayloadLength() - 2);
                                            if (f) {
                                                jinfo(
                                                    "deliverFrame %x %x %x %x %x %x", f->getFrameBuffer()[0], f->getFrameBuffer()[1], f->getFrameBuffer()[2],
                                                    f->getFrameBuffer()[3], f->getFrameBuffer()[4], f->getFrameBuffer()[5]);
                                                deliverFrame(f);
                                                // rtpStatistics[RTP_RECV].fps_++;
                                            }
                                            break;
                                        }
                                        case 49: {
                                            if (payload_size < 3)
                                                break;
                                            std::shared_ptr<Frame> f;
                                            if (fu_header.s) {
                                                // bbuf_.Clear();
                                                uint8_t reconstructNALU[2] = { (fu_indicator.forbindden_zero_bit << 7) | (fu_header.type << 1),
                                                                               (fu_indicator.nuh_layer_id << 3) | fu_indicator.nuh_temporal_id_plus1 };
                                                // jinfo("[%x %x]", reconstructNALU[0], reconstructNALU[1]);
                                                bbuf_.WriteBytes((const char *)start_sequence, sizeof(start_sequence));
                                                bbuf_.WriteBytes((const char *)reconstructNALU, 2);
                                            }
                                            if (bbuf_.Length() > 0)
                                                bbuf_.WriteBytes((const char *)(payload + 3), payload_size - 3);
                                            if (fu_header.e) {
                                                if (bbuf_.Length() > 0) {
                                                    f = Frame::CreateFrame(FRAME_FORMAT_H265);
                                                    f->createFrameBuffer(bbuf_.Length());
                                                    memcpy(f->getFrameBuffer(), bbuf_.Data(), bbuf_.Length());
                                                    bbuf_.Clear();
                                                }
                                            }
                                            if (f) {
                                                deliverFrame(f);
                                                rtpStatistics[RTP_RECV].fps_++;
                                            }
                                            break;
                                        }
                                        // case 2:
                                            // TSA_N
                                        case 40:
                                            // SUFFIX_SEI_NUT
                                            break;
                                        default: {
                                            // if (fu_indicator.nal_unit_type != 1)
                                            //     jinfo("type = %d", fu_indicator.nal_unit_type);
                                            // TODO:
                                            // bbuf_.Clear();
                                            std::shared_ptr<Frame> f = Frame::CreateFrame(FRAME_FORMAT_H265);
                                            f->createFrameBuffer(sizeof(start_sequence) + pack->GetPayloadLength());
                                            memcpy(f->getFrameBuffer(), start_sequence, sizeof(start_sequence));
                                            memcpy(f->getFrameBuffer() + sizeof(start_sequence), pack->GetPayloadData(), pack->GetPayloadLength());
                                            // jinfo("nalu type: %d", fu_indicator.nal_unit_type);
                                            deliverFrame(f);
                                            rtpStatistics[RTP_RECV].fps_++;
                                            break;
                                        }
                                    }
                                } break;
                                default: {
                                    auto f = Frame::CreateFrame((FrameFormat)fmt_);
                                    if (f) {
#ifdef USE_AUDIO_SIMULATION
                                        // if (should_drop_packet()) break;    // for test
#endif
                                        if (outputRawRtp) {
                                            // jinfo("outputRawRtp");
                                            f->createFrameBuffer(pack->GetPacketLength());
                                            memcpy(f->getFrameBuffer(), pack->GetPacketData(), pack->GetPacketLength());
                                            f->setFrameFormat(FRAME_FORMAT_RTP);
                                        } else {
                                            f->createFrameBuffer(pack->GetPayloadLength());
                                            memcpy(f->getFrameBuffer(), pack->GetPayloadData(), pack->GetPayloadLength());
                                            if (!seqValid_) {
                                                seqValid_ = true;
                                                seq_ = pack->GetSequenceNumber() + 1;
                                            } 
                                            else {
                                                if (pack->GetSequenceNumber() > seq_ && pack->GetSequenceNumber() - seq_ < 8) {
                                                    while (seq_ != pack->GetSequenceNumber()) {
                                                        seq_++;
                                                        f->setPayloadType(setted_pt_);
                                                        deliverFrame(f); //触发plc
                                                        if (stopped_)
                                                            break;
                                                    }
                                                }
                                                seq_ = pack->GetSequenceNumber() + 1;
                                            }
                                        }
                                        f->setPayloadType(setted_pt_);
                                        deliverFrame(f);
                                    }
                                } break;
                            }
                        }

                        sess_->DeletePacket(pack);
                        printOutputStatus("jrtp");
                        if (stopped_)
                            break;
                    }
                }

                if (stopped_)
                    break;
            } while (sess_->GotoNextSourceWithData());
        }

        sess_->EndDataAccess();
        sess_->Poll();
    });
    thread_->start();
    if (cb_thread_)
        cb_thread_->start();
#ifdef USE_LIBNICE
    if (stun_)
    {
        startNICE();
    }
#endif
    FN_END;
    jinfo("JRtpEngine::start[%p]", this);
    {
        std::unique_lock<std::mutex> l(stopMutex_);
        stopped_ = false;
    }
    // std::thread([this](){
    //     notifyCallback();
    // }).detach();
    cb_thread_->loop()->setInterval(1000, [this](hv::TimerID id) {
        notifyCallback();
        // sendPLI(sess_->GetLocalSSRC(), remote_ssrc_);
        // sendFIR(sess_->GetLocalSSRC(), remote_ssrc_);
    });
}

void JRtpEngine::stop()
{
    jinfo("JRtpEngine::stop");
    {
        std::unique_lock<std::mutex> l(stopMutex_);
        stopped_ = true;
    }
    if (cb_thread_) {
        cb_thread_->stop(false);    // FIXME: 需要等待回调资源回收
        cb_thread_.reset();
    }
    if (thread_)
    {
        thread_->stop(true);
        thread_.reset();
    }
    if (rtp_send_thread_.isRunning()) {
        rtp_send_thread_.stop(true);
        rtp_send_thread_.join();
    } 
#ifdef USE_LIBNICE
    if (stun_)
    {
        stopNICE();
    }
#endif
    sess_->Destroy();
    lastUpdate_ = "";

    for (size_t i = 0; i < 2; i++)
    {
        rtpStatistics[i].bps_ = 0;
        rtpStatistics[i].fps_ = 0;
        rtpStatistics[i].packetRate_ = 0;
        rtpStatistics[i].bpsTick_ = 0;
        rtpStatistics[i].packetCount_ = 0;
        rtpStatistics[i].bpsEverySecond_.clear();
        rtpStatistics[i].packetRateEverySecond_.clear();
        rtpStatistics[i].delay = 0.0;
    }

    avSeq_ = 0;
    avStartSeq_ = 0;
    avSeqSet_.clear();
    disorderCount_ = 0;
    lostCount_ = 0;

    for (auto &ip: servers_to_ping_) {
        CPing::instance().RemoveServer(ip);
    }
    servers_to_ping_.clear();
    jinfo("JRtpEngine::stop[%p]", this);
}

void JRtpEngine::sendH264(const std::shared_ptr<Frame> &f)
{
    uint8_t* frameBuffer = f->getFrameBuffer();
    int frameSize = f->getFrameSize();
    uint8_t* buf = frameBuffer;
    int size = frameSize;

    int nal_start = 0;
    int nal_end = 0;
    int prefixSize = 0;

    int len = findNALU(buf, size, &nal_start, &nal_end, &prefixSize);
    while (len > 0)
    {
        uint8_t* nal_buf = buf + nal_start;
        int nal_size = len;
        int status = 0;
        uint8_t type = nal_buf[0] & 0x1F;
        uint8_t nri = nal_buf[0] & 0x60;

        if (len <= payload_.size())
        {
            int inc = 0;
            switch (type)
            {
            case H264_NAL_SPS:
            case H264_NAL_PPS:
                status = sess_->SendPacket(nal_buf, nal_size, remote_payload_type_, false, 0);
                prev_timestamp_inc_ = 0;
                break;
            case H264_NAL_IDR:
            case H264_NAL_P:
                inc = nextTimestampInc();
                status = sess_->SendPacket(nal_buf, nal_size, remote_payload_type_, true, inc);
                prev_timestamp_inc_ = inc;
                break;
            default:
                break;
            }
            //LOG(ERROR) << "SendPacket ret = " << status;
        }
        else
        {
            //jinfo("sendH264 %d", len);
            payload_[0] = 28;        /* FU Indicator; Type = 28 ---> FU-A */
            payload_[0] |= nri;
            payload_[1] = type;
            payload_[1] |= 1 << 7;

            nal_buf += 1;
            nal_size -= 1;

            int flag_byte = 1;
            int header_size = 2;
            uint32_t timestampInc = 0;
            while (nal_size + header_size > payload_.size()) {
                memcpy(&payload_[header_size], nal_buf, payload_.size() - header_size);
                status = sess_->SendPacket(payload_.data(), payload_.size(), remote_payload_type_, false, timestampInc);
                //LOG(ERROR) << "SendPacket ret = " << status;
                nal_buf  += payload_.size() - header_size;
                nal_size -= payload_.size() - header_size;
                payload_[flag_byte] &= ~(1 << 7);
            }
            payload_[flag_byte] |= 1 << 6;
            memcpy(&payload_[header_size], nal_buf, nal_size);
            timestampInc = nextTimestampInc();
            status = sess_->SendPacket(payload_.data(), payload_.size(), remote_payload_type_, true, timestampInc);
            prev_timestamp_inc_ = timestampInc;          
            //LOG(ERROR) << "SendPacket ret = " << status;
        }

        buf += len;
        size -= len;
        buf += prefixSize;
        size -= prefixSize;
        len = findNALU(buf, size, &nal_start, &nal_end, &prefixSize);
    }
}

void JRtpEngine::sendH265(const std::shared_ptr<Frame> &f)
{
    uint8_t* frameBuffer = f->getFrameBuffer();
    int frameSize = f->getFrameSize();
    uint8_t* buf = frameBuffer;
    int size = frameSize;

    int nal_start = 0;
    int nal_end = 0;
    int prefixSize = 0;
    int len = findNALU(buf, size, &nal_start, &nal_end, &prefixSize);
    while (len > 0) {
        uint8_t* nal_buf = buf + nal_start;
        int nal_size = len;  
        int status = 0;
        uint8_t type = (nal_buf[0] >> 1) & 0x3F;
        if (len <= payload_.size())
        {
            uint32_t inc = 0;
            switch (type) {
                case H265_NAL_VPS:
                case H265_NAL_SPS:
                case H265_NAL_PPS: 
                case H265_NAL_SEI: status = sess_->SendPacket(nal_buf, nal_size, remote_payload_type_, false, 0); inc = 0; break;
                default: inc = nextTimestampInc(); status = sess_->SendPacket(nal_buf, nal_size, remote_payload_type_, true, inc); break;  // TODO: 0-31
            }
            prev_timestamp_inc_ = inc;
            // LOG(ERROR) << "SendPacket ret = " << status;
        }  else {
            payload_[0] = 49 << 1;
            payload_[1] = 1;

            payload_[2] = type;
            payload_[2] |= 1 << 7;

            nal_buf += 2;
            nal_size -= 2;
            int flag_byte = 2;
            int header_size = 2 + 1;
            uint32_t timestampInc = 0;
            while (nal_size + header_size > payload_.size()) {
                memcpy(&payload_[header_size], nal_buf, payload_.size() - header_size);
                status = sess_->SendPacket(payload_.data(), payload_.size(), remote_payload_type_, false, timestampInc);
                nal_buf  += payload_.size() - header_size;
                nal_size -= payload_.size() - header_size;
                payload_[flag_byte] &= ~(1 << 7);
            }
            payload_[flag_byte] |= 1 << 6;
            memcpy(&payload_[header_size], nal_buf, nal_size);
            timestampInc = nextTimestampInc();
            status = sess_->SendPacket(payload_.data(), payload_.size(), remote_payload_type_, true, timestampInc);
            prev_timestamp_inc_ = timestampInc;
        }
        buf += len;
        size -= len;
        buf += prefixSize;
        size -= prefixSize;
        len = findNALU(buf, size, &nal_start, &nal_end, &prefixSize);
    }
}

uint32_t JRtpEngine::convertToRTPTimestampInc(struct timeval tv) {
  uint32_t timestampIncrement = (90000*tv.tv_sec);
  timestampIncrement += (uint32_t)(90000*(tv.tv_usec/1000000.0) + 0.5);
  return timestampIncrement;
}

uint32_t JRtpEngine::nextTimestampInc() {
    return 3000;
    // struct timeval timeNow;
    // gettimeofday(&timeNow, NULL);

    // u_int32_t tsNow = convertToRTPTimestampInc(timeNow);
    // return tsNow;
}

int32_t JRtpEngine::sendFIR(uint32_t ssrc, uint32_t media_ssrc) {
    uint8_t fir_packet[20] {0};
    fir_packet[0] = 0x84;
    fir_packet[1] = 0xCE;
    fir_packet[2] = 0x00;
    fir_packet[3] = 0x04;
    fir_packet[4] = (ssrc >> 24) & 0xFF;
    fir_packet[5] = (ssrc >> 16) & 0xFF;
    fir_packet[6] = (ssrc >> 8) & 0xFF;
    fir_packet[7] = ssrc & 0xFF;
    fir_packet[8] = (media_ssrc >> 24) & 0xFF;
    fir_packet[9] = (media_ssrc >> 16) & 0xFF;
    fir_packet[10] = (media_ssrc >> 8) & 0xFF;
    fir_packet[11] = media_ssrc & 0xFF;
    fir_packet[12] = (media_ssrc >> 24) & 0xFF;
    fir_packet[13] = (media_ssrc >> 16) & 0xFF;
    fir_packet[14] = (media_ssrc >> 8) & 0xFF;
    fir_packet[15] =  media_ssrc & 0xFF;
    fir_packet[16] = fir_index_++;
    fir_packet[17] = 0x00;
    fir_packet[18] = 0x00;
    fir_packet[19] = 0x00;
    auto status = sess_->SendRawData(fir_packet, 20, false);    
    jinfo("###sendFIR: Request key frame %d!!!!!!!!!!!!!!!!!!!!!!!", status);
    return status;
}

int32_t JRtpEngine::sendPLI(uint32_t ssrc, uint32_t media_ssrc) {
    uint8_t pli_packet[20] {0};
    // Byte 0: Version(2 bits), Padding(1 bit), Extension(1 bit), CSRC count(4 bits)
    pli_packet[0] = 0x81;   // Version: 2, Padding: 0, FMT: 1
    // Byte 1: Payload type (PT=206 - Picture Loss Indication)
    pli_packet[1] = 0xCE;
    // Byte 2,3: Message Length 
    pli_packet[2] = 0x00;
    pli_packet[3] = 0x04;   // Length: 4 words(20bytes / 4 - 1)
    // Byte 4-7: SSRC of sender
    pli_packet[4] = (ssrc >> 24) & 0xFF;
    pli_packet[5] = (ssrc >> 16) & 0xFF;
    pli_packet[6] = (ssrc >> 8) & 0xFF;
    pli_packet[7] = ssrc & 0xFF;
    // Byte 8-15: SSRC of media source(要请求关键帧的流)
    pli_packet[8] = (media_ssrc >> 24) & 0xFF;
    pli_packet[9] = (media_ssrc >> 16) & 0xFF;
    pli_packet[10] = (media_ssrc >> 8) & 0xFF;
    pli_packet[11] = media_ssrc & 0xFF;
    // pli_packet[12] = (media_ssrc >> 24) & 0xFF;
    // pli_packet[13] = (media_ssrc >> 16) & 0xFF;
    // pli_packet[14] = (media_ssrc >> 8) & 0xFF;
    // pli_packet[15] =  media_ssrc & 0xFF;
    // pli_packet[16] = fir_index_++;
    // pli_packet[17] = 0x00;
    // pli_packet[18] = 0x00;
    // pli_packet[19] = 0x00;
    auto status = sess_->SendRawData(pli_packet, 20, false);    
    jinfo("###sendPLI: Request key frame %d!!!!!!!!!!!!!!!!!!!!!!!", status);
    return status;
}

#ifdef USE_AUDIO_SIMULATION
bool JRtpEngine::should_drop_packet() {
    return uniform_dist_(gen_) < 0.1;
}

void JRtpEngine::simulate_network_conditions() {
    static int BASE_DELAY = 100;    // ms
    std::uniform_int_distribution<> int_dist(-50, 50);
    int actual_delay = BASE_DELAY + int_dist(gen_);
    std::this_thread::sleep_for(std::chrono::milliseconds(actual_delay));
}
#endif

void JRtpEngine::notifyCallback() {
    nlohmann::json notify;
    rtpStatistics[RTP_SEND].bpsTick_ = GetTickCount();
    rtpStatistics[RTP_RECV].bpsTick_ = GetTickCount();
    // while (!stopped_) {
    int64_t now = GetTickCount();
    // int64_t last;
    // {
    //     std::unique_lock<std::mutex> l(stopMutex_);
    //     if (stopped_) return;
    //     last = rtpStatistics[RTP_SEND].bpsTick_;
    // }
    // if (now - last >= 1000) {
    std::string id;
    {
        std::unique_lock<std::mutex> l(stopMutex_);
        if (stopped_)
            return;
        id = notifycallbacks_["RTPStatistics"].id;
#ifdef USE_WEBRTC
        // FIXME：To prevent mismatches between the fields required by upper-level business logic and those provided by the underlying library, conversion is
        // necessary.
        if (rtpSourceProxy_) {
            nlohmann::json stats_json;
            auto stats = rtpSourceProxy_->GetStats2();
            if (!stats.empty()) {
                stats_json = nlohmann::json::parse(stats);
            }
            stats_json["codec"] = Frame::formatName((FrameFormat)fmt_);
            if (Frame::isAudioFormat((FrameFormat)fmt_)) {
                notify["recv"]["audio"] = stats_json;
            } else {
                notify["recv"]["video"] = stats_json;
            }
        }
        if (rtpDestinationProxy_) {
            nlohmann::json stats_json;
            auto stats = rtpDestinationProxy_->GetStats2();
            if (!stats.empty()) {
                stats_json = nlohmann::json::parse(stats);
            }
            double delay = 0.0;
            if (ips_.size() > 0) {
                auto reply = CPing::instance().GetReply(ips_[0]);
                delay = reply.m_dwRoundTripTime;
            }
            rtpStatistics[RTP_SEND].delay = delay;
            stats_json["codec"] = Frame::formatName((FrameFormat)fmt_);
            stats_json["delay"] = rtpStatistics[RTP_SEND].delay;
            if (Frame::isAudioFormat((FrameFormat)fmt_)) {
                notify["send"]["audio"] = stats_json;
            } else {
                notify["send"]["video"] = stats_json;
            }
        }
        if (rtpSourceProxy_ || rtpDestinationProxy_) {
            if (notifycallbacks_.find("RTPStatistics") != notifycallbacks_.end() && notifycallbacks_["RTPStatistics"].cb2) {
                if (!stopped_) {
                    // notifycallbacks_["RTPStatistics"].cb("RTPStatistics", notify.dump(), notifycallbacks_["RTPStatistics"].param);
                    notifycallbacks_["RTPStatistics"].cb2(id, "RTPStatistics", notify.dump(), notifycallbacks_["RTPStatistics"].param);
                }
            }
            // jinfo("notify: %s", notify.dump().c_str());
            return;
        }
#endif

        rtpStatistics[RTP_SEND].bpsTick_ = now;
        rtpStatistics[RTP_SEND].bpsEverySecond_.push_back(rtpStatistics[RTP_SEND].bps_);
        rtpStatistics[RTP_SEND].bps_ = 0;
        rtpStatistics[RTP_SEND].packetRateEverySecond_.push_back(rtpStatistics[RTP_SEND].packetRate_);
        rtpStatistics[RTP_SEND].packetRate_ = 0;
        if (rtpStatistics[RTP_SEND].bpsEverySecond_.size() > 5) {
            rtpStatistics[RTP_SEND].bpsEverySecond_.pop_front();
            rtpStatistics[RTP_SEND].packetRateEverySecond_.pop_front();
        }
        uint64_t total = 0;
        for (auto i : rtpStatistics[RTP_SEND].bpsEverySecond_) {
            total += i;
        }
        uint64_t average = total / rtpStatistics[RTP_SEND].bpsEverySecond_.size();
        uint64_t average_rate = 0;
        for (auto rate : rtpStatistics[RTP_SEND].packetRateEverySecond_)
            average_rate += rate;
        average_rate /= rtpStatistics[RTP_SEND].packetRateEverySecond_.size();
        double delay = 0.0;
        if (ips_.size() > 0) {
            auto reply = CPing::instance().GetReply(ips_[0]);
            delay = reply.m_dwRoundTripTime;
        }
        rtpStatistics[RTP_SEND].delay = delay;
        if (Frame::isAudioFormat((FrameFormat)fmt_)) {
            notify["send"]["audio"]["codec"] = Frame::formatName((FrameFormat)fmt_);
            notify["send"]["audio"]["bps"] = average * 8;
            notify["send"]["audio"]["packetRate"] = average_rate;
            notify["send"]["audio"]["sendPacketCount"] = rtpStatistics[RTP_SEND].packetCount_;
            notify["send"]["audio"]["delay"] = rtpStatistics[RTP_SEND].delay;
            notify["send"]["audio"]["jitter"] = rtpStatistics[RTP_SEND].jitter;
        }
        if (Frame::isVideoFormat((FrameFormat)fmt_)) {
            notify["send"]["video"]["codec"] = Frame::formatName((FrameFormat)fmt_);
            notify["send"]["video"]["bps"] = average * 8;
            notify["send"]["video"]["fps"] = rtpStatistics[RTP_SEND].fps_;
            notify["send"]["video"]["sendPacketCount"] = rtpStatistics[RTP_SEND].packetCount_;
            notify["send"]["video"]["packetRate"] = average_rate;
            notify["send"]["video"]["delay"] = rtpStatistics[RTP_SEND].delay;
            notify["send"]["video"]["jitter"] = rtpStatistics[RTP_SEND].jitter;
            rtpStatistics[RTP_SEND].fps_ = 0;
        }

        rtpStatistics[RTP_RECV].bpsTick_ = now;
        rtpStatistics[RTP_RECV].bpsEverySecond_.push_back(rtpStatistics[RTP_RECV].bps_);
        rtpStatistics[RTP_RECV].bps_ = 0;
        rtpStatistics[RTP_RECV].packetRateEverySecond_.push_back(rtpStatistics[RTP_RECV].packetRate_);
        rtpStatistics[RTP_RECV].packetRate_ = 0;
        if (rtpStatistics[RTP_RECV].bpsEverySecond_.size() > 5) {
            rtpStatistics[RTP_RECV].bpsEverySecond_.pop_front();
            rtpStatistics[RTP_RECV].packetRateEverySecond_.pop_front();
        }
        total = 0;
        for (auto i : rtpStatistics[RTP_RECV].bpsEverySecond_) {
            total += i;
        }
        average = total / rtpStatistics[RTP_RECV].bpsEverySecond_.size();

        average_rate = 0;
        for (auto rate : rtpStatistics[RTP_RECV].packetRateEverySecond_)
            average_rate += rate;
        average_rate /= rtpStatistics[RTP_RECV].packetRateEverySecond_.size();

        notify["recv"]["lost"] = lostCount_;
        notify["recv"]["disorder"] = disorderCount_;
        if (Frame::isAudioFormat((FrameFormat)fmt_)) {
            notify["recv"]["audio"]["codec"] = Frame::formatName((FrameFormat)fmt_);
            notify["recv"]["audio"]["bps"] = average * 8;
            notify["recv"]["audio"]["packetRate"] = average_rate;
            notify["recv"]["audio"]["recvPacketCount"] = rtpStatistics[RTP_RECV].packetCount_;
            notify["recv"]["audio"]["lost"] = lostCount_;
            notify["recv"]["audio"]["disorder"] = disorderCount_;
            notify["recv"]["audio"]["jitter"] = rtpStatistics[RTP_RECV].jitter;
        }
        if (Frame::isVideoFormat((FrameFormat)fmt_)) {
            notify["recv"]["video"]["codec"] = Frame::formatName((FrameFormat)fmt_);
            notify["recv"]["video"]["bps"] = average * 8;
            notify["recv"]["video"]["fps"] = rtpStatistics[RTP_RECV].fps_;
            notify["recv"]["video"]["packetRate"] = average_rate;
            notify["recv"]["video"]["recvPacketCount"] = rtpStatistics[RTP_RECV].packetCount_;
            notify["recv"]["video"]["lost"] = lostCount_;
            notify["recv"]["video"]["disorder"] = disorderCount_;
            notify["recv"]["video"]["jitter"] = rtpStatistics[RTP_RECV].jitter;
            rtpStatistics[RTP_RECV].fps_ = 0;
        }
        // jinfo("rtp statistics: %s", notify.dump().c_str());
        // jinfo("send jitter: %f recv jitter: %f", rtpStatistics[RTP_SEND].jitter, rtpStatistics[RTP_RECV].jitter);
    }
    if (notifycallbacks_.find("RTPStatistics") != notifycallbacks_.end() && notifycallbacks_["RTPStatistics"].cb2) {
        if (!stopped_) {
            // notifycallbacks_["RTPStatistics"].cb("RTPStatistics", notify.dump(), notifycallbacks_["RTPStatistics"].param);
            notifycallbacks_["RTPStatistics"].cb2(id, "RTPStatistics", notify.dump(), notifycallbacks_["RTPStatistics"].param);
        }
    }
    // }
    // usleep(10000);
    // }
}
