cmake_minimum_required(VERSION 3.10)

project(csph3loopback)

set(CMAKE_CXX_STANDARD 17)

include_directories(${CMAKE_CURRENT_SOURCE_DIR} ${CMAKE_PREFIX_PATH}/include)
link_directories(${CMAKE_PREFIX_PATH}/lib)

add_definitions(-Wall -O3 -g -fexceptions -fpermissive)
add_executable(${PROJECT_NAME} test.cpp)
target_link_libraries(${PROJECT_NAME} PRIVATE MediaPipeline ptoolkit jrtp jthread ljcore hv_static ZLToolKit aec rnnoise opus g729 fdk-aac DTLSTool nice glib-2.0 gio-2.0 gobject-2.0 gmodule-2.0 z ffi pcre2-8 srtp2 PNPcm ssl crypto pthread dl)
if (ENABLE_RK)
    target_link_libraries(${PROJECT_NAME} PRIVATE drm rockchip_mpp rga asound)
endif()



