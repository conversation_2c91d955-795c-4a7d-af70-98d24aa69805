// created by gyj 2024-3-6
#ifndef P_CuVideoFrameMixer_h
#define P_CuVideoFrameMixer_h

#include <hv/EventLoopThread.h>
#include "VideoFrameMixer.h"
#include "VideoLayout.h"

namespace panocom
{
    class CuFrameBufferManager;
    class CuVideoFrameMixer : public VideoFrameMixer
    {
    public:
        CuVideoFrameMixer(const std::string& jsonParams);
        ~CuVideoFrameMixer() override;

        void onFrame(const std::shared_ptr<Frame>& frame) override;

    private:
        void generateOutputFrame();

    private:
        long outputFrameGap_;
        long outputFrameTick_;

        std::vector<Region> videoLayout_;

        int x_;
        int y_;
        int width_;
        int height_;

        std::shared_ptr<Frame> outputFrame_;
        std::unique_ptr<CuFrameBufferManager> frameBufferManager_;

        int cudaId_ = 0;
        int gpuIndex_ = 0;
        hv::EventLoopThread thread_;
    };
}

#endif