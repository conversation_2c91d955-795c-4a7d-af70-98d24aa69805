#include <stdint.h>
#include "RtcAudioFrameLec.h"
#include "Frame.h"
#include "Utils.h"
#include <ljcore/jlog.h>
#include <stdio.h>
#include <json.hpp>

#define LEC_BUFFER_MAX_LEN 160

extern int mmi_record_audio;

using namespace panocom;

RtcAudioFrameLec::RtcAudioFrameLec(const std::string& jsonParams)
    : pcm_logging_(false)
    , aec_on_(true)
    , agc_on_(false)
    , ns_on_(true)
    , cng_on_(false)
    , howlingSuppress_on_(false) {
    FN_BEGIN;
    jinfo("RtcAudioFrameLec %s", jsonParams.c_str());
    name_ = "RtcAudioFrameLec";
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    int chn = 1;
    if (j.contains("channel")) {
        chn = j["channel"];
    }
    samplerate_ = 8000;
    if (j.contains("samplerate"))
    {
        samplerate_ = j["samplerate"];
    }
    if (j.contains("aec")) {
        int aec = j["aec"];
        aec_on_ = (aec > 1);
    }
    if (j.contains("agc")) {
        int agc = j["agc"];
        agc_on_ = (agc > 1);
    }
    if (j.contains("ns")) {
        int ns = j["ns"];
        ns_on_ = (ns > 1);
    }
    if (j.contains("cng")) {
        int cng = j["cng"];
        jinfo("containcng");
        cng_on_ = (cng > 1);
    }
    if (j.contains("log")) {
        int pcm_logging = j["log"];
        pcm_logging_ = (pcm_logging > 1);
    }
    if (j.contains("anti-howling")) {
        int howlingSuppress = j["anti-howling"];
        howlingSuppress_on_ = (howlingSuppress > 1);
    }
    fmt_ = Frame::getPCMFormat(chn, samplerate_);
    lec_input_buf_ = new int16_t[samplerate_ * 10 / 1000 * 5 * 2];
    start();
    FN_END;
}

RtcAudioFrameLec::~RtcAudioFrameLec()
{
    FN_BEGIN;
    stop();
    if (lec_input_buf_) {
        delete[] lec_input_buf_;
        lec_input_buf_ = nullptr;
    }
    if (ofs_.is_open()) ofs_.close();
    if (ofs_1.is_open()) ofs_1.close();
    FN_END;
}

void RtcAudioFrameLec::onFrame(const std::shared_ptr<Frame>& frame) {
    std::unique_lock<std::mutex> locker(frame_mutex_);
    if (!thread_.isRunning()) return;
    if (Frame::isPCM(frame->getFrameFormat()))
    {
        thread_.loop()->runInLoop([this, frame](){
            //jinfo("RtcAudioFrameAec::onFrame %d %d", frame->getFrameFormat(), frame->getFrameSize());
            auto f = frame;
            int samplerate = 0;
            int chn = 0;
            if (!f->isFarEndFrame()) {
                if (Frame::getSamplerate(frame->getFrameFormat(), samplerate, chn))
                {
                    if (samplerate_ != samplerate)
                    {
                        if (!initResampler_ || source_samplerate_ != samplerate)
                        {
                            source_samplerate_ = samplerate;
                            initResampler_ = true;
    #ifdef WEBRTC_RESAMPLE_ANOTHER
                            resampler_.InitializeIfNeeded(samplerate, samplerate_, 1);
    #else
                            resampler_.ResetIfNeeded(samplerate, samplerate_, 1);
    #endif
                        }
                        FrameFormat fmt = (FrameFormat)fmt_;
                        auto frame = Frame::CreateFrame(fmt);
                        frame->createFrameBuffer(f->getFrameSize() * samplerate_ / samplerate);
                        memset(frame->getFrameBuffer(), 0, frame->getFrameSize());
    #ifdef WEBRTC_RESAMPLE_ANOTHER
                        resampler_.Resample((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2);
    #else
                        size_t outlen = 0;
                        resampler_.Push((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2, outlen);
    #endif
                        f = frame;
                        if (first_)
                        {
                            first_ = false;
                            jinfo("RtcAec_Process resample %d -> %d", samplerate, samplerate_);
                        }
                    }
                }
                FrameFormat fmt = (FrameFormat)fmt_;
                auto frame = Frame::CreateFrame(fmt);
                frame->createFrameBuffer(f->getFrameSize());
                //jinfo("RtcAec_Process %d %d", f->getFrameFormat(), f->getFrameSize());
                int total = frame->getFrameSize() / sizeof(int16_t);
                int pos = 0;
                // if (!ofs_.is_open()) ofs_.open("capture.pcm", std::ios::binary | std::ios::out);
                // ofs_.write(f->getFrameBuffer(), f->getFrameBufferSize());
                while (total > 0) {
                    int size = (total > LEC_BUFFER_MAX_LEN ? LEC_BUFFER_MAX_LEN : total);
                    memcpy((char*)lec_input_buf_, f->getFrameBuffer() + pos * sizeof(int16_t), size * sizeof(int16_t));
                    rtc_aec_process_mics(lec_.get(), lec_input_buf_, (int16_t*)frame->getFrameBuffer() + pos, size, 5);
                    // rtc_aec_process(lec_.get(), (int16_t*)f->getFrameBuffer() + pos, (int16_t*)frame->getFrameBuffer() + pos, size);
                    if (mmi_record_audio)
                    {
                        if (!pf_)
                        {
                            std::string fileName = name_ + std::to_string(samplerate_) + "-" + std::to_string((long)this) + ".pcm";
                            pf_ = fopen(fileName.c_str(), "w+b");
                        }
                        if (pf_)
                        {
                            fwrite(frame->getFrameBuffer() + pos * sizeof(int16_t), 1, size, pf_);
                        }
                    }
                    total -= size;
                    pos += size;
                }
                deliverFrame(frame);
                printOutputStatus("RtcAudioFrameLec");   
            } else {
                if (Frame::getSamplerate(frame->getFrameFormat(), samplerate, chn))
                {
                    if (samplerate_ != samplerate)
                    {
                        if (!initResampler_farend_ || source_farend_samplerate_ != samplerate)
                        {
                            source_farend_samplerate_ = samplerate;
                            initResampler_farend_ = true;
    #ifdef WEBRTC_RESAMPLE_ANOTHER
                            resampler_farend_.InitializeIfNeeded(samplerate, samplerate_, 1);
    #else
                            resampler_farend_.ResetIfNeeded(samplerate, samplerate_, 1);
    #endif
                        }
                        FrameFormat fmt = (FrameFormat)fmt_;
                        fmt = Frame::getFormatFarEnd(fmt);
                        auto frame = Frame::CreateFrame(fmt);
                        frame->createFrameBuffer(f->getFrameSize() * samplerate_ / samplerate);
                        memset(frame->getFrameBuffer(), 0, frame->getFrameSize());
    #ifdef WEBRTC_RESAMPLE_ANOTHER
                        resampler_farend_.Resample((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2);
    #else
                        size_t outlen = 0;
                        resampler_farend_.Push((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2, outlen);
    #endif
                        f = frame;
                        if (first_farend_)
                        {
                            first_farend_ = false;
                            jinfo("RtcAec_BufferFarend resample %d -> %d", samplerate, samplerate_);
                        }
                    }
                }      
                // jinfo("RtcAec_BufferFarend %d %d", f->getFrameFormat(), f->getFrameSize());
                // TODO: 
                // if (!ofs_1.is_open()) ofs_1.open("farend.pcm", std::ios::binary | std::ios::out);
                // ofs_1.write(f->getFrameBuffer(), f->getFrameBufferSize());
                int total = f->getFrameSize() / sizeof(int16_t);
                int pos = 0;
                while (total > 0) {
                    int size = (total > LEC_BUFFER_MAX_LEN ? LEC_BUFFER_MAX_LEN : total);
                    rtc_aec_buffer_farend(lec_.get(), (int16_t*)f->getFrameBuffer() + pos, size);
                    total -= size;
                    pos += size;
                }       
            }
        });
    }
    printInputStatus("RtcAudioFrameLec");
}

void RtcAudioFrameLec::start() {
    if (thread_.isRunning()) return;
    FN_BEGIN;
    void* core = nullptr;
    rtc_aec_create(&core);
	AecConfig config;
	memset(&config, 0, sizeof(config));
	// config.nlpMode = kAecNlpConservative;
	config.agc_on = agc_on_;
    config.ns_on = ns_on_;
    config.cng_on = cng_on_;
    config.aec_on = aec_on_;
	config.pcm_logging = pcm_logging_;
    config.howlingSuppress_on = howlingSuppress_on_;
    config.lec_off = false;
	rtc_aec_set_config(core, config);
    rtc_aec_init(core, samplerate_);
    lec_ = std::shared_ptr<void>(core, [](void* core){
        rtc_aec_free(core);
    });
    thread_.start();
    FN_END;
}

void RtcAudioFrameLec::stop() {
    if (!thread_.isRunning()) return;
    FN_BEGIN;
    std::unique_lock<std::mutex> locker(frame_mutex_);
    thread_.stop(true);
    lec_.reset();
    initResampler_ = false;
    initResampler_farend_ = false;
    FN_END;
}