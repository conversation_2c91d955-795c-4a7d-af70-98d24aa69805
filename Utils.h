#ifndef P_Utils_h
#define P_Utils_h

#include <stdint.h>
#include <string>

#if defined(WIN32) || defined(WIN64)
#include <Windows.h>
#endif
#if defined(__linux__)
#include <sys/syscall.h>
#include <unistd.h>
#endif
namespace panocom
{
#ifndef WIN32
    long GetTickCount();
#endif
    int findNALU(uint8_t* buf, int size, int* nal_start, int* nal_end, int* sc_len);

    bool isIDRNALU(uint8_t *buf, int size, int fmt);

    bool setThreadAffinity(int i);

    std::string hexStr(uint8_t* buf, int size);

    // frames: period_size
    void stereo_to_mono(int16_t *stereo_buffer, int16_t *mono_buffer, size_t frames);

    void mono_to_stereo(int16_t *mono_buffer, int16_t *stereo_buffer, size_t frames);

    void mono_to_stereo(int16_t *buffer, size_t frames);   

    uint64_t simple_resample_s16(const int16_t *input, int16_t *output, int inSampleRate, int outSampleRate, uint64_t inputSize);
#if defined(__linux__)
    pid_t getThreadLWP();
#endif
    void setThreadName(const char *name);   // zltoolkit

    // StartsWith()
    //
    // Returns whether a given string `text` begins with `prefix`.
    bool StartsWith(const std::string& text, const std::string& prefix);

    // 计算RMS值，返回RMS值。
    double caculateRMS(int16_t *buffer, size_t frames);

    // 将RMS值转换为分贝值，返回分贝值。
    double rms_to_db(double rms);

    // 量化为0-100的音量值，返回音量值。
    int quantize_volume(double rms);

    int GetPcmLevel(const uint8_t* data, int len, int sampleSize, int channelCount);

    // 计算抖动值，返回抖动值。
    float caculateJitter(uint32_t last_ts, uint32_t ts, uint32_t last_rtp_time, uint32_t rtp_time, uint32_t clock_rate, float previous_jitter);
    float caculateJitter(uint32_t last_ts, uint32_t ts, uint32_t rtp_ts_inc, uint32_t clock_rate, float previous_jitter);

    void adjust_gain(int16_t* out_data, size_t out_len, int16_t* in_data, size_t in_len, int db);
}

#endif