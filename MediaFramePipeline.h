#ifndef P_MediaFramePipeline_h
#define P_MediaFramePipeline_h

#include "Frame.h"
#include "FrameManager.h"
#include "RtpEngine.h"
#include "file/FileFramePipeline.h"

#ifdef USE_WEBRTC
#include "ip/UdpFramePipeline.h"
#endif

#ifdef ENABLE_VIDEO
#include "video/VideoFrameCapturer.h"
#include "video/VideoFrameRender.h"
#include "video/VideoFrameProcesser.h"
#include "video/VideoFrameDecoder.h"
#include "video/VideoFrameEncoder.h"
#include "video/VideoFrameMixer.h"
#endif

#include "audio/AudioFrameCapturer.h"
#include "audio/AudioFrameRender.h"
#include "audio/AudioFrameProcesser.h"
#include "audio/AudioFrameDecoder.h"
#include "audio/AudioFrameEncoder.h"
#include "audio/AudioFrameMixer.h"

#endif

