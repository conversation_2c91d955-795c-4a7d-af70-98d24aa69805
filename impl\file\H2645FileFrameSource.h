#ifdef ENABLE_VIDEO
#ifndef P_H2645FileFrameSource_h
#define P_H2645FileFrameSource_h

#include "file/FileFramePipeline.h"
#include <hv/EventLoopThread.h>

namespace panocom
{
    class H2645FileFrameSource : public FileFramePipeline
    {
    public:
        H2645FileFrameSource(const std::string& jsonParams);
        ~H2645FileFrameSource() override;
        void start() override;

        void stop() override;
    private:
        void readFile(const std::string& path);

    private:
        hv::EventLoopThread thread_;
        std::vector<uint8_t> buffer_;
        std::vector<std::shared_ptr<Frame>> frames_;
        int index_;
        int size_;
        int fps_;
        std::string path_;
    };
}

#endif
#endif