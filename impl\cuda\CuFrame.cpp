#include "CuFrame.h"
#include "CuCommon.h"
#include <json.hpp>
#include <ljcore/jlog.h>

using namespace panocom;

bool CuNV12Frame::createFrameBuffer(int width, int height, int hStride, int vStride)
{
    assignFrameArgs(width, height, hStride, vStride);
    int bufferSize = hStride_ * vStride_ * 3 / 2;
    cuCtxPushCurrent(*CuCommon::instance().getCudaContext(cudaId_, gpuIndex_));
    CUresult ret = cuMemAlloc((CUdeviceptr *)&frameBuffer_, bufferSize);
    cuCtxPopCurrent(NULL);
    if (ret != CUDA_SUCCESS)
    {
        jerror("cuMemAlloc(%d) %d x %d fail ret %d", bufferSize, width, height, ret);
        return false;
    }
    // size_t inputYPlanePitch = 0;
    // CUresult ret = cuMemAllocPitch((CUdeviceptr *)&frameBuffer_,
    //         &inputYPlanePitch,
    //         width_,
    //         height_, 16);
    // if (ret != CUDA_SUCCESS)
    // {
    //     jerror("cuMemAllocPitch fail ret %d", ret);
    // }
    else
    {
        //jinfo("createFrameBuffer %d %d %d", inputYPlanePitch, width_, height_);
        //hStride_ = inputYPlanePitch;
        data_[0] = frameBuffer_;
        data_[1] = frameBuffer_ + hStride_ * vStride_;
        frameBufferSize_ = hStride_ * vStride_ * 3 / 2;
        frameSize_ = frameBufferSize_;
        innerBuffer_ = true;
    }
    return true;
}

void CuNV12Frame::destroyFrameBuffer()
{
    if (innerBuffer_ && frameBuffer_)
    {
        cuCtxPushCurrent(*CuCommon::instance().getCudaContext(cudaId_, gpuIndex_));
        cuMemFree((CUdeviceptr)frameBuffer_);
        cuCtxPopCurrent(NULL);
        frameBuffer_ = nullptr;
    }
}

CuDecodedNV12Frame::CuDecodedNV12Frame(int cudaId, int gpuIndex, const CUVIDPROCPARAMS& videoProcessingParameters, std::shared_ptr<CUvideodecoder> decoder, int index)
: CuFrame(cudaId, gpuIndex, FRAME_FORMAT_CU_DECODED_NV12), videoProcessingParameters_(videoProcessingParameters), decoder_(decoder), index_(index)
{
    frameBuffer_ = nullptr;
    //jinfo("CuDecodedNV12Frame %d", index);
}

bool CuDecodedNV12Frame::createFrameBuffer(int width, int height, int hStride, int vStride)
{
    assignFrameArgs(width, height, hStride, vStride);
    int bufferSize = hStride_ * vStride_ * 3 / 2;
    //jinfo("CuDecodedNV12Frame::createFrameBuffer %p", this);
    CUdeviceptr dpSrcFrame = 0;
    unsigned int nSrcPitch = 0;
    cuCtxPushCurrent(*CuCommon::instance().getCudaContext(cudaId_, gpuIndex_));
    CUresult ret = cuvidMapVideoFrame(*decoder_, index_, &dpSrcFrame, &nSrcPitch, &videoProcessingParameters_);
    cuCtxPopCurrent(NULL);
    if (ret != CUDA_SUCCESS)
    {
        jerror("cuvidMapVideoFrame([%d]-[%d])(%d) %d", cudaId_, gpuIndex_, index_, ret);
        return false;
    }
    CUVIDGETDECODESTATUS DecodeStatus;
    memset(&DecodeStatus, 0, sizeof(DecodeStatus));
    CUresult result = cuvidGetDecodeStatus(*decoder_, index_, &DecodeStatus);
    //jinfo("decoder nSrcPitch %d", nSrcPitch);
    linesize_[0] = nSrcPitch;
    //linesize_[1] = height_;
    hStride_ = linesize_[0];
    //vStride_ = linesize_[1];
    frameBuffer_ = (uint8_t*)dpSrcFrame;
    frameBufferSize_ = hStride_* vStride_ * 3 / 2;
    frameSize_ = frameBufferSize_;
    data_[0] = frameBuffer_;
    data_[1] = frameBuffer_ + hStride_ * vStride_;
    return true;
}

void CuDecodedNV12Frame::destroyFrameBuffer()
{
    //jinfo("CuDecodedNV12Frame::destroyFrameBuffer([%d]-[%d]) %p", cudaId_, gpuIndex_, this);
    if (frameBuffer_ && decoder_)
    {
        cuCtxPushCurrent(*CuCommon::instance().getCudaContext(cudaId_, gpuIndex_));
        CUresult ret = cuvidUnmapVideoFrame(*decoder_, (CUdeviceptr)frameBuffer_);
        cuCtxPopCurrent(NULL);
        if (ret != CUDA_SUCCESS)
        {
            jerror("cuvidUnmapVideoFrame([%d]-[%d]) %d", cudaId_, gpuIndex_, ret);
        }
        frameBuffer_ = nullptr;
        frameBufferSize_ = 0;
        frameSize_ = 0;
    }
}

CuEncoderInputNV12Frame::CuEncoderInputNV12Frame(int cudaId, int gpuIndex, const std::shared_ptr<Frame>& cuNV12Frame, void *encoder, NV_ENCODE_API_FUNCTION_LIST nvenc)
: CuFrame(cudaId, gpuIndex, FRAME_FORMAT_CU_ENCODE_NV12), cuNV12Frame_(cuNV12Frame), encoder_(encoder), nvenc_(nvenc)
{
}

bool CuEncoderInputNV12Frame::createFrameBuffer(int width, int height, int hStride, int vStride)
{
    Frame * f = (Frame*)cuNV12Frame_.get();
    NV_ENC_REGISTER_RESOURCE registerResource = { NV_ENC_REGISTER_RESOURCE_VER };
    registerResource.resourceType = NV_ENC_INPUT_RESOURCE_TYPE_CUDADEVICEPTR;
    registerResource.resourceToRegister = f->getFrameBuffer();
    registerResource.width = f->width();
    registerResource.height = f->height();
    registerResource.pitch = f->linesize(0);
    registerResource.bufferFormat = NV_ENC_BUFFER_FORMAT_NV12;
    registerResource.bufferUsage = NV_ENC_INPUT_IMAGE;
    registerResource.pInputFencePoint = NULL;
    registerResource.pOutputFencePoint = NULL;
    cuCtxPushCurrent(*CuCommon::instance().getCudaContext(cudaId_, gpuIndex_));
    NVENCSTATUS ret = nvenc_.nvEncRegisterResource(encoder_, &registerResource);
    if (ret != NV_ENC_SUCCESS)
    {
        frameBuffer_ = nullptr;
        frameBufferSize_ = 0;
        frameSize_ = 0;
        jerror("nvEncRegisterResource([%d]-[%d]) %d", cudaId_, gpuIndex_, ret);
        cuCtxPopCurrent(NULL);
        return false;
    }
    else
    {
        resource_ = registerResource.registeredResource;
        NV_ENC_MAP_INPUT_RESOURCE mapInputResource = { NV_ENC_MAP_INPUT_RESOURCE_VER };
        mapInputResource.registeredResource = registerResource.registeredResource;
        ret = nvenc_.nvEncMapInputResource(encoder_, &mapInputResource);
        if (ret != NV_ENC_SUCCESS)
        {
            frameBuffer_ = nullptr;
            frameBufferSize_ = 0;
            frameSize_ = 0;
            jerror("nvEncMapInputResource %d", ret);
        }
        else
        {
            frameBuffer_ = (uint8_t*)mapInputResource.mappedResource;
            frameBufferSize_ = f->linesize(0) * f->linesize(1) * 3 / 2;
            frameSize_ = frameBufferSize_;
        }
    }
    cuCtxPopCurrent(NULL);
    return true;
}

void CuEncoderInputNV12Frame::destroyFrameBuffer()
{
    if (frameBuffer_ && resource_)
    {
        cuCtxPushCurrent(*CuCommon::instance().getCudaContext(cudaId_, gpuIndex_));
        NVENCSTATUS ret = nvenc_.nvEncUnmapInputResource(encoder_, frameBuffer_);
        
        if (ret != NV_ENC_SUCCESS)
        {
            jerror("nvEncUnmapInputResource %d", ret);
        }
        else
        {
            frameBuffer_ = nullptr;
            ret = nvenc_.nvEncUnregisterResource(encoder_, resource_);
            if (ret != NV_ENC_SUCCESS)
            {
                jerror("nvEncUnmapInputResource %d", ret);
            }
            else
            {
                resource_ = nullptr;
            }
        }
        cuCtxPopCurrent(NULL);
    }
    frameBuffer_ = nullptr;
    frameBufferSize_ = 0;
    frameSize_ = 0;
}

CuWrapNV12Frame::CuWrapNV12Frame(uint32_t seq, cudaIpcMemHandle_t t) : Frame(FRAME_FORMAT_CU_WRAP_NV12), seq_(seq), t_(t)
{

}

bool CuWrapNV12Frame::createFrameBuffer(uint8_t* frameBuffer, int width, int height, int hStride, int vStride)
{
    assignFrameArgs(width, height, hStride, vStride);
    data_[0] = frameBuffer_;
    data_[1] = frameBuffer_ + hStride_ * vStride_;
    frameBuffer_ = frameBuffer;
    frameBufferSize_ = hStride_* vStride_ * 3 / 2;
    frameSize_ = frameBufferSize_;
    innerBuffer_ = false;
    return true;
}

void CuWrapNV12Frame::destroyFrameBuffer()
{
    frameBuffer_ = nullptr;
    frameBufferSize_ = 0;
    frameSize_ = 0;
}