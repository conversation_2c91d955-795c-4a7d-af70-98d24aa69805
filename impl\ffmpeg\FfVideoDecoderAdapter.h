// Created by li-weibing on 2025/4/23.
#ifndef P_FfVideoDecoderAdapter_h
#define P_FfVideoDecoderAdapter_h

#include <hv/EventLoopThread.h>
#include "VideoFrameDecoder.h"

extern "C" {
#include <libavutil/opt.h>
#include <libavutil/imgutils.h>
#include <libavcodec/avcodec.h>
#include <libswscale/swscale.h>
}

namespace panocom
{
    class FfVideoDecoderAdapter : public VideoFrameDecoder
    {
    public:
        FfVideoDecoderAdapter(const std::string& jsonParams);
        virtual ~FfVideoDecoderAdapter();

        void onFeedbackFrame(const std::shared_ptr<Frame> &f) override;

        FramePipeline::Ptr addAudioDestination(const FramePipeline::Ptr& dst) override;
        FramePipeline::Ptr removeAudioDestination(const FramePipeline::Ptr& dst) override;

        FramePipeline::Ptr addVideoDestination(const FramePipeline::Ptr& dst) override;
        FramePipeline::Ptr removeVideoDestination(const FramePipeline::Ptr& dst) override;

        FramePipeline::Ptr addDataDestination(const FramePipeline::Ptr& dst) override;
        FramePipeline::Ptr removeDataDestination(const FramePipeline::Ptr& dst) override;

        std::list<FramePipeline::WPtr> getAudioDestinations() override;
        std::list<FramePipeline::WPtr> getVideoDestinations() override;
        std::list<FramePipeline::WPtr> getDataDestinations() override;

        bool isAudioDestination(const FramePipeline::Ptr& dst) override;
        bool isVideoDestination(const FramePipeline::Ptr& dst) override;
        bool isDataDestination(const FramePipeline::Ptr& dst) override;

        void clearPipeline();
        void stopPipeline();

        void setGroupId(int groupId) override;
        int getGroupId() override;

        void printOutputStatus(const std::string& name) override;

        void start() override;
        void pause() override;
        void stop() override;
        void reset() {}

        bool isRunning() { return false; }


        WorkState workState() override;

        void onFrameTransfer(const std::shared_ptr<Frame> &f) override;

        void onVideoSourceChanged() {}

        void checkFrameTimeout() override;

        void setFrameNotify(const FrameNotifyCallback& cb)
        {
            frameNotifyCB_ = cb;
        }

        void setPreOnFrameListener(const FrameListener& frameListener, void* param)
        {
            preOnFrameListener_ = frameListener;
            preOnFrameParam_ = param;
        }
        void setAfterOnFrameListener(const FrameListener& frameListener, void* param)
        {
            afterOnFrameListener_ = frameListener;
            afterOnFrameParam_ = param;
        }

        void setPreDeliverFrameListener(const FrameListener& frameListener, void* param)
        {
            preDeliverFrameListener_ = frameListener;
            preDeliverFrameParam_ = param;
        }
        void setAfterDeliverFrameListener(const FrameListener& frameListener, void* param)
        {
            afterDeliverFrameListener_ = frameListener;
            afterDeliverFrameParam_ = param;
        }

        int updateParam(const std::string& jsonParam) { return 0; };

        void addAudioSource(const FramePipeline::Ptr& source);
        void removeAudioSource(const FramePipeline::Ptr& source);

        void addVideoSource(const FramePipeline::Ptr& source);
        void removeVideoSource(const FramePipeline::Ptr& source);

        void addDataSource(const FramePipeline::Ptr& source);
        void removeDataSource(const FramePipeline::Ptr& source);

        std::list<FramePipeline::WPtr> getAudioSources();
        std::list<FramePipeline::WPtr> getVideoSources();
        std::list<FramePipeline::WPtr> getDataSources();

        void printInputStatus(const std::string& name);
        void printDestinationStatus(const std::string& name);
        void resetConnectState(bool state);
        bool getConnectState() { return connected_; }
    protected:

    private:
        FramePipeline::Ptr decoder_;

    };
} // namespace panocom


#endif