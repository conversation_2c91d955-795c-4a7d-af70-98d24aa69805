#ifndef P_CuFrame_h
#define P_CuFrame_h

#include "Frame.h"
#include <nvcuvid.h>
#include <nvEncodeAPI.h>
#include <cuda_runtime.h>

namespace panocom
{
    class CuFrame : public Frame
    {
    public:
        CuFrame(int cudaId, int gpuIndex, FrameFormat fmt): Frame(fmt) { cudaId_ = cudaId; gpuIndex_ = gpuIndex; }
        // ~CuFrame() override = default;
        virtual ~CuFrame() = default;
    protected:
        int cudaId_ = 0;
        int gpuIndex_ = 0;
    };

    class CuNV12Frame : public CuFrame
    {
    public:
        CuNV12Frame(int cudaId, int gpuIndex): CuFrame(cudaId, gpuIndex, FRAME_FORMAT_CU_FRAME) {}
        ~CuNV12Frame() override { destroyFrameBuffer(); }

        bool createFrameBuffer(int width, int height, int hStride, int vStride) override;
        void destroyFrameBuffer() override;
    };

    class CuDecodedNV12Frame : public CuFrame
    {
    public:
        CuDecodedNV12Frame(int cudaId, int gpuIndex, const CUVIDPROCPARAMS& videoProcessingParameters, std::shared_ptr<CUvideodecoder> decoder, int index);
        ~CuDecodedNV12Frame() { destroyFrameBuffer(); }

        bool createFrameBuffer(int width, int height, int hStride, int vStride) override;
        void destroyFrameBuffer() override;

    private:
        CUVIDPROCPARAMS videoProcessingParameters_;
        std::shared_ptr<CUvideodecoder> decoder_;
        int index_;
    };

    class CuEncoderInputNV12Frame : public CuFrame
    {
    public:
        CuEncoderInputNV12Frame(int cudaId, int gpuIndex, const std::shared_ptr<Frame>& cuNV12Frame, void *encoder, NV_ENCODE_API_FUNCTION_LIST nvenc);
        ~CuEncoderInputNV12Frame() { destroyFrameBuffer(); }

        bool createFrameBuffer(int width, int height, int hStride, int vStride) override;
        void destroyFrameBuffer() override;
    private:
        std::shared_ptr<Frame> cuNV12Frame_;
        void *encoder_;
        NV_ENCODE_API_FUNCTION_LIST nvenc_;
        void *resource_;
    };

    class CuIPCNV12Frame : public CuFrame
    {
    public:
        CuIPCNV12Frame(int cudaId, int gpuIndex);
        ~CuIPCNV12Frame() { destroyFrameBuffer(); }

        bool createFrameBuffer(int width, int height, int hStride, int vStride) override;
        void destroyFrameBuffer() override;
    private:
        std::shared_ptr<Frame> cuNV12Frame_;
        void *encoder_;
        NV_ENCODE_API_FUNCTION_LIST nvenc_;
        void *resource_;
    };

    class CuWrapNV12Frame : public Frame
    {
     public:
        CuWrapNV12Frame(uint32_t seq, cudaIpcMemHandle_t t);
        ~CuWrapNV12Frame() { destroyFrameBuffer(); }

        bool createFrameBuffer(uint8_t* frameBuffer, int width, int height, int hStride, int vStride) override;
        void destroyFrameBuffer() override;

        uint32_t seq() { return seq_; }
        cudaIpcMemHandle_t handle() { return t_; }
    private:
        uint32_t seq_;
        cudaIpcMemHandle_t t_;
    };
} // namespace panocom


#endif