#include "FrameBufferManager.h"
#include "FramePipeline.h"
#include <ljcore/jlog.h>
#include <json.hpp>

using namespace panocom;

FrameBufferManager::FrameBufferManager(int maxFrame, int frameSize, FrameFormat fmt, const std::string& jsonParams)
{
    FN_BEGIN;
    jinfo("FrameBufferManager() %d %d %d", maxFrame, frameSize, fmt);
    fmt_ = fmt;
    bufferSize_ = maxFrame * frameSize;
    buffer_ = new uint8_t[bufferSize_];
    for (size_t i = 0; i < maxFrame; i++)
    {
        Frame* f = Frame::CreateFramePtr(fmt, jsonParams);
        f->createFrameBuffer(buffer_ + i * frameSize, frameSize);
        frames_.push_back(f);
    }
    FN_END;
}

FrameBufferManager::~FrameBufferManager()
{
    FN_BEGIN;
    jinfo("~FrameBufferManager()");
    delete [] buffer_;
    FN_END;
}
        
std::shared_ptr<Frame> FrameBufferManager::getFrame(const std::string& jsonParams)
{
    Frame* ptr = nullptr;
    {
        std::unique_lock<std::mutex> lock(mutex_);
        if (frames_.empty())
        {
            //jinfo("getFrame() nullptr");
            return nullptr;
        }
        ptr = frames_.front();
        frames_.pop_front();
    }
    
    std::shared_ptr<Frame> f = std::shared_ptr<Frame>(ptr, [this](Frame* f){
        //jinfo("getFrame() release %p", f);
        std::unique_lock<std::mutex> lock(mutex_);
        frames_.push_back(f);
    });
    f->updateParams(jsonParams);
    //jinfo("getFrame() %p", f.get());
    
    return f;
}