#ifndef IMPL_AUDIO_G729AUDIOFRAMEENCODER_H_
#define IMPL_AUDIO_G729AUDIOFRAMEENCODER_H_
#include "AudioFrameEncoder.h"
#include "Frame.h"
#include <resampler/resampler.h>
#include <resampler/push_resampler.h>
#ifdef __cplusplus
extern "C" {
#endif
#include "g729/codecParameters.h"
#include "g729/utils.h"
#include "g729/encoder.h"
#ifdef __cplusplus
}
#endif
namespace panocom
{

class G729AudioFrameEncoder : public AudioFrameEncoder
{
public:
	G729AudioFrameEncoder(const std::string& jsonParams);
	~G729AudioFrameEncoder() override;
	void onFrame(const std::shared_ptr<Frame> &f) override;
private:
	FrameFormat fmt_;
	int samplerate_;
	bcg729EncoderChannelContextStruct *ctx_;
#ifdef WEBRTC_RESAMPLE_ANOTHER
	nswebrtc::PushResampler<int16_t> resampler_;
#else
	nswebrtc::Resampler resampler_;
#endif       
	bool initResampler_ = false;
	int source_samplerate_;
    bool first_ = true;	
};



} // namespace panocom

#endif