#include "RKAudioFrameRender.h"

#include <json.hpp>
#include <PNPcm/HDPcm.h>
// #include <libPCM/IODef.h>

#include "audiocodec/g711.h"


namespace panocom {
namespace
{
const std::string DEV_NAME =  "/dev/pcm_dev0";
const uint32_t kSampleRate = 8000;
const uint32_t kFrameDurationInMs = 20;
} // namespace

RKAudioFrameRender::RKAudioFrameRender(const std::string& jsonParams) : nChn_(0), channel_count_(1)
{
    name_ = "RKAudioFrameRender";
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    if (j.contains("channels")) {
        channel_count_ = j["channels"];
    }
    std::string dev_name(DEV_NAME);
    if (j.contains("dev_name")) {
        dev_name = j["dev_name"];
    }

    openPCM(dev_name.c_str());
    running_ = true;

    int sample_per_channel = kSampleRate * kFrameDurationInMs;
    int playout_size = sample_per_channel * channel_count_ * 2;
    

    loop_thread_ = std::make_shared<hv::EventLoopThread>();
    loop_ = loop_thread_->loop();
    loop_thread_->start();

    ClearSendBuf(nChn_);
    uint8_t writeBuf[640] = { 0 };
    loop_->setInterval(kFrameDurationInMs, [this, &writeBuf](hv::TimerID) {
        uint32_t free_len = Get_SendFreeLen(nChn_);
        uint32_t process_size = 0;
        if (free_len > 0)
        {
            std::lock_guard<std::mutex> lock(mutex_);
            uint32_t remain_size = audio_buffer_.Length();
            process_size = std::min(free_len, remain_size);
            uint16_t *p = (uint16_t*)audio_buffer_.Data();
            for (int i = 0; i < process_size; i++){
                writeBuf[i] = linear_to_alaw(p[i]);
            }
        }
        process_size = WritePcm(nChn_, writeBuf, process_size);
        audio_buffer_.Consume(process_size);
    });

    
}

RKAudioFrameRender::~RKAudioFrameRender()
{
    running_ = false;
    if (loop_thread_) {
        loop_thread_->stop();
        loop_thread_->join();
    }
    audio_buffer_.Clear();
    ClearSendBuf(nChn_);
}

void RKAudioFrameRender::onFrame(const std::shared_ptr<Frame>& frame) {
    std::lock_guard<std::mutex> lock(mutex_);
    audio_buffer_.WriteBytes((char*)frame->getFrameBuffer(), frame->getFrameBufferSize());
}
}