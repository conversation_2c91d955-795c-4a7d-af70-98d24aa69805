// created by gyj 2024-3-28
#ifndef P_Sdl2AudioFrameMixer_h
#define P_Sdl2AudioFrameMixer_h

#include "AudioFrameMixer.h"
#include <SDL2/SDL.h>
#include <set>
#include <hv/EventLoopThread.h>

namespace panocom
{
    class Sdl2AudioFrameMixer : public AudioFrameMixer
    {
    public:
        Sdl2AudioFrameMixer(const std::string& jsonParams);
        ~Sdl2AudioFrameMixer() override;

        void onFrame(const std::shared_ptr<Frame>& frame) override;
    private:
        SDL_AudioDeviceID devid_ = 0;
        std::set<int> mixIds_;
        hv::EventLoopThread thread_;
    };
}

#endif