syntax = "proto3";

package mmi;

// The greeting service definition.
service MediaManager {
  // Sends a greeting
  rpc CreateSession (CreateSessionRequest) returns (CreateSessionReply) {}

  rpc ReleaseSession (ReleaseSessionRequest) returns (ReleaseSessionReply) {}

  rpc GetLocalSDP (GetLocalSDPRequest) returns (GetLocalSDPReply) {}

  rpc SetRemoteSDP (SetRemoteSDPRequest) returns (SetRemoteSDPReply) {}
}

message CreateSessionRequest {
    string id = 1;
    string jsonParams = 2;
}

message CreateSessionReply {
    int32 ret = 1;
}

message ReleaseSessionRequest {
    string id = 1;
    string jsonParams = 2;
}

message ReleaseSessionReply {
    int32 ret = 1;
}

message GetLocalSDPRequest {
    string id = 1;
    string jsonParams = 2;
}

message GetLocalSDPReply {
    int32 ret = 1;
    string sdp = 2;
}

message SetRemoteSDPRequest {
    string id = 1;
    string sdp = 2;
    string jsonParams = 3;
}

message SetRemoteSDPReply {
    int32 ret = 1;
    string sdp = 2;
}