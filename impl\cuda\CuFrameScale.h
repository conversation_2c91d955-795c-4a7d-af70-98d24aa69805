#ifndef P_CuFrameScale_h
#define P_CuFrameScale_h

#include "VideoFrameProcesser.h"
#include <hv/EventLoopThread.h>
#include <cuda.h>

namespace panocom
{
    class CuFrameScale: public VideoFrameProcesser
    {
    public:
        CuFrameScale(const std::string& jsonParams);
        ~CuFrameScale() override;

        void onFrame(const std::shared_ptr<Frame> &frame) override;
    private:
        int width_;
        int height_;
        int hStride_;
        int vStride_;
        hv::EventLoopThread thread_;
        int cudaId_ = 0;
        int gpuIndex_ = 0;
    };
}

#endif