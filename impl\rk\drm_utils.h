#ifndef DISPLAY_DRM_UTILS_H_
#define DISPLAY_DRM_UTILS_H_

#include <stdint.h>

namespace panocom
{
#define UPALIGNTO(value, align) ((value + align - 1) & (~(align - 1)))
#define UPALIGNTO16(value) UPALIGNTO(value, 16)

struct buffer_object
{
	uint32_t width;
	uint32_t height;
	uint32_t bpp;
	uint32_t format;
	uint32_t pitch;
	uint32_t handle;
	uint32_t size;
	uint32_t fb_id;
	int32_t dst_fd = -1;
	uint8_t *vaddr = nullptr;
};
class DrmUtils
{
private:
    /* data */
public:
    DrmUtils(/* args */);
    ~DrmUtils();
	static int drm_format_to_bpp(uint32_t format);
    static int32_t modeset_create_fb2(int dev_fd, struct buffer_object *bo, bool mapped = false);
    static void modeset_destroy_fb(int dev_fd, struct buffer_object *bo);
	static void create_dumb_buffer(int dev_fd, buffer_object &bo);
	static int convert_to_dma_buf_fd(int dev_fd, uint32_t handle);
	static void* mmap_dumb_handle(int dev_fd, uint32_t handle, int size);
	static uint32_t get_fb_id(int dev_fd, const buffer_object &bo);

	static bool rga_copy(int32_t dst_fd, int32_t dst_width, int32_t dst_height, int32_t dst_format, 
                 int32_t src_fd, int32_t src_width, int32_t src_height, int32_t src_format);
	static bool rga_copy(int32_t dst_fd, int32_t dst_width, int32_t dst_height, int32_t dst_format, 
                 int32_t src_fd, int32_t src_width, int32_t src_height, int32_t hor_stride, int32_t ver_stride, int32_t src_format);
	static void set_black(struct buffer_object *bo);
};
} // namespace panocom

#endif