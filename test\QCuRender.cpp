#include "QCuRender.h"
#include "Utils.h"
#include <ljcore/jlog.h>
#include <json.hpp>
#include <unistd.h>

using namespace panocom;

QCuRender::QCuRender(QObject *window, int maxWidth, int maxHeight, int dev, int cudaIndex, int gpuIndex) : 
m_texture(QOpenGLTexture::Target2D), 
m_vbo(QOpenGLBuffer::VertexBuffer), 
m_pbo(QOpenGLBuffer::PixelUnpackBuffer), 
m_window(window),
m_maxWidth(maxWidth),
m_maxHeight(maxHeight)
{
    nlohmann::json js;
    js["dev"] = dev;
    js["cudaId"] = cudaIndex;
    js["gpuIndex"] = gpuIndex;
    m_render = OpenGLRender::CreateOpenGLRender("CUDA", js.dump());
}

QCuRender::~QCuRender()
{
    if (m_vbo.isCreated())
        m_vbo.destroy();
    
    if (m_pbo.isCreated())
        m_pbo.destroy();
    
    if (m_texture.isCreated())
        m_texture.destroy();
}

void QCuRender::initializeGL()
{
    jinfo("QCuRender::initializeGL");
    initializeOpenGLFunctions();

    initializeShader();

    GLfloat points[]{
        -1.0f, 1.0f,
        1.0f, 1.0f,
        1.0f, -1.0f,
        -1.0f, -1.0f,

        0.0f, 0.0f,
        1.0f, 0.0f,
        1.0f, 1.0f,
        0.0f, 1.0f};

    m_vbo.create();
    m_vbo.bind();
    m_vbo.allocate(points, sizeof(points));
    m_vbo.release();

    m_pbo.create();
    m_pbo.bind();
    m_pbo.allocate(nullptr, m_maxWidth * m_maxHeight * 3);
    m_pbo.setUsagePattern(QOpenGLBuffer::StreamDraw);
    m_pbo.release();

    m_render->setPBO(m_pbo.bufferId(), m_maxWidth, m_maxHeight);

    m_texture.create();
    m_texture.bind();
    m_texture.setMinificationFilter(QOpenGLTexture::Nearest);
    m_texture.setMagnificationFilter(QOpenGLTexture::Nearest);
    m_texture.setWrapMode(QOpenGLTexture::ClampToEdge);
    m_texture.setSize(m_maxWidth, m_maxHeight, 3);
    m_texture.setFormat(QOpenGLTexture::RGBFormat);
    m_texture.allocateStorage(QOpenGLTexture::BGR, QOpenGLTexture::UInt8);
    m_texture.release();
}

int QCuRender::createVideoFrameRender(int videoId, int areaId, int x, int y, int z, int w, int h, int cudaId, int gpuIdex)
{
    jinfo("createVideoFrameRender[%d] %d %d (%d, %d, %d) (%d, %d)", pthread_self(), videoId, areaId, x, y, z, w, h);
    if (area_video_ids_.count(areaId)) {
        if (area_video_ids_[areaId] == videoId) return;
        destroyVideoFrameRender(areaId);
    }
    area_video_ids_[areaId] = videoId;
    return m_render->addFrameRect(videoId, areaId, x, y, z, w, h, cudaId, gpuIdex);
}

int QCuRender::destroyVideoFrameRender(int areaId)
{
    jinfo("destroyVideoFrameRender[%d] areaId: %d ", pthread_self(), areaId);
    if (area_video_ids_.count(areaId) == 0) return -1;
    int videoId = area_video_ids_[areaId];
    area_video_ids_.erase(areaId);
    return m_render->removeFrameRect(videoId, areaId);
}    

int QCuRender::destroyVideoFrameRenderByVideoId(int videoId) {
    jinfo("destroyVideoFrameRender[%d]", pthread_self());
    for (auto it = area_video_ids_.begin(); it != area_video_ids_.end();) {
        if (it->second == videoId) {
            m_render->removeFrameRect(videoId, it->first);
            it = area_video_ids_.erase(it);
        } else {
            it++;
        }
    }
    return 0;    
}

int QCuRender::moveVideoFrameRender(int areaId, int x, int y, int z, int w, int h)
{
    jinfo("moveVideoFrameRender[%d]", pthread_self());
    if (area_video_ids_.count(areaId) == 0) return -1;
    return m_render->changeFrameRect(area_video_ids_[areaId], areaId, x, y, z, w, h);
}

void QCuRender::doRender()
{
    m_pbo.bind();
    if (m_texture.isCreated())
    {
        m_texture.bind();
        //jinfo("m_texture.setData (%d,%d) %d x %d", x, y, w, h);
        m_texture.setData(0, 0, 0, m_maxWidth, m_maxHeight, 3, QOpenGLTexture::RGB, QOpenGLTexture::UInt8, nullptr);
    }
    m_pbo.release();

    m_program.bind();
    m_vbo.bind();
    m_program.enableAttributeArray(0);
    m_program.setAttributeBuffer(0, GL_FLOAT, 0, 2, 2 * sizeof(GLfloat));

    m_program.enableAttributeArray(1);
    m_program.setAttributeBuffer(1, GL_FLOAT, 2 * 4 * sizeof(GLfloat), 2, 2 * sizeof(GLfloat));

    m_program.setUniformValue("texture", 0);

    glDrawArrays(GL_QUADS, 0, 4);

    m_vbo.release();
    m_program.release();
}

void QCuRender::display()
{
    if (m_render->prepareFrame())
    {
        doRender();
    }
}

void QCuRender::initializeShader()
{
    if (!m_program.addShaderFromSourceCode(QOpenGLShader::Vertex,
                                           "#version 330 core\n"
                                           "layout(location = 0) in vec4 position;"
                                           "layout(location = 1) in vec2 texCoord0;"
                                           "out vec2 texCoord;"
                                           "void main(void)"
                                           "{"
                                           "    gl_Position = position;"
                                           "    texCoord = texCoord0;"
                                           "}"))
        qDebug() << m_program.log();

    if (!m_program.addShaderFromSourceCode(QOpenGLShader::Fragment,
                                           "#version 330 core\n"
                                           "in vec2 texCoord;"
                                           "out vec4 FragColor;"
                                           "uniform sampler2D texture;"
                                           "void main(void)"
                                           "{"
                                           "    FragColor = texture2D(texture, texCoord);"
                                           "}"))
        qDebug() << m_program.log();

    if (!m_program.link())
        qDebug() << m_program.log();

    if (!m_program.bind())
        qDebug() << m_program.log();
}