#include "MediaPipeline.h"
#include <json.hpp>
#include <stdio.h>
#include <ljcore/jlog.h>
#include <hv/EventLoop.h>

using namespace panocom;

#define CAM_WIDTH  1920
#define CAM_HEIGHT 1088

int main()
{
    auto loop = std::make_shared<hv::EventLoop>();
    jlog_init(nullptr);
    nlohmann::json j;
    j["hardwareAcc"] = "nv";
    j["width"] = CAM_WIDTH;
    j["height"] = CAM_HEIGHT;
    auto capturer = VideoFrameCapturer::CreateVideoCapturer("V4L2VideoFrameCapturer", j.dump());
    capturer->setGroupId(1);
    j.clear();
    j["codec"] = "mjpeg_cuvid";
    auto mdecoder = VideoFrameDecoder::CreateVideoDecoder("FfVideoDecoder", j.dump());
    mdecoder->setGroupId(1);
    j.clear();
    j["width"] = CAM_WIDTH * 2;
    j["height"] = CAM_HEIGHT * 2;
    j["videoLayout"] = nlohmann::json::array();
    nlohmann::json r;

    r["id"] = 1;
    r["rect"]["x"] = 0;
    r["rect"]["y"] = 0;
    r["rect"]["w"] = CAM_WIDTH;
    r["rect"]["h"] = CAM_HEIGHT;
    j["videoLayout"].push_back(r);

    r["id"] = 1;
    r["rect"]["x"] = 0;
    r["rect"]["y"] = CAM_HEIGHT;
    r["rect"]["w"] = CAM_WIDTH;
    r["rect"]["h"] = CAM_HEIGHT;
    j["videoLayout"].push_back(r);

    r["id"] = 1;
    r["rect"]["x"] = CAM_WIDTH;
    r["rect"]["y"] = 0;
    r["rect"]["w"] = CAM_WIDTH;
    r["rect"]["h"] = CAM_HEIGHT;
    j["videoLayout"].push_back(r);

    r["id"] = 1;
    r["rect"]["x"] = CAM_WIDTH;
    r["rect"]["y"] = CAM_HEIGHT;
    r["rect"]["w"] = CAM_WIDTH;
    r["rect"]["h"] = CAM_HEIGHT;
    j["videoLayout"].push_back(r);

    auto mixer = VideoFrameMixer::CreateVideoMixer("LibyuvVideoFrameMixer", j.dump());
    mixer->setGroupId(1);

    j.clear();
    j["width"] = CAM_WIDTH;
    j["height"] = CAM_HEIGHT;
    j["videoLayout"] = nlohmann::json::array();

    r["id"] = 1;
    r["rect"]["x"] = 0;
    r["rect"]["y"] = 0;
    r["rect"]["w"] = CAM_WIDTH;
    r["rect"]["h"] = CAM_HEIGHT;
    j["videoLayout"].push_back(r);

    auto render = VideoFrameRender::CreateVideoRender("Sdl2VideoFrameRender", j.dump());

    j.clear();
    j["codec"] = "h264_nvenc";
    j["width"] = CAM_WIDTH * 2;
    j["height"] = CAM_HEIGHT * 2;
    j["kbps"] = 8000;
    auto encoder = VideoFrameEncoder::CreateVideoEncoder("FfVideoEncoder", j.dump());
    encoder->setGroupId(1);

    j.clear();
    j["payloadType"] = 96;
    j["hz"] = 90000;
    j["dst"] = nlohmann::json::array();
    nlohmann::json dst;
    dst["ip"] = "************";
    dst["port"] = 4444;
    j["dst"].push_back(dst);
    auto rtpSender = RtpEngine::CreateRTPEngine("JRtpEngine", j.dump());

    j.clear();
    j["codec"] = "h264_cuvid";
    j["width"] = CAM_WIDTH * 2;
    j["height"] = CAM_HEIGHT * 2;
    auto decoder = VideoFrameDecoder::CreateVideoDecoder("FfVideoDecoder", j.dump());
    decoder->setGroupId(1);

    j.clear();
    j["path"] = "4k.h264";
    auto filer = FileFramePipeline::CreateFileDestination("file", j.dump());

    capturer->addVideoDestination(mdecoder);
    mdecoder->addVideoDestination(mixer);
    mixer->addVideoDestination(encoder);
    encoder->addVideoDestination(rtpSender);
    encoder->addVideoDestination(filer);
    encoder->addVideoDestination(decoder);
    decoder->addVideoDestination(render);

    loop->run();
    return 0;
}