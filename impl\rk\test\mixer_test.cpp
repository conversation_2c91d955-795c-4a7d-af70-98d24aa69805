#include <fstream>
#include <future>
#include <vector>
#include <unistd.h>

#include <json.hpp>
// #include <osal/mpp_common.h>
#include <ljcore/jlog.h>


#include "Frame.h"
#include "VideoFrameDecoder.h"
#include "VideoFrameMixer.h"
#include "VideoFrameRender.h"

using namespace panocom;
#define WIDTH 1920
#define HEIGHT 1080
#define BUFFER_SIZE 4096
class TestFileSource : public FramePipeline {
private:
	/* data */
public:
	TestFileSource(const std::string& jsonParams);
	~TestFileSource();
private:
	std::future<bool> future_;
	std::ifstream input_;
	bool running_;
};

TestFileSource::TestFileSource(const std::string& jsonParams)
{
	nlohmann::json j;
	if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
	if (j.contains("path")) {
		input_.open(j["path"], std::ios::in | std::ios::binary);
		running_ = true;
	}
	int interval = 5;
	FrameFormat format = FRAME_FORMAT_H264;
	if (j.contains("codec")) {
		if (j["codec"] == "h264") format = FRAME_FORMAT_H264;
		else if (j["codec"] == "h265") {
			format = FRAME_FORMAT_H265; 
			interval = 8;
		}
	}
	future_ = std::async([this, format, interval]() -> bool {
		uint32_t size = BUFFER_SIZE;
		while (running_)
		{
			if (!input_.eof()) {
				auto frame = Frame::CreateFrame(format);
				frame->createFrameBuffer(BUFFER_SIZE);
				input_.read((char*)frame->getFrameBuffer(), size);
				deliverFrame(frame);
			} else {
				input_.clear();
				input_.seekg(0, std::ios::beg);
			}
			std::this_thread::sleep_for(std::chrono::milliseconds(interval));
		}
		return true;
	});
}

TestFileSource::~TestFileSource()
{
	running_ = false;
	auto status = future_.wait_for(std::chrono::milliseconds(200));
	if (status == std::future_status::timeout) {
		
	} else {
		future_.get();
	}
	if (input_ && input_.is_open()) {
		input_.close();
	}
}

int main() {
    jlog_init(nullptr);
    nlohmann::json j;
	nlohmann::json r;
	int count = 1;

	j.clear();
	j["card_id"] = 0;
	j["encoder_id"] = 210;
	j["y"] = 200;
	j["x"] = 200;
	j["width"] = 1280;
	j["height"] = 720;
	j["format"] = "nv12";
	j["dev"] = 1;
	j["videoLayout"] = nlohmann::json::array();

	r.clear();
	r["id"] = 1;
	r["rect"]["w"] = WIDTH / count;
	r["rect"]["h"] = HEIGHT / count;
	for (int row = 0; row < count; row++) {
		for (int line = 0; line < count; line++) {
			r["rect"]["x"] = (WIDTH / count) * line;
			r["rect"]["y"] = (HEIGHT / count) * row;
			j["videoLayout"].push_back(r);
		}
	}
	auto render = VideoFrameRender::CreateVideoRender("RKVideoRender", j.dump());

	count = 2;
	j.clear();
	j["width"] = WIDTH;
	j["height"] = HEIGHT;
	j["fps"] = 25;
	j["format"] = "nv12";
	j["videoLayout"] = nlohmann::json::array();
	
	r.clear();
	r["rect"]["w"] = WIDTH / count;
	r["rect"]["h"] = HEIGHT / count;
	for (int row = 0; row < count; row++) {
		r["id"] = (row & 1);
		for (int line = 0; line < count; line++) {
			r["rect"]["x"] = (WIDTH / count) * line;
			r["rect"]["y"] = (HEIGHT / count) * row;
			j["videoLayout"].push_back(r);
		}
	}

	auto mixer = VideoFrameMixer::CreateVideoMixer("RKVideoFrameMixer", j.dump());
	mixer->setGroupId(1);

	// j.clear();
    // j["codec"] = "H265";
    // j["width"] = WIDTH;
    // j["height"] = HEIGHT;
	// auto decoder = VideoFrameDecoder::CreateVideoDecoder("RKVideoDecoder", j.dump());
	// decoder->setGroupId(1);

	// j.clear();
	// j["path"] = "race.h265";
	// auto file_source = std::make_shared<TestFileSource>(j.dump());
	
	std::vector<std::shared_ptr<TestFileSource>> file_sources;
	std::vector<std::shared_ptr<VideoFrameDecoder>> decoders;
	std::vector<std::string> codec_types{"h264", "h265"};
	std::vector<std::string> filenames{"test.h264", "test.h265",};
	for (int i = 0; i < codec_types.size(); i++) {
		j.clear();
		j["codec"] = codec_types[i];
		j["width"] = WIDTH;
		j["height"] = HEIGHT;
		auto decoder = VideoFrameDecoder::CreateVideoDecoder("RKVideoDecoder", j.dump());
		decoder->setGroupId(i);
		decoders.emplace_back(decoder);

		j.clear();
		j["path"] = filenames[i];
		j["codec"] = codec_types[i];
		file_sources.emplace_back(std::make_shared<TestFileSource>(j.dump()));
	}
	for (int i = 0; i < decoders.size(); i++) {
		file_sources[i]->addVideoDestination(decoders[i]);
		decoders[i]->addVideoDestination(mixer);	
	}
	// file_source->addVideoDestination(decoder);
	// decoder->addVideoDestination(mixer);
	mixer->addVideoDestination(render);
	// decoder->addVideoDestination(render);

	printf("init success!!!!!!!\n");
    while (true)
    {
        usleep(1000);
    }
	return 0;
}