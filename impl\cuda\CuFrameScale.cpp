#include "CuFrameScale.h"
#include "Frame.h"
#include "CuCommon.h"
#include "NvCodecUtils.h"
#include <json.hpp>

using namespace panocom;

CuFrameScale::CuFrameScale(const std::string& jsonParams)
{
    FN_BEGIN;
    name_ = "CuFrameScale";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    width_ = 800;
    if (j.contains("width"))
    {
        width_ = j["width"];
    }
    height_ = 640;
    if (j.contains("height"))
    {
        height_ = j["height"];
    }
    hStride_ = width_;
    if (j.contains("hStride"))
    {
        hStride_ = j["hStride"];
    }
    vStride_ = height_;
    if (j.contains("vStride"))
    {
        vStride_ = j["vStride"];
    }
    cudaId_ = 0;
    if (j.contains("cudaId"))
    {
        cudaId_ = j["cudaId"];
    }
    gpuIndex_ = 0;
    if (j.contains("gpuIndex"))
    {
        gpuIndex_ = j["gpuIndex"];
    }
    thread_.start();
    FN_END;
}

CuFrameScale::~CuFrameScale()
{
    FN_BEGIN;
    FN_END;
}

void CuFrameScale::onFrame(const std::shared_ptr<Frame> &frame)
{
    if (!thread_.isRunning()) return;
    thread_.loop()->runInLoop([frame, this](){
        std::shared_ptr<Frame> f = Frame::CreateFrame(FRAME_FORMAT_CU_FRAME);
        f->createFrameBuffer(width_, height_, hStride_, vStride_);
        cuCtxPushCurrent(*CuCommon::instance().getCudaContext(cudaId_, gpuIndex_));
        ResizeNv12(f->data(0), f->linesize(0), f->width(), f->height(), frame->data(0), frame->linesize(0), frame->width(), frame->height());
        cuCtxPopCurrent(NULL);
        deliverFrame(f);
    });
}