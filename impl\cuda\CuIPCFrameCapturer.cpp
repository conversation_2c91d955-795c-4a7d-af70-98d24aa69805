#include "CuIPCFrameCapturer.h"
#include "CuFrame.h"
#include <json.hpp>
#include <cuda_runtime.h>
#include <ljcore/jlog.h>

using namespace panocom;

CuIPCFrameCapturer::CuIPCFrameCapturer(const std::string& jsonParams): seq_(1), frame_(nullptr)
{
    FN_BEGIN;
    name_ = "CuIPCFrameCapturer";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    
    if (j.contains("dev"))
    {
        dev_ = j["dev"];
    }
    frameCount_ = 8;
    if (j.contains("frameCount"))
    {
        frameCount_ = j["frameCount"];
    }
    cudaId_ = 0;
    if (j.contains("cudaId"))
    {
        cudaId_ = j["cudaId"];
    }
    gpuIndex_ = 0;
    if (j.contains("gpuIndex"))
    {
        gpuIndex_ = j["gpuIndex"];
    }
    jinfo("create CuIPCFrameCapturer %d use gpu %d", frameCount_, gpuIndex_);
    start();
    FN_END;
}

CuIPCFrameCapturer::~CuIPCFrameCapturer()
{
    FN_BEGIN;
    stop();
    FN_END;
}

void CuIPCFrameCapturer::start()
{
    if (thread_.isRunning()) return;
    FN_BEGIN;
    std::string shmName = "CuIPC";
    shmName += std::to_string(dev_);
    thread_.loop()->setInterval(40, [shmName, this](hv::TimerID id){
        if (frame_)
        {
            if (frame_->sync)
            {
                frame_->sync = 0;
                syncSeq();
            }
            freeFrames();
            freeShmFrames();
            findShmFrame();
        }
        else
        {
            int ret = shm_.sharedMemoryOpen(shmName.c_str(), sizeof(FrameShm) * frameCount_);
            if (ret != 0) {
                jerror("sharedMemoryOpen %s failed: %s", shmName.c_str(), strerror(ret));
            } else {
                jinfo("sharedMemoryOpen %s succcess.", shmName.c_str());
            }
            frame_ = (FrameShm*)shm_.data();
            if (frame_) 
            {
                //memset(frame_, 0, sizeof(FrameShm) * frameCount_);
                syncSeq();
            }
        }
    });
    thread_.start();
    FN_END;
}

void CuIPCFrameCapturer::stop()
{
    if (!thread_.isRunning()) return;
    FN_BEGIN;
    thread_.stop(true);
    freeFrames();
    shm_.sharedMemoryClose();
    FN_END;
}

void CuIPCFrameCapturer::syncSeq()
{
    // sync seq
    uint32_t newseq = 0xffffffff;
    bool foundNewSeq = false;
    for (size_t i = 0; i < frameCount_ / 2; i++)
    {
        if (frame_[i].status)
        {
            if (frame_[i].seq == seq_)
            {
                newseq = seq_;
                foundNewSeq = true;
                break;
            }
            else
            {
                if (frame_[i].seq < newseq)
                {
                    if (!isFrameWaitFree(frame_[i].seq))
                    {
                        newseq = frame_[i].seq;
                        foundNewSeq = true;
                    }
                }
            }
        }
    }
    if (foundNewSeq)
    {
        seq_ = newseq;
    }
    else
    {
        seq_ = 1;
    }
    jinfo("syncSeq(%d) %d", dev_, seq_);
}

void CuIPCFrameCapturer::freeFrames()
{
    Frame* f = getWaitFreeFrame();
    while (f)
    {
        uint32_t seq = ((CuWrapNV12Frame*)f)->seq();
        cudaIpcMemHandle_t t = ((CuWrapNV12Frame*)f)->handle();
        for (size_t i = frameCount_ / 2; i < frameCount_; i++)
        {
            if (frame_[i].status == 0)
            {
                //jinfo("free %d", seq);
                frame_[i].seq = seq;
                frame_[i].memHandle = t;
                frame_[i].status = 1;
                break;
            }
        }
        cuCtxPushCurrent(*CuCommon::instance().getCudaContext(cudaId_, gpuIndex_));
        cudaIpcCloseMemHandle(f->getFrameBuffer());
        cuCtxPopCurrent(NULL);
        delete f;
        f = getWaitFreeFrame();
    }
}

void CuIPCFrameCapturer::freeShmFrames()
{
    // 根据下半部分的信息移除上半部不存在的信息(移除下半部分中上半部分信息不存在的信息)
    for (size_t i = frameCount_ / 2; i < frameCount_; i++)
    {
        if (frame_[i].status == 1)
        {
            uint32_t seq = frame_[i].seq;
            bool found = false;
            for (size_t j = 0; j < frameCount_ / 2; j++)
            {
                if (frame_[j].status && frame_[j].seq == seq)
                {
                    found = true;
                }
            }
            if (!found)
            {
                frame_[i].status = 0;
            }
        }
    }
}

void CuIPCFrameCapturer::findShmFrame()
{
    for (size_t i = 0; i < frameCount_ / 2; i++)
    {
        if (frame_[i].status && frame_[i].seq == seq_)
        {
            void* ptr;
            CUresult ctxRet = cuCtxPushCurrent(*CuCommon::instance().getCudaContext(cudaId_, gpuIndex_));
            //jinfo("findShmFrame(%d) %d %d", dev_, seq_, ctxRet);
            cudaError_t ret = cudaIpcOpenMemHandle(&ptr, frame_[i].memHandle, cudaIpcMemLazyEnablePeerAccess);
            cuCtxPopCurrent(NULL);
            if (ret == cudaSuccess)
            {
                std::shared_ptr<Frame> frame = std::shared_ptr<Frame>(new CuWrapNV12Frame(frame_[i].seq, frame_[i].memHandle), [this](Frame* f){
                    std::unique_lock<std::mutex> lock(mutex_);
                    waitFree_.insert(f);
                });
                //jinfo("CuIPCFrameCapturer find frame %d x %d %d x %d", frame_[i].width, frame_[i].height, frame_[i].hStride, frame_[i].vStride);
                frame->createFrameBuffer((uint8_t*)ptr, frame_[i].width, frame_[i].height, frame_[i].hStride, frame_[i].vStride);
                frame->setGroupId(getGroupId());
                deliverFrame(frame);
                printOutputStatus(name_ + std::to_string(dev_));
            }
            else
            {
                jinfo("[%p]cudaIpcOpenMemHandle %d faild ret = %d", this, frame_[i].memHandle, ret);
                for (size_t j = frameCount_ / 2; j < frameCount_; j++)
                {
                    if (frame_[j].status == 0)
                    {
                        // jinfo("free %d", seq);
                        frame_[j].seq = frame_[i].seq;
                        frame_[j].memHandle = frame_[i].memHandle;
                        frame_[j].status = 1;
                        break;
                    }
                }
            }
            seq_++;
        }
    }
}

Frame* CuIPCFrameCapturer::getWaitFreeFrame()
{
    std::unique_lock<std::mutex> lock(mutex_);
    if (waitFree_.empty())
        return nullptr;
    Frame* ret = *waitFree_.begin();
    waitFree_.erase(ret);
    return ret;
}

bool CuIPCFrameCapturer::isFrameWaitFree(uint32_t seq)
{
    bool ret = false;
    for (size_t i = frameCount_ / 2; i < frameCount_; i++)
    {
        if (frame_[i].status && frame_[i].seq == seq)
        {
            ret = true;
            break;
        }
    }
    return ret;
}