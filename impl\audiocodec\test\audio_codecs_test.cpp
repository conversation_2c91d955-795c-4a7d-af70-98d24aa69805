//
// Created by li-weibing on 2025/5/24.
//
#include <fstream>
#include <future>
#include <unistd.h>

#include <ljcore/jlog.h>
#include <json.hpp>

#include "AudioFrameDecoder.h"
#include "AudioFrameEncoder.h"

using namespace panocom;


namespace {
    static size_t kSampleRateHz = 16000;
//    static size_t kSampleRateHz = 8000;
}

class TestFileSource : public FramePipeline {
private:
    /* data */
public:
    TestFileSource(const std::string &jsonParams);
    ~TestFileSource();

private:
    std::future<bool> future_;
    std::ifstream input_;
    bool running_;
    int num_10ms_frames_per_packet_;
};

TestFileSource::TestFileSource(const std::string &jsonParams)
        : num_10ms_frames_per_packet_(1) {
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    if (j.contains("path")) {
        input_.open(j["path"], std::ios::in | std::ios::binary);
        running_ = true;
    }
    FrameFormat format = kSampleRateHz == 8000?FRAME_FORMAT_PCM_8000_1:FRAME_FORMAT_PCM_16000_1;
    future_ = std::async([this, format]() -> bool {

        uint32_t size = kSampleRateHz / 100 * num_10ms_frames_per_packet_ * 2;
        // uint32_t size = 2048;
        int count = 3000;
        while (running_ && count>0) {
            count --;
            if (!input_.eof()) {
//                printf("size = %d\n", size);
                auto frame = Frame::CreateFrame(format);
                frame->createFrameBuffer(size);
                input_.read((char *)frame->getFrameBuffer(), size);
                deliverFrame(frame);
                usleep(num_10ms_frames_per_packet_* 10 *1000);
            }
            // else {
            // 	input_.clear();
            // 	input_.seekg(0, std::ios::beg);
            // }
        }

        printf("read end size = %d\n", size);
        return true;
    });
}

TestFileSource::~TestFileSource() {
    running_ = false;
    auto status = future_.wait_for(std::chrono::milliseconds(200));
    if (status == std::future_status::timeout) {

    } else {
        future_.get();
    }
    if (input_ && input_.is_open()) {
        input_.close();
    }
}



class TestFileDestination : public FramePipeline {
private:
    /* data */
public:
    TestFileDestination(const std::string &jsonParams);
    ~TestFileDestination();
    virtual void onFrame(const std::shared_ptr<Frame> &f) override;

private:
    std::ofstream output_;
};

TestFileDestination::TestFileDestination(const std::string &jsonParams) {
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    if (j.contains("path")) {
        output_.open(j["path"], std::ios::out | std::ios::binary);
    }
}

void TestFileDestination::onFrame(const std::shared_ptr<Frame> &f) {
//    printf("TestFileDestination::onFrame getFrameBufferSize:%d\n", f->getFrameBufferSize());
    output_.write((char *)f->getFrameBuffer(), f->getFrameBufferSize());
}

TestFileDestination::~TestFileDestination() {
    if (output_ && output_.is_open()) {
        output_.close();
    }
}

/***
acc : FRAME_FORMAT_AAC_48000_2  FRAME_FORMAT_AAC_48000_1
opus
g711(g711、PCMA、PCMU)  8000 16000
g722、g7221、g729、aac(acc、MP4A-LATM)
 ./audio_codec_test codec  channel samplerate bitrate
 codec
 **/

int main(int argc, char **argv) {
    jlog_init(nullptr);
    jinfo("audio_codec_test main() v1  argc:%d",argc);
    // 打印命令行参数以确保正确传递
    for (int i = 0; i < argc; ++i) {
        jinfo("audio_codec_test main() argv i:%d   %s",i, argv[i] );
        printf("audio_codec_test main() argv i:%d   %s\n",i, argv[i]);
    }

    std::string dir = "/home/<USER>/videos/audio/";

    char* codecName = "aac"; // acc、opus、g711(g711、PCMA、PCMU)、g722、g7221、g729、aac(acc、MP4A-LATM)
    if (argc >= 2) {
        codecName = argv[1];
    }


    if (argc >= 3) {
        kSampleRateHz = atoll(argv[2]);
    }



    jinfo("audio_codec_test main() codecName:%s",codecName );
    jinfo("audio_codec_test main() codkSampleRateHzecName:%lld",kSampleRateHz );

    nlohmann::json j;
    j.clear();
    j["codec"] = codecName;
    auto encoder = AudioFrameEncoder::CreateAudioEncoder("native", j.dump());
    j.clear();
    j["codec"] = codecName;
    auto decoder = AudioFrameDecoder::CreateAudioDecoder("native", j.dump());

    j.clear();
    std::string path = std::string(dir) + "audio_short16_16000_1.pcm";
    if(kSampleRateHz == 8000){
        path = std::string(dir) + "audio_short16_8000_1.pcm";
    }
    j["path"] = path;
    auto file_source = std::make_shared<TestFileSource>(j.dump());
    printf("audio_codec_test main() file_source:%s\n",path.c_str() );
    jinfo("audio_codec_test main() file_source:%s",path.c_str() );



    j.clear();
    std::string en_output_path = std::string(dir) +  "src" + std::to_string(kSampleRateHz) +"_" + std::string(codecName) + "_output." +   std::string(codecName)+ ".rar";
    j["path"] = en_output_path;
    auto en_file_destination = std::make_shared<TestFileDestination>(j.dump());
    jinfo("audio_codec_test main() en_output_path:%s",en_output_path.c_str() );


    j.clear();
    std::string de_output_path = std::string(dir) +  "src" + std::to_string(kSampleRateHz) +"_"  +  std::string(codecName) + "_output.pcm"+ ".rar";
    j["path"] = de_output_path;
    auto de_file_destination = std::make_shared<TestFileDestination>(j.dump());
    jinfo("audio_codec_test main() de_output_path:%s",de_output_path.c_str() );


    file_source->addAudioDestination(encoder);
    encoder->addAudioDestination(decoder);
    encoder->addAudioDestination(en_file_destination);
    decoder->addAudioDestination(de_file_destination);





    jinfo("audio_codec_test main() start");
    printf("audio_codec_test main() start\n" );
    while (true) {
        usleep(10000);
    }
    jinfo("audio_codec_test main() finish  argc:%d",argc);
    printf("audio_codec_test main() finish\n" );
    return 0;
}
