#include "AudioFrameEncoder.h"
#include "audiocodec/OpusAudioFrameEncoder.h"
#include "audiocodec/G711AudioFrameEncoder.h"
#include "audiocodec/G722AudioFrameEncoder.h"
#include "audiocodec/G7221AudioFrameEncoder.h"
#include "audiocodec/G729AudioFrameEncoder.h"
#include "audiocodec/AACAudioFrameEncoder.h"
#include "audiocodec/L16AudioFrameEncoder.h"

#include <algorithm>
#include <json.hpp>

using namespace panocom;

std::list<std::string> AudioFrameEncoder::encoderTypes_;
std::list<CodecInst> AudioFrameEncoder::codecs_;

bool AudioFrameEncoder::isRegistered = false;

void AudioFrameEncoder::Regist()
{
    if (!isRegistered)
    {
        encoderTypes_.push_back("native");
        getCodecInst("opus", codecs_);
        getCodecInst("PCMA", codecs_);
        getCodecInst("PCMU", codecs_);
		getCodecInst("G722", codecs_);
        getCodecInst("G7221", codecs_);
		getCodecInst("G729", codecs_);
		getCodecInst("AAC", codecs_);
        getCodecInst("MP4A-LATM", codecs_);
        getCodecInst("L16", codecs_);
        isRegistered = true;
    }
}

std::list<std::string> &AudioFrameEncoder::GetSupportEncoderTypes()
{
    Regist();
    return encoderTypes_;
}

bool AudioFrameEncoder::IsSupportEncoderType(const std::string &encoderType)
{
    Regist();
    return std::find(encoderTypes_.begin(), encoderTypes_.end(), encoderType) != encoderTypes_.end();
}

std::shared_ptr<AudioFrameEncoder> AudioFrameEncoder::CreateAudioEncoder(const std::string &encoderType, const std::string &jsonParams)
{
    Regist();
    std::shared_ptr<AudioFrameEncoder> ret;
    if (encoderType == "native") {
        nlohmann::json j;
        if (jsonParams != "")
            j = nlohmann::json::parse(jsonParams);
        std::string codecName = "opus";
        if (j.contains("codec")) {
            codecName = j["codec"];
        }
        if (strcasecmp(codecName.c_str(), "opus") == 0) {
            ret = std::make_shared<OpusAudioFrameEncoder>(jsonParams);
        } else if (strcasecmp(codecName.c_str(), "g711") == 0 || strcasecmp(codecName.c_str(), "PCMA") == 0 || strcasecmp(codecName.c_str(), "PCMU") == 0) {
            if (strcasecmp(codecName.c_str(), "PCMU") == 0) {
                j["ulaw"] = true;
            }
            ret = std::make_shared<G711AudioFrameEncoder>(j.dump());
        } else if (strcasecmp(codecName.c_str(), "g722") == 0) {
            ret = std::make_shared<G722AudioFrameEncoder>(jsonParams);
        } else if (strcasecmp(codecName.c_str(), "g7221") == 0) {
            ret = std::make_shared<G7221AudioFrameEncoder>(jsonParams);
        } else if (strcasecmp(codecName.c_str(), "g729") == 0) {
            ret = std::make_shared<G729AudioFrameEncoder>(jsonParams);
        } else if (strcasecmp(codecName.c_str(), "aac") == 0 || strcasecmp(codecName.c_str(), "MP4A-LATM") == 0) {
            ret = std::make_shared<AACAudioFrameEncoder>(jsonParams);
        } else if (strcasecmp(codecName.c_str(), "l16") == 0) {
            ret = std::make_shared<L16AudioFrameEncoder>(jsonParams);
        }
    }
    return ret;
}

std::list<CodecInst> &AudioFrameEncoder::GetSupportCodecs()
{
    Regist();
    return codecs_;
}

bool AudioFrameEncoder::IsSupportCodec(const CodecInst &codec)
{
    Regist();
    return std::find(codecs_.begin(), codecs_.end(), codec) != codecs_.end();
}

bool AudioFrameEncoder::IsAudioFrameEncoder(const FramePipeline::Ptr& ptr)
{
    if (ptr->name() == "OpusAudioFrameEncoder")
    {
        return true;
    }
    else if (ptr->name() == "G711AudioFrameEncoder")
    {
        return true;
    } else if (ptr->name() == "G722AudioFrameEncoder") {
        return true;
    } else if (ptr->name() == "G729AudioFrameEncoder") {
        return true;
    } else if (ptr->name() == "AACAudioFrameEncoder") {
        return true;
    } else if (ptr->name() == "G7221AudioFrameEncoder") {
        return true;
    } else if (ptr->name() == "L16AudioFrameEncoder") {
        return true;
    }
    return false;
}