/*******************************************
* SPDX-License-Identifier: MIT             *
* Copyright (C) 2023-.... <PERSON>g        *
* Contact: <PERSON> <<EMAIL>> *
* https://github.com/lengjingzju/ljcore    *
*******************************************/
#pragma once
#include <string.h>
#include <stdio.h>
#include <errno.h>

#ifdef __cplusplus
extern "C" {
#endif

#define jfatal( fmt, ...) printf(fmt "\n", ##__VA_ARGS__)
#define jerror( fmt, ...) printf(fmt "\n", ##__VA_ARGS__)
#define jwarn(  fmt, ...) printf(fmt "\n", ##__VA_ARGS__)
#define jinfo(  fmt, ...) printf(fmt "\n", ##__VA_ARGS__)
#define jdebug( fmt, ...) printf(fmt "\n", ##__VA_ARGS__)
#define jtrace( fmt, ...) printf(fmt "\n", ##__VA_ARGS__)


#ifdef __cplusplus
}
#endif
