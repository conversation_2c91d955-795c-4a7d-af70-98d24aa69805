#include "FramePipeline.h"
#include "Frame.h"
#include "Utils.h"
#include <ljcore/jlog.h>

#ifdef USE_CUDA
#include "cuda/CuFrameDeviceToHost.h"
#endif
#ifdef USE_WEBRTC
#include "ip/UdpFramePipeline.h"
#endif
#ifdef USE_LIBNICE
#include "libnice/LibniceAgent.h"
#endif
#ifdef USE_DTLS
#include "dtls/DTLSFramePipeline.h"
#endif

using namespace panocom;

int FrameInputFpsIntervel = 5;
extern int mmi_print_state;

FramePipeline::Ptr FramePipeline::CreateFramePipeline(const std::string &name, const std::string& jsonParams)
{
    FramePipeline::Ptr ret;
#ifdef USE_CUDA
    if (name == "CuFrameDeviceToHost")
    {
        ret = std::make_shared<CuFrameDeviceToHost>(jsonParams);
    }
    if (name == "CuFrameDeviceToHostNV12")
    {
        ret = std::make_shared<CuFrameDeviceToHostNV12>(jsonParams);
    }
#endif
#ifdef USE_WEBRTC
    if (name == "UdpFramePipeline")
    {
        ret = std::make_shared<UdpFramePipeline>(jsonParams);
    }
#endif
#ifdef USE_LIBNICE
    if (name == "LibniceAgent")
    {
        ret = std::make_shared<LibniceAgent>(jsonParams);
    }
#endif
#ifdef USE_DTLS
    if (name == "DTLSFramePipeline")
    {
        ret = std::make_shared<DTLSFramePipeline>(jsonParams);
    }
#endif
    return ret;
}

FramePipeline::~FramePipeline()
{
    //FN_BEGIN;
    //clearPipeline();
    //FN_END;
}

void FramePipeline::clearPipeline()
{
    {
        std::unique_lock<std::mutex> lock(audio_dests_mutex_);
        for (auto it = audio_dests_.begin(); it != audio_dests_.end(); ++it)
        {
            auto dest = it->lock();
            if (dest)
            {
                jinfo("clear audio [%s]-[%p]", dest->name().c_str(), this);
                dest->removeAudioSource(shared_from_this());
            }
        }
        audio_dests_.clear();
    }

    {
        std::unique_lock<std::mutex> lock(video_dests_mutex_);
        for (auto it = video_dests_.begin(); it != video_dests_.end(); ++it)
        {
            auto dest = it->lock();
            if (dest)
            {
                jinfo("clear video [%s]-[%p]", dest->name().c_str(), this);
                dest->removeVideoSource(shared_from_this());
            }
        }
        video_dests_.clear();
    }

    {
        std::unique_lock<std::mutex> lock(data_dests_mutex_);
        for (auto it = data_dests_.begin(); it != data_dests_.end(); ++it)
        {
            auto dest = it->lock();
            if (dest)
            {
                jinfo("clear data [%s]-[%p]", dest->name().c_str(), this);
                dest->removeDataSource(shared_from_this());
            }
        }
        data_dests_.clear();
    }
}

void FramePipeline::stopPipeline()
{
    {
        std::unique_lock<std::mutex> lock(audio_dests_mutex_);
        for (auto it = audio_dests_.begin(); it != audio_dests_.end(); ++it)
        {
            auto dest = it->lock();
            if (dest)
            {
                printf("stop audio [%s]-[%p]\n", dest->name().c_str(), this);
                dest->stop();
            }
        }
    }

    {
        std::unique_lock<std::mutex> lock(video_dests_mutex_);
        for (auto it = video_dests_.begin(); it != video_dests_.end(); ++it)
        {
            auto dest = it->lock();
            if (dest)
            {
                printf("stop video [%s]-[%p]\n", dest->name().c_str(), this);
                dest->stop();
            }
        }
    }

    {
        std::unique_lock<std::mutex> lock(data_dests_mutex_);
        for (auto it = data_dests_.begin(); it != data_dests_.end(); ++it)
        {
            auto dest = it->lock();
            if (dest)
            {
                printf("stop data [%s]-[%p]\n", dest->name().c_str(), this);
                dest->stop();
            }
        }
    }
}

FramePipeline::Ptr FramePipeline::addAudioDestination(const FramePipeline::Ptr& dest)
{
    if (dest)
    {
        {
            std::unique_lock<std::mutex> lock(audio_dests_mutex_);
            audio_dests_.push_back(dest);
        }
        dest->addAudioSource(shared_from_this());
    }
    return dest;
}

FramePipeline::Ptr FramePipeline::addVideoDestination(const FramePipeline::Ptr& dest)
{
    if (dest)
    {
        {
            std::unique_lock<std::mutex> lock(video_dests_mutex_);
            video_dests_.push_back(dest);
        }
        dest->addVideoSource(shared_from_this());
    }
    return dest;
}

FramePipeline::Ptr FramePipeline::addDataDestination(const FramePipeline::Ptr& dest)
{
    if (dest)
    {
        {
            std::unique_lock<std::mutex> lock(data_dests_mutex_);
            data_dests_.push_back(dest);
        }
        dest->addDataSource(shared_from_this());
    }
    return dest;
}

FramePipeline::Ptr FramePipeline::removeAudioDestination(const FramePipeline::Ptr& dest)
{
    {
        std::unique_lock<std::mutex> lock(audio_dests_mutex_);
        for (auto it = audio_dests_.begin(); it != audio_dests_.end(); ++it)
        {
            auto dst = it->lock();
            if (dst == dest)
            {
                audio_dests_.erase(it);
                break;
            }
        }
    }
    dest->removeAudioSource(shared_from_this());
    return shared_from_this();
}

FramePipeline::Ptr FramePipeline::removeVideoDestination(const FramePipeline::Ptr& dest)
{
    {
        std::unique_lock<std::mutex> lock(video_dests_mutex_);
        for (auto it = video_dests_.begin(); it != video_dests_.end(); ++it)
        {
            auto dst = it->lock();
            if (dst == dest)
            {
                video_dests_.erase(it);
                break;
            }
        }
    }
    dest->removeVideoSource(shared_from_this());
    dest->clearCache();
    return shared_from_this();
}

FramePipeline::Ptr FramePipeline::removeDataDestination(const FramePipeline::Ptr& dest)
{
    {
        std::unique_lock<std::mutex> lock(data_dests_mutex_);
        for (auto it = data_dests_.begin(); it != data_dests_.end(); ++it)
        {
            auto dst = it->lock();
            if (dst == dest)
            {
                data_dests_.erase(it);
                break;
            }
        }
    }
    dest->removeDataSource(shared_from_this());
    return shared_from_this();
}

std::list<FramePipeline::WPtr> FramePipeline::getAudioDestinations()
{
    std::unique_lock<std::mutex> lock(audio_dests_mutex_);
    return audio_dests_;
}

std::list<FramePipeline::WPtr> FramePipeline::getVideoDestinations()
{
    std::unique_lock<std::mutex> lock(video_dests_mutex_);
    return video_dests_;
}

std::list<FramePipeline::WPtr> FramePipeline::getDataDestinations()
{
    std::unique_lock<std::mutex> lock(data_dests_mutex_);
    return data_dests_;
}

bool FramePipeline::isAudioDestination(const std::shared_ptr<FramePipeline> &dst)
{
    std::unique_lock<std::mutex> lock(audio_dests_mutex_);
    for (auto it = audio_dests_.begin(); it != audio_dests_.end(); ++it)
    {
        auto dest = it->lock();
        if (dest == dst)
            return true;
    }
    return false;
}

bool FramePipeline::isVideoDestination(const std::shared_ptr<FramePipeline> &dst)
{
    std::unique_lock<std::mutex> lock(video_dests_mutex_);
    for (auto it = video_dests_.begin(); it != video_dests_.end(); ++it)
    {
        auto dest = it->lock();
        if (dest == dst)
            return true;
    }
    return false;
}

bool FramePipeline::isDataDestination(const std::shared_ptr<FramePipeline> &dst)
{
    std::unique_lock<std::mutex> lock(data_dests_mutex_);
    for (auto it = data_dests_.begin(); it != data_dests_.end(); ++it)
    {
        auto dest = it->lock();
        if (dest == dst)
            return true;
    }
    return false;
}

void FramePipeline::deliverFrame(const std::shared_ptr<Frame> &frame)
{
    if (preDeliverFrameListener_)
    {
        preDeliverFrameListener_(frame, preDeliverFrameParam_);
    }
    frame->setSource(this);
    frame->addToSourceList(this);
    if (Frame::isAudioFrame(frame))
    {
        std::unique_lock<std::mutex> lock(audio_dests_mutex_);
        for (auto it = audio_dests_.begin(); it != audio_dests_.end(); ++it)
        {
            auto dest = it->lock();
            if (dest)
            {
                dest->onFrameTransfer(frame);
            }
        }
    }
    else if (Frame::isVideoFrame(frame))
    {
        std::unique_lock<std::mutex> lock(video_dests_mutex_);
        for (auto it = video_dests_.begin(); it != video_dests_.end(); ++it)
        {
            auto dest = it->lock();
            if (dest)
            {
                dest->onFrameTransfer(frame);
            }
        }
    }
    else if (Frame::isDataFrame(frame))
    {
        std::unique_lock<std::mutex> lock(data_dests_mutex_);
        for (auto it = data_dests_.begin(); it != data_dests_.end(); ++it)
        {
            auto dest = it->lock();
            if (dest)
            {
                dest->onFrameTransfer(frame);
            }
        }
    }
    else
    {
        jerror("deliverFrame fail %d", frame->getFrameFormat());
        // TODO: log error here.
    }
    if (afterDeliverFrameListener_)
    {
        afterDeliverFrameListener_(frame, afterDeliverFrameParam_);
    }
}

void FramePipeline::start()
{
    std::unique_lock<std::mutex> lock(mutexState_);
    switch (workState_)
    {
    case STOPPED:
    case STOPPING:
    case PAUSE:
        workState_ = STARTING;
        break;
    default:
        return;
    }
    connected_ = true;
    connected_timeout_ = true;
}

void FramePipeline::pause()
{
    std::unique_lock<std::mutex> lock(mutexState_);
    workState_ = PAUSE;
}

void FramePipeline::stop()
{
    std::unique_lock<std::mutex> lock(mutexState_);
    switch (workState_)
    {
    case STARTED:
    case STARTING:
    case PAUSE:
        workState_ = STOPPING;
        break;
    default:
        return;
    }
    connected_ = false;
    connected_timeout_ = false;
}

FramePipeline::WorkState FramePipeline::workState()
{
    std::unique_lock<std::mutex> lock(mutexState_);
    return workState_;
}

void FramePipeline::setStarted()
{
    std::unique_lock<std::mutex> lock(mutexState_);
    if (workState_ == STARTING)
        workState_ = STARTED;
}

void FramePipeline::setStopped()
{
    std::unique_lock<std::mutex> lock(mutexState_);
    if (workState_ == STOPPING)
        workState_ = STOPPED;
}

int FrameOutputFpsIntervel = 5;
int FramePipelineFpsIntervel = 5;

void FramePipeline::addAudioSource(const FramePipeline::Ptr &source)
{
    std::unique_lock<std::mutex> lock(audio_src_mutex_);
    audio_srcs_.push_back(source);
}

void FramePipeline::removeAudioSource(const FramePipeline::Ptr &source)
{
    std::unique_lock<std::mutex> lock(audio_src_mutex_);
    for (auto it = audio_srcs_.begin(); it != audio_srcs_.end(); ++it)
    {
        auto src = it->lock();
        if (src == source)
        {
            audio_srcs_.erase(it);
            break;
        }
    }
}

void FramePipeline::addVideoSource(const FramePipeline::Ptr &source)
{
    std::unique_lock<std::mutex> lock(video_src_mutex_);
    video_srcs_.push_back(source);
}

void FramePipeline::removeVideoSource(const FramePipeline::Ptr &source)
{
    std::unique_lock<std::mutex> lock(video_src_mutex_);
    for (auto it = video_srcs_.begin(); it != video_srcs_.end(); ++it)
    {
        auto src = it->lock();
        if (src == source)
        {
            video_srcs_.erase(it);
            break;
        }
    }
}

void FramePipeline::addDataSource(const FramePipeline::Ptr &source)
{
    std::unique_lock<std::mutex> lock(data_src_mutex_);
    data_srcs_.push_back(source);
}

void FramePipeline::removeDataSource(const FramePipeline::Ptr &source)
{
    std::unique_lock<std::mutex> lock(data_src_mutex_);
    for (auto it = data_srcs_.begin(); it != data_srcs_.end(); ++it)
    {
        auto src = it->lock();
        if (src == source)
        {
            data_srcs_.erase(it);
            break;
        }
    }
}

std::list<FramePipeline::WPtr> FramePipeline::getAudioSources()
{
    std::unique_lock<std::mutex> lock(audio_src_mutex_);
    return audio_srcs_;
}

std::list<FramePipeline::WPtr> FramePipeline::getVideoSources()
{
    std::unique_lock<std::mutex> lock(video_src_mutex_);
    return video_srcs_;
}

std::list<FramePipeline::WPtr> FramePipeline::getDataSources()
{
    std::unique_lock<std::mutex> lock(data_src_mutex_);
    return data_srcs_;
}

void FramePipeline::checkFrameTimeout()
{
    if (timeoutTick_ != 0 && timeoutMS_ != 0 && timeoutNotifyCB_)
    {
        long now = GetTickCount();
        long gap = now - timeoutTick_;
        if (gap >= timeoutMS_ && connected_timeout_)
        {
            connected_timeout_ = false;
            timeoutNotifyCB_(gap, param_);
        } else if (gap < timeoutMS_ && !connected_timeout_) {
            connected_timeout_ = true;
            timeoutNotifyCB_(0, param_);
        }
    }

	if (timeoutTick4Pool_ != 0 && timeoutMS4Pool_ != 0 && timeoutNotifyCB4Pool_)
	{
		long now = GetTickCount();
		long gap = now - timeoutTick4Pool_;
		if (gap >= timeoutMS4Pool_ && connected_ && timeoutMS4Pool_ != 0)
		{
			connected_ = false;
			timeoutNotifyCB4Pool_(gap, param4Pool_);
		}
		else if (gap < timeoutMS4Pool_ && !connected_ && timeoutMS4Pool_ != 0) {
			connected_ = true;
			timeoutNotifyCB4Pool_(0, param4Pool_);
		}
	}
}

void FramePipeline::resetConnectState(bool state) {
    connected_ = state;
    timeoutTick4Pool_ = GetTickCount();
}

void FramePipeline::onFrameTransfer(const std::shared_ptr<Frame> &f)
{
    if (timeoutTick_ != 0 && timeoutMS_ != 0 && timeoutNotifyCB_)
    {
        timeoutTick_ = GetTickCount();
    }
    if (timeoutTick4Pool_ != 0 && timeoutMS4Pool_ != 0 && timeoutNotifyCB4Pool_)
    {
        timeoutTick4Pool_ = GetTickCount();
    }
    if (preOnFrameListener_)
    {
        preOnFrameListener_(f, preOnFrameParam_);
    }
    onFrame(f);
    if (afterOnFrameListener_)
    {
        afterOnFrameListener_(f, afterOnFrameParam_);
    }
}

void FramePipeline::deliverFeedbackFrame(const std::shared_ptr<Frame> &frame)
{
    if (frame->getFrameFormat() == FRAME_FORMAT_AUDIO_FEEDBACK)
    {
        std::unique_lock<std::mutex> lock(audio_src_mutex_);
        for (auto src: audio_srcs_)
        {
            FramePipeline::Ptr source = src.lock();
            if (source)
            {
                source->onFeedbackFrame(frame);
            }
        }
    }
    else if (frame->getFrameFormat() == FRAME_FORMAT_VIDEO_FEEDBACK)
    {
        std::unique_lock<std::mutex> lock(video_src_mutex_);
        for (auto src: video_srcs_)
        {
            FramePipeline::Ptr source = src.lock();
            if (source)
            {
                source->onFeedbackFrame(frame);
            }
        }
    }
    else if (frame->getFrameFormat() == FRAME_FORMAT_DATA_FEEDBACK)
    {
        std::unique_lock<std::mutex> lock(data_src_mutex_);
        for (auto src: data_srcs_)
        {
            FramePipeline::Ptr source = src.lock();
            if (source)
            {
                source->onFeedbackFrame(frame);
            }
        }
    }
    else
    {
        // TODO: log error here.
    }
}

void FramePipeline::printOutputStatus(const std::string& name)
{
    if (mmi_print_state)
    {
        outputFpsCounter_++;
        long tick = GetTickCount();
        if (tick - outputTick_ >= FrameInputFpsIntervel * 1000)
        {
            outputFps_ = outputFpsCounter_ / FrameInputFpsIntervel;
            jinfo("%s output fps = %d", name.c_str(), outputFps_);
            outputFpsCounter_ = 0;
            outputTick_ = tick;
        }
    }
}

void FramePipeline::printInputStatus(const std::string& name)
{
    if (mmi_print_state)
    {
        inputFpsCounter_++;
        long tick = GetTickCount();
        if (tick - inputTick_ >= FrameOutputFpsIntervel * 1000)
        {
            inputFps_ = inputFpsCounter_ / FrameOutputFpsIntervel;
            jinfo("%s input fps = %d", name.c_str(), inputFps_);
            inputFpsCounter_ = 0;
            inputTick_ = tick;
        }
    }
}

void FramePipeline::printDestinationStatus(const std::string& name)
{
    if (mmi_print_state)
    {
        destinationFpsCounter_++;
        long tick = GetTickCount();
        if (tick - destinationTick_ >= FramePipelineFpsIntervel * 1000)
        {
            destinationFps_ = destinationFpsCounter_ / FramePipelineFpsIntervel;
            jinfo("%s dst fps = %d", name.c_str(), destinationFps_);
            destinationFpsCounter_ = 0;
            destinationTick_ = tick;
        }
    }
}