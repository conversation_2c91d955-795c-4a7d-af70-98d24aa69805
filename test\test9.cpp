#include "MediaPipeline.h"
#include <json.hpp>
#include <stdio.h>
#include <ljcore/jlog.h>
#include <hv/EventLoop.h>

using namespace panocom;

int main()
{
    auto loop = std::make_shared<hv::EventLoop>();
    jlog_init(nullptr);
    nlohmann::json j;
    j.clear();
    j["path"] = "mjpeg.yuv";
    auto filesource = FileFramePipeline::CreateFileSource("NV12", j.dump());
    j.clear();
    j["path"] = "mjpeg2.yuv";
    auto fileYUV = FileFramePipeline::CreateFileDestination("file", j.dump());
    auto deviceToHost = FramePipeline::CreateFramePipeline("CuFrameDeviceToHostNV12");
    auto nv12scale = VideoFrameProcesser::CreateVideoProcesser("CuNv12Scale");
    filesource->addVideoDestination(nv12scale);
    nv12scale->addVideoDestination(deviceToHost);
    deviceToHost->addVideoDestination(fileYUV);

    loop->run();
    return 0;
}