#include "AudioFrameRender.h"
#ifdef USE_SDL2
#include "sdl2/Sdl2AudioFrameRender.h"
#endif
#ifdef USE_SPEC
#include "spec/AudioSpecMulticaster.h"
#include "spec/AudioSpec.h"
#include "spec/MixedAudioSpec.h"
#include "spec/AudioSpecMulticasterSingle.h"
#include "spec/AudioSpecMulticasterMulti.h"
#endif
#ifdef USE_TINYALSA
#include "tinyalsa/TinyALSARender.h"
#endif

#include <algorithm>
#include <json.hpp>
#include <ljcore/jlog.h>
#ifdef RK_PLATFORM
#include "rk/RKAudioFrameRender.h"
#endif
#ifdef USE_UART
#include "uart/UartWriter.h"
#endif
#ifdef USE_ALSA
#include "alsa/AlsaAudioFrameRender.h"
#endif

using namespace panocom;

bool AudioFrameRender::isRegistered = false;

std::vector<std::string> AudioFrameRender::renders_;

void AudioFrameRender::RegistRenders()
{
    if (!isRegistered)
    {
#ifdef USE_SDL2
        renders_.push_back("Sdl2AudioFrameRender");
#endif
#ifdef USE_SPEC
        renders_.push_back("AudioSpecMulticaster");
        renders_.push_back("AudioSpec");
        renders_.push_back("MixedAudioSpec");
        renders_.push_back("AudioSpecMulticasterSingle");
        renders_.push_back("AudioSpecMulticasterMulti");
#endif
#ifdef RK_PLATFORM
        renders_.push_back("RKAudioFrameRender");
#endif
#ifdef USE_ALSA
        renders_.push_back("AlsaAudioFrameRender");
#endif
#ifdef USE_TINYALSA
        renders_.push_back("TinyALSARender");
#endif
#ifdef USE_UART
        renders_.push_back("UartWriter");
#endif
        isRegistered = true;
    }
}

std::vector<std::string>& AudioFrameRender::GetSupportRenders()
{
    RegistRenders();
    return renders_;
}

bool AudioFrameRender::IsSupportRender(const std::string &renderName)
{
    RegistRenders();
    return std::find(renders_.begin(), renders_.end(), renderName) != renders_.end();
}

std::shared_ptr<AudioFrameRender> AudioFrameRender::CreateAudioRender(const std::string &renderName, const std::string &jsonParams)
{
    jinfo("CreateAudioRender %s", renderName.c_str());
    RegistRenders();
    std::shared_ptr<AudioFrameRender> ret;
#ifdef USE_SDL2
    if (renderName == "Sdl2AudioFrameRender")
    {
        ret = std::shared_ptr<AudioFrameRender>(new Sdl2AudioFrameRender(jsonParams));
    }
#endif
#ifdef RK_PLATFORM
    if (renderName == "RKAudioFrameRender") 
    {
        ret = std::make_shared<RKAudioFrameRender>(jsonParams);
    } 
#endif
#ifdef USE_ALSA
    if (renderName == "AlsaAudioFrameRender") 
    {
        ret = std::make_shared<AlsaAudioFrameRender>(jsonParams);
    }
#endif
#ifdef USE_SPEC
    if (renderName == "AudioSpecMulticaster")
    {
        ret = std::make_shared<AudioFrameRender>(AudioSpecMulticaster::CreateInstance(jsonParams));
    }
    if (renderName == "AudioSpecMulticasterSingle")
    {
        ret = std::make_shared<AudioFrameRender>(AudioSpecMulticasterSingle::CreateInstance(jsonParams));
    }
    if (renderName == "AudioSpec")
    {
        nlohmann::json j;
        if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
        j["RenderOnly"] = true;
        ret = std::make_shared<AudioFrameRender>(std::make_shared<AudioSpec>(j.dump()));
    }
    if (renderName == "AudioSpecMulticasterMulti")
    {
        nlohmann::json j;
        if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
        j["RenderOnly"] = true;
        ret = std::make_shared<AudioFrameRender>(std::make_shared<AudioSpecMulticasterMulti>(j.dump()));
    }
    if (renderName == "MixedAudioSpec")
    {
        nlohmann::json j;
        if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
        j["RenderOnly"] = true;
        // ret = std::make_shared<AudioFrameRender>(std::make_shared<MixedAudioSpec>(j.dump()));
        auto audio_spec = MixedAudioSpecManager::instance().CreateMixedAudioSpec(j.dump());
        if (audio_spec)
            ret = std::make_shared<AudioFrameRender>(audio_spec);
    }
#endif
#ifdef USE_TINYALSA
    if (renderName == "TinyALSARender")
    {
        ret = std::make_shared<TinyALSARender>(jsonParams);
    }
#endif
#ifdef USE_UART
    if (renderName == "UartWriter") {
        ret = std::make_shared<UartWriter>(jsonParams);
    }
#endif
    return ret;
}

bool AudioFrameRender::isAudioFrameRender(const FramePipeline::Ptr& ptr)
{
#ifdef USE_SDL2
    if (ptr->name() == "Sdl2AudioFrameRender")
    {
        return true;
    }
#endif
#ifdef RK_PLATFORM
    if (ptr->name() == "RKAudioFrameRender")
    {
        return true;
    } 
#endif
#ifdef USE_ALSA
    if (ptr->name() == "AlsaAudioFrameRender") 
    {
        return true;
    }
#endif
#ifdef USE_SPEC    
    if (ptr->name() == "AudioSpecMulticaster")
    {
        return true;
    }
    if (ptr->name() == "AudioSpec")
    {
        return true;
    }
    if (ptr->name() == "AudioSpecMulticasterMulti")
    {
        return true;
    }
    if (ptr->name() == "MixedAudioSpec")
    {
        return true;
    }
    if (ptr->name() == "AudioSpecMulticasterSingle")
    {
        return true;
    }
#endif
#ifdef USE_TINYALSA
    if (ptr->name() == "TinyALSARender")
    {
        return true;
    }
#endif
#ifdef USE_UART
    if (ptr->name() == "UartWriter") {
        return true;
    }
#endif
    return false;
}

std::string AudioFrameRender::loadDefaultConfig(const std::string& renderName) {
    if (!IsSupportRender(renderName)) return "";
    nlohmann::json j;
#ifdef RK_PLATFORM
    if (renderName == "AlsaAudioFrameRender") {
        j["samplerate"] = 48000;
        j["channel"] = 2;
        j["card"] = 0;
        j["device"] = 0;
        return j.dump();
    }
#endif
#ifdef USE_SPEC   
    if (renderName == "AudioSpec") {
        j["samplerate"] = 16000;
        j["channel"] = 1;
        j["maxChn"] = 8;
        return j.dump();
    }
#endif
    return "";
}