#ifndef WIDGET_H
#define WIDGET_H

#include <vector>
#include <QWidget>
#include "QCuVideoWidget.h"

QT_BEGIN_NAMESPACE
namespace Ui { class Widget; }
QT_END_NAMESPACE

class Widget : public QWidget
{
    Q_OBJECT

public:
    Widget(QWidget *parent = nullptr);
    ~Widget();

    
    void resizeEvent(QResizeEvent *event) override;

private:
    Ui::Widget *ui;
    //std::vector<panocom::QCuVideoWidget*> wids;
    panocom::QCuVideoWidget* wid;
    
};
#endif // WIDGET_H
