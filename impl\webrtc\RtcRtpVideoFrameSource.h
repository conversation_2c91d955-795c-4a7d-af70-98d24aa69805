#ifndef P_RtcRtpVideoFrameSource_h
#define P_RtcRtpVideoFrameSource_h

#include "RtpEngine.h"
#include "Frame.h"

#include <api/video_codecs/video_codec.h>
#include <api/video_codecs/video_decoder.h>
#include <api/video_codecs/video_decoder_factory.h>
#include <modules/remote_bitrate_estimator/include/remote_bitrate_estimator.h>
#include <call/call.h>
#include <rtc_base/task_queue.h>
#include <rtc_base/event.h>
#include <hv/EventLoopThread.h>
#include <json.hpp>
#include <map>
namespace panocom {

class RtcRtpVideoFrameSource : public RtpEngine,
                                public rtc::VideoSinkInterface<webrtc::VideoFrame>,
                                public webrtc::VideoDecoderFactory,
                                public webrtc::Transport,
                                public webrtc::RemoteBitrateObserver {
public:
    typedef std::function<void(uint32_t ssrc, uint32_t bps)> BitrateEstimationCallback;
    RtcRtpVideoFrameSource(const std::string& jsonParams);
    virtual ~RtcRtpVideoFrameSource();

    // RTCP receiver
    void onFrame(const std::shared_ptr<Frame> &f) override;
    void start() override;
    void stop() override;

    // Implements rtc::VideoSinkInterface<VideoFrame>.
    void OnFrame(const webrtc::VideoFrame& video_frame) override;

    // Implements the webrtc::VideoDecoderFactory interface.
    std::vector<webrtc::SdpVideoFormat> GetSupportedFormats() const override;
    std::unique_ptr<webrtc::VideoDecoder> CreateVideoDecoder(const webrtc::SdpVideoFormat& format) override;

    // Implements webrtc::Transport
    bool SendRtp(const uint8_t* packet, size_t length, const webrtc::PacketOptions& options) override;
    bool SendRtcp(const uint8_t* packet, size_t length) override;

    void OnReceiveBitrateChanged(const std::vector<uint32_t>& ssrcs, uint32_t bitrate) override;
    std::string GetStats2() override;
private:
    class AdapterDecoder : public webrtc::VideoDecoder {
    public:
        AdapterDecoder(RtcRtpVideoFrameSource* parent)
            : parent_(parent)
        {
        }

        int32_t InitDecode(const webrtc::VideoCodec* config,
            int32_t number_of_cores) override;

        int32_t Decode(const webrtc::EncodedImage& input,
            bool missing_frames,
            int64_t render_time_ms) override;

        int32_t RegisterDecodeCompleteCallback(
            webrtc::DecodedImageCallback* callback) override { return 0; }

        int32_t Release() override { return 0; }
    private:
        RtcRtpVideoFrameSource* parent_;
    };

private:
    void CreateReceiveVideo(FrameFormat format, int payload_type);

    FrameFormat format_;
    int payloadType_ = 0;
    uint16_t width_ = 0;
    uint16_t height_ = 0;
    uint32_t ssrc_ = 0;
    //uint32_t transport_cc_ = 1;

    webrtc::VideoReceiveStream* videoRecvStream_ = nullptr;

    uint32_t currentBitrate_;
    BitrateEstimationCallback cb_;

    typedef std::shared_ptr<webrtc::Call> CallPtr;
    CallPtr call_;

    std::shared_ptr<webrtc::RtcEventLog> eventLog_;
    std::shared_ptr<rtc::TaskQueue> taskQueue_;
    std::shared_ptr<webrtc::TaskQueueFactory> taskQueueFactory_;

    webrtc::VideoCodecType codec_;

    rtc::Event started_;
    rtc::Event stopped_;
    rtc::Event stats_ready_;

    bool first_;

    std::mutex mutex_;

    uint8_t red_payload_type_ = 116;
    uint8_t ulpfec_payload_type_ = 118;

    bool nack_enabled_;
    bool ulpfec_enabled_;
    bool red_enabled_;
    bool ajb_enabled_;
    nlohmann::json rtp_extmap_;
    nlohmann::json rtp_rtcpfb_;
    std::map<int,int> mapPayloadTypes_;

    webrtc::VideoReceiveStream::Stats stats_;
    uint64_t last_received_packet_count_;
};

} // namespace panocom

#endif /* P_RtcRtpVideoFrameSource_h */
