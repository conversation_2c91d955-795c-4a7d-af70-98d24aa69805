#include <stdint.h>
#include "AudioFrameResampler.h"
#include "Frame.h"
#include "Utils.h"
#include <json.hpp>
#include <ljcore/jlog.h>
#include <stdio.h>

extern int mmi_record_audio;

using namespace panocom;

AudioFrameResampler::AudioFrameResampler(const std::string& jsonParams)
{
    FN_BEGIN;
    jinfo("AudioFrameResampler %s", jsonParams.c_str());
    name_ = "AudioFrameResampler";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    if (j.contains("src"))
    {
        if (j["src"].contains("channel"))
        {
            src_chn_ = j["src"]["channel"];
        }
        if (j["src"].contains("samplerate"))
        {
            src_samplerate_ = j["src"]["samplerate"];
        }
    }
    if (j.contains("dst"))
    {
        if (j["dst"].contains("channel"))
        {
            dst_chn_ = j["dst"]["channel"];
        }
        if (j["dst"].contains("samplerate"))
        {
            dst_samplerate_ = j["dst"]["samplerate"];
        }
    }
#ifdef WEBRTC_RESAMPLE_ANOTHER
    resampler_.InitializeIfNeeded(src_samplerate_, dst_samplerate_, 1);
#else
    resampler_.ResetIfNeeded(src_samplerate_, dst_samplerate_, 1);
#endif
    FN_END;
}

AudioFrameResampler::~AudioFrameResampler()
{
    FN_BEGIN;
    FN_END;
}

void AudioFrameResampler::onFrame(const std::shared_ptr<Frame>& frame)
{
    //jinfo("AudioFrameResampler::onFrame(%d:%d) %p %d %d", src_samplerate_, dst_samplerate_, this, frame->getFrameSize(), frame->getFrameBufferSize());
    uint64_t outputSize = (uint64_t) (frame->getFrameSize() * (double) dst_samplerate_ / (double) src_samplerate_);
    outputSize -= outputSize % dst_chn_;
    auto f = frame;
    
    {
        auto frame = Frame::CreateFrame(Frame::getPCMFormat(dst_chn_, dst_samplerate_));
        frame->createFrameBuffer(outputSize);
        memset(frame->getFrameBuffer(), 0, frame->getFrameSize());
#ifdef WEBRTC_RESAMPLE_ANOTHER
        resampler_.Resample((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2);
#else
        size_t outlen = 0;
        resampler_.Push((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2, outlen);
#endif
        f = frame;

        //jinfo("AudioFrameResampler::deliverFrame %p %d %d", this, f->getFrameSize(), f->getFrameBufferSize());
        if (mmi_record_audio)
        {
            if (!pf_)
            {
                std::string fileName = name_ + std::to_string(src_samplerate_) + "-" + std::to_string(dst_samplerate_)  + "-" + std::to_string((long)this) + ".pcm";
                pf_ = fopen(fileName.c_str(), "w+b");
            }
            if (pf_)
            {
                fwrite(frame->getFrameBuffer(), 1, frame->getFrameSize(), pf_);
            }
        }
        deliverFrame(f);
        printOutputStatus("AudioFrameResampler");
    }
    printInputStatus("AudioFrameResampler");
}