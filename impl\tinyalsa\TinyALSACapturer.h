// created by gyj 2024-7-3
#ifndef P_TinyALSACapturer_h
#define P_TinyALSACapturer_h

#include "AudioFrameCapturer.h"
#include <tinyalsa/pcm.h>
#include <hv/EventLoopThread.h>
#include <ptoolkit/bytebuffer.h>
#include <mutex>

namespace panocom
{
    class TinyALSACapturer: public AudioFrameCapturer
    {
    public:
        TinyALSACapturer(const std::string& jsonParams);
        ~TinyALSACapturer() override;

        void start() override;
        void stop() override;
    private:
        unsigned int card_ = 0;
        unsigned int dev_ = 0;
        int samplerate_ = 16000;
        int chn_ = 1;
        int ptime_ = 20;
        int ptimeDataLen_;
        struct pcm *pcm_ = nullptr;
        hv::EventLoopThread thread_;
        hv::EventLoopThread threadPacer_;
        ByteBuffer bbuf_;
        std::mutex mutex_;
        std::unique_ptr<int16_t[]> readbuffer_;
        std::unique_ptr<int16_t[]> readbufferMono_;
        int readbufferLen_;
        long readTick_ = 0;
        long readBytes_ = 0;
    };
}

#endif