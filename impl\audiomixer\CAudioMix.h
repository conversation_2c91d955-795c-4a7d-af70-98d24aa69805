#ifndef IMPL_AUDIOMIXER_CAUDIOMIX_H_
#define IMPL_AUDIOMIXER_CAUDIOMIX_H_
#include <vector>
#include <cstdint>

#define AUDIO_DATA_TYPE short
#define AUDIO_DATA_TYPE_MAX 32767
#define AUDIO_DATA_TYPE_MIN -32768
#define WIDEN_TEMP_TYPE int

namespace panocom
{
class CAudioMix
{
private:
    /* data */
public:
    CAudioMix(/* args */);
    ~CAudioMix();

    static void TimeSliceByPoint(std::vector<AUDIO_DATA_TYPE*> &allMixingSounds, 
                        uint16_t raw_data_cnt, AUDIO_DATA_TYPE *raw_data_buffer);

    static void TimeSliceBySection(std::vector<AUDIO_DATA_TYPE*> &allMixingSounds, 
                        uint16_t raw_data_cnt, AUDIO_DATA_TYPE *raw_data_buffer);

    static void CombinePointsToOneWay1(std::vector<AUDIO_DATA_TYPE*> &allMixingSounds, 
                        uint16_t raw_data_cnt, AUDIO_DATA_TYPE *raw_data_buffer);

    static void CombinePointsToOneNewLC(std::vector<AUDIO_DATA_TYPE*> &allMixingSounds, 
                        uint16_t raw_data_cnt, AUDIO_DATA_TYPE *raw_data_buffer);
                    
    static void MixSoundsBySimplyAdd(std::vector<AUDIO_DATA_TYPE*> &allMixingSounds, 
                        uint16_t raw_data_cnt, AUDIO_DATA_TYPE *raw_data_buffer);
                        
    static void MixSoundsBySimplyAdd(AUDIO_DATA_TYPE *allMixingSounds, uint16_t AmountOfMixedAudioSources,
                        uint16_t raw_data_cnt, AUDIO_DATA_TYPE *raw_data_buffer);
                    
    static void MixSoundsByMean(std::vector<AUDIO_DATA_TYPE*> &allMixingSounds, 
                        uint16_t raw_data_cnt, AUDIO_DATA_TYPE *raw_data_buffer);

    static void AddAndNormalization(std::vector<AUDIO_DATA_TYPE*> &allMixingSounds, 
                        uint16_t raw_data_cnt, AUDIO_DATA_TYPE *raw_data_buffer);

    static void AddAndNormalization(AUDIO_DATA_TYPE *allMixingSounds, uint16_t AmountOfMixedAudioSources,
                        uint16_t raw_data_cnt, AUDIO_DATA_TYPE *raw_data_buffer);
    
    static void AddAndNormalization(AUDIO_DATA_TYPE *allMixingSounds, uint16_t AmountOfMixedAudioSources,
                        uint16_t raw_data_cnt, uint16_t stride, AUDIO_DATA_TYPE *raw_data_buffer);
};


} // namespace panocom

#endif