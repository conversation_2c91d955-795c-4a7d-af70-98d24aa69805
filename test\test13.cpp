#include "MediaPipeline.h"
#include "Frame.h"
#include <json.hpp>
#include <stdio.h>
#include <ljcore/jlog.h>
#include <string>
#include <hv/EventLoop.h>

using namespace panocom;

int main(int argc, char *argv[])
{
#ifdef USE_WEBRTC
    auto loop = std::make_shared<hv::EventLoop>();

    jlog_init(nullptr);
    nlohmann::json j;
    nlohmann::json dst;
    j["bindPort"] = 10000;
    j["fmt"] = FRAME_FORMAT_RTP;
    auto rtp = std::make_shared<UdpFramePipeline>(j.dump());
    j["bindPort"] = 10001;
    j["fmt"] = FRAME_FORMAT_RTCP;
    dst["ip"] = "************";
    dst["port"] = 10001;
    j["dst"].push_back(dst);
    auto rtcp = std::make_shared<UdpFramePipeline>(j.dump());

    j.clear();
    j["ssrc"] = atoi(argv[1]);
    auto rtcReciever = RtpEngine::CreateRTPEngine("RtcRtpVideoFrameSource", j.dump());

    j.clear();
    j["codec"] = "h264";
    auto decoder = VideoFrameDecoder::CreateVideoDecoder("CuVideoDecoder", j.dump());

    rtp->addDataDestination(rtcReciever);
    rtcp->addDataDestination(rtcReciever);
    rtcReciever->addVideoDestination(decoder);
    rtcReciever->addDataDestination(rtcp);

    loop->run();
#endif
    return 0;
}