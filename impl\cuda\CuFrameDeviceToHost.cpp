#include "CuFrameDeviceToHost.h"
#include "CuCommon.h"
#include "Frame.h"
#include <ljcore/jlog.h>
#include <json.hpp>
#include <cmath>

using namespace panocom;

CuFrameDeviceToHost::CuFrameDeviceToHost(const std::string& jsonParams)
{
    name_ = "CuFrameDeviceToHost";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    cudaId_ = 0;
    if (j.contains("cudaId"))
    {
        cudaId_ = j["cudaId"];
    }
    gpuIndex_ = 0;
    if (j.contains("gpuIndex"))
    {
        gpuIndex_ = j["gpuIndex"];
    }
}

void CuFrameDeviceToHost::onFrame(const std::shared_ptr<Frame> &f)
{
    switch (f->getFrameFormat())
    {
    case FRAME_FORMAT_CU_FRAME:
    case FRAME_FORMAT_CU_DECODED_NV12:
    case FRAME_FORMAT_CU_ENCODE_NV12:
        {
            jinfo("onFrame %d x %d %d x %d size = %d", f->width(), f->height(), f->hStride(), f->vStride(), f->getFrameSize());
            std::shared_ptr<Frame> output = Frame::CreateFrame(FRAME_FORMAT_COMMON_VIDEO);
            output->createFrameBuffer(f->getFrameSize());
            cuCtxPushCurrent(*CuCommon::instance().getCudaContext(cudaId_, gpuIndex_));
            cuMemcpyDtoH(output->getFrameBuffer(), (CUdeviceptr)f->getFrameBuffer(), f->getFrameSize());
            cuCtxPopCurrent(NULL);
            deliverFrame(output);
            break;
        }
    default:
        deliverFrame(f);
        break;
    }

}

CuFrameDeviceToHostNV12::CuFrameDeviceToHostNV12(const std::string& jsonParams)
{
    name_ = "CuFrameDeviceToHostNV12";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    if (j.contains("cudaId"))
    {
        cudaId_ = j["cudaId"];
    }
}

void CuFrameDeviceToHostNV12::onFrame(const std::shared_ptr<Frame> &f)
{
    switch (f->getFrameFormat())
    {
    case FRAME_FORMAT_CU_FRAME:
    case FRAME_FORMAT_CU_DECODED_NV12:
    case FRAME_FORMAT_CU_ENCODE_NV12:
        {
            std::shared_ptr<Frame> output = Frame::CreateFrame(FRAME_FORMAT_COMMON_VIDEO);
            output->createFrameBuffer(f->width() * f->height() * 3 / 2);
            
            // Copy luma plane
            CUDA_MEMCPY2D m = { 0 };
            m.srcMemoryType = CU_MEMORYTYPE_DEVICE;
            m.srcDevice = (CUdeviceptr)f->getFrameBuffer();
            m.srcPitch = f->hStride();
            m.dstMemoryType = CU_MEMORYTYPE_HOST;
            m.dstDevice = (CUdeviceptr)(m.dstHost = output->getFrameBuffer());
            m.dstPitch = f->width();
            m.WidthInBytes = f->width();
            m.Height = f->height();
            cuCtxPushCurrent(*CuCommon::instance().getCudaContext(cudaId_, gpuIndex_));
            cuMemcpy2D(&m);

            // Copy chroma plane
            // NVDEC output has luma height aligned by 2. Adjust chroma offset by aligning height
            m.srcDevice = (CUdeviceptr)((uint8_t *)f->getFrameBuffer() + m.srcPitch * ((f->height() + 1) & ~1));
            m.dstDevice = (CUdeviceptr)(m.dstHost = output->getFrameBuffer() + m.dstPitch * f->height());
            m.Height = f->height() / 2;
            cuMemcpy2D(&m);
            cuCtxPopCurrent(NULL);
            deliverFrame(output);
            break;
        }
    default:
        deliverFrame(f);
        break;
    }
}