#include "RKFileFrameReader.h"
#include <json.hpp>
#include "rk_frame_buffer.h"
namespace panocom {
std::shared_ptr<RKFileFrameReader> RKFileFrameReader::Create(const std::string& jsonParams) { 
    return std::make_shared<RKFileFrameReader>(jsonParams); 
}

RKFileFrameReader::RKFileFrameReader(const std::string &jsonParams) : FileFramePipeline(jsonParams), running_(false), init_done_(false), file_size_(0) {
    name_ = "RKFileFrameReader";
    nlohmann::json jsonParams_ = nlohmann::json::parse(jsonParams);
    file_name_ = "4.jpg";
    if (jsonParams_.contains("path")) {
        file_name_ = jsonParams_["path"];
    }
    coding_type_ = MPP_VIDEO_CodingMJPEG;
    width_ = 8192;
    height_ = 4320;
    init();
}

RKFileFrameReader::~RKFileFrameReader()
{
    jinfo("RKFileFrameReader::~RKFileFrameReader");
    deinit();
    jinfo("RKFileFrameReader::~RKFileFrameReader done");
}

void RKFileFrameReader::check_file_type(std::string &file_name, FileType &type) {
    if (file_name.find(".ivf") != std::string::npos) {
        type = FILE_IVF_TYPE;
    } else if (file_name.find(".jpg") != std::string::npos || file_name.find(".jpeg") != std::string::npos) {
        type = FILE_JPEG_TYPE;
    } else {
        type = FILE_NORMAL_TYPE;
    }
}

void RKFileFrameReader::init() {
    jinfo("RKFileFrameReader::init");
    if (init_done_) return;
    check_file_type(file_name_, file_type_);
    ifs_.open(file_name_, std::ios::binary | std::ios::in);
    if (!ifs_ || !ifs_.is_open()) {
        jerror("Failed to open file: %s", file_name_.c_str());
        return;
    }
    switch (file_type_) {
        case FILE_IVF_TYPE:
            coding_type_ = MPP_VIDEO_CodingVP8;
            break;
        case FILE_JPEG_TYPE:
            coding_type_ = MPP_VIDEO_CodingMJPEG;
            break;
        default:
            jerror("Unsupported file type: %s", file_name_.c_str());
    }
    ifs_.seekg(0, std::ios::end);
    file_size_ = ifs_.tellg();
    jinfo("file %s size: %d", file_name_.c_str(), file_size_);
    ifs_.clear();
    ifs_.seekg(0, std::ios::beg);
    auto ret = mpp_buffer_group_get_internal(&group_, MPP_BUFFER_TYPE_DRM);
    if (ret != MPP_OK) {
        jerror("Failed to get buffer group ret: %d", ret);
        return;
    }
    ret = InitDecode();
    if (ret != MPP_OK) {
        jerror("Failed to init decode");
        return;
    }
    frameBufferManager_ = std::make_shared<RKBufferManager>(4, width_, height_);
    init_done_ = true;
    run();
}

void RKFileFrameReader::deinit() {
    running_ = false;
    init_done_ = false;
    if (loop_thread_.isRunning()) {
        loop_thread_.stop(true);
        loop_thread_.join();
    }
    if (ifs_.is_open())
        ifs_.close();
    if (buf_)
        mpp_buffer_put(buf_);
    buf_ = NULL;
    if (group_)
        mpp_buffer_group_put(group_);
    group_ = NULL;
	if (dec_pkt_pre_) {
		mpp_packet_deinit(&dec_pkt_pre_);
		dec_pkt_pre_ = nullptr;
	}
    if (dec_ctx_pre_) {
		if (dec_api_pre_) {
			dec_api_pre_->reset(dec_ctx_pre_);
			dec_api_pre_ = nullptr;
		}
        mpp_destroy(dec_ctx_pre_);
        dec_ctx_pre_ = nullptr;
    }
}

MPP_RET RKFileFrameReader::InitDecode() {
    RK_U32 need_split = 0;
    MppPollType block = MPP_POLL_BLOCK;
    RK_U32 fast_en = 0;
    MppApi *dec_mpi = NULL;
    MppCtx dec_ctx = NULL;
    MppFrameFormat format = MPP_FMT_YUV420SP;
    // RK_U32 fbc_en = 0;
    MPP_RET ret = MPP_OK;

    // mpp_env_get_u32("fbc_dec_en", &fbc_en, 0);
    // mpp_env_get_u32("fast_en", &fast_en, 0);

    ret = mpp_create(&dec_ctx, &dec_mpi);
    if (ret != MPP_OK) {
        jerror("mpp_create failed ret %d", ret);
        return ret;
    }
    dec_api_pre_ = dec_mpi;
    dec_ctx_pre_ = dec_ctx;

    ret = dec_mpi->control(dec_ctx, MPP_DEC_SET_PARSER_SPLIT_MODE, &need_split);
    if (ret) {
        jerror("control MPP_DEC_SET_PARSER_SPLIT_MODE failed ret %d", ret);
        return ret;
    }

    // ret = dec_mpi->control(dec_ctx, MPP_DEC_SET_PARSER_FAST_MODE, &fast_en);
    // if (ret) {
    //     jerror("control MPP_DEC_SET_PARSER_FAST_MODE failed ret %d", ret);
    //     return ret;
    // }

    // ret = dec_mpi->control(dec_ctx, MPP_SET_INPUT_TIMEOUT, (MppParam)&block);
    // if (ret) {
    //     jerror("control MPP_SET_INPUT_TIMEOUT failed ret %d", ret);
    //     return ret;
    // }

    // block = MPP_POLL_BLOCK;
    // ret = dec_mpi->control(dec_ctx, MPP_SET_OUTPUT_TIMEOUT, (MppParam)&block);
    // if (ret) {
    //     jerror("control MPP_SET_OUTPUT_TIMEOUT failed ret %d", ret);
    //     return ret;
    // }

    ret = mpp_init(dec_ctx, MPP_CTX_DEC, coding_type_);
    if (ret) {
        jerror("mpp init failed ret %d", ret);
        return ret;
    }

    ret = dec_mpi->control(dec_ctx, MPP_DEC_SET_OUTPUT_FORMAT, &format);
    if (ret) {
        jerror("control MPP_DEC_SET_OUTPUT_FORMAT failed ret %d", ret);
        return ret;
    }

    jinfo("RKFileFrameReader mpp init success");
    return ret;
}

void RKFileFrameReader::run() {
    jinfo("RKFileFrameReader run");
    if (loop_thread_.isRunning()) {
        loop_thread_.stop(true);
        loop_thread_.join();
    }
    loop_thread_.start();
    running_= true;
    // loop_thread_.loop()->runInLoop([this] {
    //     jinfo("RKFileFrameReader read_jpeg_file run in loop");
    if (file_type_ == FILE_JPEG_TYPE)
        read_jpeg_file();
    // });

    if (eos_sent_) {
        loop_thread_.loop()->runInLoop([this] {
            jinfo("send EOS frame");
        });
    } else {
        int first = true;
        loop_thread_.loop()->setInterval(40, [this, &first](hv::TimerID id) {
            MppApi *mpi = dec_api_pre_;
            MppCtx dec_ctx = dec_ctx_pre_;
            if (!buf_ || !mpi || !dec_ctx || file_size_ == 0 || !running_) {
                // jerror("RKFileFrameReader buf_ is null");
                return;
            }
            MppFrame frame = frameBufferManager_->getFreeFrame(width_, height_);
            if (frame == NULL) {
                jerror("RKFileFrameReader getFreeFrame failed");
                return;
            }
            auto ret = mpp_packet_init_with_buffer(&dec_pkt_pre_, buf_);
            if (ret) {
                jerror("mpp_packet_init failed ret %d", ret);
                return;
            }
            MppTask task = NULL;
            MppPacket packet = dec_pkt_pre_;
            // mpp_packet_set_eos(packet);
            ret = mpi->poll(dec_ctx, MPP_PORT_INPUT, MPP_POLL_BLOCK);
            if (ret) {
                jerror("%p mpp input poll failed", dec_ctx);
                return;
            }
            ret = mpi->dequeue(dec_ctx, MPP_PORT_INPUT, &task);  /* input queue */
            if (ret) {
                jerror("%p mpp task input dequeue failed", dec_ctx);
                return;
            }
            assert(task);

            mpp_task_meta_set_packet(task, KEY_INPUT_PACKET, packet);
            mpp_task_meta_set_frame (task, KEY_OUTPUT_FRAME,  frame);
            ret = mpi->enqueue(dec_ctx, MPP_PORT_INPUT, task);  /* input queue */
            if (ret) {
                jerror("%p mpp task input enqueue failed", dec_ctx);
                return;
            }
            /* poll and wait here */
            ret = mpi->poll(dec_ctx, MPP_PORT_OUTPUT, MPP_POLL_BLOCK);
            if (ret) {
                jerror("%p mpp output poll failed", dec_ctx);
                return;
            }
            ret = mpi->dequeue(dec_ctx, MPP_PORT_OUTPUT, &task); /* output queue */
            if (ret) {
                jerror("%p mpp task output dequeue failed", dec_ctx);
                return;
            }
            if (task) {
                MppFrame frame_out = NULL;
                mpp_task_meta_get_frame(task, KEY_OUTPUT_FRAME, &frame_out);
                if (frame) {
                    RK_U32 buf_size = mpp_frame_get_buf_size(frame);
                    auto width = mpp_frame_get_width(frame);
                    auto height = mpp_frame_get_height(frame);
                    if (mpp_frame_get_info_change(frame)) {
                        jinfo("RKFileFrameReader mpp frame info changed: size %d -> %dx%d", buf_size, width, height);
                    }
                    auto out_frame = std::make_shared<WrapRKMppFrame>(frame);
                    out_frame->setWidth(width);
                    out_frame->setHeight(height);
                    out_frame->setWidth(MPP_ALIGN(width, 16));
                    out_frame->setHeight(MPP_ALIGN(height, 16));
                    out_frame->setFormatStr("nv12");
                    deliverFrame(out_frame);
                }
                /* output queue */
                ret = mpi->enqueue(dec_ctx, MPP_PORT_OUTPUT, task);
                if (ret)
                    jerror("%p mpp task output enqueue failed", dec_ctx);
            }

            ret = mpi->dequeue(dec_ctx, MPP_PORT_INPUT, &task);  /* input queue */
            if (ret) {
                jerror("%p mpp task input dequeue failed", dec_ctx);
                return;
            }

            assert(task);
            if (task) {
                MppPacket packet_out = NULL;

                mpp_task_meta_get_packet(task, KEY_INPUT_PACKET, &packet_out);

                if (!packet_out || packet_out != packet)
                    jerror("mismatch packet %p -> %p", packet, packet_out);

                mpp_packet_deinit(&packet_out);
                dec_pkt_pre_ = NULL;
                /* input empty task back to mpp to maintain task status */
                ret = mpi->enqueue(dec_ctx, MPP_PORT_INPUT, task);
                if (ret)
                    jerror("%p mpp task input enqueue failed", dec_ctx);
            }
        });
    }
}

void RKFileFrameReader::read_jpeg_file() {
    if (!ifs_.good() || file_size_ <= 0 || !group_) return;
    MppBuffer hw_buf = NULL;
    mpp_buffer_get(group_, &hw_buf, file_size_);
    if (!hw_buf)
        return;
    int8_t *buf_ptr = (int8_t *)mpp_buffer_get_ptr(hw_buf);
    if (!buf_ptr)
        return;
    size_t read_size = 0;
    ifs_.seekg(0, std::ios_base::beg);
    while (read_size < file_size_) {
        ifs_.read((char *)buf_ptr + read_size, file_size_ - read_size);
        if (!ifs_.good()) break;
        read_size += ifs_.gcount();
    }
    buf_ = hw_buf;
    buf_ptr_ = buf_ptr;
}
} // namespace panocom