#include "QCuVideoWidget.h"
#include <QResizeEvent>

using namespace panocom;

QCuVideoWidget::QCuVideoWidget(QWidget *parent): QOpenGLWidget(parent), m_index(-1), m_renderer(nullptr)
{
    jinfo("1 QCuVideoWidget %d", m_index);
}

QCuVideoWidget::QCuVideoWidget(int index, QWidget *parent): QOpenGLWidget(parent), m_index(index), m_renderer(nullptr)
{
    jinfo("2 QCuVideoWidget %d", m_index);
}

void QCuVideoWidget::initializeGL()
{
    jinfo("initializeGL %d x %d", width(), height());
    createRender();
}

void QCuVideoWidget::paintGL()
{
    //jinfo("paintGL");
    //createRender();
    if (m_renderer) m_renderer->display();
}

void QCuVideoWidget::resizeGL(int w, int h)
{
    jinfo("resizeGL %d x %d", w, h);
    //createRender();
    if (m_renderer)
    {
        m_renderer->resizeGL(w, h);
        m_renderer->moveVideoFrameRender(m_render, 0, 0, 0, w, h);
    }
}

void QCuVideoWidget::resizeEvent(QResizeEvent *event)
{
}

void QCuVideoWidget::showEvent(QShowEvent *event)
{
}

void QCuVideoWidget::createRender()
{
    if (m_renderer == nullptr && m_index != -1)
    {
        int w = width();
        int h = height();
        jinfo("createRender %d x %d", w, h);
        m_renderer = new QCuRender(this);
        for (size_t i = 0; i < 4; i++)
        {
            for (size_t j = 0; j < 4; j++)
            {
                int index = i * 4 + j;
                nlohmann::json js;
                js["dev"] = index;
                js["gpuIndex"] = index < 8 ? 0 : 1;
                //js["cudaId"] = index;
                auto capturer = VideoFrameCapturer::CreateVideoCapturer("CuIPCFrameCapturer", js.dump());
                capturer->setGroupId(1);
                auto render = m_renderer->createVideoFrameRender(index, 0, js["gpuIndex"], w / 4 * i, h / 4 * j, 0, w / 4, h / 4);
                capturer->addVideoDestination(render);
                m_capturers.push_back(capturer);
            }
        }
    }
}