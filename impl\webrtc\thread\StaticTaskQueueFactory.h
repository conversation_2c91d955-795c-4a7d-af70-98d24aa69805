// Copyright (C) <2020> Intel Corporation
//
// SPDX-License-Identifier: Apache-2.0

#ifndef RTC_ADAPTER_THREAD_STATIC_TASK_QUEUE_FACTORY_
#define RTC_ADAPTER_THREAD_STATIC_TASK_QUEUE_FACTORY_

#include <memory>

#include "api/task_queue/task_queue_factory.h"

namespace panocom {
// 区分编码线程和解码线程，分别使用不同的队列
enum class TaskQueueType {
    kCall,
    kDecoding,
    kEncoding,
};
std::unique_ptr<webrtc::TaskQueueFactory> createStaticTaskQueueFactory(TaskQueueType type = TaskQueueType::kCall);

}  // namespace webrtc

#endif  // RTC_ADAPTER_THREAD_STATIC_TASK_QUEUE_FACTORY_
