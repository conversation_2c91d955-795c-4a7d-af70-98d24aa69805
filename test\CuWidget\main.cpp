#include "widget.h"
#include <QApplication>
#include <QEvent>
#include <ljcore/jlog.h>
#include <signal.h>

extern int mmi_print_state;

int main(int argc, char *argv[])
{
     signal(SIGSEGV, [](int) {
        jlog_uninit();
        (void) signal(SIGSEGV, SIG_DFL);  
    }); // 设置退出信号

    signal(SIGINT, [](int) {
        jlog_uninit();
        (void) signal(SIGINT, SIG_DFL);  
    }); // 设置退出信号

    signal(SIGTERM, [](int) {
        jlog_uninit();
        (void) signal(SIGTERM, SIG_DFL);  
    });
    jlog_init(nullptr);
    mmi_print_state = 1;
    QApplication a(argc, argv);
    Widget w;
    w.show();
    return a.exec();
}
