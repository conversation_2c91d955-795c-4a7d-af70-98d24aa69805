#ifndef P_RtcAudioFrameLec_h
#define P_RtcAudioFrameLec_h
#include <fstream>

#include "AudioFrameProcesser.h"
#include <aecinterface.h>
#include <hv/EventLoopThread.h>
#include <resampler/resampler.h>
#include <resampler/push_resampler.h>
#include <ptoolkit/bytebuffer.h>

namespace panocom
{
    class Frame;
    class RtcAudioFrameLec : public AudioFrameProcesser
    {
    public:
        RtcAudioFrameLec(const std::string& jsonParams);
        ~RtcAudioFrameLec() override;

        void onFrame(const std::shared_ptr<Frame>& frame) override;

        void start() override;
        void stop() override;
    private:
        std::shared_ptr<void> lec_;
        int fmt_;
        int samplerate_;
        hv::EventLoopThread thread_;
        FILE* pf_ = nullptr;
        bool first_ = true;
        bool first_farend_ = true;

#ifdef WEBRTC_RESAMPLE_ANOTHER
        nswebrtc::PushResampler<int16_t> resampler_;
        nswebrtc::PushResampler<int16_t> resampler_farend_;
#else
        nswebrtc::Resampler resampler_;
        nswebrtc::Resampler resampler_farend_;
#endif       
        bool initResampler_ = false;
        bool initResampler_farend_ = false;
        int source_samplerate_;
        int source_farend_samplerate_;

        bool pcm_logging_; //是否开启数据打印，默认是false;
        bool aec_on_;      //是否开启回声消除aec,默认是true; 
        bool agc_on_;      //是否开启自动增益agc,默认是false;
        bool ns_on_;       //是否开启降噪,默认是true;
        bool cng_on_;      //是否打开舒适噪音,默认是false;
        bool howlingSuppress_on_; //是否打开防啸叫，默认是false;

        int16_t* lec_input_buf_;

        std::ofstream ofs_;
        std::ofstream ofs_1;
    };
}

#endif