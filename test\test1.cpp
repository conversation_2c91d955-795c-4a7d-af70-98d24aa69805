#include "MediaPipeline.h"
#include <json.hpp>
#include <stdio.h>
#include <ljcore/jlog.h>
#include <hv/EventLoop.h>

using namespace panocom;

int main()
{
    auto loop = std::make_shared<hv::EventLoop>();
    jlog_init(nullptr);
    nlohmann::json j;
    auto capturer = VideoFrameCapturer::CreateVideoCapturer("V4L2VideoFrameCapturer", j.dump());
    j.clear();
    j["codec"] = "mjpeg_cuvid";
    auto decoder = VideoFrameDecoder::CreateVideoDecoder("FfVideoDecoder", j.dump());
    j.clear();
    j["codec"] = "h264_nvenc";
    auto encoder = VideoFrameEncoder::CreateVideoEncoder("FfVideoEncoder", j.dump());
    j.clear();
    j["payloadType"] = 96;
    j["hz"] = 90000;
    j["dst"] = nlohmann::json::array();
    nlohmann::json dst;
    dst["ip"] = "127.0.0.1";
    dst["port"] = 4444;
    j["dst"].push_back(dst);
    auto rtpSender = RtpEngine::CreateRTPEngine("JRtpEngine", j.dump());
    capturer->addVideoDestination(decoder);
    decoder->addVideoDestination(encoder);
    encoder->addVideoDestination(rtpSender);
    loop->run();
    return 0;
}