#ifndef P_PCMFileFrameSource_h
#define P_PCMFileFrameSource_h

#include "file/FileFramePipeline.h"
#include <hv/EventLoopThread.h>

namespace panocom
{
    class PCMFileFrameSource : public FileFramePipeline
    {
    public:
        PCMFileFrameSource(const std::string& jsonParams);
        ~PCMFileFrameSource() override;
        void start() override;
        void stop() override;
    private:
        void readFile(const std::string& path);
    private:
        FILE* pf_ = nullptr;
        hv::EventLoopThread thread_;
        std::vector<uint8_t> buffer_;
        int ptime_;
        int ptimeDataLen_;
        std::string path_;
        int index_;
        int samplerate_ = 0;
        int chn_ = 0;
        int fmt_ = 0;
        bool loop_ = false;
    };
}

#endif
