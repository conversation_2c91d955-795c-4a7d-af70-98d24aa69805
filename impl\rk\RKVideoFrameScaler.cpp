#include "RKVideoFrameScaler.h"

#include <json.hpp>
#include <rga/im2d.hpp>
#include <rga/rga.h>
#include <rockchip/mpp_frame.h>

#include "drm_utils.h"

namespace panocom
{

namespace {
MppFrameFormat Fmtstr2Mppfmt(const std::string &fmt_str) {
    MppFrameFormat fmt = MPP_FMT_YUV420SP;
    if (strcasecmp(fmt_str.c_str(), "nv12") == 0) {
        fmt = MPP_FMT_YUV420SP;
    } else if (strcasecmp(fmt_str.c_str(), "nv21") == 0) {
        fmt = MPP_FMT_YUV420SP_VU;
    } else if (strcasecmp(fmt_str.c_str(), "nv24") == 0) {
        fmt = MPP_FMT_YUV422SP;
    } else if (strcasecmp(fmt_str.c_str(), "nv42") == 0) {
        fmt = MPP_FMT_YUV422SP_VU;
    } else if (strcasecmp(fmt_str.c_str(), "I420") == 0) {
        fmt = MPP_FMT_YUV420P;
    } else if (strcasecmp(fmt_str.c_str(), "bgr3") == 0) {
        fmt = MPP_FMT_BGR888;
    }
    return fmt;
}

int32_t Mppfmt2Rgafmt(MppFrameFormat fmt) {
    int32_t res = RK_FORMAT_YCbCr_420_SP;
    switch (fmt)
    {
    case MPP_FMT_YUV420SP:
        res = RK_FORMAT_YCbCr_420_SP;
        break;
    case MPP_FMT_YUV420SP_VU:
        res = RK_FORMAT_YCrCb_420_SP;
        break;  
    case MPP_FMT_YUV422SP:
        res = RK_FORMAT_YCbCr_422_SP;
        break; 
    case MPP_FMT_YUV422SP_VU:
        res = RK_FORMAT_YCrCb_420_SP;
        break;
    case MPP_FMT_YUV420P:
        res = RK_FORMAT_YCbCr_420_P;
        break;
    case MPP_FMT_BGR888:
        res = RK_FORMAT_BGR_888;
        break;
    default:
        break;
    }
    return res;
}
}

RKVideoFrameScaler::RKVideoFrameScaler(const std::string &jsonParams)
    : width_(1920)
    , height_(1080)
    , pixel_format_("nv12") {
    name_ = "RKVideoFrameScaler";
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    if (j.contains("width")) {
        width_ = j["width"];
    }
    if (j.contains("height")) {
        height_ = j["height"];
    }
    if (j.contains("pixelFormat")) {
        pixel_format_ = j["pixelFormat"];
    }
    frameBufferManager_ = std::make_shared<RKBufferManager>(8);
    thread_.start();
}

RKVideoFrameScaler::~RKVideoFrameScaler()
{
}

void RKVideoFrameScaler::onFrame(const std::shared_ptr<Frame> &frame) {
    if (!thread_.isRunning())
        return;
    thread_.loop()->runInLoop([frame, this]() {
        if (frame->getFrameFormat() == FRAME_FORMAT_RK) {
            auto wrap_frame = std::dynamic_pointer_cast<WrapRKMppFrame>(frame);
            MppFrame src_frame = wrap_frame->InnerFrame();

            uint32_t width = mpp_frame_get_width(src_frame);
            uint32_t height = mpp_frame_get_height(src_frame);
            MppFrameFormat src_fmt = mpp_frame_get_fmt(src_frame);
            MppFrameFormat dst_fmt = Fmtstr2Mppfmt(pixel_format_);
            if (width != width_ || height != height_ || src_fmt != dst_fmt) {
                auto out_frame = frameBufferManager_->getFreeFrame(width_, height_);
                if (out_frame) {
                    auto dst_buffer = mpp_frame_get_buffer(out_frame);
                    int dst_fd = mpp_buffer_get_fd(dst_buffer);
                    auto src_buffer = mpp_frame_get_buffer(src_frame);
                    int src_fd = mpp_buffer_get_fd(src_buffer);
                    DrmUtils::rga_copy(dst_fd, width_, height_, Mppfmt2Rgafmt(dst_fmt), src_fd, width, height, Mppfmt2Rgafmt(src_fmt));
                    auto wrap_out_frame = std::make_shared<WrapRKMppFrame>(out_frame);
                    wrap_out_frame->setGroupId(getGroupId());
                    deliverFrame(wrap_out_frame);
                }
            } else {
                deliverFrame(frame);
            }
        }
    });
}
} // namespace panocom
