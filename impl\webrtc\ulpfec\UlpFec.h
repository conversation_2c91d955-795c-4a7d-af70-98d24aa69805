#ifndef P_ULPFEC_h
#define P_ULPFEC_h

#include "ulpfec/ulpfec_generator.h"
#include "ulpfec/module_fec_types.h"
#include "ulpfec/byte_io.h"
#include "ulpfec/ulpfec_receiver.h"
#include "ulpfec/rtp_headers.h"

using namespace webrtc;

constexpr size_t kDefaultPacketSize = 1500;

namespace panocom
{
    class RtpPacket
    {
    public:
        RtpPacket() : RtpPacket(kDefaultPacketSize) {}
        RtpPacket(size_t capacity)
            : capacity_(capacity), buffer_(new uint8_t[capacity])
        {
        }
        RtpPacket(const RtpPacket &) = default;
        ~RtpPacket() {}
        void SetPayloadType(uint8_t pt)
        {
            buffer_[1] = (buffer_[1] & 0x80) | pt;
        }
        void SetTimestamp(uint32_t timestamp)
        {
            ByteWriter<uint32_t>::WriteBigEndian(&buffer_[4], timestamp);
        }
        void SetSequenceNumber(uint16_t seq_no)
        {
            ByteWriter<uint16_t>::WriteBigEndian(&buffer_[2], seq_no);
        }
        void SetSsrc(uint32_t ssrc)
        {
            ByteWriter<uint32_t>::WriteBigEndian(&buffer_[8], ssrc);
        }
        void GetRtpHeader(RTPHeader &header)
        {
            header.markerBit = (buffer_[1] & 0x80) != 0;
            header.payloadType = buffer_[1] & 0x7f;
            header.sequenceNumber = ByteReader<uint16_t>::ReadBigEndian(&buffer_[2]);
            header.timestamp = ByteReader<uint32_t>::ReadBigEndian(&buffer_[4]);
            header.ssrc = ByteReader<uint32_t>::ReadBigEndian(&buffer_[8]);
            header.headerLength = 12;
        }
        void SetData(const uint8_t *data, size_t length)
        {
            if (data == nullptr || length > capacity_)
                return;
            memcpy(buffer_.get(), data, length);
            length_ = length;
        }
        void SetLength(size_t length) { length_ = length; }
        uint8_t *data()
        {
            return buffer_.get();
        }
        size_t length()
        {
            return length_;
        }
        static void RtpHeaderParse(RTPHeader &header, const uint8_t *buffer, size_t size)
        {
            header.markerBit = (buffer[1] & 0x80) != 0;
            header.payloadType = buffer[1] & 0x7f;
            header.sequenceNumber = ByteReader<uint16_t>::ReadBigEndian(&buffer[2]);
            header.timestamp = ByteReader<uint32_t>::ReadBigEndian(&buffer[4]);
            header.ssrc = ByteReader<uint32_t>::ReadBigEndian(&buffer[8]);
            header.headerLength = 12;
        }
    private:
        size_t length_{0};
        size_t capacity_;
        std::unique_ptr<uint8_t[]> buffer_;
    };
}

#endif