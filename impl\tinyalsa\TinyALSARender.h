// created by gyj 2024-7-3
#ifndef P_TinyALSARender_h
#define P_TinyALSARender_h

#include "AudioFrameRender.h"
#include "Frame.h"
#include <tinyalsa/pcm.h>
#include <hv/EventLoopThread.h>
#include <ptoolkit/bytebuffer.h>
#include <resampler/resampler.h>
#include <resampler/push_resampler.h>
namespace panocom
{
    class TinyALSARender: public AudioFrameRender
    {
    public:
        TinyALSARender(const std::string& jsonParams);
        ~TinyALSARender() override;

        void onFrame(const std::shared_ptr<Frame>& frame) override;

        void start() override;
        void stop() override;

    private:
        std::shared_ptr<Frame> raiseChn(const std::shared_ptr<Frame>& frame);
    private:
        unsigned int card_ = 0;
        unsigned int dev_ = 0;
        int samplerate_ = 16000;
        int chn_ = 1;
        struct pcm *pcm_ = nullptr;
        hv::EventLoopThread thread_;
        ByteBuffer bbuf_;
        int writebufferLen_;

#ifdef WEBRTC_RESAMPLE_ANOTHER
        nswebrtc::PushResampler<int16_t> resampler_;
#else
        nswebrtc::Resampler resampler_;
#endif       
        bool initResampler_ = false;
        int source_samplerate_;
    };
}

#endif