#ifndef P_QCuVideoWidget_h
#define P_QCuVideoWidget_h

#include "../QCuRender.h"
#include "MediaPipeline.h"
#include <json.hpp>
#include <ljcore/jlog.h>

namespace panocom
{
    class QCuVideoWidget : public QOpenGLWidget
    {
    public:
        QCuVideoWidget(QWidget *parent = nullptr);
        QCuVideoWidget(int index, QWidget *parent = nullptr);
        void setIndex(int index);
        virtual void initializeGL() override;
        virtual void paintGL() override;
        virtual void resizeGL(int w, int h) override;

    private:
        void createRender();

    private:
        QCuRender *m_renderer = nullptr;
        int m_index = 0;
        std::shared_ptr<VideoFrameCapturer> m_capturer;
        std::shared_ptr<VideoFrameRender> m_render;
        //std::shared_ptr<VideoFrameDecoder> m_decoder;
    };
}

#endif
