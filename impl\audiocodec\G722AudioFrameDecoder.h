#ifndef IMPL_AUDIO_G722AUDIOFRAMEDECODER_H_
#define IMPL_AUDIO_G722AUDIOFRAMEDECODER_H_
#include <future>
#include <mutex>
#include <queue>
#include <condition_variable>
#include "AudioFrameDecoder.h"
#include "Frame.h"
#ifdef __cplusplus
extern "C" {
#endif
#include "g722_enc_dec.h"
#ifdef __cplusplus
}
#endif
// #include <fstream>
namespace panocom
{
class G722AudioFrameDecoder : public AudioFrameDecoder
{
public:
	G722AudioFrameDecoder(const std::string& jsonParams);
	~G722AudioFrameDecoder();
	void onFrame(const std::shared_ptr<Frame> &f) override;
private:
	FrameFormat fmt_;
	int rate_;	// bps
	G722DecoderState *state_;
	int16_t* decoded_buf_;
	// std::ofstream ofs_;

	bool running_;
    std::queue<std::shared_ptr<Frame>> frame_queue_;
    std::mutex mutex_;
	std::future<bool> decode_future_;
	std::condition_variable queue_available_;
};



} // namespace panocom

#endif