#ifndef UART_UARTWRITER_H_
#define UART_UARTWRITER_H_

#include "Frame.h"
#include "AudioFrameRender.h"
#include <hv/EventLoopThread.h>

namespace panocom
{
class UartWriter : public AudioFrameRender
{
private:
    /* data */

public:
    UartWriter(const std::string& jsonParams);
    virtual ~UartWriter();

    void onFrame(const std::shared_ptr<Frame>& frame) override;
    void start() override;
    void stop() override;
    int updateParam(const std::string& jsonParams) override;
private:
    int dev_;

    int32_t bitrate_;
    int32_t samplerate_;
    int32_t channel_;
    int16_t codec_;
    int16_t gain_;
    int8_t ptime_;

    uint8_t *encoded_buf_;

    hv::EventLoopThread writeThread_;
};
    
} // namespace panocom


#endif