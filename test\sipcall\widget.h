#ifndef WIDGET_H
#define WIDGET_H

#include <QWidget>
#include <QTimer>

#include <resip/stack/TransactionUser.hxx>
#include <resip/stack/EventStackThread.hxx>
#include <rutil/SelectInterruptor.hxx>
#include <resip/stack/UdpTransport.hxx>
#include <resip/dum/MasterProfile.hxx>
#include <resip/dum/DumShutdownHandler.hxx>
#include <resip/dum/DialogUsageManager.hxx>
#include <resip/dum/InviteSessionHandler.hxx>
#include <resip/dum/DialogSetHandler.hxx>
#include <resip/dum/OutOfDialogHandler.hxx>
#include <resip/dum/RedirectHandler.hxx>
#include <resip/dum/SubscriptionHandler.hxx>
#include <resip/dum/RegistrationHandler.hxx>
#include <resip/dum/ServerRegistration.hxx>
#include <resip/dum/ServerPagerMessage.hxx>
#include <resip/dum/ClientPagerMessage.hxx>
#include <resip/dum/PagerMessageHandler.hxx>
#include <resip/dum/KeepAliveManager.hxx>
#include <resip/dum/InMemorySyncRegDb.hxx>
#include <rutil/Mutex.hxx>
#include <rutil/Logger.hxx>

#include <Util/logger.h>
#define RESIPROCATE_SUBSYSTEM resip::Subsystem::SIP
#include "MediaManagerInterface.h"

#include <grpcpp/grpcpp.h>
#include "../mmi.grpc.pb.h"

using grpc::Channel;
using grpc::ClientContext;
using grpc::Status;
using mmi::CreateSessionReply;
using mmi::CreateSessionRequest;
using mmi::GetLocalSDPReply;
using mmi::GetLocalSDPRequest;
using mmi::MediaManager;
using mmi::ReleaseSessionReply;
using mmi::ReleaseSessionRequest;
using mmi::SetRemoteSDPReply;
using mmi::SetRemoteSDPRequest;
using namespace panocom;
using namespace resip;

QT_BEGIN_NAMESPACE
namespace Ui { class Widget; }
QT_END_NAMESPACE

class MediaManagerClient {
 public:
  MediaManagerClient(std::shared_ptr<Channel> channel)
      : stub_(MediaManager::NewStub(channel)) {}


  int CreateSession(const std::string& id, const std::string& jsonParams = "") {

    CreateSessionRequest request;
    request.set_id(id);
    request.set_jsonparams(jsonParams);

    // Container for the data we expect from the server.
    CreateSessionReply reply;

    // Context for the client. It could be used to convey extra information to
    // the server and/or tweak certain RPC behaviors.
    ClientContext context;

    // The actual RPC.
    Status status = stub_->CreateSession(&context, request, &reply);

    // Act upon its status.
    if (status.ok()) {
      return reply.ret();
    } else {
      std::cout << status.error_code() << ": " << status.error_message()
                << std::endl;
      return "RPC failed";
    }
  }

  int ReleaseSession(const std::string& id) {

    ReleaseSessionRequest request;
    request.set_id(id);

    // Container for the data we expect from the server.
    ReleaseSessionReply reply;

    // Context for the client. It could be used to convey extra information to
    // the server and/or tweak certain RPC behaviors.
    ClientContext context;

    // The actual RPC.
    Status status = stub_->ReleaseSession(&context, request, &reply);

    // Act upon its status.
    if (status.ok()) {
      return reply.ret();
    } else {
      std::cout << status.error_code() << ": " << status.error_message()
                << std::endl;
      return "RPC failed";
    }
  }

  int GetLocalSDP(const std::string& id, std::string& sdp, const std::string& jsonParams = "") {

    GetLocalSDPRequest request;
    request.set_id(id);
    request.set_jsonparams(jsonParams);

    // Container for the data we expect from the server.
    GetLocalSDPReply reply;

    // Context for the client. It could be used to convey extra information to
    // the server and/or tweak certain RPC behaviors.
    ClientContext context;

    // The actual RPC.
    Status status = stub_->GetLocalSDP(&context, request, &reply);

    // Act upon its status.
    if (status.ok()) {
      sdp = reply.sdp();
      return reply.ret();
    } else {
      std::cout << status.error_code() << ": " << status.error_message()
                << std::endl;
      return "RPC failed";
    }
  }

  int SetRemoteSDP(const std::string& id, const std::string& remoteSdp, std::string& sdp, const std::string& jsonParams = "") {

    SetRemoteSDPRequest request;
    request.set_id(id);
    request.set_sdp(remoteSdp);
    request.set_jsonparams(jsonParams);

    // Container for the data we expect from the server.
    SetRemoteSDPReply reply;

    // Context for the client. It could be used to convey extra information to
    // the server and/or tweak certain RPC behaviors.
    ClientContext context;

    // The actual RPC.
    Status status = stub_->SetRemoteSDP(&context, request, &reply);

    // Act upon its status.
    if (status.ok()) {
      sdp = reply.sdp();
      return reply.ret();
    } else {
      std::cout << status.error_code() << ": " << status.error_message()
                << std::endl;
      return "RPC failed";
    }
  }

 private:
  std::unique_ptr<MediaManager::Stub> stub_;
};

class Widget : public QWidget, public resip::InviteSessionHandler
{
    Q_OBJECT

public:
    Widget(QWidget *parent = nullptr);
    ~Widget();

    // Invite Session Handler /////////////////////////////////////////////////////
    virtual void onNewSession(resip::ClientInviteSessionHandle h, resip::InviteSession::OfferAnswerType oat, const resip::SipMessage &msg);
    virtual void onNewSession(resip::ServerInviteSessionHandle h, resip::InviteSession::OfferAnswerType oat, const resip::SipMessage &msg);
    virtual void onFailure(resip::ClientInviteSessionHandle h, const resip::SipMessage &msg);
    virtual void onEarlyMedia(resip::ClientInviteSessionHandle, const resip::SipMessage &, const resip::SdpContents &);
    virtual void onProvisional(resip::ClientInviteSessionHandle, const resip::SipMessage &msg);
    virtual void onConnected(resip::ClientInviteSessionHandle h, const resip::SipMessage &msg);
    virtual void onConnected(resip::InviteSessionHandle, const resip::SipMessage &msg);
    virtual void onStaleCallTimeout(resip::ClientInviteSessionHandle);
    virtual void onTerminated(resip::InviteSessionHandle h, resip::InviteSessionHandler::TerminatedReason reason, const resip::SipMessage *msg);
    virtual void onRedirected(resip::ClientInviteSessionHandle, const resip::SipMessage &msg);
    virtual void onAnswer(resip::InviteSessionHandle, const resip::SipMessage &msg, const resip::SdpContents &);
    virtual void onOffer(resip::InviteSessionHandle handle, const resip::SipMessage &msg, const resip::SdpContents &offer);
    virtual void onOfferRequired(resip::InviteSessionHandle, const resip::SipMessage &msg);
    virtual void onOfferRejected(resip::InviteSessionHandle, const resip::SipMessage *msg);
    virtual void onOfferRequestRejected(resip::InviteSessionHandle, const resip::SipMessage &msg);
    virtual void onRemoteSdpChanged(resip::InviteSessionHandle, const resip::SipMessage &msg, const resip::SdpContents &sdp);
    virtual void onInfo(resip::InviteSessionHandle, const resip::SipMessage &msg);
    virtual void onInfoSuccess(resip::InviteSessionHandle, const resip::SipMessage &msg);
    virtual void onInfoFailure(resip::InviteSessionHandle, const resip::SipMessage &msg);
    virtual void onRefer(resip::InviteSessionHandle, resip::ServerSubscriptionHandle, const resip::SipMessage &msg);
    virtual void onReferAccepted(resip::InviteSessionHandle, resip::ClientSubscriptionHandle, const resip::SipMessage &msg);
    virtual void onReferRejected(resip::InviteSessionHandle, const resip::SipMessage &msg);
    virtual void onReferNoSub(resip::InviteSessionHandle, const resip::SipMessage &msg);
    virtual void onMessage(resip::InviteSessionHandle, const resip::SipMessage &msg);
    virtual void onMessageSuccess(resip::InviteSessionHandle, const resip::SipMessage &msg);
    virtual void onMessageFailure(resip::InviteSessionHandle, const resip::SipMessage &msg);
    virtual void onForkDestroyed(resip::ClientInviteSessionHandle);

private slots:
    void on_pushButton_clicked();
    void on_hangup_clicked();

private:
    void initStack();

private:
    Ui::Widget *ui;
    QTimer mTimer;
    std::shared_ptr<resip::MasterProfile> mProfile;
    resip::FdPollGrp *mPollGrp;
    resip::EventThreadInterruptor *mEventInterruptor;
    resip::SipStack mStack;
    resip::DialogUsageManager mDum;
    resip::EventStackThread mStackThread;
    
    MediaManagerInterface mMmi;
    std::shared_ptr<MediaManagerClient> mClient;
    bool mIncommingCall;
    InviteSessionHandle mH;
};
#endif // WIDGET_H
