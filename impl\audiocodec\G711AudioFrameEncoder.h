// created by gyj 2024-4-1
#ifndef P_G711AudioFrameEncoder_h
#define P_G711AudioFrameEncoder_h

#include "AudioFrameEncoder.h"
#include "Frame.h"
#include <resampler/resampler.h>
#include <resampler/push_resampler.h>
#include<stdio.h>

namespace panocom
{
    class G711AudioFrameEncoder : public AudioFrameEncoder
    {
    public:
        G711AudioFrameEncoder(const std::string& jsonParams);
        ~G711AudioFrameEncoder() override;

        void onFrame(const std::shared_ptr<Frame> &f) override;
    private:
        FrameFormat fmt_;
        int samplerate_;
        FILE* pf_ = nullptr;
        bool first_ = true;
#ifdef WEBRTC_RESAMPLE_ANOTHER
        nswebrtc::PushResampler<int16_t> resampler_;
#else
        nswebrtc::Resampler resampler_;
#endif       
        bool initResampler_ = false;
        int source_samplerate_;

        int debug_mode_;
        // FILE* file;
    };
}

#endif