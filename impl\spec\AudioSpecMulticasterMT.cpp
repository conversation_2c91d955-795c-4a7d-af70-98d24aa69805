#include "AudioSpecMulticasterMT.h"
#include "Frame.h"
#include "Utils.h"
#include <json.hpp>
#include <ljcore/jlog.h>

using namespace panocom;

#ifdef __cplusplus
extern "C" {
#endif

enum {
  kAecNlpConservative = 0,
  kAecNlpModerate,
  kAecNlpAggressive
};

enum {
  kAecFalse = 0,
  kAecTrue
};

typedef struct {
  int16_t nlpMode;      // default kAecNlpModerate
  int16_t skewMode;     // default kAecFalse
  int16_t metricsMode;  // default kAecFalse
  int delay_logging;    // default kAecFalse
} AecConfig;

int32_t rtc_aec_create(void** aecInst);
int32_t rtc_aec_free(void* aecInst);
int32_t rtc_aec_init(void* aecInst, int32_t sampFreq);
int32_t rtc_aec_buffer_farend(void* aecInst,
		const int16_t* farend,
		int16_t nrOfSamples);
int32_t rtc_aec_process(void* aecInst,
		const int16_t* nearend,
		int16_t* out,
		int16_t nrOfSamples);
int rtc_aec_set_config(void* aecInst, AecConfig config);

#ifdef __cplusplus
} // extern "C"
#endif

#if 0
int32_t rtc_aec_create(void** aecInst)
{
    return 0;
}

int32_t rtc_aec_free(void* aecInst)
{
    return 0;
}

int32_t rtc_aec_init(void* aecInst, int32_t sampFreq)
{
    return 0;
}

int32_t rtc_aec_buffer_farend(void* aecInst,
		const int16_t* farend,
		int16_t nrOfSamples)
{
    return 0;
}

int32_t rtc_aec_process(void* aecInst,
		const int16_t* nearend,
		int16_t* out,
		int16_t nrOfSamples)
{
    return 0;
}

int rtc_aec_set_config(void* aecInst, AecConfig config)
{
    return 0;
}
#endif

void AudioSpecMulticasterMT::writeLoop()
{
    writeRunning_ = 1;
    while (writeRunning_)
    {
        if (pcmFd_ >= 0)
        {
            short buf[SAMPLE_LEN] = { 0 };
            {
                std::unique_lock<std::mutex> lock(downlinkBufferMutex_);
                if (Rtc_available_read(downlinkBuffer_) < SAMPLE_LEN)
                {
                    cvDownlink_.wait(lock, [this]{
                        return Rtc_available_read(downlinkBuffer_) >= SAMPLE_LEN;
                    });
                }
                Rtc_ReadBuffer(downlinkBuffer_, nullptr, buf, SAMPLE_LEN);
            }
            WritePcm(CH_LP, (uint8_t*)buf, BUF_LEN);
            if (enableAEC_)
            {
                std::unique_lock<std::mutex> locker(aecMutex_);
                rtc_aec_buffer_farend(aec_.get(), buf, SAMPLE_LEN);
            }
            writeBytes_ += SAMPLE_LEN * 2;
            if (GetTickCount() - writeTick_ >= 1000)
            {
                jinfo("writeBytes %d per second", writeBytes_);
                writeTick_ = GetTickCount();
                writeBytes_ = 0;
            }
        }
        else
        {
            usleep(1000 * 1000);
        }
    }
}

void AudioSpecMulticasterMT::readLoop()
{
    readRunning_ = 1;
    while (readRunning_)
    {
        if (pcmFd_ >= 0)
        {
            char buf[BUF_LEN];
            ReadPcm(CH_GOOSE, (uint8_t *)buf, BUF_LEN);
            if (enableAEC_)
            {
                std::unique_lock<std::mutex> locker(aecBufferMutex_);
                if (Rtc_available_write(aecBuffer_) >= SAMPLE_LEN)
                {
                    Rtc_WriteBuffer(aecBuffer_, buf, SAMPLE_LEN);
                }
                else
                {
                    jerror("aecBuffer is full");
                }
                cvAec_.notify_one();
            }
            else
            {
                std::unique_lock<std::mutex> locker(uplinkBufferMutex_);
                if (Rtc_available_write(uplinkBuffer_) >= SAMPLE_LEN)
                {
                    Rtc_WriteBuffer(uplinkBuffer_, buf, SAMPLE_LEN);
                }
                else
                {
                    jerror("uplinkBuffer is full");
                }
                cvUplink_.notify_one();
            }
        }
        else
        {
            usleep(1000 * 1000);
        }
    }
}

void AudioSpecMulticasterMT::downlinkLoop()
{
    downlinkRunning_ = 1;
    int len = 0;
    socklen_t addrlen;
    struct sockaddr_in addr;
    float radio = 1.0;
    if (useMulticast_ && multiIP_ != "")
    {
        struct ip_mreq imr;
        imr.imr_multiaddr.s_addr = inet_addr(multiIP_.c_str());
        imr.imr_interface.s_addr = inet_addr(bindIP_.c_str());
        setsockopt(socketFd_, IPPROTO_IP, IP_ADD_MEMBERSHIP, (char *)&imr, sizeof(struct ip_mreq));
    }
    while (downlinkRunning_)
    {
        if (socketFd_ > 0)
        {
            //printf("START RECV\n");
            short buf[BUF_LEN] = { 0 };
            len = recvfrom(socketFd_, buf, BUF_LEN, 0, (struct sockaddr *)&addr, &addrlen);
            if (len > 0)
            {
                //printf("recvfrom(%d) %d\n", htons(addr.sin_port), len);
                uint64_t key = addr.sin_addr.s_addr << 32 | addr.sin_port;
                int chn = -1;
                if (devChnMap_.find(key) != devChnMap_.end())
                {
                    chn = devChnMap_[key];
                }
                else
                {
                    if (chnIndex_ < CH_MAX)
                    {
                        // 0通道大包使用
                        chn = ++chnIndex_;
                        devChnMap_[key] = chn;
                    }
                    else
                    {
                        jerror("chn over use");
                        continue;
                    }
                }
                for (size_t i = 0; i < SAMPLE_LEN; i++)
                {
                    int x = buf[i] * radio;
                    if (x > 32767)
                        buf[i] = 32767;
                    else if (x < -32768)
                        buf[i] = -32768;
                    else
                        buf[i] = (short)x;
                }
                {
                    std::unique_lock<std::mutex> locker(downlinkBufferMutex_);
                    if (Rtc_available_write(downlinkBuffer_) >= SAMPLE_LEN)
                    {
                        Rtc_WriteBuffer(downlinkBuffer_, buf, SAMPLE_LEN);
                    }
                    else
                    {
                        jerror("DownlinkBuffer is full");
                    }
                    cvDownlink_.notify_one();
                }
            }
        }
        else
        {
            usleep(1000 * 1000);
        }
    }
}

void AudioSpecMulticasterMT::uplinkLoop()
{
    socklen_t addrlen;
    struct sockaddr_in addr;
    bzero(&addr, sizeof(addr));
    addr.sin_family = AF_INET;
    addr.sin_addr.s_addr = inet_addr(dstIP_.c_str());
    addr.sin_port = htons(dstPort_);
    printf("sendto %s:%d\n", dstIP_.c_str(), dstPort_);
    uplinkRunning_ = 1;
    while (uplinkRunning_)
    {
        short buf[SAMPLE_LEN] = { 0 };
        {
            std::unique_lock<std::mutex> lock(uplinkBufferMutex_);
            if (Rtc_available_read(uplinkBuffer_) < SAMPLE_LEN)
            {
                cvUplink_.wait(lock, [this](){
                    return Rtc_available_read(uplinkBuffer_) >= SAMPLE_LEN;
                });
            }
            Rtc_ReadBuffer(uplinkBuffer_, nullptr, buf, SAMPLE_LEN);
        }
        if (socketFd_ > 0)
        {
            int len = sendto(socketFd_, buf, BUF_LEN, 0, (struct sockaddr *)&addr, sizeof(struct sockaddr));
            if (len < 0)
            {
                jerror("sendto fail for %d", errno);
            }
        }
        readBytes_ += SAMPLE_LEN * 2;
        if (GetTickCount() - readTick_ >= 1000)
        {
            jinfo("readBytes %d per second", readBytes_);
            readTick_ = GetTickCount();
            readBytes_ = 0;
        }
        {
            std::unique_lock<std::mutex> locker(uplinkMutex_);
            bbuf_.WriteBytes((const char *)buf, BUF_LEN);
        }
    }
}

void AudioSpecMulticasterMT::aecLoop()
{
    aecRunning_ = 1;
    while (aecRunning_)
    {
        short buf[SAMPLE_LEN] = { 0 };
        short output[SAMPLE_LEN] = { 0 };
        int bufSize = BUF_LEN;
        int index = 0;
        {
            std::unique_lock<std::mutex> lock(aecBufferMutex_);
            if (Rtc_available_read(aecBuffer_) < SAMPLE_LEN)
            {
                cvAec_.wait(lock, [this](){
                    return Rtc_available_read(aecBuffer_) >= SAMPLE_LEN;
                });
            }
            Rtc_ReadBuffer(aecBuffer_, nullptr, buf, SAMPLE_LEN);
        }
        
        int ret = rtc_aec_process(aec_.get(), buf, output, SAMPLE_LEN);
        gain_radio_ = ret * 1.0 / 100.0;
        std::unique_lock<std::mutex> locker(uplinkBufferMutex_);
        if (Rtc_available_write(uplinkBuffer_) >= SAMPLE_LEN)
        {
            Rtc_WriteBuffer(uplinkBuffer_, buf, SAMPLE_LEN);
        }
        else
        {
            jerror("uplinkBuffer is full");
        }
        cvUplink_.notify_one();
    }
}

std::shared_ptr<AudioSpecMulticasterMT> AudioSpecMulticasterMT::inst_;

std::shared_ptr<AudioSpecMulticasterMT> AudioSpecMulticasterMT::CreateInstance(const std::string& jsonParams)
{
    if (!inst_)
    {
        jinfo("CreateInstance AudioSpecMulticasterMT");
        inst_ = std::make_shared<AudioSpecMulticasterMT>(jsonParams);
    }
    return inst_;
}

void AudioSpecMulticasterMT::ReleaseInstance()
{
    inst_.reset();
}

AudioSpecMulticasterMT::AudioSpecMulticasterMT(const std::string &jsonParams)
{
    FN_BEGIN;
    jinfo("AudioSpecMulticasterMT %s", jsonParams.c_str());
    name_ = "AudioSpecMulticasterMT";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    bindIP_ = "0.0.0.0";
    if (j.contains("bindIP"))
    {
        bindIP_ = j["bindIP"];
    }
    bindPort_ = 5566;
    if (j.contains("bindPort"))
    {
        bindPort_ = j["bindPort"];
    }
    dstIP_ = "***************";
    if (j.contains("dstIP"))
    {
        dstIP_ = j["dstIP"];
    }
    dstPort_ = 5566;
    if (j.contains("dstPort"))
    {
        dstPort_ = j["dstPort"];
    }
    multiIP_ = "***************";
    if (j.contains("multiIP"))
    {
        multiIP_ = j["multiIP"];
    }
    devName_ = "/dev/lp_pcm_dev0";
    if (j.contains("devName"))
    {
        devName_ = j["devName"];
    }
    dev_ = 0;
    if (j.contains("dev"))
    {
        dev_ = j["dev"];
    }
    ptime_ = 20;
    if (j.contains("ptime"))
    {
        ptime_ = j["ptime"];
    }
    useMulticast_ = true;
    if (j.contains("useMulticast"))
    {
        useMulticast_ = j["useMulticast"];
    }
    enableAEC_ = 0;
    if (j.contains("aec"))
    {
        enableAEC_ = j["aec"];
    }
    jinfo("AudioSpecMulticasterMT open %s %d", devName_.c_str(), dev_);
    start();
    FN_END;
}

AudioSpecMulticasterMT::~AudioSpecMulticasterMT()
{
    FN_BEGIN;
    stop();
    FN_END;
}

void AudioSpecMulticasterMT::start()
{
    if (thread_.isRunning()) return;
    FN_BEGIN;
    downlinkBuffer_ = Rtc_CreateBuffer(kMaxCount * SAMPLE_LEN, sizeof(short));
    Rtc_InitBuffer(downlinkBuffer_);
    uplinkBuffer_ = Rtc_CreateBuffer(kMaxCount * SAMPLE_LEN, sizeof(short));
    Rtc_InitBuffer(uplinkBuffer_);
    aecBuffer_ = Rtc_CreateBuffer(kMaxCount * SAMPLE_LEN, sizeof(short));
    Rtc_InitBuffer(aecBuffer_);

    void* core = nullptr;
    rtc_aec_create(&core);
    rtc_aec_init(core, samplerate_);
    AecConfig config;
    memset(&config, 0, sizeof(config));
    config.nlpMode = kAecNlpConservative;
    rtc_aec_set_config(core, config);
    aec_ = std::shared_ptr<void>(core, [](void* core){
        rtc_aec_free(core);
    });

    pcmFd_ = openPCM(devName_.c_str());
    if (pcmFd_ < 0)
    {
        jerror("%s open %s fail", __FUNCTION__, devName_.c_str());
        return;
    }
    else
    {
        for (size_t i = 0; i < CH_MAX; i++)
        {
            Set_Type(i, IOCTL_CMD_PCM_TYPE_HIFI);
        }
    }

    socketFd_ = socket(AF_INET, SOCK_DGRAM, 0);
    if (socketFd_ > 0)
    {
        struct sockaddr_in servaddr;
        bzero(&servaddr, sizeof(servaddr));
        servaddr.sin_family = AF_INET;
        servaddr.sin_addr.s_addr = inet_addr(bindIP_.c_str());
        servaddr.sin_port = htons(bindPort_);
        if (bind(socketFd_, (struct sockaddr *)&servaddr, sizeof(servaddr)) == -1)
        {
            jerror("%s bind fail", __FUNCTION__);
        }
        else
        {
            writeThread_ = std::make_unique<std::thread>([this](){
                writeLoop();
            });
            readThread_ = std::make_unique<std::thread>([this](){
                readLoop();
            });
            downlinkThread_ = std::make_unique<std::thread>([this](){
                downlinkLoop();
            });
            uplinkThread_ = std::make_unique<std::thread>([this](){
                uplinkLoop();
            });
            aecThread_ = std::make_unique<std::thread>([this](){
                aecLoop();
            });
        }
    }
    else
    {
        jerror("%s create socket fail", __FUNCTION__);
    }

    thread_.start();

    ptimeDataLen_ = samplerate_ / 1000 * ptime_  * sizeof(int16_t);
    jinfo("samplerate=%d ptime=%d ptimeDataLen=%d", samplerate_, ptime_, ptimeDataLen_);
    threadPacer_.start();
    threadPacer_.loop()->setInterval(ptime_, [this](hv::TimerID id){
        if (bbuf_.Length() >= ptimeDataLen_)
        {
            std::shared_ptr<Frame> f = Frame::CreateFrame(FRAME_FORMAT_PCM_16000_1);
            f->createFrameBuffer(ptimeDataLen_);
            {
                std::unique_lock<std::mutex> locker(uplinkMutex_);
                memcpy(f->getFrameBuffer(), bbuf_.Data(), ptimeDataLen_);
                bbuf_.Consume(ptimeDataLen_);
            }
            deliverFrame(f);
            printOutputStatus("AudioSpecMulticasterMT");
        }
    });
    FN_END;
}

void AudioSpecMulticasterMT::stop()
{
    if (!thread_.isRunning()) return;
    std::unique_lock<std::mutex> locker(frame_mutex_);
    thread_.stop(true);
    threadPacer_.stop(true);
    writeRunning_ = 0;
    readRunning_ = 0;
    downlinkRunning_ = 0;
    uplinkRunning_ = 0;
    aecRunning_ = 0;

    writeThread_->join();
    readThread_->join();
    downlinkThread_->join();
    uplinkThread_->join();
    aecThread_->join();

    writeThread_.reset();
    readThread_.reset();
    downlinkThread_.reset();
    uplinkThread_.reset();
    aecThread_.reset();

    aec_.reset();

    if (socketFd_ > 0)
    {
        close(socketFd_);
        socketFd_ = -1;
    }

    if (pcmFd_ >= 0)
    {
        closePCM();
        pcmFd_ = -1;
    }

    Rtc_FreeBuffer(downlinkBuffer_);
    Rtc_FreeBuffer(uplinkBuffer_);
    Rtc_FreeBuffer(aecBuffer_);
    initResampler_ = false;
}

// 大包
void AudioSpecMulticasterMT::onFrame(const std::shared_ptr<Frame> &frame)
{
    if (!thread_.isRunning()) return;
    int samplerate = 0;
    int chn = 0;
    if (Frame::getSamplerate(frame->getFrameFormat(), samplerate, chn))
    {
        std::unique_lock<std::mutex> locker(frame_mutex_);
        if (!thread_.isRunning()) return;
        thread_.loop()->runInLoop([this, frame, samplerate](){
            auto f = frame;
            if (samplerate_ != samplerate)
            {
                if (!initResampler_ || source_samplerate_ != samplerate)
                {
                    source_samplerate_ = samplerate;
                    initResampler_ = true;
#ifdef WEBRTC_RESAMPLE_ANOTHER
                    resampler_.InitializeIfNeeded(samplerate, samplerate_, 1);
#else
                    resampler_.ResetIfNeeded(samplerate, samplerate_, 1);
#endif
                }
                auto frame = Frame::CreateFrame(FRAME_FORMAT_PCM_16000_1);
                frame->createFrameBuffer(f->getFrameSize() * samplerate_ / samplerate);
                memset(frame->getFrameBuffer(), 0, frame->getFrameSize());
#ifdef WEBRTC_RESAMPLE_ANOTHER
                resampler_.Resample((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2);
#else
                size_t outlen = 0;
                resampler_.Push((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2, outlen);
#endif
                f = frame;
            }
            if (pcmFd_ >= 0)
            {
                int index = 0;
                uint8_t* buf = f->getFrameBuffer();
                int bufSize = f->getFrameSize();
                while (bufSize > 0)
                {
                    int ret = WritePcm(CH_COMMON, buf + index, bufSize);
                    if (ret > 0)
                    {
                        index += ret;
                        bufSize -= ret;
                    }
                }
            }
            printInputStatus("AudioSpecMulticasterMT");
        });
    }
}