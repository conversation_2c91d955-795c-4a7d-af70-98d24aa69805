#ifndef __FRAME_SELECTOR_H__
#define __FRAME_SELECTOR_H__
#include <memory>
#include <vector>
#include <map>
#include <list>
#include <string>
#include <hv/EventLoopThread.h>
#include "FramePipeline.h"

namespace panocom {
//  选择策略
enum class SelectStrategy {
    RoundRobin, // 轮询
    Priority, // 优先选择，超时切换
    Manual, // 手动选择
};
class FrameSelector : public FramePipeline {
public:
    static std::shared_ptr<FrameSelector> Create(const std::string &jsonParam);
    static bool IsFrameSelector(const FramePipeline::Ptr& ptr);
    virtual void registerInput(FramePipeline::Ptr input);
    virtual void unregisterInput(FramePipeline::Ptr input);
    virtual void setSelectedInput(FramePipeline::Ptr input);    // 手动设置选中的输入
    virtual void setPriorityInput(FramePipeline::Ptr input);   // 设置优先级最高的输入
    virtual FramePipeline::Ptr getSelectedInput();
    FrameSelector(const std::string &jsonParam);
    virtual ~FrameSelector();
    virtual void onFrame(const std::shared_ptr<Frame> &frame) override;

protected:
    virtual void selectNextInput();
private:
    bool isTimeout();
private:
    SelectStrategy strategy_;
    std::mutex mutex_;
    std::list<FramePipeline::WPtr> inputs_;
    FramePipeline::WPtr selected_;
    FramePipeline::WPtr priority_;

    // TEST: 累计计数后选择下一个输入源
    int count_ = 0;

    // TODO: 增加超时切换策略，当选中的输入超过一定时间没有数据，则自动切换到下一个输入
    //       超时时间可以通过参数设置，默认为2秒
    int timeout_ = 2000;
    std::chrono::steady_clock::time_point lastFrameTime_;
    hv::EventLoopThread timer_;  // 定时器，用于超时选择下一个输入
    bool isTimeout_;     // 是否已经超时
};    
}

#endif