#ifndef IMPL_AUDIOPROCESSING_PCMGAINCONTROLLER_H_
#define IMPL_AUDIOPROCESSING_PCMGAINCONTROLLER_H_
#include <string>
#include <stdint.h>
#include <unordered_map>
#include "AudioFrameProcesser.h"
#include "Frame.h"

namespace panocom
{
// 根据onFrame中的frame携带的增益进行调节，只适用于4A或2v、4V板用串口传输语音数据的情况，
// 正常情况下使用可以每次更新gain_
class PCMGainController : public AudioFrameProcesser
{
public:
    PCMGainController(const std::string& jsonParams);
    ~PCMGainController();
    void onFrame(const std::shared_ptr<Frame> &frame) override;
    void AdjustGain(int16_t* out_data, size_t out_len, int16_t* in_data, size_t in_len, int db);
    int updateParam(const std::string& jsonParams) override;
private:
    int sample_rate_;
    int number_of_channels_;
    FrameFormat fmt_;
    int gain_;

    int gain_min_;
    int gain_max_;
    std::unordered_map<int, float> gain_table_;

    bool use_default_gain_;
};    
} // namespace panocom

#endif