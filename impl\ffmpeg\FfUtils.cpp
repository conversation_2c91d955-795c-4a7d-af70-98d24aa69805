//
// Created by li-weibing on 2025/5/9.
//

#include "FfUtils.h"
const char * FfUtils::ff_err2str(int errRet){
    static thread_local char errbuff[500];
    av_strerror(errRet,errbuff,sizeof(errbuff));
    return errbuff;
}


const char * FfUtils::ff_get_pix_fmt_name(AVPixelFormat pix_fmt){
    const char* pix_fmt_name = av_get_pix_fmt_name(pix_fmt);
    return pix_fmt_name?pix_fmt_name:"unknown PixelFormat";
}


