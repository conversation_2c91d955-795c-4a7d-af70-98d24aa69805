#include "MediaPipeline.h"
#include <json.hpp>
#include <stdio.h>
#include <ljcore/jlog.h>
#include <vector>
#include <memory>
#include <hv/EventLoop.h>

using namespace panocom;

#define CAM_WIDTH  1920
#define CAM_HEIGHT 1088

extern int FrameOutputFpsIntervel;
extern int FramePipelineFpsIntervel;
extern int FrameInputFpsIntervel;

int main()
{
    auto loop = std::make_shared<hv::EventLoop>();
    FrameOutputFpsIntervel = 1;
    FramePipelineFpsIntervel = 1;
    FrameInputFpsIntervel = 1;
    jlog_init(nullptr);
    nlohmann::json j;
    j["width"] = CAM_WIDTH;
    j["height"] = CAM_HEIGHT;
    auto capturer = VideoFrameCapturer::CreateVideoCapturer("V4L2VideoFrameCapturer", j.dump());
    capturer->setGroupId(1);
    j.clear();
    j["codec"] = "mjpeg_cuvid";
    auto mdecoder = VideoFrameDecoder::CreateVideoDecoder("FfVideoDecoder", j.dump());
    mdecoder->setGroupId(1);
    j.clear();
    j["width"] = CAM_WIDTH * 2;
    j["height"] = CAM_HEIGHT * 2;
    j["videoLayout"] = nlohmann::json::array();
    nlohmann::json r;

    r["id"] = 1;
    r["rect"]["x"] = 0;
    r["rect"]["y"] = 0;
    r["rect"]["w"] = CAM_WIDTH;
    r["rect"]["h"] = CAM_HEIGHT;
    j["videoLayout"].push_back(r);

    r["id"] = 1;
    r["rect"]["x"] = 0;
    r["rect"]["y"] = CAM_HEIGHT;
    r["rect"]["w"] = CAM_WIDTH;
    r["rect"]["h"] = CAM_HEIGHT;
    j["videoLayout"].push_back(r);

    r["id"] = 1;
    r["rect"]["x"] = CAM_WIDTH;
    r["rect"]["y"] = 0;
    r["rect"]["w"] = CAM_WIDTH;
    r["rect"]["h"] = CAM_HEIGHT;
    j["videoLayout"].push_back(r);

    r["id"] = 1;
    r["rect"]["x"] = CAM_WIDTH;
    r["rect"]["y"] = CAM_HEIGHT;
    r["rect"]["w"] = CAM_WIDTH;
    r["rect"]["h"] = CAM_HEIGHT;
    j["videoLayout"].push_back(r);

    auto mixer = VideoFrameMixer::CreateVideoMixer("LibyuvVideoFrameMixer", j.dump());
    mixer->setGroupId(1);

    j.clear();
    j["width"] = CAM_WIDTH;
    j["height"] = CAM_HEIGHT;
    j["videoLayout"] = nlohmann::json::array();

    int w = CAM_WIDTH / 3;
    int h = CAM_HEIGHT / 3;
    for (int i = 0; i < 3; ++i)
    {
        for (int k = 0; k < 3; ++k)
        {
            r["id"] = i * 3 + k;
            r["rect"]["x"] = i * w;
            r["rect"]["y"] = k * h;
            r["rect"]["w"] = w;
            r["rect"]["h"] = h;
            j["videoLayout"].push_back(r);
        }
    }
    j["videoLayout"].push_back(r);
    auto render = VideoFrameRender::CreateVideoRender("Sdl2VideoFrameRender", j.dump());

    j.clear();
    j["codec"] = "h264_nvenc";
    j["width"] = CAM_WIDTH * 2;
    j["height"] = CAM_HEIGHT * 2;
    j["kbps"] = 8000;
    auto encoder = VideoFrameEncoder::CreateVideoEncoder("FfVideoEncoder", j.dump());
    encoder->setGroupId(1);

    std::vector<std::shared_ptr<VideoFrameDecoder>> decoders;
    //std::vector<std::shared_ptr<FileFramePipeline>> filers;
    for (int i = 0; i < 9; ++i)
    {
        j.clear();
        j["codec"] = "h264_cuvid";
        j["width"] = CAM_WIDTH * 2;
        j["height"] = CAM_HEIGHT * 2;
        auto decoder = VideoFrameDecoder::CreateVideoDecoder("FfVideoDecoder", j.dump());
        decoder->setGroupId(i);
        decoders.push_back(decoder);
        encoder->addVideoDestination(decoder);
        decoder->addVideoDestination(render);

        // j.clear();
        // j["path"] = std::to_string(i) + ".yuv";
        // auto filer = FileFramePipeline::CreateFileDestination("file", j.dump());
        // filers.push_back(filer);
        // decoder->addVideoDestination(filer);
    }

    capturer->addVideoDestination(mdecoder);
    mdecoder->addVideoDestination(mixer);
    mixer->addVideoDestination(encoder);

    loop->run();
    return 0;
}