#ifndef P_VideoFrameCapturer_h
#define P_VideoFrameCapturer_h

#include "../FramePipeline.h"

namespace panocom
{
    class VideoFrameCapturer : public FramePipeline
    {
    public:
        static void RegistCapturers();
        static std::vector<std::string>& GetSupportCapturers();
        static bool IsSupportCapturer(const std::string &captureName);
        static std::shared_ptr<VideoFrameCapturer> CreateVideoCapturer(const std::string &captureName, const std::string &jsonParams = "");
        static void ReleaseVideoCapturer(const std::string &capturerName, int dev, int gpu_index);
        static void ReleaseVideoCapturer(const FramePipeline::Ptr& ptr);
        static bool IsVideoFrameCapturer(const FramePipeline::Ptr& ptr);
        virtual bool GetConnectStatus() { return false; }
        virtual ~VideoFrameCapturer() = default;
    protected:
        static bool isRegistered;
        static std::vector<std::string> capturers_;
        struct CapturerInfo
        {
            std::string name;
            int dev;
            int gpu_index;
            bool operator ==(const struct CapturerInfo& d) const {
                return name == d.name && dev == d.dev && gpu_index == d.gpu_index;
            }
        };

        struct InfoHash
        {
            std::size_t operator() (const CapturerInfo& info) const {
                std::size_t h1 = std::hash<std::string>{}(info.name);
                std::size_t h2 = std::hash<int>{}(info.dev);
                std::size_t h3 = std::hash<int>{}(info.gpu_index);
                return h1 ^ (h2 << 1) ^ (h3 << 2);
            }
        };
        
        
        struct CapturerRef
        {
            std::shared_ptr<VideoFrameCapturer> capturer;
            int ref;
        };
        static std::unordered_map<CapturerInfo, CapturerRef, InfoHash> existed_capturers_;
        // static std::unordered_map<std::shared_ptr<VideoFrameCapturer>, int> capturer_refs_;
    };
}

#endif