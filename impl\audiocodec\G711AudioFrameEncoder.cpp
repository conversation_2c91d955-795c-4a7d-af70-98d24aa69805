#include "G711AudioFrameEncoder.h"
#include "Frame.h"
#include "g711.h"
#include "Utils.h"
#include <json.hpp>
#include <ljcore/jlog.h>
#include <stdio.h>

extern int mmi_record_audio;

using namespace panocom;

static size_t WebRtcG711_EncodeA(const int16_t *speechIn,
                                 size_t len,
                                 uint8_t *encoded)
{
  size_t n;
  for (n = 0; n < len; n++)
    encoded[n] = linear_to_alaw(speechIn[n]);
  return len;
}

static size_t WebRtcG711_EncodeU(const int16_t *speechIn,
                                 size_t len,
                                 uint8_t *encoded)
{
  size_t n;
  for (n = 0; n < len; n++)
    encoded[n] = linear_to_ulaw(speechIn[n]);
  return len;
}

G711AudioFrameEncoder::G711AudioFrameEncoder(const std::string &jsonParams) : debug_mode_(0)
{
  FN_BEGIN;
  name_ = "G711AudioFrameEncoder";
  nlohmann::json j;
  if (jsonParams != "")
    j = nlohmann::json::parse(jsonParams);
  fmt_ = FRAME_FORMAT_PCMA;
  if (j.contains("ulaw"))
  {
    if (j["ulaw"])
    {
      fmt_ = FRAME_FORMAT_PCMU;
    }
  }
  samplerate_ = 8000;
  if (j.contains("samplerate"))
  {
    samplerate_ = j["samplerate"];
  }
  if (j.contains("debug")) debug_mode_ = j["debug"];
  // file = fopen("g711file.pcm","ab");
  // if(!file){
  //   jinfo("open pcm file error");
  // }
  FN_END;
}

G711AudioFrameEncoder::~G711AudioFrameEncoder()
{
  FN_BEGIN;
  FN_END;
}

void G711AudioFrameEncoder::onFrame(const std::shared_ptr<Frame> &frame)
{
  if (debug_mode_ == 1) {
    frame->setFrameFormat(fmt_);
    deliverFrame(frame);
    return;
  }
  int samplerate = 0;
  int chn = 0;
  auto f = frame;
  if (Frame::getSamplerate(f->getFrameFormat(), samplerate, chn))
  {
    if (samplerate_ != samplerate)
    {
      if (!initResampler_ || samplerate != source_samplerate_)
      {
        source_samplerate_ = samplerate;
        initResampler_ = true;
#ifdef WEBRTC_RESAMPLE_ANOTHER
        resampler_.InitializeIfNeeded(samplerate, samplerate_, 1);
#else
        resampler_.ResetIfNeeded(samplerate, samplerate_, 1);
#endif
        jinfo("G711AudioFrameEncoder resample %d -> %d", samplerate, samplerate_);
      }
      auto frame = Frame::CreateFrame(FRAME_FORMAT_COMMON_AUDIO);
      frame->createFrameBuffer(f->getFrameSize() * samplerate_ / samplerate);
      memset(frame->getFrameBuffer(), 0, frame->getFrameSize());
#ifdef WEBRTC_RESAMPLE_ANOTHER
      resampler_.Resample((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2);
#else
      size_t outlen = 0;
      resampler_.Push((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2, outlen);
#endif
      f = frame;
      if (first_)
      {
        first_ = false;
      }
    }
    std::shared_ptr<Frame> output = Frame::CreateFrame(fmt_);
    output->createFrameBuffer(f->getFrameSize() / 2);
    int16_t speechType = 0;
    
    // if(file){
    //   fwrite(f->getFrameBuffer(),sizeof(uint8_t),f->getFrameBufferSize(),file);
    // }
    // jinfo("711bufferlen : %d", f->getFrameBufferSize());

    if (fmt_ == FRAME_FORMAT_PCMA)
    {
      WebRtcG711_EncodeA((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, output->getFrameBuffer());
    }
    else if (fmt_ == FRAME_FORMAT_PCMU)
    {
      WebRtcG711_EncodeU((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, output->getFrameBuffer());
    }
    if (mmi_record_audio)
    {
      if (!pf_)
      {
        std::string fileName = name_ + std::to_string(samplerate_) + "-" + std::to_string((long)this) + ".g711";
        pf_ = fopen(fileName.c_str(), "w+b");
      }
      if (pf_)
      {
        fwrite(output->getFrameBuffer(), 1, output->getFrameSize(), pf_);
      }
    }
    deliverFrame(output);
    printOutputStatus("G711AudioFrameEncoder");
  }
  else
  {
    jerror("opus_encode err fmt(%d)", frame->getFrameFormat());
  }
}