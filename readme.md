# win32编译需知
1. 至少vs2017以上版本，同时安装高版本的cmake
2. 解压3rdpart/source/win32下的ffmpeg和SDL的目标码到3rdpart目录
3. 解压3rdpart/source/win32下的rtc.aec和rnnoise的源代码到3rdpart/source目录
4. 解压3rdpart/source/下jrtplib、jthread、libhv、libsdptransform、opus-1.1.2、ZLToolKit到3rdpart/source/目录下
5. 3rdpart\source\opus-1.1.2\win32\VS2010下点击opus.sln升级项目文件
6. 使用开始菜单找到"x64 Native Tools Command Prompt for VS 2019"，打开cd到MediaManager目录
7. 运行build3rdpart.bat
8. 运行build.bat
9. 本人对windows脚本不熟悉，如果有错误根据错误手动调整路径，打开MediaPipeline.sln手动编译.
10. 自行修改build.bat中生成sln文件的的路径，方便整合进自己的工程


# FIXME:
1. 没有找到nv有方便的接口实现yuv scale和yuv composite
2. cuQML，使用的同一个texture多个pbo

# WORKING:
1. 增加播放文件接口，可支持循环播放

# TODO:
1. 接收到数据才开始创建解码器，协商多种编码类型，不需要选择sdp中的首选编码
2. 处理re-INVITE信令
3. 发送混音复用通道
