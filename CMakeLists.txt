cmake_minimum_required(VERSION 3.10)

project(MediaPipeline)

set(CMAKE_CXX_STANDARD 14)

# 添加 git 版本信息
set(COMMIT_HASH "Git_Unkown_commit")
set(COMMIT_TIME "Git_Unkown_time")
set(<PERSON><PERSON><PERSON>_NAME "Git_Unkown_branch")
set(BUILD_TIME "")
# set(USE_WEBRTC 1)
# set(USE_ALSA 1)
# set(USE_UART 1)


string(TIMESTAMP BUILD_TIME "%Y-%m-%dT%H:%M:%S")

find_package(Git QUIET)
if(GIT_FOUND)
  execute_process(
    COMMAND ${GIT_EXECUTABLE} rev-parse --short=7 HEAD
    OUTPUT_VARIABLE COMMIT_HASH
    OUTPUT_STRIP_TRAILING_WHITESPACE
    ERROR_QUIET
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR})
  execute_process(
    COMMAND ${GIT_EXECUTABLE} symbolic-ref --short -q HEAD
    OUTPUT_VARIABLE BRANCH_NAME
    OUTPUT_STRIP_TRAILING_WHITESPACE
    ERROR_QUIET
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR})

  execute_process(
    COMMAND ${GIT_EXECUTABLE} log --format=format:%aI -1
    OUTPUT_VARIABLE COMMIT_TIME
    OUTPUT_STRIP_TRAILING_WHITESPACE
    ERROR_QUIET
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR})
endif ()

configure_file(
  ${CMAKE_CURRENT_SOURCE_DIR}/version.h.ini
  ${CMAKE_CURRENT_BINARY_DIR}/version.h
  @ONLY)

message(STATUS "Git version is ${BRANCH_NAME} ${COMMIT_HASH}/${COMMIT_TIME} ${BUILD_TIME}")

if (ENABLE_VIDEO)
  if (USE_CUDA)
    find_library(CUVID_LIB nvcuvid)
    find_library(NVENCODEAPI_LIB nvidia-encode)
    find_package(CUDA)

    set(CUDA_HOST_COMPILER ${CMAKE_CXX_COMPILER})

    find_library(FREEGLUT_LIB glut)
    find_library(GLEW32_LIB GLEW)
    find_library(X11_LIB X11)
    find_library(GL_LIB GL)
    find_library(CUDART_LIB cudart HINTS ${CUDA_TOOLKIT_ROOT_DIR}/lib64)
  endif ()
endif ()

if (CMAKE_SYSTEM_NAME STREQUAL "Windows")
  add_definitions(-DWIN32_LEAN_AND_MEAN)
else()
  add_definitions(-Wall -g -fexceptions -fpermissive)
endif()

if (USE_SDL2)
  add_definitions(-DUSE_SDL2)
endif ()
if (ENABLE_VIDEO)
  add_definitions(-DENABLE_VIDEO)
  if (USE_CUSTOM_RENDER)
    add_definitions(-DUSE_CUSTOM_RENDER)
  endif ()
endif ()
if (USE_CUDA)
  add_definitions(-DUSE_CUDA)
endif ()
if (USE_JRTP)
  add_definitions(-DUSE_JRTP)
  if (USE_DTLS_RTP)
    add_definitions(-DUSE_DTLS_RTP)
  endif()
endif ()
if (USE_LIBYUV)
  add_definitions(-DUSE_LIBYUV)
endif ()
if (USE_V4L2)
  add_definitions(-DUSE_V4L2)
endif ()
if (USE_FFMPEG)
  add_definitions(-DUSE_FFMPEG)
endif ()
if (USE_WEBRTC)
  add_definitions(-DUSE_WEBRTC -DWEBRTC_POSIX -DWEBRTC_LINUX)
  aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/webrtc/neteq SRC_LIST)
  if (USE_ULPFEC)
    add_definitions(-DUSE_ULPFEC)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/webrtc/ulpfec2 SRC_LIST)
  endif ()
else()
  if (USE_NETEQ)
    add_definitions(-DUSE_NETEQ)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/webrtc/neteq SRC_LIST)
  endif ()
  if (USE_ULPFEC)
    add_definitions(-DUSE_ULPFEC)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/webrtc/ulpfec SRC_LIST)
  endif ()
endif ()
if (USE_RPC)
  add_definitions(-DUSE_RPC)
endif ()
if (USE_SPEC)
  add_definitions(-DUSE_SPEC)
endif ()
if (USE_LIBNICE)
  add_definitions(-DUSE_LIBNICE)
endif ()
if (USE_DTLS)
  add_definitions(-DUSE_DTLS)
endif ()
if (USE_TINYALSA)
add_definitions(-DUSE_TINYALSA)
endif ()

include_directories(${CMAKE_CURRENT_SOURCE_DIR}
${CMAKE_CURRENT_SOURCE_DIR}/impl
${CMAKE_CURRENT_SOURCE_DIR}/audio
${CMAKE_CURRENT_SOURCE_DIR}/video
${CMAKE_PREFIX_PATH}/include
${CMAKE_PREFIX_PATH}/include/glib-2.0
${CMAKE_PREFIX_PATH}/lib/glib-2.0/include/
${CMAKE_CURRENT_SOURCE_DIR}/3rdparty/include/
)

if (USE_CUDA)
  include_directories(${CMAKE_CURRENT_SOURCE_DIR}/impl/cuda)
endif ()

if (USE_WEBRTC)
  include_directories(${CMAKE_PREFIX_PATH}/include/webrtc)
endif ()

link_directories(${CMAKE_PREFIX_PATH}/lib)

option(ENABLE_RK "Enable RK" false)
if(ENABLE_RK)
  include_directories(${CMAKE_CURRENT_SOURCE_DIR} ${CMAKE_PREFIX_PATH}/include/rockchip 
                      ${CMAKE_PREFIX_PATH}/include/rockchip/rockchip
                      ${CMAKE_PREFIX_PATH}/include/rockchip/rga)
  aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/rk SRC_LIST)
  aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/rk/camera SRC_LIST)
  # if (ENABLE_VIDEO)
  # include_directories(${CMAKE_PREFIX_PATH}/include/freetype2)
  #   aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/rk/osd SRC_LIST)
  # endif ()
  add_definitions(-DSDL_DISABLE_IMMINTRIN_H -DRK_PLATFORM)
  message(STATUS "Platform is RK")
endif ()

aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR} SRC_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/audio SRC_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/file SRC_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/ SRC_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/file SRC_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/aec SRC_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/lec SRC_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/ns SRC_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/audiocodec SRC_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/audiomixer SRC_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/audiodispatcher SRC_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/ip SRC_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/monitor SRC_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/audioprocessing SRC_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/selector SRC_LIST)

if (USE_ALSA)
  add_definitions(-DUSE_ALSA)
  aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/alsa SRC_LIST)
endif()

if (USE_UART)
  aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/uart SRC_LIST)
  add_definitions(-DUSE_UART)
endif()
if (USE_LIBNICE)
  aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/libnice SRC_LIST)
endif ()
if (USE_DTLS)
  aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/dtls SRC_LIST)
endif ()
if (USE_SDL2)
  aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/sdl2 SRC_LIST)
endif ()
if (USE_SPEC)
  aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/spec SRC_LIST)
endif ()
if (USE_JRTP)
  aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/jrtp SRC_LIST)
endif ()
if (USE_RTSP)
  aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/rtsp SRC_LIST)
endif ()
if (USE_TINYALSA)
  aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/tinyalsa SRC_LIST)
endif ()

if (ENABLE_VIDEO)
  aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/video SRC_LIST)
  if (USE_FFMPEG)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/ffmpeg SRC_LIST)
  endif ()
  if (USE_LIBYUV)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/libyuv SRC_LIST)
  endif ()
  if (USE_V4L2)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/v4l2 SRC_LIST)
  endif ()
  if (USE_CUDA)
    aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/cuda SRC_LIST)
  endif ()
endif ()

if (USE_WEBRTC)
  aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/webrtc SRC_LIST)
  aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/impl/webrtc/thread SRC_LIST)
endif ()
if (USE_CUDA)
  cuda_add_library(${PROJECT_NAME} SHARED ${SRC_LIST})
else ()
  add_library(${PROJECT_NAME} SHARED ${SRC_LIST})
endif ()

if (CMAKE_SYSTEM_NAME STREQUAL "Windows")
  target_link_libraries(${PROJECT_NAME} hv jrtplib_d jthread_d avcodec swscale avformat avdevice avutil SDL2 sdptransform ZLToolKit opus celt silk_fixed silk_float silk_common aec rnnoise)
else ()
  if (USE_WEBRTC)
    if (USE_RTSP)
      target_link_libraries(${PROJECT_NAME} webrtc rtsp http rtp resampler ADL)
    else ()
      target_link_libraries(${PROJECT_NAME} webrtc resampler ADL)
    endif ()
  else ()
    if (USE_RTSP)
      target_link_libraries(${PROJECT_NAME} rtsp http rtp resampler ADL)
    else ()
      target_link_libraries(${PROJECT_NAME} resampler ADL)
    endif ()
  endif ()
endif ()

# if(ENABLE_VIDEO)
#   if (ENABLE_RK)
#     target_link_libraries(${PROJECT_NAME} freetype)
#   endif()
# endif()

target_link_libraries(${PROJECT_NAME} g722_1)

if (USE_RPC)
  add_subdirectory(service)
endif ()

install(TARGETS ${PROJECT_NAME} DESTINATION ${CMAKE_INSTALL_PREFIX}/lib)
install(FILES 
${PROJECT_SOURCE_DIR}/MediaManagerInterface.h 
${PROJECT_SOURCE_DIR}/VideoLayout.h
${PROJECT_SOURCE_DIR}/CodecInfo.h 
${PROJECT_SOURCE_DIR}/DevInfo.h
${PROJECT_SOURCE_DIR}/Frame.h
${PROJECT_SOURCE_DIR}/VideoHelper.h
${PROJECT_SOURCE_DIR}/Utils.h
${PROJECT_SOURCE_DIR}/FramePipeline.h
${PROJECT_SOURCE_DIR}/OpenGLRender.h
${PROJECT_SOURCE_DIR}/Frame.h
${PROJECT_SOURCE_DIR}/RtpEngine.h
${PROJECT_SOURCE_DIR}/MediaPipeline.h
DESTINATION ${CMAKE_INSTALL_PREFIX}/include/${PROJECT_NAME})

install(DIRECTORY ${PROJECT_SOURCE_DIR}/audio DESTINATION ${CMAKE_INSTALL_PREFIX}/include/${PROJECT_NAME} FILES_MATCHING PATTERN "*.h")
install(DIRECTORY ${PROJECT_SOURCE_DIR}/video DESTINATION ${CMAKE_INSTALL_PREFIX}/include/${PROJECT_NAME} FILES_MATCHING PATTERN "*.h")
install(DIRECTORY ${PROJECT_SOURCE_DIR}/file DESTINATION ${CMAKE_INSTALL_PREFIX}/include/${PROJECT_NAME} FILES_MATCHING PATTERN "*.h")


#add_subdirectory(test/FfIPCVideoRender)
#add_subdirectory(test/FfVideoEncoder)
#add_subdirectory(impl/v4l2/test)
#add_subdirectory(impl/audiocodec/test)
#add_subdirectory(audio/test)

#add_subdirectory(test/stun_dtls)
#add_subdirectory(test/csph3)
#add_subdirectory(test/terminal)
#add_subdirectory(test/CuQML)

if (USE_TEST)
  if (CMAKE_SYSTEM_NAME STREQUAL "Windows")
    add_subdirectory(win32/test)
  endif ()
  if (USE_SPEC)
    add_subdirectory(test/csph3)
  endif ()
  if (USE_CUDA)
    add_subdirectory(test/CuQML)
  endif ()
  if(ENABLE_RK)
    add_subdirectory(impl/rk/test)
  endif ()
  if (USE_UNITEST)
    add_subdirectory(test)
  endif ()
endif ()