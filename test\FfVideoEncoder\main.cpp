#include "MediaPipeline.h"
#include "VideoFrameDecoder.h"
#include "VideoFrameEncoder.h"
#include "FramePipeline.h"
#include "./impl/file/VideoFileFrameSource.h"
#include "./file/FileFramePipeline.h"
#include "ffmpeg/FrameSender.h"
#include <json.hpp>
#include <stdio.h>
#include <ljcore/jlog.h>
#include <hv/EventLoop.h>
#include <string.h>

using namespace panocom;

/***
 * 编码测试
 * ./FfVideoEncoder h264

./run.sh  16   /home/<USER>/videos/race.h265  FfVideoDecoderAdapter
./run.sh  16   /home/<USER>/videos/race.h265  FfHwVideoDecoder
./run.sh  16   /home/<USER>/videos/race.h265  FfVideoDecoder
 *
***/

int main(int argc, char **argv) {
    jlog_init(nullptr);
    jinfo("FfVideoEncoder main() start 7777 ");

    // 打印命令行参数以确保正确传递
    for (int i = 0; i < argc; ++i) {
        jinfo("FfVideoEncoder main() argv i:%d   %s",i, argv[i] );
    }


    auto loop = std::make_shared<hv::EventLoop>();

    int id = 0;
    nlohmann::json j;

    j["path"] = "/home/<USER>/videos/chushi.h264";
    if (argc >= 2 && strcmp(argv[1] ,"h265") == 0 ) {
        j["path"] = "/home/<USER>/videos/race.h265";
    }else  if (argc >= 2 && strcmp(argv[1] ,"720") == 0 ) {
        j["path"] = "/home/<USER>/videos/720.h264";
    }

    auto source = FileFramePipeline::CreateFileSource("VideoFileFrameSource", j.dump());
    source->setGroupId(id);

    std::shared_ptr<panocom::VideoFileFrameSource> videoFileFrameSourcePtr = std::dynamic_pointer_cast<panocom::VideoFileFrameSource>(source);
    if (videoFileFrameSourcePtr) {
        if (videoFileFrameSourcePtr->getFps() > 0) {
            j["fps"] = videoFileFrameSourcePtr->getFps();
        }
        if (videoFileFrameSourcePtr->getWidth() > 0) {
            j["width"] = videoFileFrameSourcePtr->getWidth();
        }
        if (videoFileFrameSourcePtr->getHeight() > 0) {
            j["height"] = videoFileFrameSourcePtr->getHeight();
        }
        if (!videoFileFrameSourcePtr->getCodec().empty()) {
            j["codec"] = videoFileFrameSourcePtr->getCodec(); //codec可选：mjpeg h264 hevc
        }
    }

    auto decoder = VideoFrameDecoder::CreateVideoDecoder("FfVideoDecoder", j.dump());
    decoder->setGroupId(id);


    j.clear();
    j["path"] = "/home/<USER>/videos/dst_chushi.h264";
    if (argc >= 3 && strcmp(argv[2] ,"h265") == 0){
        j["path"] = "/home/<USER>/videos/dst_race.h265";
    }else  if (argc >= 3 && strcmp(argv[2] ,"720") == 0 ) {
        j["path"] = "/home/<USER>/videos/dst_720.h264";
    }

    if (argc >= 3 && strcmp(argv[2] ,argv[1]) == 0  && videoFileFrameSourcePtr) {
        if (videoFileFrameSourcePtr->getFps() > 0) {
            j["fps"] = videoFileFrameSourcePtr->getFps();
        }
        if (videoFileFrameSourcePtr->getWidth() > 0) {
            j["width"] = videoFileFrameSourcePtr->getWidth();
        }
        if (videoFileFrameSourcePtr->getHeight() > 0) {
            j["height"] = videoFileFrameSourcePtr->getHeight();
        }
        if (!videoFileFrameSourcePtr->getCodec().empty()) {
            j["codec"] = videoFileFrameSourcePtr->getCodec(); //codec可选：mjpeg h264 hevc
        }
        if (videoFileFrameSourcePtr->getGop() > 0) {
            j["gop"] = videoFileFrameSourcePtr->getGop();
        }
    }
    j["kbps"] = 4000;
    auto encoder = VideoFrameEncoder::CreateVideoEncoder("FfVideoEncoder", j.dump());
    encoder->setGroupId(id);


    auto dstFile = std::make_shared<FileFramePipeline>(j.dump());
    dstFile->setGroupId(id);
    source->addVideoDestination(decoder);
    decoder->addVideoDestination(encoder);
    encoder->addVideoDestination(dstFile);
    videoFileFrameSourcePtr->start();
    jinfo("FfVideoEncoder main() start  end  ");
    loop->run();

    jinfo("FfVideoEncoder main() end  ");
    return 0;
}



