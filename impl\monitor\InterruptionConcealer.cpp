#include "InterruptionConcealer.h"

#include "json.hpp"

namespace panocom
{
InterruptionConcealer::InterruptionConcealer(const std::string &jsonParams)
    : send_flag_(false)
    , timeoutMS_(1000)
    , len_(640)
    , fmt_(FRAME_FORMAT_PCM_16000_1) {
    name_ = "InterruptionConcealer";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    if (j.contains("ptime")) ptime_ = j["ptime"];
    if (j.contains("timeoutMS")) timeoutMS_ = j["timeoutMS"];
    start();
}

InterruptionConcealer::~InterruptionConcealer()
{
    if (check_timeout_thread_.isRunning()) {
        check_timeout_thread_.stop(true);
        jinfo("InterruptionConcealer check_timeout_thread stop success!");
    }
}

void InterruptionConcealer::start() {
    if (check_timeout_thread_.isRunning()) return;
    check_timeout_thread_.loop()->setInterval(timeoutMS_, [this](hv::TimerID id) {
        if (send_flag_ && len_ > 0) {
            auto frame = Frame::CreateFrame(fmt_);
            frame->createFrameBuffer(len_);
            memset(frame->getFrameBuffer(), 0, len_);
            deliverFrame(frame);
        } else {
            send_flag_ = true;
        }
    });
    check_timeout_thread_.start(true); 
}

void InterruptionConcealer::onFrame(const std::shared_ptr<Frame> &frame) {
    send_flag_ = false;
    len_ = frame->getFrameBufferSize();
    fmt_ = frame->getFrameFormat();
    deliverFrame(frame);
}
} // namespace panocom
