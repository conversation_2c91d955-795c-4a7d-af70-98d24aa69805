#include "AudioFrameProcesser.h"

#include "aec/AudioFrameAsFarEnd.h"
#include "aec/RtcAudioFrameAec.h"

#include "ns/RnnAudioFrameNS.h"

#include "audiocodec/AudioFrameResampler.h"
#include "audiocodec/AudioFramePacer.h"

#include "lec/AudioFrameAsLineIn.h"
#include "lec/RtcAudioFrameLec.h"

#include "audioprocessing/PCMGainController.h"

#include <algorithm>

using namespace panocom;

bool AudioFrameProcesser::isRegistered = false;

std::vector<std::string> AudioFrameProcesser::processers_;

void AudioFrameProcesser::RegistProcessers()
{
    if (!isRegistered)
    {
        processers_.push_back("AudioFrameResampler");
        processers_.push_back("AudioFrameAsFarEnd");
        processers_.push_back("RnnAudioFrameNS");
        processers_.push_back("RtcAudioFrameAec");
        processers_.push_back("AudioFramePacer");
        processers_.push_back("AudioFrameAsLineIn");
        processers_.push_back("RtcAudioFrameLec");
        processers_.push_back("PCMGainController");
        isRegistered = true;
    }
}

std::vector<std::string>& AudioFrameProcesser::GetSupportProcessers()
{
    RegistProcessers();
    return processers_;
}

bool AudioFrameProcesser::IsSupportProcesser(const std::string &processerName)
{
    RegistProcessers();
    return std::find(processers_.begin(), processers_.end(), processerName) != processers_.end();
}

std::shared_ptr<AudioFrameProcesser> AudioFrameProcesser::CreateAudioProcesser(const std::string &processerName, const std::string &jsonParams)
{
    std::shared_ptr<AudioFrameProcesser> ret;
    if (processerName == "AudioFrameResampler")
    {
        ret = std::make_shared<AudioFrameResampler>(jsonParams);
    }
    else if (processerName == "AudioFrameAsFarEnd")
    {
        ret = std::make_shared<AudioFrameAsFarEnd>(jsonParams);
    }
    else if (processerName == "RnnAudioFrameNS")
    {
        ret = std::make_shared<RnnAudioFrameNS>(jsonParams);
    }
    else if (processerName == "RtcAudioFrameAec")
    {
        ret = std::make_shared<RtcAudioFrameAec>(jsonParams);
    }
    else if (processerName == "AudioFramePacer")
    {
        ret = std::make_shared<AudioFramePacer>(jsonParams);
    }
    else if (processerName == "RtcAudioFrameLec")
    {
        ret = std::make_shared<RtcAudioFrameLec>(jsonParams);
    }
    else if (processerName == "AudioFrameAsLineIn")
    {
        ret = std::make_shared<AudioFrameAsLineIn>(jsonParams);
    }
    else if (processerName == "PCMGainController")
    {
        ret = std::make_shared<PCMGainController>(jsonParams);
    }
    return ret;
}