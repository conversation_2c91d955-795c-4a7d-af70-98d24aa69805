#include "MediaPipeline.h"
#include <json.hpp>
#include <stdio.h>
#include <signal.h>
#include <ljcore/jlog.h>
#include <string>
#include <hv/EventLoop.h>

using namespace panocom;
extern int mmi_print_state;

int main()
{
    signal(SIGSEGV, [](int) {
        jlog_uninit();
        (void) signal(SIGSEGV, SIG_DFL);  
    }); // 设置退出信号

    signal(SIGINT, [](int) {
        jlog_uninit();
        (void) signal(SIGINT, SIG_DFL);  
    }); // 设置退出信号

    signal(SIGTERM, [](int) {
        jlog_uninit();
        (void) signal(SIGTERM, SIG_DFL);  
    });
    jlog_init(nullptr);
    mmi_print_state = 1;
    auto loop = std::make_shared<hv::EventLoop>();
    nlohmann::json j;
    j["path"] = "4k-test.h264";
    auto source = FileFramePipeline::CreateFileSource("H2645FileFrameSource", j.dump());
    std::vector<std::shared_ptr<VideoFrameDecoder>> decoders;
    std::vector<std::shared_ptr<VideoFrameRender>> cuIPCRenders;
    for (size_t i = 0; i < 16; i++)
    {
        j.clear();
        j["codec"] = "h264";
        j["gpuIndex"] = i < 8 ? 0 : 1;
        auto decoder = VideoFrameDecoder::CreateVideoDecoder("CuVideoDecoder", j.dump());
        decoders.push_back(decoder);
        j.clear();
        j["dev"] = i;
        j["gpuIndex"] = i < 8 ? 0 : 1;
        auto cuIPCRender = VideoFrameRender::CreateVideoRender("CuIPCFrameRender", j.dump());
        cuIPCRenders.push_back(cuIPCRender);
        source->addVideoDestination(decoder);
        decoder->addVideoDestination(cuIPCRender);
    }

    loop->run();
    return 0;
}