// created by gyj 2024-6-19
#ifndef P_AudioSpec_h
#define P_AudioSpec_h

#include "FramePipeline.h"
#include <ptoolkit/bytebuffer.h>
#include <hv/EventLoopThread.h>
#include <malloc.h>
#include <stdio.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <arpa/inet.h>
#include <net/if.h>
#include <string.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <pthread.h>
#include <fcntl.h>
#include <stdint.h>
#include <unordered_map>
#include <PNPcm/HDPcm.h>
#include <resampler/resampler.h>
#include <resampler/push_resampler.h>

namespace panocom
{
    class AudioSpec : public FramePipeline
    {
    public:
        AudioSpec(const std::string& jsonParams);
        ~AudioSpec() override;

        void onFrame(const std::shared_ptr<Frame>& frame) override;
        void start() override;
        void stop() override;
        int updateParam(const std::string& jsonParam) override;

        // deprecated
		void setFrameTimeoutNotify(int ms, void* param, const TimeoutNotifyCallback& cb) override;
		void setFrameTimeoutNotify2(int ms, void* param, const TimeoutNotifyCallback& cb) override;

        bool getStatus(std::string& status) override;
    private:
        static void openDev(int dev);
        static void closeDev(int dev);
    private:
        static std::string devName_;
        static int pcmFd_;
        static int maxChn_;
        static std::vector<int> chnState_;
        static std::mutex mutex_;
        int ptime_ = 20;
        bool block_ = false;
        int samplerate_ = 16000;
        int codec_;
        int dev_;
        bool renderOnly_ = false;
        int fmt_;
        int channel_;

        hv::EventLoopThread readThread_;
        hv::EventLoopThread writeThread_;
        hv::EventLoopThread notifycallbacksThread_;
        static std::shared_ptr<AudioSpec> inst_;
        ByteBuffer bbuf_;
        FILE* sendf = nullptr;
        FILE* recvf = nullptr;

        bool isSetReadThreadAffinity = false;
        bool isSetWriteThreadAffinity = false;

#ifdef WEBRTC_RESAMPLE_ANOTHER
        nswebrtc::PushResampler<int16_t> resampler_;
#else
        nswebrtc::Resampler resampler_;
#endif       
        bool initResampler_ = false;
        int source_samplerate_;
        
        hv::EventLoopThread check_timeout_thread_;

        int debug_mode_;
        int ref_count;

        // 是否开启音量检测，默认关闭
        bool enable_volume_ = false;
        // 音量[0, 100]，每20ms调整一次音量
        int volume_;
    };
}

#endif