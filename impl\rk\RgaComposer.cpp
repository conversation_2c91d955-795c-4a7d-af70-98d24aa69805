#include "RgaComposer.h"
#include <rga/rga.h>
#include <rga/im2d.hpp>
#include <rga/RgaApi.h>
#include <rga/RgaUtils.h>
#include "ljcore/jlog.h"

namespace panocom
{
RgaComposer::RgaComposer(int width, int height, int format)
    : output_width_(width)
    , output_height_(height)
    , format_(format) {}

RgaComposer::~RgaComposer()
{
}

void RgaComposer::beginCompose(int dst_fd) {
    im_rect dst_rect = {};
    rga_buffer_t dst = {};
    int dst_buf_size = output_width_ * output_height_ * get_bpp_from_format(format_);
    int ret = 0;

    rga_buffer_handle_t dst_handle = importbuffer_fd(dst_fd, dst_buf_size);
    if (dst_handle == 0) {
        jerror("import dma_fd error!");
        ret = -1;
        return;
    }

    dst = wrapbuffer_handle(dst_handle, output_width_, output_height_, format_);

    dst_rect.x = 0;
    dst_rect.y = 0;
    dst_rect.width = output_width_;
    dst_rect.height = output_height_;

    ret = imcheck({}, dst, {}, dst_rect, IM_COLOR_FILL);
    if (IM_STATUS_NOERROR != ret) {
        jerror("%d, check error! %s", __LINE__, imStrError((IM_STATUS)ret));
        return;
    }

    ret = imfill(dst, dst_rect, 0xff00ff00);
    if (ret == IM_STATUS_SUCCESS) {
        jerror("beginCompose running success!");
    } else {
        jerror("beginCompose running failed, %s", imStrError((IM_STATUS)ret));
        return;
    }
}
} // namespace panocom
