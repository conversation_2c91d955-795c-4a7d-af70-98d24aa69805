#include "UartWriter.h"
#include "UartCommon.h"
#include <json.hpp>

namespace panocom
{
UartWriter::UartWriter(const std::string& jsonParams) : dev_(0), bitrate_(0), samplerate_(16000), channel_(1), gain_(0), ptime_(20)
{
    FN_BEGIN;
    jinfo("UartWriter %s", jsonParams.c_str());
    name_ = "UartWriter";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    if (j.contains("dev")) dev_ = j["dev"];
    if (j.contains("samplerate")) samplerate_ = j["samplerate"];
    if (j.contains("channel")) channel_ = j["channel"];
    if (j.contains("bitrate")) bitrate_ = j["bitrate"];
    if (j.contains("gain")) gain_ = j["gain"];
    if (j.contains("codec")) codec_ = j["codec"];

    int32_t sample_frame_size = samplerate_ * ptime_ / 1000 * channel_ * sizeof(int16_t);
    encoded_buf_ = new uint8_t[sample_frame_size + sizeof(UartData)];
    start();
    FN_END;
}

UartWriter::~UartWriter()
{
    FN_BEGIN;
    // stop();
    if (!writeThread_.isRunning()) return;
    writeThread_.stop(true);
    writeThread_.join();
    if (encoded_buf_) delete[] encoded_buf_;
    encoded_buf_ = nullptr;
    FN_END;
}

void UartWriter::onFrame(const std::shared_ptr<Frame>& frame) {
    if (!writeThread_.isRunning()) return;
    writeThread_.loop()->runInLoop([this, frame]() {
        char *data = frame->getFrameBuffer();
        int32_t len = frame->getFrameSize();
        if (data && len > 0 && UartChnlSta()) {
            UartData *uart_data = (UartData *)encoded_buf_;
            uart_data->magic = 1;
            uart_data->gain = gain_;
            uart_data->len = len;
            // uart_data->codec = codec_;
            uart_data->codec = frame->getFrameFormat();
            memcpy(encoded_buf_ + sizeof(UartData), data, len);
            bool ok = SendUartMsg(dev_, encoded_buf_, sizeof(UartData) + len);
            // jinfo("SendUartMsg : %d", ok);
        }
    });
}
void UartWriter::start() {
    ClearSendQue(dev_);
    if (writeThread_.isRunning()) return;
    writeThread_.start();
}
void UartWriter::stop() {
    // if (!writeThread_.isRunning()) return;
    // writeThread_.stop(true);
    // writeThread_.join();
}

int UartWriter::updateParam(const std::string& jsonParams) {
    jinfo("UartWriter updateParam: %s", jsonParams.c_str());
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    if (j.contains("samplerate")) samplerate_ = j["samplerate"];
    if (j.contains("bitrate")) bitrate_ = j["bitrate"];
    if (j.contains("channel")) bitrate_ = j["channel"];
    if (j.contains("gain")) gain_ = j["gain"];
    if (j.contains("codec")) codec_ = j["codec"];
    return 0;
}
} // namespace panocom
