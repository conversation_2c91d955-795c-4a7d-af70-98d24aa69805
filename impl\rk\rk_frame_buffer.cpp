#include "rk_frame_buffer.h"
// #include "osal/mpp_common.h"
#include "rga/im2d.hpp"
#include "rockchip/mpp_buffer.h"
#include "rockchip/mpp_frame.h"
#include <hv/hlog.h>
#include <rga/rga.h>
#include <ljcore/jlog.h>

#include <unordered_map>

namespace panocom {
namespace {
std::unordered_map<std::string, MppFrameFormat> tab_s_MPPpixel {
	{"nv12", MPP_FMT_YUV420SP},
	{"nv16", MPP_FMT_YUV422SP},
    {"bgr3", MPP_FMT_BGR888}
};   
}

RKVideoFrameBuffer::RKVideoFrameBuffer(MppFrame frame) : frame_(frame) {
	MppBuffer buffer = mpp_frame_get_buffer(frame_);
	data_ = (int8_t*)mpp_buffer_get_ptr(buffer);;
	size_ = mpp_frame_get_buf_size(frame_);
	width_ = mpp_frame_get_width(frame_);
	height_ = mpp_frame_get_height(frame_);
	hor_stride_ = mpp_frame_get_hor_stride(frame_);
	ver_stride_ = mpp_frame_get_ver_stride(frame_);
}

RKVideoFrameBuffer::~RKVideoFrameBuffer() {
    if (frame_) {
        MppBuffer buffer = mpp_frame_get_buffer(frame_);
        if (buffer) {
            mpp_buffer_put(buffer);
        }
        mpp_frame_deinit(&frame_);
		frame_ = nullptr;
    }
}

std::shared_ptr<RKVideoFrameBuffer> RKVideoFrameBuffer::ConvertFrom(const std::shared_ptr<Frame> &f) {
	return std::make_shared<RKVideoFrameBuffer>(f->getFrameBuffer());
}

bool RKVideoFrameBuffer::CopyFrom(std::shared_ptr<RKVideoFrameBuffer> input, int32_t x, int32_t y, int32_t width, int32_t height) {
	MppBuffer srcbuffer = mpp_frame_get_buffer(input->InnerFrame());
	if (!srcbuffer) return false;
	MppBuffer dstbuffer = mpp_frame_get_buffer(InnerFrame());
	if (!dstbuffer) return false;
    RK_U32 src_width = mpp_frame_get_width(input->InnerFrame());
    RK_U32 src_height = mpp_frame_get_height(input->InnerFrame());
    RK_U32 src_hor_stride = mpp_frame_get_hor_stride(input->InnerFrame());
    RK_U32 src_ver_stride = mpp_frame_get_ver_stride(input->InnerFrame());

    RK_U32 dst_width = mpp_frame_get_width(InnerFrame());
    RK_U32 dst_height = mpp_frame_get_height(InnerFrame());
    RK_U32 dst_hor_stride = mpp_frame_get_hor_stride(InnerFrame());
    RK_U32 dst_ver_stride = mpp_frame_get_ver_stride(InnerFrame());

    im_rect src_rect;
    im_rect dst_rect;
    rga_buffer_t src;
    rga_buffer_t dst;
    rga_buffer_handle_t src_handle;
    rga_buffer_handle_t dst_handle;

    int src_fd = mpp_buffer_get_fd(srcbuffer);
    int dst_fd = mpp_buffer_get_fd(dstbuffer);

    im_handle_param_t src_param = {(uint32_t)src_width, (uint32_t)src_height, (uint32_t)RK_FORMAT_YCbCr_420_SP};
    im_handle_param_t dst_param = {(uint32_t)dst_width, (uint32_t)dst_height, (uint32_t)RK_FORMAT_YCbCr_420_SP};

    src_handle = importbuffer_fd(src_fd, &src_param);
    if (src_handle <= 0) {
        LOGE("Failed to importbuffer_fd for src channel!\n");
        return false;
    }
    dst_handle = importbuffer_fd(dst_fd, &dst_param);
    if (dst_handle <= 0) {
        LOGE("Failed to importbuffer_fd for dst channel!\n");
        releasebuffer_handle(src_handle);
        return false;
    }

    src = wrapbuffer_handle(src_handle, src_width, src_height, RK_FORMAT_YCbCr_420_SP, src_hor_stride, src_ver_stride);
    dst = wrapbuffer_handle(dst_handle, dst_width, dst_height, RK_FORMAT_YCbCr_420_SP, dst_hor_stride, dst_ver_stride);

    int usage = 0;
    IM_STATUS ret = IM_STATUS_NOERROR;

    im_opt_t opt { 0 };
    rga_buffer_t pat { 0 };
    im_rect srect { 0 };
    im_rect drect { 0 };
    im_rect prect { 0 };

    // empty_structure(NULL, NULL, &pat, &srect, &drect, &prect, &opt);

    usage |= IM_SYNC;

    srect.width = src_width & ~1;
    srect.height = src_height & ~1;
    drect.x = x & ~1;
    drect.y = y & ~1;
    drect.width = width & ~1;
    drect.height = height & ~1;

    LOGD("CopyFrom size(%d x %d)(%d x %d) to pos(%d, %d) size(%d x %d)(%d x %d)", src_hor_stride, src_ver_stride, srect.width, srect.height, drect.x, drect.y, drect.width, drect.height, dst_hor_stride, dst_ver_stride);

    ret = improcess(src, dst, pat, srect, drect, prect, -1, NULL, &opt, usage);

    releasebuffer_handle(src_handle);
    releasebuffer_handle(dst_handle);
    if (ret == IM_STATUS_SUCCESS)
        return true;
    LOGE("Failed to CopyFrom size(%d x %d)(%d x %d) to pos(%d, %d) size(%d x %d)(%d x %d)", src_hor_stride, src_ver_stride, srect.width, srect.height,  drect.x, drect.y, drect.width, drect.height, dst_hor_stride, dst_ver_stride);
    return false;
}

int RKVideoFrameBuffer::getFD() {
    MppBuffer buffer = mpp_frame_get_buffer(InnerFrame());
	if (!buffer) return false;
    int fd = mpp_buffer_get_fd(buffer);
    return fd;
}

RKBufferManager::RKBufferManager(uint32_t maxFrames) {
    m_maxFrames = maxFrames;
    m_width = 3840;
    m_height = 2160;
	m_buf_grp = nullptr;
	MPP_RET ret = MPP_OK;
    do {
        // MppBufferType type = (MppBufferType)(MPP_BUFFER_TYPE_DMA_HEAP | MPP_BUFFER_FLAGS_DMA32);
        ret = mpp_buffer_group_get_internal(&m_buf_grp, MPP_BUFFER_TYPE_DRM);
        if (ret) {
			jerror("failed to get mpp buffer group ret %d", ret);
            break;
        }
        uint32_t w_stride = MPP_ALIGN(m_width, 16);
        uint32_t h_stride = MPP_ALIGN(m_height, 16);
        int buf_size = w_stride * h_stride * 3 / 2;

        ret = mpp_buffer_group_limit_config(m_buf_grp, buf_size, m_maxFrames);
        if (ret) {
            jerror("limit buffer group failed ret %d", m_maxFrames);
            break;
        }
    } while (0);
}

RKBufferManager::RKBufferManager(uint32_t maxFrames, int width, int height) {
    m_maxFrames = maxFrames;
    m_width = width;
    m_height = height;
	m_buf_grp = nullptr;
	MPP_RET ret = MPP_OK;
    do {
        // MppBufferType type = (MppBufferType)(MPP_BUFFER_TYPE_DMA_HEAP | MPP_BUFFER_FLAGS_DMA32);
        ret = mpp_buffer_group_get_internal(&m_buf_grp, MPP_BUFFER_TYPE_DRM);
        if (ret) {
			jerror("failed to get mpp buffer group ret %d", ret);
            break;
        }
        uint32_t w_stride = MPP_ALIGN(m_width, 16);
        uint32_t h_stride = MPP_ALIGN(m_height, 16);
        int buf_size = w_stride * h_stride * 3 / 2;

        ret = mpp_buffer_group_limit_config(m_buf_grp, buf_size, m_maxFrames);
        if (ret) {
            jerror("limit buffer group failed ret %d", m_maxFrames);
            break;
        }
    } while (0);    
}

RKBufferManager::~RKBufferManager() {
    LOGI("~RKBufferManager");
    MPP_RET ret = MPP_OK;
    if (m_buf_grp) {
        ret = mpp_buffer_group_clear(m_buf_grp);
        if (ret) {
            jerror("clear buffer group failed ret %d", ret);
        }
    }
}

std::shared_ptr<RKVideoFrameBuffer> RKBufferManager::getFreeBuffer(uint32_t width, uint32_t height) {
    MPP_RET ret = MPP_OK;
    // if (m_width != width || m_height != height) {
    //     m_width = width;
    //     m_height = height;
    //     if (m_buf_grp) {
    //         ret = mpp_buffer_group_clear(m_buf_grp);
    //         if (ret) {
    //             ELOG_ERROR_T("clear buffer group failed ret %d\n", ret);
    //         }
    //     }
    // }

    uint32_t w = MPP_ALIGN(width, 2);
    uint32_t h = MPP_ALIGN(height, 2);
    int buf_size = w * h * 3 / 2;

    MppFrame frame;
    ret = mpp_frame_init(&frame);
    if (ret) {
        jerror("mpp_frame_init failed");
        return nullptr;
    }

    MppBuffer buf;
    ret = mpp_buffer_get(m_buf_grp, &buf, buf_size);
    if (ret) {
        jerror("failed to get buffer for input frame ret %d", ret);
        return nullptr;
    }

    mpp_frame_set_width(frame, w);
    mpp_frame_set_height(frame, h);
    mpp_frame_set_hor_stride(frame, w);
    mpp_frame_set_ver_stride(frame, h);
    mpp_frame_set_fmt(frame, MPP_FMT_YUV420SP);	// TODO:
    mpp_frame_set_eos(frame, 1);
    mpp_frame_set_buffer(frame, buf);

    auto fb = std::make_shared<RKVideoFrameBuffer>(frame);
    LOGI("success get buffer for input frame(0x%x) %d x %d\n", fb.get(), w, h);

    return fb;
}

MppFrame RKBufferManager::getFreeFrame(uint32_t width, uint32_t height) {
    MPP_RET ret = MPP_OK;
    // if (m_width != width || m_height != height) {
    //     m_width = width;
    //     m_height = height;
    //     if (m_buf_grp) {
    //         ret = mpp_buffer_group_clear(m_buf_grp);
    //         if (ret) {
    //             ELOG_ERROR_T("clear buffer group failed ret %d\n", ret);
    //         }
    //     }
    // }

    uint32_t w = MPP_ALIGN(width, 16);
    uint32_t h = MPP_ALIGN(height, 16);
    int buf_size = w * h * 3 / 2;               // TODO: 现仅支持nv12

    MppFrame frame = nullptr;
    ret = mpp_frame_init(&frame);
    if (ret) {
        jerror("mpp_frame_init failed %d\n", ret);
        return nullptr;
    }

    MppBuffer buf;
    ret = mpp_buffer_get(m_buf_grp, &buf, buf_size);
    if (ret) {
        jerror("%d: failed to get buffer %d for input frame ret %d", __LINE__, buf_size, ret);
        return nullptr;
    }
	mpp_buffer_put(buf);
    mpp_frame_set_width(frame, width);
    mpp_frame_set_height(frame, height);
    mpp_frame_set_hor_stride(frame, w);
    mpp_frame_set_ver_stride(frame, h);
    mpp_frame_set_fmt(frame, MPP_FMT_YUV420SP);	// TODO:
    mpp_frame_set_eos(frame, 1);
    mpp_frame_set_buffer(frame, buf);

    return frame;
}

int RKBufferManager::getFreeFrame(uint32_t width, uint32_t height, MppFrame *frame_ptr) {
    MPP_RET ret = MPP_OK;
    // if (m_width != width || m_height != height) {
    //     m_width = width;
    //     m_height = height;
    //     if (m_buf_grp) {
    //         ret = mpp_buffer_group_clear(m_buf_grp);
    //         if (ret) {
    //             ELOG_ERROR_T("clear buffer group failed ret %d\n", ret);
    //         }
    //     }
    // }

    uint32_t w = MPP_ALIGN(width, 16);
    uint32_t h = MPP_ALIGN(height, 16);
    int buf_size = w * h * 3 / 2;               // TODO: 现仅支持nv12

    ret = mpp_frame_init(frame_ptr);
    if (ret) {
        jerror("mpp_frame_init failed %d\n", ret);
        return ret;
    }

    MppFrame frame = *frame_ptr;
    MppBuffer buf;
    ret = mpp_buffer_get(m_buf_grp, &buf, buf_size);
    if (ret) {
        jerror("%d: failed to get buffer %d for input frame ret %d", __LINE__, buf_size, ret);
        return ret;
    }
	mpp_buffer_put(buf);
    mpp_frame_set_width(frame, width);
    mpp_frame_set_height(frame, height);
    mpp_frame_set_hor_stride(frame, w);
    mpp_frame_set_ver_stride(frame, h);
    mpp_frame_set_fmt(frame, MPP_FMT_YUV420SP);	// TODO:
    mpp_frame_set_eos(frame, 1);
    mpp_frame_set_buffer(frame, buf);
    return ret;
}

// TODO: 先暂时只用于v4l2，2对齐
int RKBufferManager::PrepareFrameWithFd(const int& fd, const int& size, const uint32_t& width, const uint32_t& height, const std::string &format_str, MppFrame &frame) {
    mpp_frame_set_width(frame, width);
    mpp_frame_set_height(frame, height);
    mpp_frame_set_hor_stride(frame, MPP_ALIGN(width, 2));
    mpp_frame_set_ver_stride(frame, MPP_ALIGN(height, 2));
    mpp_frame_set_eos(frame, 1);
    auto format = MPP_FMT_YUV420SP;
    if (tab_s_MPPpixel.count(format_str) != 0) format = tab_s_MPPpixel[format_str];
    mpp_frame_set_fmt(frame, format);
    if (fd >= 0) {
        MppBufferInfo info;
        memset(&info, 0, sizeof(info));
        info.type = MPP_BUFFER_TYPE_DRM;
        info.size = size;
        info.fd = fd;
        // info.ptr = ptr;
        MppBuffer buffer;
        int ret = mpp_buffer_import(&buffer, &info);
        if (ret) {
            jerror("import input picture buffer failed\n");
            mpp_buffer_put(buffer);
            return ret;
        }
        mpp_frame_set_buffer(frame, buffer);
        mpp_buffer_put(buffer);
        return MPP_OK;
    }
    return MPP_NOK;
}

WrapRKMppFrame::WrapRKMppFrame() : Frame(FRAME_FORMAT_RK), frame_(nullptr) {
}

WrapRKMppFrame::WrapRKMppFrame(MppFrame frame) : Frame(FRAME_FORMAT_RK), frame_(frame) {
}

WrapRKMppFrame::~WrapRKMppFrame() {
    if (frame_) {
        // MppBuffer buffer = mpp_frame_get_buffer(frame_);
        // if (buffer) {
        //     mpp_buffer_put(buffer);
        // }
        mpp_frame_deinit(&frame_);
        frame_ = nullptr;
    }
}

}