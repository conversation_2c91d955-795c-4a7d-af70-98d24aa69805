#include "AudioFrameMixer.h"
#include "audiomixer/DefaultAuidoFrameMixer.h"

#include <algorithm>

using namespace panocom;

bool AudioFrameMixer::isRegistered = false;

std::vector<std::string> AudioFrameMixer::mixers_;

void AudioFrameMixer::RegistMixers()
{
    if (!isRegistered)
    {
        mixers_.emplace_back("DefaultAudioFrameMixer");
        isRegistered = true;
    }
}

std::vector<std::string>& AudioFrameMixer::GetSupportMixers()
{
    RegistMixers();
    return mixers_;
}

bool AudioFrameMixer::IsSupportMixer(const std::string &mixerName)
{
    RegistMixers();
    return std::find(mixers_.begin(), mixers_.end(), mixerName) != mixers_.end();
}

std::shared_ptr<AudioFrameMixer> AudioFrameMixer::CreateAudioMixer(const std::string &mixerName, const std::string &jsonParams)
{
    std::shared_ptr<AudioFrameMixer> ret;
    if (mixerName == "DefaultAudioFrameMixer") 
    {
        // return DefaultAudioFrameMixerManager::instance().CreateMixer(jsonParams);
        return std::make_shared<DefaultAudioFrameMixer>(jsonParams);
    }
    return ret;
}