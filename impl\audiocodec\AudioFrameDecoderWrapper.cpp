#include "AudioFrameDecoderWrapper.h"
#include <json.hpp>
#include<stdio.h>
#include "CodecInfo.h"
namespace panocom {
AudioFrameDecoderWrapper::AudioFrameDecoderWrapper(const std::string &jsonParams)
    : decoder_(nullptr)
    , fmt_(FRAME_FORMAT_UNKNOWN)
    , setted_pt_(-1)
    , isNative_(true) {
    name_ = "AudioFrameDecoderWrapper";
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    if (j.contains("native")) isNative_ = j["native"];
}

AudioFrameDecoderWrapper::~AudioFrameDecoderWrapper()
{
    destinations_.clear();
}

FramePipeline::Ptr AudioFrameDecoderWrapper::addAudioDestination(const FramePipeline::Ptr& dst) {
    for(const auto& wptr : destinations_){
        auto ptr = wptr.lock();
        if(ptr == dst)return dst;
    }
    FramePipeline::addAudioDestination(dst);
    destinations_.push_back(dst);
    if (decoder_) decoder_->addAudioDestination(dst);
    return dst;
}

FramePipeline::Ptr AudioFrameDecoderWrapper::removeAudioDestination(const FramePipeline::Ptr& dst) {
    for(auto it = destinations_.begin(); it != destinations_.end(); it++){
        auto ptr = (*it).lock();
        if(ptr == dst){
            FramePipeline::removeAudioDestination(dst);
            destinations_.erase(it);
            if (decoder_) decoder_->removeAudioDestination(dst);
            break;
        }
    }
    return dst;
}

void AudioFrameDecoderWrapper::onFrameTransfer(const std::shared_ptr<Frame> &frame) {
    // FN_BEGIN;
    if (frame) {
        int fmt = frame->getFrameFormat();
        int pt = frame->getPayloadType();
        if ((FrameFormat)fmt != fmt_ || pt != setted_pt_ || !decoder_) {
            fmt_ = (FrameFormat)fmt;
            setted_pt_ = pt;
            if (fmt_ == FRAME_FORMAT_RTP)
                ReconfigureDecoder(frame->getPayloadType());
            else
                ReconfigureDecoder();
        }
        // jinfo("decoderWrapperFrameTransfer");
        if (decoder_) {
            // jinfo("decoderwrapperfmt: %d", fmt);
            decoder_->onFrameTransfer(frame);
        }
    }
}

void AudioFrameDecoderWrapper::ReconfigureDecoder() {
    jinfo("ReconfigureDecoder: %d", fmt_);
    if (decoder_) {
        for (auto &dst: destinations_) {
            auto ptr = dst.lock();
            if(ptr)
                decoder_->removeAudioDestination(ptr);
        }
        decoder_->reset();
    }
    std::string name;
    int32_t samplerate, chn, bitrate;
    if (Frame::getCodecConfig(fmt_, name, samplerate, chn, bitrate)) {
        jinfo("reconfigDecoder fmt:%d, name:%s, samplerate:%d, chn:%d, bitrate:%d",
              fmt_, name.c_str(), samplerate, chn, bitrate);
        nlohmann::json j;
        j["codec"] = name;
        j["samplerate"] = samplerate;
        j["bitrate"] = bitrate;
        j["channel"] = chn;
        if (isNative_) {
            decoder_ = AudioFrameDecoder::CreateAudioDecoder("native", j.dump());
        } else {
            decoder_ = AudioFrameDecoder::CreateAudioDecoder("NetEqAudioDecoder", j.dump());
        }
        for (auto &dst: destinations_) {
            auto ptr = dst.lock();
            if(ptr)
                decoder_->addAudioDestination(ptr);
        }        
    }
}

void AudioFrameDecoderWrapper::ReconfigureDecoder(int pt) {
    jinfo("ReconfigureDecoder: pt %d", pt);
    if (decoder_) {
        for (auto &dst: destinations_) {
            auto ptr = dst.lock();
            if(ptr)
                decoder_->removeAudioDestination(ptr);
        }
        decoder_->reset();
    }
    CodecInst codec;
    if (getCodecInst(pt, codec)) {
        jinfo("reconfigDecoder fmt:%d, name:%s, samplerate:%d, chn:%d, bitrate:%d",
              codec.fmt, codec.name, codec.rate, codec.chn, codec.bitrate);
        nlohmann::json j;
        std::string name(codec.name);
        j["codec"] = name;
        j["samplerate"] = codec.rate;
        j["bitrate"] = codec.bitrate;
        j["channel"] = codec.chn;

        if (isNative_) {
            decoder_ = AudioFrameDecoder::CreateAudioDecoder("native", j.dump());
        } else {
            decoder_ = AudioFrameDecoder::CreateAudioDecoder("NetEqAudioDecoder", j.dump());
        }
        for (auto &dst: destinations_) {
            auto ptr = dst.lock();
            if(ptr)
                decoder_->addAudioDestination(ptr);
        }        
    }
}

} // namespace panocom
