cmake_minimum_required(VERSION 3.10)

project(MMIService)

set(CMAKE_CXX_STANDARD 17)

include_directories(${CMAKE_CURRENT_SOURCE_DIR} ${CMAKE_PREFIX_PATH}/include)
link_directories(${CMAKE_PREFIX_PATH}/lib)

if (USE_CUDA)
    link_directories(/usr/local/cuda-12.4/targets/x86_64-linux/lib)
endif()

add_definitions(-Wall -O3 -g -fexceptions -fpermissive)
add_executable(${PROJECT_NAME} main.cpp)

if (USE_CUDA)
    message("${CUDA_CUDA_LIBRARY} ${CUDART_LIB}/${COMMIT_TIME} ${BUILD_TIME}")
    target_link_libraries(${PROJECT_NAME} PRIVATE MediaPipeline RpcProxy PChannelInterface protobuf ptoolkit jrtp jthread ljcore hv_static ZLToolKit proto ssl crypto aec rnnoise opus pthread dl nppicc nppig ${CUDA_CUDA_LIBRARY} ${CUDART_LIB} ${CMAKE_DL_LIBS} ${NVENCODEAPI_LIB} ${CUVID_LIB} ${FREEGLUT_LIB} ${GLEW32_LIB} ${X11_LIB} ${GL_LIB})
else ()
    target_link_libraries(${PROJECT_NAME} PRIVATE MediaPipeline RpcProxy PChannelInterface protobuf ptoolkit jrtp jthread ljcore hv_static aec rnnoise opus ZLToolKit PNPcm proto ssl crypto pthread dl)
endif ()

