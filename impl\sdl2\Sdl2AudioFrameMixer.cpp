#include "Sdl2AudioFrameMixer.h"
#include "Frame.h"
#include "Utils.h"
#include <json.hpp>
#include <ljcore/jlog.h>
#include <stdio.h>

using namespace panocom;

Sdl2AudioFrameMixer::Sdl2AudioFrameMixer(const std::string& jsonParams)
{
    FN_BEGIN;
    jinfo("Sdl2AudioFrameMixer %s", jsonParams.c_str());
    name_ = "Sdl2AudioFrameMixer";
    thread_.start();
    FN_END;
}

Sdl2AudioFrameMixer::~Sdl2AudioFrameMixer()
{
    FN_BEGIN;
    thread_.stop(true);
    FN_END;
}

void Sdl2AudioFrameMixer::onFrame(const std::shared_ptr<Frame>& frame)
{
    
}