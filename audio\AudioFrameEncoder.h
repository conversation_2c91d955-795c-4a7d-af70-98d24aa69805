#ifndef P_AudioFrameEncoder_h
#define P_AudioFrameEncoder_h

#include "../FramePipeline.h"
#include "../CodecInfo.h"

namespace panocom
{
    class AudioFrameEncoder : public FramePipeline
    {
    public:
        static void Regist();
        static std::list<std::string>& GetSupportEncoderTypes();
        static bool IsSupportEncoderType(const std::string &encoderType);
        static std::shared_ptr<AudioFrameEncoder> CreateAudioEncoder(const std::string &encoderType, const std::string& jsonParams = "");

        static std::list<CodecInst>& GetSupportCodecs();
        static bool IsSupportCodec(const CodecInst &codec);
        static bool IsAudioFrameEncoder(const FramePipeline::Ptr& ptr);

        virtual bool updateParams(const std::string& jsonParams) { return false; }

        virtual ~AudioFrameEncoder() = default;
    protected:
        static bool isRegistered;
        static std::list<CodecInst> codecs_;
        static std::list<std::string> encoderTypes_;
    };
}

#endif