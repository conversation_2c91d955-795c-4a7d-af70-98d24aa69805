#ifndef IMPL_RK_RKFILEFRMAEREADER_H_
#define IMPL_RK_RKFILEFRMAEREADER_H_
#include <memory>
#include <fstream>
#include <future>

#include <rockchip/rk_type.h>
#include <rockchip/rk_mpi.h>
#include <rockchip/mpp_log.h>
#include <hv/EventLoopThread.h>

#include "file/FileFramePipeline.h"
#include "rk_frame_buffer.h"
namespace panocom
{
typedef enum {
    FILE_NORMAL_TYPE,
    FILE_JPEG_TYPE,
    FILE_IVF_TYPE,
    FILE_BUTT,
} FileType;

class RKFileFrameReader : public FileFramePipeline
{
public:
    static std::shared_ptr<RKFileFrameReader> Create(const std::string& jsonParams);
    static void check_file_type(std::string &file_name, FileType &type);
    explicit RKFileFrameReader(const std::string &jsonParams);
    ~RKFileFrameReader() override;
private:
    
    void init();
    void deinit();
    MPP_RET InitDecode();
    void run();
    void read_normal_file() {}
    void read_jpeg_file();
    void read_ivf_file() {}
    
private:
    bool running_ = false;
    bool init_done_ = false;
    std::string file_name_;
    std::ifstream ifs_;
    size_t file_size_;

    MppCodingType coding_type_;
    FileType        file_type_;
    MppBufferGroup  group_ = nullptr;

    MppBuffer       buf_ = nullptr;
    int8_t          *buf_ptr_ = nullptr;
    MppFrame       frame_;

    MppCtx          dec_ctx_pre_ = nullptr;
    MppApi         *dec_api_pre_ = nullptr;
    MppPacket       dec_pkt_pre_ = nullptr;
    
    hv::EventLoopThread loop_thread_;

    // 只发送一次EOS帧
    bool eos_sent_ = false;

    RK_U32 width_;
    RK_U32 height_;
    std::shared_ptr<RKBufferManager> frameBufferManager_;
};
} // namespace panocom

#endif
