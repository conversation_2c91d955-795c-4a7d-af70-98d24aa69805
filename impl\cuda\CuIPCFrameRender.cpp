#include "CuIPCFrameRender.h"
#include "CuFrame.h"
#include <json.hpp>
#include <cuda_runtime.h>
#include <ljcore/jlog.h>

using namespace panocom;

uint32_t CuIPCFrameRender::convertToRTPTimestamp(struct timeval tv)
{
    // Begin by converting from "struct timeval" units to RTP timestamp units:
    u_int32_t timestampIncrement = (90000 * tv.tv_sec);
    timestampIncrement += (u_int32_t)(90000 * (tv.tv_usec / 1000000.0) + 0.5); // note: rounding

    u_int32_t const rtpTimestamp = timestampBase_ + timestampIncrement;
    return rtpTimestamp;
}

uint32_t CuIPCFrameRender::presetNextTimestamp()
{
    struct timeval timeNow;
    gettimeofday(&timeNow, NULL);

    u_int32_t tsNow = convertToRTPTimestamp(timeNow);
    return tsNow;
}

CuIPCFrameRender::CuIPCFrameRender(const std::string& jsonParams) : seq_(0), timestampBase_(0)
{
    FN_BEGIN;
    name_ = "CuIPCFrameRender";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    
    if (j.contains("dev"))
    {
        dev_ = j["dev"];
    }
    frameCount_ = 8;
    if (j.contains("frameCount"))
    {
        frameCount_ = j["frameCount"];
    }
    cudaId_ = 0;
    if (j.contains("cudaId"))
    {
        cudaId_ = j["cudaId"];
    }
    gpuIndex_ = 0;
    if (j.contains("gpuIndex"))
    {
        gpuIndex_ = j["gpuIndex"];
    }
    jinfo("CuIPCFrameRender use GPU %d", gpuIndex_);
    start();
    FN_END;
}

CuIPCFrameRender::~CuIPCFrameRender()
{
    FN_BEGIN;
    stop();
    {
        std::unique_lock<std::mutex> locker(mutex_);
        if (check_timeout_thread_.isRunning())
        {
            check_timeout_thread_.stop(true);
            jinfo("check_timeout_thread stop success!");
        }
    }
    FN_END;
}

void CuIPCFrameRender::start()
{
    if (thread_.isRunning()) return;
    FN_BEGIN;
    std::string shmName = "CuIPC";
    shmName += std::to_string(dev_);
    shm_.sharedMemoryCreate(shmName.c_str(), sizeof(FrameShm) * frameCount_);
    frame_ = (FrameShm*)shm_.data();
    if (frame_)
    {
        memset(frame_, 0, sizeof(FrameShm) * frameCount_);
        frame_->sync = 1;
    }
    thread_.start();
    last_tick_ = std::chrono::steady_clock::now();
    FN_END;
}

void CuIPCFrameRender::stop() 
{
    if (!thread_.isRunning()) return;
    FN_BEGIN;
    {
        std::unique_lock<std::mutex> locker(mutex_);
        thread_.stop(true);
        cuCtxPushCurrent(*CuCommon::instance().getCudaContext(cudaId_, gpuIndex_));
        ipcFrames_.clear();
        shm_.sharedMemoryClose();
		cuCtxPopCurrent(NULL);
    }
    FN_END;
}

void CuIPCFrameRender::onFrame(const std::shared_ptr<Frame> &frame)
{
    std::unique_lock<std::mutex> locker(mutex_);
    if (!thread_.isRunning()) return;
    frames_recieved_++;
    thread_.loop()->runInLoop([frame, this](){
        if (!frame->getFrameBuffer()) {
            jerror("CuIPCFrameRender[%p] onframe frame->getFrameBuffer() is nullptr", this);
            return;
        }
        cudaIpcMemHandle_t t;
        cuCtxPushCurrent(*CuCommon::instance().getCudaContext(cudaId_, gpuIndex_));
        cudaError_t ret = cudaIpcGetMemHandle((cudaIpcMemHandle_t *)&t, frame->getFrameBuffer());
        cuCtxPopCurrent(NULL);
        if (ret != cudaSuccess) {
            jerror("cudaIpcGetMemHandle %p failed ret = %d", this, ret);
            return;
        }
        FrameShm* f = findFrameToFill();
        if (f)
        {
            f->seq = ++seq_;
            f->timestamp = presetNextTimestamp();
            f->memHandle = t;
            f->status = 1;
            f->width = frame->width();
            f->height = frame->height();
            f->hStride = frame->hStride();
            f->vStride = frame->vStride();
            std::lock_guard<std::mutex> locker(mutex_);
            ipcFrames_[t] = frame;
            printOutputStatus("CuIPCFrameRender");
            frames_to_render_++;
            //jinfo("CuIPCFrameRender::onFrame(%d) fill %d", dev_, f->seq);
        }
        else
        {
            //jinfo("CuIPCFrameRender::onFrame(%d) not fill", dev_);
        }
        auto now_tick = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now_tick - last_tick_).count();
        if (duration > 10000) {
            jinfo("CuIPCFrameRender[%p] [%d] duration: %dms, frames_recieved: %d, frames_to_render: %d", this, dev_, duration, frames_recieved_, frames_to_render_);
            frames_recieved_ = 0;
            frames_to_render_ = 0;
            last_tick_ = now_tick;
        }
    });
    printInputStatus("CuIPCFrameRender");
}

FrameShm* CuIPCFrameRender::findFrameToFill()
{
    FrameShm* ret = nullptr;

    // 根据下半部分的信息，移除上半部的信息
    for (size_t i = frameCount_ / 2; i < frameCount_; i++)
    {
        if (frame_[i].status == 1)
        {
            uint32_t seq = frame_[i].seq;
            //jinfo("wait free %d", seq);
            for (size_t j = 0; j < frameCount_ / 2; j++)
            {
                if (frame_[j].status && frame_[j].seq == seq)
                {
                    //jinfo("free %d", seq);
                    frame_[j].status = 0;
                    std::lock_guard<std::mutex> lock(mutex_);
                    if (ipcFrames_.count(frame_[j].memHandle) != 0)
                        ipcFrames_.erase(frame_[j].memHandle);
                }
            }
        }
    }
    
    // 返回上半部分的空闲帧
    for (size_t i = 0; i < frameCount_; i++)
    {
        if (i < frameCount_ / 2)
        {
            if (frame_[i].status == 0)
            {
                ret = frame_ + i;
                break;
            }
        }
    }
    return ret;
}

void CuIPCFrameRender::setFrameTimeoutNotify(int ms, void* param, const TimeoutNotifyCallback& cb) {
    if (ms <= 0) {
        jinfo("CuIPCFrameRender setFrameTimeoutNotify timeout must > 0");
        return;
    }
    jinfo("CuIPCFrameRender setFrameTimeoutNotify %d", ms);
    FramePipeline::setFrameTimeoutNotify(ms, param, cb);
    std::lock_guard<std::mutex> lock(mutex_);
    if (!check_timeout_thread_.isRunning()) {
        check_timeout_thread_.loop()->setInterval(30, [this](hv::TimerID id) {
            checkFrameTimeout();
        });
        check_timeout_thread_.start(true);
    }
}

void CuIPCFrameRender::clearCache() {
    jinfo("CuIPCFrameRender %p clearCache", this);
    std::lock_guard<std::mutex> lock(mutex_);
    ipcFrames_.clear();
}
