{"name": "csph3", "audio": {"capturer": {"name": "AudioSpecMulticaster", "samplerate": 16000, "channel": 1, "dev": -1}, "render": {"name": "AudioSpecMulticaster", "samplerate": 16000, "channel": 1, "dev": -1}, "encoder": {"name": "native", "codec": [{"name": "opus", "samplerate": 16000, "channel": 1, "fec": 1, "rc": "vbr"}, {"name": "g711", "samplerate": 8000, "channel": 1}]}, "decoder": {"name": "native", "codec": [{"name": "opus", "samplerate": 16000, "channel": 1, "fec": 1, "rc": "vbr"}, {"name": "g711", "samplerate": 8000, "channel": 1}]}, "uplink": {"processer": [], "file-src": {"name": "PCMFileFrameSource"}, "rtp": "JRtpEngine"}, "downlink": {"processer": [], "file-src": {"name": "PCMFileFrameSource", "codec": "PCMA"}, "rtp": "JRtpEngine"}}}