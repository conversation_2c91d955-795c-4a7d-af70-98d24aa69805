//
// Created by li-weibing on 2025/4/23.
//

#ifdef ENABLE_VIDEO
#ifndef MEDIAPIPELINE_FFRENDER_H
#define MEDIAPIPELINE_FFRENDER_H

#include "VideoFrameRender.h"
#include <hv/EventLoopThread.h>

#ifdef USE_FFMPEG
extern "C" {
#include <libavutil/imgutils.h>
#include <libavutil/opt.h>
#include <libavcodec/avcodec.h>
#include <libswscale/swscale.h>
}
#endif

#include "FrameSender.h"

namespace panocom
{
    class FfIPCFrameRender : public VideoFrameRender
    {
    public:
        FfIPCFrameRender(const std::string& jsonParams);
        ~FfIPCFrameRender() override;
        void onFrame(const std::shared_ptr<Frame>& frame) override;
        void start() override;
        void stop() override;
    private:
        hv::EventLoopThread thread_;
    };
}

#endif
#endif

