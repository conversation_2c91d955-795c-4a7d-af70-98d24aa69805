#ifndef P_AudioFrameCapturer_h
#define P_AudioFrameCapturer_h

#include "../FramePipeline.h"

namespace panocom
{
    class AudioFrameCapturer : public FramePipeline
    {
    public:
        static void RegistCapturers();
        static std::vector<std::string>& GetSupportCapturers();
        static bool IsSupportCapturer(const std::string &captureName);
        static std::shared_ptr<AudioFrameCapturer> CreateAudioCapturer(const std::string &captureName, const std::string &jsonParams = "");
        static bool isAudioFrameCapturer(const FramePipeline::Ptr& ptr);
        static std::string loadDefaultConfig(const std::string& name);
        
        AudioFrameCapturer() = default;
        AudioFrameCapturer(const FramePipeline::Ptr& ptr)
        { 
            proxy_ = ptr; 
            proxy_->start(); 
        }

        virtual ~AudioFrameCapturer()
        {
            if (proxy_)
            {
                proxy_->stop();
            }
        }

        std::string name() override
        {
            if (proxy_)
            {
                return proxy_->name();
            }
            return name_;
        }
        void setGroupId(int groupId) override {
            if (proxy_) {
                proxy_->setGroupId(groupId);
            } else {
                return FramePipeline::setGroupId(groupId);
            }
        }
        void onFrameTransfer(const std::shared_ptr<Frame> &f) override 
        { 
            if(proxy_) 
            {
                proxy_->onFrameTransfer(f);
            }
            else
            {
                return FramePipeline::onFrameTransfer(f);
            }
        }
        FramePipeline::Ptr addAudioDestination(const FramePipeline::Ptr& dst)  override 
        { 
            if(proxy_) 
            {
                return proxy_->addAudioDestination(dst);  
            }
            else
            {
                return FramePipeline::addAudioDestination(dst);
            }
        }

        FramePipeline::Ptr removeAudioDestination(const FramePipeline::Ptr& dst) override
        { 
            if(proxy_) 
            {
                return proxy_->removeAudioDestination(dst);  
            }
            else
            {
                return FramePipeline::removeAudioDestination(dst);
            }
        }

        FramePipeline::Ptr addVideoDestination(const FramePipeline::Ptr& dst) override
        { 
            if(proxy_) 
            {
                return proxy_->addVideoDestination(dst);  
            }
            else
            {
                return FramePipeline::addVideoDestination(dst);
            }
        }

        FramePipeline::Ptr removeVideoDestination(const FramePipeline::Ptr& dst) override
        { 
            if(proxy_) 
            {
                return proxy_->removeVideoDestination(dst);  
            }
            else
            {
                return FramePipeline::removeVideoDestination(dst);
            }
        }

        FramePipeline::Ptr addDataDestination(const FramePipeline::Ptr& dst) override
        { 
            if(proxy_) 
            {
                return proxy_->addDataDestination(dst);  
            }
            else
            {
                return FramePipeline::addDataDestination(dst);
            }
        }

        FramePipeline::Ptr removeDataDestination(const FramePipeline::Ptr& dst) override
        { 
            if(proxy_) 
            {
                return proxy_->removeDataDestination(dst);  
            }
            else
            {
                return FramePipeline::removeDataDestination(dst);
            }
        }
        std::list<FramePipeline::WPtr> getAudioDestinations() {
            if(proxy_) 
            {
                return proxy_->getAudioDestinations();  
            }
            else
            {
                return FramePipeline::getAudioDestinations();
            }
        }
        std::list<FramePipeline::WPtr> getDataDestinations() {
            if(proxy_) 
            {
                return proxy_->getDataDestinations();  
            }
            else
            {
                return FramePipeline::getDataDestinations();
            }
        }
        int updateParam(const std::string &jsonParams) override
        {
            if(proxy_) 
            {
                return proxy_->updateParam(jsonParams);  
            }
            else
            {
                return FramePipeline::updateParam(jsonParams);
            }
        }
        bool getStatus(std::string &status) override { 
            if(proxy_) 
            {
                return proxy_->getStatus(status);  
            }
            else
            {
                return FramePipeline::getStatus(status);
            } 
        }
        void setNotify(const std::string &key, const std::string &jsonParam, void *param, const NotifyCallback &cb, bool use_async = true) override {
            if (proxy_) {
                return proxy_->setNotify(key, jsonParam, param, cb, use_async);
            } else {
                return FramePipeline::setNotify(key, jsonParam, param, cb, use_async);
            }
        }

    protected:
        static bool isRegistered;
        static std::vector<std::string> capturers_;
        FramePipeline::Ptr proxy_;
    };
}

#endif