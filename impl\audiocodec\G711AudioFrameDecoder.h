// created by gyj 2024-4-1
#ifndef P_G711AudioFrameDecoder_h
#define P_G711AudioFrameDecoder_h

#include "AudioFrameDecoder.h"
#include "Frame.h"

namespace panocom
{
    class G711AudioFrameDecoder : public AudioFrameDecoder
    {
    public:
        G711AudioFrameDecoder(const std::string& jsonParams);
        ~G711AudioFrameDecoder() override;

        void onFrame(const std::shared_ptr<Frame> &f) override;
    private:
        FrameFormat fmt_;
        int samplerate_;
        FILE* pf_ = nullptr;

        int debug_mode_;
    };
}

#endif