#include "FramePipeline.h"
#include "CuFrameBufferManager.h"
#include "CuFrame.h"
#include <ljcore/jlog.h>
#include <json.hpp>

using namespace panocom;

CuFrameBufferManager::CuFrameBufferManager(int cudaId, int gpuIndex, int maxFrame, int width, int height)
{
    FN_BEGIN;
    jinfo("NvFrameBufferManager() %d %d x %d", maxFrame, width, height);
    width_ = width;
    height_ = height;
    for (size_t i = 0; i < maxFrame; i++)
    {
        CuNV12Frame* f = new CuNV12Frame(cudaId, gpuIndex);
        f->createFrameBuffer(width, height, width, height);
        frames_.push_back(f);
    }
    FN_END;
}

CuFrameBufferManager::~CuFrameBufferManager()
{
    FN_BEGIN;
    for (auto it = frames_.begin(); it != frames_.end(); it++)
    {
        delete *it;
    }
    FN_END;
}
        
std::shared_ptr<Frame> CuFrameBufferManager::getFrame(const std::string& jsonParams)
{
    Frame* ptr = nullptr;
    {
        std::unique_lock<std::mutex> lock(mutex_);
        if (frames_.empty())
        {
            jinfo("getFrame() nullptr");
            return nullptr;
        }
        ptr = frames_.front();
        frames_.pop_front();
    }
    
    std::shared_ptr<Frame> f = std::shared_ptr<Frame>(ptr, [this](Frame* f){
        std::unique_lock<std::mutex> lock(mutex_);
        frames_.push_back(f);
    });    
    return f;
}