
#ifdef ENABLE_VIDEO
#ifdef USE_FFMPEG
#include "VideoFileFrameSource.h"
#include "Frame.h"
#include "Utils.h"
#include <ljcore/jlog.h>
#include <json.hpp>

using namespace panocom;
void VideoFileFrameSource::readAVPackets()
{
    int ret = -1;
    do{
        AVPacket* pkt = av_packet_alloc();
        ret = av_read_frame(fmtCtx_, pkt);
        if(ret >= 0) {
            if (pkt->stream_index == videoStreamIndex_) {
                std::shared_ptr<Frame> f = std::make_shared<FFAVPacket>(pkt);
                frames_.push_back(f);
            }else{
                av_packet_unref(pkt);//audio
            }
        }else{
           av_packet_unref(pkt);
        }
    }while(ret>=0);
    jinfo("readAVPackets frames_.size = %d", frames_.size());
}

VideoFileFrameSource::VideoFileFrameSource(const std::string& jsonParams) : FileFramePipeline(jsonParams)
{
    FN_BEGIN;
    name_ = "VideoFileFrameSource";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    path_ = "";
    if (j.contains("path"))
    {
        path_ = j["path"];
    }
    fps_ = 25;
    if (j.contains("fps"))
    {
        fps_ = j["fps"];
    }

    speed_ = 1.0f;
    if (j.contains("speed"))
    {
        speed_ = j["speed"];
    }


    init();

    FN_END;
}

void VideoFileFrameSource::init(){
    if (path_.empty()) {
        jerror("path_为空");
        return;
    }

    fmtCtx_ = avformat_alloc_context();
    if (!fmtCtx_) {
        jerror("无法分配格式上下文");
        return;
    }

    if (avformat_open_input(&fmtCtx_, path_.c_str(), nullptr, nullptr) < 0) {
        jerror("无法打开输入文件 path:%s",path_.c_str());
        return;
    }

    if (avformat_find_stream_info(fmtCtx_, nullptr) < 0) {
        jerror("无法找到流信息 path:%s",path_.c_str());
        return;
    }

        // 查找视频流
    videoStreamIndex_ = av_find_best_stream(fmtCtx_, AVMEDIA_TYPE_VIDEO, -1, -1, nullptr, 0);
    if (videoStreamIndex_ < 0) {
        jerror("无法找到视频流id path:%s",path_.c_str());
        return;
    }

    AVStream* video_stream = fmtCtx_->streams[videoStreamIndex_];
    if (video_stream < 0) {
        jerror("无法找到视频流 path:%s",path_.c_str());
        return;
    }

    width_  = fmtCtx_->streams[videoStreamIndex_]->codec->width;
    height_ = fmtCtx_->streams[videoStreamIndex_]->codec->height;
    AVRational frameRate = fmtCtx_->streams[videoStreamIndex_]->avg_frame_rate;
    if(frameRate.den>0)
        fps_ = (int) frameRate.num/frameRate.den;
    if(fps_<=0)
        fps_ = 25;
    codec_  = avcodec_get_name(fmtCtx_->streams[videoStreamIndex_]->codecpar->codec_id);

    gop_  = fmtCtx_->streams[videoStreamIndex_]->codecpar->video_delay;
    if(gop_<=0){
        gop_ = fps_;
    }
    jinfo("width_ = %d height_ = %d frameRate.num:%d den:%d fps_ = %d codec_ = %s gop_:%d speed_:%f path_:%s", width_,height_,frameRate.num,frameRate.den,fps_,codec_.c_str(),gop_,speed_,path_.c_str());
    index_ = 0;

    readAVPackets();
}

VideoFileFrameSource::~VideoFileFrameSource()
{
    FN_BEGIN;
    stop();
    FN_END;
}

void VideoFileFrameSource::start()
{
    if (thread_.isRunning()) return;
    FN_BEGIN;
    int gap = (int)(1000.0f / fps_/speed_);
    jinfo("start fps_ = %d  speed_:%f gap:%d", fps_,speed_,gap);

    thread_.loop()->setInterval(gap, [this](hv::TimerID id) {
        if (index_ < frames_.size())
        {
            printOutputStatus("VideoFileFrameSource");
            deliverFrame(frames_[index_]);
            index_++;
        }
//        if (index_ >= frames_.size())
//        {
//            index_ = 0;
//        }
    });
    thread_.start();
    FN_END;
}

void VideoFileFrameSource::stop()
{
    FN_BEGIN;
    thread_.stop(true);
    if (fmtCtx_) {
        avformat_close_input(&fmtCtx_);
        fmtCtx_ = nullptr;
    }
    videoStreamIndex_ = -1;
    FN_END;
}

#endif
#endif