#include "widget.h"
#include <ljcore/jlog.h>

#include "./ui_widget.h"

Widget::Widget(QWidget *parent)
    : QWidget(parent), ui(new Ui::Widget),
      mProfile(new MasterProfile),
      mPollGrp(FdPollGrp::create()),
      mEventInterruptor(new EventThreadInterruptor(*mPollGrp)),
      mDum(mStack),
      mStackThread(mStack, *mEventInterruptor, *mPollGrp),
      mIncommingCall(false)
{
    mClient = std::make_shared<MediaManagerClient>(grpc::CreateChannel("127.0.0.1:12345", grpc::InsecureChannelCredentials()));
    ui->setupUi(this);
    ui->local->setIndex(0);
    ui->remote->setIndex(1);
    initStack();
    connect(&mTimer, &QTimer::timeout, this, [this](){
        mDum.process();
    });
    mTimer.start(20);
    Log::initialize(Log::Cout, Log::Debug, "test");

    auto audioCodecs = mMmi.GetSupportAudioCodecs();
    for (auto it = audioCodecs.begin(); it != audioCodecs.end(); )
    {
        if (strcasecmp(it->name, "opus") == 0)
        {
            audioCodecs.erase(it++);
        }
        else
        {
            it++;
        }
    }
    mMmi.SetAudioCodecPriority(audioCodecs);
}

Widget::~Widget()
{
    delete ui;
}

void Widget::on_pushButton_clicked()
{
    if (mIncommingCall)
    {

    }
    else
    {
        mClient->CreateSession("0");
        std::string sdp;
        int ret = mClient->GetLocalSDP("0", sdp, "{\"video\": true}");
        if (ret == 0)
        {
            HeaderFieldValue hfv(sdp.data(), sdp.size());
            Mime type("Application", "sdp");
            Contents* sdpContent = ContentsFactoryBase::getFactoryMap()[type]->create(hfv, type);
            Data destinationUri = ui->lineEdit->text().toStdString().c_str();
            jinfo("=========================\n%s", sdpContent->getBodyData().c_str());
            // Create the invite message
            auto invitemsg = mDum.makeInviteSession(
                NameAddr(destinationUri), 
                mProfile,
                sdpContent);
            
            invitemsg->header(h_Vias).front().sentHost() = mProfile->getDefaultFrom().uri().host();
            invitemsg->header(h_Vias).front().sentPort() = mProfile->getDefaultFrom().uri().port();
            invitemsg->header(h_Vias).front().transport() = "UDP";

            invitemsg->header(h_Contacts).front().uri() = mProfile->getDefaultFrom().uri();

            Data encoded;
            DataStream encodeStream(encoded);
            invitemsg->encode(encodeStream);
            encodeStream.flush();
            
            jinfo("--------------------------\n%s", encoded.c_str());
            // Send the invite message
            mDum.send(std::move(invitemsg));

            delete sdpContent;
        }
    }
}

void Widget::on_hangup_clicked()
{
    if (mH.isValid())
    {
        mH->end();
    }
}

void Widget::initStack()
{
    // Add transports
    Data host = mMmi.GetDefaultIP().c_str();
    jinfo("host = %s", host.c_str());
    try
    {
        mStack.addTransport(UDP, 5060, V4, StunEnabled);
    }
    catch (BaseException &e)
    {
        std::cerr << "Likely a port is already in use" << endl;
        InfoL << "Caught: " << e;
        exit(-1);
    }

    // Disable Statisitics Manager
    mStack.statisticsManagerEnabled() = false;

    // Setup MasterProfile
    mProfile->clearSupportedMimeTypes();
    mProfile->addSupportedMimeType(INVITE, Mime("application", "sdp"));

    mProfile->clearSupportedMethods();
    mProfile->addSupportedMethod(INVITE);
    mProfile->addSupportedMethod(ACK);
    mProfile->addSupportedMethod(CANCEL);
    mProfile->addSupportedMethod(BYE);

    mProfile->clearSupportedSchemes();
    mProfile->addSupportedScheme("sip");

    NameAddr addr;
    addr.uri().host() = host;
    addr.uri().port() = 5060;

    mProfile->setDefaultFrom(addr);

    mProfile->setUserAgent("sipcall");

    // Install Handlers
    mDum.setMasterProfile(mProfile);

    // Install this Widget as handler
    mDum.setInviteSessionHandler(this);

    mStack.run();
    mStackThread.run();
}

void Widget::onNewSession(ClientInviteSessionHandle h, InviteSession::OfferAnswerType oat, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void Widget::onNewSession(ServerInviteSessionHandle h, InviteSession::OfferAnswerType oat, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void Widget::onFailure(ClientInviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}
      
void Widget::onEarlyMedia(ClientInviteSessionHandle h, const SipMessage& msg, const SdpContents& sdp)
{
    InfoLog (<< msg);
}

void Widget::onProvisional(ClientInviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void Widget::onConnected(ClientInviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void Widget::onConnected(InviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void Widget::onStaleCallTimeout(ClientInviteSessionHandle h)
{
}

void Widget::onTerminated(InviteSessionHandle h, InviteSessionHandler::TerminatedReason reason, const SipMessage* msg)
{
    InfoLog (<< msg);
    mClient->ReleaseSession("0");
}

void Widget::onRedirected(ClientInviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void Widget::onAnswer(InviteSessionHandle h, const SipMessage& msg, const SdpContents& sdp)
{
    InfoLog (<< msg);
    std::string answerSdp = sdp.getBodyData().c_str();
    std::string output;
    int ret = mClient->SetRemoteSDP("0", answerSdp, output);
    mH = h;
}

void Widget::onOffer(InviteSessionHandle h, const SipMessage& msg, const SdpContents& sdp)
{
    InfoLog (<< msg);
    mH = h;    
}

void Widget::onOfferRequired(InviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void Widget::onOfferRejected(InviteSessionHandle h, const SipMessage* msg)
{
    InfoLog (<< msg);
}

void Widget::onOfferRequestRejected(InviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void Widget::onRemoteSdpChanged(InviteSessionHandle h, const SipMessage& msg, const SdpContents& sdp)
{
    InfoLog (<< msg);
}

void Widget::onInfo(InviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void Widget::onInfoSuccess(InviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void Widget::onInfoFailure(InviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void Widget::onRefer(InviteSessionHandle h, ServerSubscriptionHandle ssh, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void Widget::onReferAccepted(InviteSessionHandle h, ClientSubscriptionHandle csh, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void Widget::onReferRejected(InviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void Widget::onReferNoSub(InviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void Widget::onMessage(InviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void Widget::onMessageSuccess(InviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void Widget::onMessageFailure(InviteSessionHandle h, const SipMessage& msg)
{
    InfoLog (<< msg);
}

void Widget::onForkDestroyed(ClientInviteSessionHandle h)
{
}