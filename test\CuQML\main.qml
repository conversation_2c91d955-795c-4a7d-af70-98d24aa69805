
import QtQuick 2.9
import QtQuick.Window 2.2
import QtQuick.Controls 2.2
import an.OpenGLItem 1.0
 
Window
{
    id: win
    visible: true
    width: Qt.platform.os == "android" ? Screen.desktopAvailableWidth  : 1440
    height: Qt.platform.os == "android" ? Screen.desktopAvailableHeight : 800

    OpenGLItem {
        id: openGLItem0; visible: true; anchors.fill: parent; index: 0; 
        Timer {
            interval: 40; 
            running: true; 
            repeat: true
            onTriggered: {
                openGLItem0.callUpdate();
            }
        }
		Timer {
			id: sipTimer
            interval: 10; 
            running: true; 
            repeat: true
            onTriggered: {
                openGLItem0.process();
            }
        }
    }

    Button{
	id: offset_btn
	anchors.right: parent.right
	width: 120
	height: 50
	text: "往右边移动100"
	onClicked: openGLItem0.moveVideoRight()
    }
    Button{
	id: add_render_btn
	anchors.right: parent.right
	anchors.top: offset_btn.bottom
	width: 120
	height: 50
	text: "添加新窗口"
	onClicked: openGLItem0.addVideoPlayWindow()
    }
    Button{
	id: remove_render_btn
	anchors.right: parent.right
	anchors.top: add_render_btn.bottom
	width: 120
	height: 50
	text: "移除新窗口"
	onClicked: openGLItem0.removeVideoPlayWindow()
    }
    Button{
	id: stop_render_btn
	anchors.right: parent.right
	anchors.top: remove_render_btn.bottom
	width: 120
	height: 50
	text: "停止视频"
	onClicked: openGLItem0.stopVideoPlayWindow()
    }
	Button{
	id: z_render_btn
	anchors.right: parent.right
	anchors.top: stop_render_btn.bottom
	width: 120
	height: 50
	text: "切换Z轴"
	onClicked: openGLItem0.setVideoPlayWindowZ()
    }
	TextEdit{
	id: ip_textEdit
	anchors.right: parent.right
	anchors.top: z_render_btn.bottom
	color: "red"
	text: "sip:*************:5060"
	width: 120
	height: 50
    }
	Button{
	id: call_btn
	anchors.right: parent.right
	anchors.top: ip_textEdit.bottom
	width: 120
	height: 50
	text: "呼叫"
	onClicked: openGLItem0.call(ip_textEdit.text)
    }
	Button{
	id: hangup_btn
	anchors.right: parent.right
	anchors.top: call_btn.bottom
	width: 120
	height: 50
	text: "挂断"
	onClicked: openGLItem0.hangup(ip_textEdit.text)
    }
    title: qsTr("Opengl Qt/Quick CUDA test")
}
