#include "MediaPipeline.h"
#include <json.hpp>
#include <stdio.h>
#include <ljcore/jlog.h>
#include <string>
#include <hv/EventLoop.h>

using namespace panocom;

int main(int argc, char *argv[])
{
#ifdef USE_WEBRTC
    auto loop = std::make_shared<hv::EventLoop>();

    jlog_init(nullptr);
    nlohmann::json j;
    nlohmann::json dst;
    j["bindPort"] = 10000;
    j["fmt"] = FRAME_FORMAT_RTP;
    dst["ip"] = "************";
    dst["port"] = 10000;
    j["dst"].push_back(dst);
    auto rtp = std::make_shared<UdpFramePipeline>(j.dump());
    j.clear();
    j["bindPort"] = 10001;
    j["fmt"] = FRAME_FORMAT_RTCP;
    dst["ip"] = "************";
    dst["port"] = 10001;
    j["dst"].push_back(dst);
    auto rtcp = std::make_shared<UdpFramePipeline>(j.dump());
    auto rtcSender = RtpEngine::CreateRTPEngine("webrtcSender", j.dump());

    j.clear();
    if (argc > 1)
    {
        j["dev"] = argv[1];
    }
    auto capturer = VideoFrameCapturer::CreateVideoCapturer("V4L2VideoFrameCapturer", j.dump());
    j.clear();
    j["codec"] = "jpeg";
    auto decoder = VideoFrameDecoder::CreateVideoDecoder("CuVideoDecoder", j.dump());
    j.clear();
    j["codec"] = "h264";
    auto encoder = VideoFrameEncoder::CreateVideoEncoder("CuVideoEncoder", j.dump());

    capturer->addVideoDestination(decoder);
    decoder->addVideoDestination(encoder);
    encoder->addVideoDestination(rtcSender);
    rtcSender->addDataDestination(rtp);
    rtcSender->addDataDestination(rtcp);
    rtcp->addDataDestination(rtcSender);

    loop->run();
#endif
    return 0;
}