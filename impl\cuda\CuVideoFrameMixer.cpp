#include "CuVideoFrameMixer.h"
#include "CuFrameBufferManager.h"
#include "CuCommon.h"
#include "NvCodecUtils.h"
#include "Frame.h"
#include "Utils.h"
#include <json.hpp>
#include <ljcore/jlog.h>
#include <stdio.h>
#include <cuda.h>

using namespace panocom;

CuVideoFrameMixer::CuVideoFrameMixer(const std::string& jsonParams)
{
    FN_BEGIN;
    jinfo("CuVideoFrameMixer %s", jsonParams.c_str());
    name_ = "CuVideoFrameMixer";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    cudaId_ = 0;
    if (j.contains("cudaId"))
    {
        cudaId_ = j["cudaId"];
    }
    gpuIndex_ = 0;
    if (j.contains("gpuIndex"))
    {
        gpuIndex_ = j["gpuIndex"];
    }
    width_ = 1280;
    if (j.contains("width"))
    {
        width_ = j["width"];
    }
    height_ = 720;
    if (j.contains("height"))
    {
        height_ = j["height"];
    }
    int fps = 25;
    if (j.contains("fps"))
    {
        fps = j["fps"];
    }
    int maxFrame = 5;
    if (j.contains("maxFrame"))
    {
        maxFrame = j["maxFrame"];
    }
    outputFrameGap_ = 1000 / fps;
    outputFrameTick_ = GetTickCount();

    frameBufferManager_ = std::make_unique<CuFrameBufferManager>(cudaId_, gpuIndex_, maxFrame, width_, height_);

    if (j.contains("videoLayout") && j["videoLayout"].is_array())
    {
        for (int i = 0; i < j["videoLayout"].size(); ++i)
        {
            if (j["videoLayout"][i].contains("id") &&
                j["videoLayout"][i].contains("rect") &&
                j["videoLayout"][i]["rect"].contains("x") &&
                j["videoLayout"][i]["rect"].contains("y") &&
                j["videoLayout"][i]["rect"].contains("w") &&
                j["videoLayout"][i]["rect"].contains("h"))
            {
                Region r;
                r.id = j["videoLayout"][i]["id"];
                r.rect.x = j["videoLayout"][i]["rect"]["x"];
                r.rect.y = j["videoLayout"][i]["rect"]["y"];
                r.rect.w = j["videoLayout"][i]["rect"]["w"];
                r.rect.h = j["videoLayout"][i]["rect"]["h"];
                jinfo("video layout id = %d x = %d y= %d w = %d h = %d", r.id, r.rect.x, r.rect.y, r.rect.w, r.rect.h);
                videoLayout_.push_back(r);
            }
        }
    }

    thread_.start();

    // thread_ = std::make_unique<std::thread>([this]{
    //     running_ = true;
    //     while (running_)
    //     {
    //         long now = GetTickCount();
    //         long gap = now - outputFrameTick_;
    //         if (gap >= outputFrameGap_)
    //         {
    //             outputFrameTick_ = now;
    //             if (outputFrame_)
    //             {
    //                 deliverFrame(outputFrame_);
    //                 generateOutputFrame();
    //                 printOutputStatus("CuVideoFrameMixer");
    //             }
    //             else
    //             {
    //                 jwarn("outputFrame_ = nullptr");
    //             }
    //         }
    //         std::shared_ptr<Frame> frame;
    //         {
    //             std::unique_lock<std::mutex> lock(mutex_);
    //             if (!frameQueue_.empty())
    //             {
    //                 frame = frameQueue_.front();
    //                 frameQueue_.pop();
    //             }
    //         }
    //         if (!frame)
    //         {
    //             usleep(1000);
    //             continue;
    //         }
    //         else
    //         {
    //             frame = copyFrame(frame);
    //             if (outputFrame_ == nullptr)
    //             {
    //                 generateOutputFrame();
    //             }
    //             if (outputFrame_ && frame)
    //             {
    //                 jinfo("1 %d %d %d", outputFrame_->linesize(0), outputFrame_->width(), outputFrame_->height());
    //                 jinfo("2 %d %d %d", frame->linesize(0), frame->width(), frame->height());
    //                 ResizeNv12(outputFrame_->data(0), outputFrame_->linesize(0), outputFrame_->width(), outputFrame_->height(), frame->data(0), frame->linesize(0), frame->width(), frame->height());
    //             }
    //         }
    //         now = GetTickCount();
    //         //int groupId = frame->getGroupId();
    //         // for (int i = 0; i < videoLayout_.size(); ++i)
    //         // {
    //         //     if (videoLayout_[i].id == groupId)
    //         //     {
    //         //         if (outputFrame_ == nullptr)
    //         //         {
    //         //             generateOutputFrame();
    //         //         }
    //         //         if (outputFrame_)
    //         //         {
    //         //             jinfo("cuda mix");
    //         //             uint8_t* dstY = outputFrame_->data(0) + videoLayout_[i].rect.y * videoLayout_[i].rect.w + videoLayout_[i].rect.x;
    //         //             uint8_t* dstUV =  outputFrame_->data(1) + (videoLayout_[i].rect.y * outputFrame_->linesize(0) + videoLayout_[i].rect.x * 2) / 2;
    //         //             uint8_t* srcY = frame->data(0);
    //         //             uint8_t* srcUV = frame->data(1);
    //         //             // ScaleYUV420(dstY, dstUV, nullptr, outputFrame_->linesize(0), outputFrame_->linesize(0), videoLayout_[i].rect.w, videoLayout_[i].rect.h,
    //         //             //     srcY, srcUV, nullptr, frame->linesize(0), frame->linesize(0), frame->width(), frame->height(), true);
    //         //             ScaleYUV420(dstY, dstUV, nullptr, outputFrame_->linesize(0), outputFrame_->linesize(0) / 2, videoLayout_[i].rect.w, videoLayout_[i].rect.h,
    //         //                 srcY, srcUV, nullptr, frame->linesize(0), frame->linesize(0) / 2, frame->width(), frame->height(), true);
    //         //         }
    //         //     }
    //         // }
    //         gap = GetTickCount() - now;
    //         if (gap > outputFrameGap_)
    //         {
    //             jwarn("nvidia gap = %ld", gap);
    //         }
    //     }
    // });
    FN_END;
}

CuVideoFrameMixer::~CuVideoFrameMixer()
{
    FN_BEGIN;
    FN_END;
}

void CuVideoFrameMixer::onFrame(const std::shared_ptr<Frame>& frame)
{

    // printInputStatus("CuVideoFrameMixer");
    // switch (frame->getFrameFormat())
    // {
    // case FRAME_FORMAT_CU_FRAME:
    // case FRAME_FORMAT_CU_DECODED_NV12:
    //     {
    //         std::unique_lock<std::mutex> lock(mutex_);
    //         frameQueue_.push(frame);
    //     }
    //     break;
    // default:
    //     break;
    // }
}

void CuVideoFrameMixer::generateOutputFrame()
{
    outputFrame_ = frameBufferManager_->getFrame();
}