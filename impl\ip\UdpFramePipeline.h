#ifndef P_UdpFramePipelineDestination_h
#define P_UdpFramePipelineDestination_h

#include "Frame.h"
#include "FramePipeline.h"
#include <set>
#include <tuple>
#include <unordered_set>
#include <Network/Socket.h>
// TEST: 测试丢包
// #define USE_SIMULATION
#ifdef USE_SIMULATION
#include <random>
#endif

namespace panocom
{
    class UdpFramePipeline: public FramePipeline
    {
    public:
        UdpFramePipeline(const std::string& jsonParams);
        ~UdpFramePipeline() override;
    
        void onFrame(const std::shared_ptr<Frame>& frame) override;
        void start();
        void stop();
    private:
#ifdef USE_SIMULATION
        bool should_drop_packet();
        void simulate_network_conditions();
#endif
        std::list<struct sockaddr_storage> dsts_;
        std::unordered_set<std::string> sock_ids_;
        toolkit::Socket::Ptr sock_;
        FrameFormat fmt_;
#ifdef USE_SIMULATION
        // for simulation
        std::random_device rd_;
        std::mt19937 gen_;
        std::uniform_real_distribution<> uniform_dist_;
        uint16_t seq_ = 0;
        bool first_ = true;
#endif
        std::mutex sock_mutex_;
        bool running_;
    };
}

#endif