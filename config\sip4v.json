{"name": "sip4v", "audio": {"capturer": {"name": "AlsaAudioFrameCapturer", "samplerate": 48000, "channel": 2, "dev": 0, "card": 2, "device": 0}, "render": {"name": "AlsaAudioFrameRender", "samplerate": 48000, "channel": 2, "dev": 0, "card": 0, "device": 0}, "encoder": {"name": "native", "codec": [{"name": "opus", "samplerate": 16000, "channel": 1, "fec": 1, "rc": "vbr"}, {"name": "g711", "samplerate": 8000, "channel": 1}, {"name": "g729", "samplerate": 8000, "channel": 1}, {"name": "g722", "samplerate": 8000, "channel": 1}]}, "decoder": {"name": "native", "codec": [{"name": "opus", "samplerate": 16000, "channel": 1, "fec": 1, "rc": "vbr"}, {"name": "g711", "samplerate": 8000, "channel": 1}, {"name": "g729", "samplerate": 8000, "channel": 1}, {"name": "g722", "samplerate": 8000, "channel": 1}]}, "uplink": {"processer": [{"name": "RtcAudioFrameAec", "samplerate": 16000, "channel": 1}], "file-src": {"name": "PCMFileFrameSource"}, "rtp": "JRtpEngine"}, "downlink": {"processer": [{"name": "AudioFrameAsFarEnd", "samplerate": 16000, "channel": 1, "bind": "RtcAudioFrameAec"}], "file-src": {"name": "PCMFileFrameSource"}, "rtp": "JRtpEngine"}}, "video": {"capturer": {"name": "V4L2DMAVideoFrameCapturer", "format": "nv16", "dev": 0}, "encoder": {"name": "RKVideoEncoder", "codec": [{"name": "h264", "width": 1920, "height": 1080, "fps": 30, "gop": 30, "rc": "AVBR", "bitrate": 2048, "max-qp": 35, "min-qp": 15}, {"name": "h265", "width": 1920, "height": 1080, "fps": 30, "gop": 30, "rc": "AVBR", "bitrate": 2048, "max-qp": 35, "min-qp": 15}]}, "decoder": {"name": "RKVideoDecoder", "codec": [{"name": "h264", "width": 3840, "height": 2160}, {"name": "h265", "width": 3840, "height": 2160}, {"name": "mjpeg", "width": 1920, "height": 1080}]}, "render": {"name": "RKVideoRender", "dev": 0}, "uplink": {"processer": [], "file-src": {"name": "H2645FileFrameSource"}, "rtp": "JRtpEngine", "rtp-proxy": "RtcRtpVideoFrameDestination"}, "downlink": {"processer": [], "file-src": {"name": "H2645FileFrameSource"}, "rtp": "JRtpEngine", "rtp-proxy": "RtcRtpVideoFrameSource"}}}