#include "Sdl2AudioFrameCapturer.h"
#include "Frame.h"
#include "Utils.h"
#include <json.hpp>
#include <ljcore/jlog.h>
#include <stdio.h>

extern int mmi_record_audio;

using namespace panocom;

void SDLCALL audioCallback(void *userdata, Uint8 *stream, int len)
{
    //jinfo("audioCallback %d", len);
    Sdl2AudioFrameCapturer* capturer = (Sdl2AudioFrameCapturer*)userdata;
    capturer->deliverFrame(stream, len);
}

Sdl2AudioFrameCapturer::Sdl2AudioFrameCapturer(const std::string& jsonParams)
{
    FN_BEGIN;
    jinfo("Sdl2AudioFrameCapturer %s", jsonParams.c_str());
    name_ = "Sdl2AudioFrameCapturer";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    int chn = 1;
    if (j.contains("channel"))
    {
        chn = j["channel"];
    }
    int samplerate = 16000;
    if (j.contains("samplerate"))
    {
        samplerate = j["samplerate"];
    }
    int samples = 512;
    if (j.contains("samples"))
    {
        samples = j["samples"];
    }
    int dev = -1;
    if (j.contains("dev"))
    {
        dev = j["dev"];
    }

    SDL_Init(SDL_INIT_AUDIO);
    int deviceCount = SDL_GetNumAudioDevices(SDL_TRUE);
    for (size_t i = 0; i < deviceCount; i++)
    {
        jinfo("sdl list capture device[%d]: %s", i, SDL_GetAudioDeviceName(i, SDL_TRUE));
    }
    
    const char* device = NULL;
    if (dev >= 0 && dev < deviceCount)
    {
        device = SDL_GetAudioDeviceName(dev, SDL_TRUE);
        jinfo("sdl capture device: %s", device);
    }

    SDL_AudioSpec wanted;
    SDL_zero(wanted);
    wanted.freq = samplerate;
    wanted.format = AUDIO_S16LSB;
    wanted.channels = chn;
    wanted.samples = samples;
    wanted.callback = audioCallback;
    wanted.userdata = this;

    SDL_AudioSpec spec;
    SDL_zero(spec);

    devid_ = SDL_OpenAudioDevice(NULL, SDL_TRUE, &wanted, &spec, 0);
    if (!devid_) 
    {
        jerror("Couldn't open an audio device for playback: %s!\n", SDL_GetError());
    }
    SDL_PauseAudioDevice(devid_, SDL_FALSE);
    FN_END;
}

Sdl2AudioFrameCapturer::~Sdl2AudioFrameCapturer()
{
    FN_BEGIN;
    if (devid_)
    {
        SDL_CloseAudioDevice(devid_);
        devid_ = 0;
    }
    FN_END;
}

void Sdl2AudioFrameCapturer::deliverFrame(const uint8_t* data, int len)
{
    printOutputStatus("Sdl2AudioFrameCapturer");
    auto f = Frame::CreateFrame(FRAME_FORMAT_PCM_16000_1);
    f->createFrameBuffer(len);
    memcpy(f->getFrameBuffer(), data, len);
    if (mmi_record_audio)
    {
        if (!pf_)
        {
            std::string fileName = name_ + std::to_string((long)this) + ".pcm";
            pf_ = fopen(fileName.c_str(), "w+b");
        }
        if (pf_)
        {
            fwrite(data, 1, len, pf_);
        }
    }
    AudioFrameCapturer::deliverFrame(f);
}