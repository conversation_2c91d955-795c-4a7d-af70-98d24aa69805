#include "NetEqAudioDecoder.h"
#include "Frame.h"
#include "Utils.h"
#include "RtpHeader.h"
#include <ljcore/jlog.h>
#include <stdio.h>
#include <json.hpp>
#include <CodecInfo.h>

using namespace panocom;

NetEqAudioDecoder::NetEqAudioDecoder(const std::string& jsonParams)
{
    jinfo("NetEqAudioDecoder %s", jsonParams.c_str());
    name_ = "NetEqAudioDecoder";
    neteq_.reset();
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    samplerate_ = 16000;
    chn_ = 1;
    encode_type_ = 97;
    codec_name_ = "l16";
    bitrate_ = 0;
    last_mute_ = false;
    if (j.contains("samplerate")) samplerate_ = j["samplerate"];
    clockrate_ = samplerate_;
    if (j.contains("clockrate")) clockrate_ = j["clockrate"];
    if (j.contains("channel")) chn_ = j["channel"];
    if (j.contains("encodetype")) encode_type_ = j["encodetype"];
    if (j.contains("codec")) codec_name_ = j["codec"];
    if (j.contains("bitrate")) bitrate_ = j["bitrate"];
    fmt_ = Frame::getPCMFormat(1, samplerate_);
    if (strcasecmp(codec_name_.c_str(), "g722") == 0) fmt_ = Frame::getPCMFormat(1, 16000);
    start();
}

NetEqAudioDecoder::~NetEqAudioDecoder()
{
    stop();
}

#if defined(USE_WEBRTC)

void NetEqAudioDecoder::onFrame(const std::shared_ptr<Frame>& frame)
{
    if (!frame) return;
    int pt = frame->getPayloadType();
    if (encode_type_ != pt) {
        initNeteq(pt);
    }
    if (frame->getFrameFormat() == FRAME_FORMAT_RTP && frame->getFrameSize() > panocom::RTPHeader::MIN_SIZE/*sizeof(panocom::RTPHeader)*/ ) {
        panocom::RTPHeader* head = (panocom::RTPHeader*)(frame->getFrameBuffer());
        webrtc::RTPHeader rtp_info;
        rtp_info.sequenceNumber = head->getSeqNumber();
        rtp_info.timestamp = head->getTimestamp();
        rtp_info.ssrc = head->getSSRC();
        rtp_info.payloadType = head->getPayloadType();
        rtp_info.markerBit = 0;
        if (neteq_->InsertPacket(rtp_info, 
            rtc::ArrayView<const uint8_t>((uint8_t *)frame->getFrameBuffer() + head->getHeaderLength(), frame->getFrameSize() - head->getHeaderLength())) == -1)
        {
            jerror("InsertPacket fail");
        }
    } else {
        jerror("%d: NetEqAudioDecoder::onFrame error format[%d] size[%d]", __LINE__, frame->getFrameFormat(), frame->getFrameSize());
    }
}

void NetEqAudioDecoder::start()
{
    // initNeteq(samplerate_, chn_, encode_type_);
    // initNeteq(samplerate_, chn_, encode_type_, codec_name_);
    thread_.loop()->setInterval(10, [this](hv::TimerID id) {
        AudioFrame out_frame;
        bool muted;
        if (neteq_ && neteq_->GetAudio(&out_frame, &muted) == webrtc::NetEq::kOK) 
        {
            // 两次都是静音数据判断没收到rtp数据
            if (last_mute_ && muted) {
                jinfo("%p GetAudio muted: %d", this, muted);
                return;
            }
            last_mute_ = muted;
            auto f = Frame::CreateFrame((FrameFormat)fmt_);
            if (f)
            {
                // TODO: 不在此区分单双通道
                // 这里其实是out_frame.samples_per_channel_ * sizeof(int16_t) * out_frame.num_channels_，下面这么写其实有问题，但因为16bit刚好正常了
                int sampleBytes = out_frame.samples_per_channel_ << out_frame.num_channels_;
                if (out_frame.num_channels_ == 2) {    
                    f->createFrameBuffer(sampleBytes >> 1);
                    stereo_to_mono((int16_t *)out_frame.data(), (int16_t *)f->getFrameBuffer(), out_frame.samples_per_channel_);
                } else {
                    f->createFrameBuffer(sampleBytes);
                    memcpy(f->getFrameBuffer(), out_frame.data(), sampleBytes);
                }
                // jinfo("NetEqAudioDecoder out_frame size %d chn %d sr %d", sampleBytes, out_frame.num_channels_, out_frame.sample_rate_hz_);
                deliverFrame(f);
            }
        }
    });
    thread_.start();
    // stats_thread_.loop()->setInterval(5000, [this](hv::TimerID id) {
    //     if (neteq_) {
    //         auto network_stats = neteq_->CurrentNetworkStatistics();
    //         jinfo("jitter buffer delay: %d, preferred buffer size: %d, Base Minimum DelayMs: %d", network_stats.current_buffer_size_ms, network_stats.preferred_buffer_size_ms, neteq_->GetBaseMinimumDelayMs());
    //     }
    // });
    // stats_thread_.start();
}

void NetEqAudioDecoder::stop()
{
    thread_.stop(true);
    freeNeteq();
}

int NetEqAudioDecoder::updateParam(const std::string& jsonParams) {
    jinfo("AudioSpecMulticasterSingle::updateParam %s", jsonParams.c_str());
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    int delay_ms = delay_ms_;
    if (j.contains("delay-time")) {
        delay_ms = j["delay-time"];
    }
    if (delay_ms != delay_ms_) {
        setTargetDelay(delay_ms);
    }
    return 0;
}

void NetEqAudioDecoder::initNeteq(int sample_rate, int channels, int encode_type)
{
    NetEq::Config config;
    config.sample_rate_hz = sample_rate;
    webrtc::Clock* clock = webrtc::Clock::GetRealTimeClock();
    auto audio_decoder_factory = CreateBuiltinAudioDecoderFactory();
    neteq_ = DefaultNetEqFactory().CreateNetEq(config, audio_decoder_factory, clock);
    switch (encode_type)
    {
    case 97:
    {
        if (!neteq_->RegisterPayloadType(
                encode_type, SdpAudioFormat("l16", sample_rate, channels, { {"stereo", "0"} }))) {
            return;
        }
    }
    case 111:
    {
        if (!neteq_->RegisterPayloadType(
            encode_type, SdpAudioFormat("opus", sample_rate, channels, { {"stereo", "0"} }))) {
            return;
        }
    }
    default:
        break;
    }
    
    // neteq_->EnableNack(128);
}
// TODO: 处理多种payload
void NetEqAudioDecoder::initNeteq(int sample_rate, int channels, int encode_type, std::string& encode_name)
{
    NetEq::Config config;
    config.sample_rate_hz = sample_rate;
    config.max_packets_in_buffer = 200;
    config.enable_muted_state = true;
    webrtc::Clock* clock = webrtc::Clock::GetRealTimeClock();
    auto audio_decoder_factory = CreateBuiltinAudioDecoderFactory();
    neteq_ = DefaultNetEqFactory().CreateNetEq(config, audio_decoder_factory, clock);
    if (!neteq_) {
        jerror("NetEqAudioDecoder initNeteq failed!");
        return;
    }
    std::string stereo = (channels == 2 ? "1" : "0");
    if (!neteq_->RegisterPayloadType(
        encode_type, SdpAudioFormat(codec_name_.c_str(), sample_rate, channels, { {"stereo", stereo }, {"bitrate", std::to_string(bitrate_)} }))) {
        return;
    }
    
    // neteq_->EnableNack(128);
}

void NetEqAudioDecoder::initNeteq(int pt) {
    CodecInst codec;
    if (getCodecInst(pt, codec)) {
        encode_type_ = pt;
        samplerate_ = codec.rate;
        chn_ = codec.chn;
        codec_name_ = codec.name;
        bitrate_ = codec.bitrate;
        std::string codec_name(codec.name);
        initNeteq(codec.rate, codec.chn, codec.pt, codec_name);
        jinfo("NetEqAudioDecoder init samplerate: %d, chn: %d, pt: %d, codec: %s bitrate: %d", samplerate_, chn_, encode_type_, codec_name_.c_str(), bitrate_);
    }
}

void NetEqAudioDecoder::initNeteq() {
    NetEq::Config config;
    config.sample_rate_hz = samplerate_;
    config.max_packets_in_buffer = 200;
    config.enable_muted_state = true;
    webrtc::Clock* clock = webrtc::Clock::GetRealTimeClock();
    auto audio_decoder_factory = CreateBuiltinAudioDecoderFactory();
    neteq_ = DefaultNetEqFactory().CreateNetEq(config, audio_decoder_factory, clock);
    if (!neteq_) {
        jerror("NetEqAudioDecoder initNeteq failed!");
        return;
    }
    const std::vector<int> rtp_payload_types { FRAME_FORMAT_PCMU,
                                               FRAME_FORMAT_PCMA,
                                               FRAME_FORMAT_OPUS_48000_2,
                                               FRAME_FORMAT_G722_16000_1,
                                               FRAME_FORMAT_G7221_16000_32000_1,
                                               FRAME_FORMAT_G7221_32000_48000_1,
                                               FRAME_FORMAT_G7221_16000_24000_1,
                                               FRAME_FORMAT_G729,
                                               FRAME_FORMAT_AAC_48000_1 };
    for (int pt: rtp_payload_types) RegisterPayloadType(pt);
}

void NetEqAudioDecoder::freeNeteq()
{
    if (!neteq_) return;
    neteq_->FlushBuffers();
    neteq_.reset();
}

void NetEqAudioDecoder::setTargetDelay(int delay_ms) {
    if (!neteq_) return;
    delay_ms = std::min(2000, std::max(delay_ms, 10));
    if (neteq_->SetMaximumDelay(delay_ms)) {
        jinfo("SetMaximumDelay %d ms", delay_ms);
    }
    if (neteq_->SetMinimumDelay(delay_ms)) {
        jinfo("SetMinimumDelay %d ms", delay_ms);
    }
    if (neteq_->SetBaseMinimumDelayMs(delay_ms)) {
        jinfo("SetBaseMinimumDelay %d ms", delay_ms);
    }
    delay_ms_ = delay_ms;
}

void NetEqAudioDecoder::RegisterPayloadType(int pt) {
    CodecInst codec;
    if (getCodecInst(pt, codec)) {
        std::string codec_name(codec.name);
        initNeteq(codec.rate, codec.chn, codec.pt, codec_name);
        std::string stereo = (codec.chn == 2 ? "1" : "0");
        if (neteq_)
            neteq_->RegisterPayloadType(
                pt, SdpAudioFormat(codec.name, codec.rate, codec.chn, { { "stereo", stereo }, { "bitrate", std::to_string(codec.bitrate) } }));
    }
}

#elif defined(USE_NETEQ)

void NetEqAudioDecoder::onFrame(const std::shared_ptr<Frame>& frame)
{
    if (frame->getFrameFormat() == FRAME_FORMAT_RTP && frame->getFrameSize() > sizeof(panocom::RTPHeader))
    {
        panocom::RTPHeader* head = (panocom::RTPHeader*)(frame->getFrameBuffer());
        char* data = frame->getFrameBuffer() + head->getHeaderLength();
        int len =  frame->getFrameSize() - head->getHeaderLength();
        neteq_insert_packet(neteq_.get(), head->getSeqNumber(), head->getTimestamp(), (short*)data, len / 2, head->getPayloadType(), len);
    }
    else
    {
        jerror("NetEqAudioDecoder::onFrame error format[%d]", frame->getFrameFormat());
    }
}

void NetEqAudioDecoder::start()
{
    initNeteq(samplerate_, chn_, encode_type_);
    thread_.loop()->setInterval(10, [this](hv::TimerID id) {
        short buf[512] = { 0 };
        int ret = neteq_get_audio(neteq_.get(), buf);
        if (ret > 0) 
        {
            auto f = Frame::CreateFrame((FrameFormat)fmt_);
            if (f)
            {
                f->createFrameBuffer(ret * 2);
                memcpy(f->getFrameBuffer(), buf, ret * 2);
                deliverFrame(f);
            }
        }
    });
    thread_.start();
}

void NetEqAudioDecoder::stop()
{
    thread_.stop(true);
    freeNeteq();
}

void NetEqAudioDecoder::initNeteq(int sample_rate, int channels, int encode_type)
{
    neteq_ = std::shared_ptr<NeteqContext>(init_neteq(sample_rate, channels, encode_type), [](NeteqContext* ctx){
        clear_neteq(ctx);
    });
}

void NetEqAudioDecoder::freeNeteq()
{
    neteq_.reset();
}

#endif