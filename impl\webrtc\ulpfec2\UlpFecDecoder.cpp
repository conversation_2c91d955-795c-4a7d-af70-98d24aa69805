#include "UlpFecDecoder.h"
#include <json.hpp>
#include <webrtc/modules/rtp_rtcp/source/rtp_packet_received.h>

using namespace panocom;

UlpFecDecoder::UlpFecDecoder(const std::string& jsonParams)
{
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    ssrc_ = 12345;
    if (j.contains("ssrc"))
    {
        ssrc_ = j["ssrc"];
    }
    ulpfec_payload_type_ = 125;
    if (j.contains("ulpfecPayloadType"))
    {
        ulpfec_payload_type_ = j["ulpfecPayloadType"];
    }
    receiver_ = UlpfecReceiver::Create(ssrc_, this, {});
}

UlpFecDecoder::~UlpFecDecoder()
{

}

void UlpFecDecoder::onFrame(const std::shared_ptr<Frame> &f)
{
    RtpPacketReceived parsed_packet;
    if (parsed_packet.Parse(f->getFrameBuffer(), f->getFrameSize()))
    {
        receiver_->AddReceivedRedPacket(parsed_packet, ulpfec_payload_type_);
    }
    receiver_->ProcessReceivedFec();
}

void UlpFecDecoder::OnRecoveredPacket(const uint8_t* packet, size_t length)
{
    auto f = Frame::CreateFrame(FRAME_FORMAT_RTP);
    f->createFrameBuffer(length);
    memcpy(f->getFrameBuffer(), packet, length);
    deliverFrame(f);
}