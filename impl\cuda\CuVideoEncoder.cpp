#include "CuVideoEncoder.h"
#include "Utils.h"
#include "CuCommon.h"
#include "CuFrame.h"
#include <json.hpp>
#include <ljcore/jlog.h>

using namespace panocom;

#ifndef _WIN32
#include <cstring>
static inline bool operator==(const GUID &guid1, const GUID &guid2) {
    return !memcmp(&guid1, &guid2, sizeof(GUID));
}

static inline bool operator!=(const GUID &guid1, const GUID &guid2) {
    return !(guid1 == guid2);
}
#endif

CuVideoEncoder::CuVideoEncoder(const std::string& jsonParams) : encoder_(nullptr)
{
    FN_BEGIN;
    jinfo("create CuVideoEncoder");
    name_ = "CuVideoEncoder";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    fps_ = 30;
    if (j.contains("fps"))
    {
        fps_ = j["fps"];
    }
    std::string codecName = "h264";
    if (j.contains("codec"))
    {
        codecName = j["codec"];
    }
    int kbps = 2000;
    if (j.contains("kbps"))
    {
        kbps = j["kbps"];
    }
    gop_ = 30;
    if (j.contains("gop"))
    {
        gop_ = j["gop"];
    }
    width_ = 1280;
    if (j.contains("width"))
    {
        width_ = j["width"];
    }
    height_ = 720;
    if (j.contains("height"))
    {
        height_ = j["height"];
    }
    cudaId_ = 0;
    if (j.contains("cudaId"))
    {
        cudaId_ = j["cudaId"];
    }
    gpuIndex_ = 0;
    if (j.contains("gpuIndex"))
    {
        gpuIndex_ = j["gpuIndex"];
    }
    fmt_ = FRAME_FORMAT_H264;
    if (codecName == "h264")
        fmt_ = FRAME_FORMAT_H264;
    else if (codecName == "h265")
        fmt_ = FRAME_FORMAT_H265;

    cuCtxPushCurrent(*CuCommon::instance().getCudaContext(cudaId_, gpuIndex_));
    LoadNvEncApi();

    if (!nvenc_.nvEncOpenEncodeSession) 
    {
        jerror("!nvenc_.nvEncOpenEncodeSession");
        return;
    }
    start();
    FN_END;
}

void CuVideoEncoder::LoadNvEncApi()
{
    uint32_t version = 0;
    uint32_t currentVersion = (NVENCAPI_MAJOR_VERSION << 4) | NVENCAPI_MINOR_VERSION;
    NVENCSTATUS ret = NvEncodeAPIGetMaxSupportedVersion(&version);
    nvenc_ = { NV_ENCODE_API_FUNCTION_LIST_VER };
    ret = NvEncodeAPICreateInstance(&nvenc_);
}

CuVideoEncoder::~CuVideoEncoder()
{
    FN_BEGIN;
    stop();
    FN_END;
}

void CuVideoEncoder::start()
{
    if (thread_.isRunning()) return;
    FN_BEGIN;
    NV_ENC_OPEN_ENCODE_SESSION_EX_PARAMS encodeSessionExParams = { NV_ENC_OPEN_ENCODE_SESSION_EX_PARAMS_VER };
    encodeSessionExParams.device = *CuCommon::instance().getCudaContext(cudaId_, gpuIndex_);
    encodeSessionExParams.deviceType = NV_ENC_DEVICE_TYPE_CUDA;
    encodeSessionExParams.apiVersion = NVENCAPI_VERSION;
    NVENCSTATUS ret = nvenc_.nvEncOpenEncodeSessionEx(&encodeSessionExParams, &encoder_);
    if (ret != NV_ENC_SUCCESS)
    {
        jerror("nvEncOpenEncodeSessionEx %d", ret);
        return;
    }

    NV_ENC_INITIALIZE_PARAMS initializeParams = { NV_ENC_INITIALIZE_PARAMS_VER };
    NV_ENC_CONFIG encodeConfig = { NV_ENC_CONFIG_VER };

    initializeParams.encodeConfig = &encodeConfig;
    if (fmt_ == FRAME_FORMAT_H264)
        CreateDefaultEncoderParams(&initializeParams, NV_ENC_CODEC_H264_GUID, NV_ENC_PRESET_P3_GUID, NV_ENC_TUNING_INFO_LOW_LATENCY);
    else if (fmt_ = FRAME_FORMAT_H265)
        CreateDefaultEncoderParams(&initializeParams, NV_ENC_CODEC_HEVC_GUID, NV_ENC_PRESET_P3_GUID, NV_ENC_TUNING_INFO_LOW_LATENCY);
    else
        CreateDefaultEncoderParams(&initializeParams, NV_ENC_CODEC_H264_GUID, NV_ENC_PRESET_P3_GUID, NV_ENC_TUNING_INFO_LOW_LATENCY);
    
    CreateEncoder(&initializeParams);
    cuCtxPopCurrent(NULL);

    thread_.start();
    FN_END;
}

void CuVideoEncoder::stop()
{
    if (!thread_.isRunning()) return;
    std::unique_lock<std::mutex> locker(frame_mutex_);
    thread_.stop(true);
    DestroyEncoder();
}

void CuVideoEncoder::onFrame(const std::shared_ptr<Frame>& frame)
{
    printInputStatus("CuVideoEncoder");
    std::unique_lock<std::mutex> locker(frame_mutex_);
    if (!thread_.isRunning()) return;
    thread_.loop()->runInLoop([this, frame]{
        std::shared_ptr<CuEncoderInputNV12Frame> f = std::make_shared<CuEncoderInputNV12Frame>(cudaId_, gpuIndex_, frame, encoder_, nvenc_);
        f->createFrameBuffer(frame->width(), frame->height(), frame->hStride(), frame->vStride());
        DoEncode(f);
    });
}

void CuVideoEncoder::CreateDefaultEncoderParams(NV_ENC_INITIALIZE_PARAMS* pIntializeParams, GUID codecGuid, GUID presetGuid, NV_ENC_TUNING_INFO tuningInfo)
{
    if (!encoder_)
    {
        return;
    }

    if (pIntializeParams == nullptr || pIntializeParams->encodeConfig == nullptr)
    {
        return;
    }

    memset(pIntializeParams->encodeConfig, 0, sizeof(NV_ENC_CONFIG));
    auto pEncodeConfig = pIntializeParams->encodeConfig;
    memset(pIntializeParams, 0, sizeof(NV_ENC_INITIALIZE_PARAMS));
    pIntializeParams->encodeConfig = pEncodeConfig;


    pIntializeParams->encodeConfig->version = NV_ENC_CONFIG_VER;
    pIntializeParams->version = NV_ENC_INITIALIZE_PARAMS_VER;

    pIntializeParams->encodeGUID = codecGuid;
    pIntializeParams->presetGUID = presetGuid;
    pIntializeParams->encodeWidth = width_;
    pIntializeParams->encodeHeight = height_;
    pIntializeParams->darWidth = width_;
    pIntializeParams->darHeight = height_;
    pIntializeParams->frameRateNum = fps_;
    pIntializeParams->frameRateDen = 1;
    pIntializeParams->enablePTD = 1;
    pIntializeParams->reportSliceOffsets = 0;
    pIntializeParams->enableSubFrameWrite = 0;
    pIntializeParams->maxEncodeWidth = width_;
    pIntializeParams->maxEncodeHeight = height_;
    pIntializeParams->enableMEOnlyMode = false;
    pIntializeParams->enableOutputInVidmem = false;

    NV_ENC_PRESET_CONFIG presetConfig = { NV_ENC_PRESET_CONFIG_VER, { NV_ENC_CONFIG_VER } };

    nvenc_.nvEncGetEncodePresetConfig(encoder_, codecGuid, presetGuid, &presetConfig);
    memcpy(pIntializeParams->encodeConfig, &presetConfig.presetCfg, sizeof(NV_ENC_CONFIG));
    pIntializeParams->encodeConfig->frameIntervalP = 1;
    pIntializeParams->encodeConfig->gopLength = gop_;
    pIntializeParams->encodeConfig->rcParams.rateControlMode = NV_ENC_PARAMS_RC_VBR;
    pIntializeParams->tuningInfo = tuningInfo;

    nvenc_.nvEncGetEncodePresetConfigEx(encoder_, codecGuid, presetGuid, tuningInfo, &presetConfig);
    memcpy(pIntializeParams->encodeConfig, &presetConfig.presetCfg, sizeof(NV_ENC_CONFIG));
    pIntializeParams->encodeConfig->frameIntervalP = 1;
    pIntializeParams->encodeConfig->gopLength = gop_;
    pIntializeParams->encodeConfig->rcParams.rateControlMode = NV_ENC_PARAMS_RC_VBR;
    pIntializeParams->tuningInfo = tuningInfo;

    if (pIntializeParams->encodeGUID == NV_ENC_CODEC_H264_GUID)
    {
        pIntializeParams->encodeConfig->encodeCodecConfig.h264Config.idrPeriod = pIntializeParams->encodeConfig->gopLength;
    }
    else if (pIntializeParams->encodeGUID == NV_ENC_CODEC_HEVC_GUID)
    {
        pIntializeParams->encodeConfig->encodeCodecConfig.hevcConfig.idrPeriod = pIntializeParams->encodeConfig->gopLength;
    }
    return;
}

void CuVideoEncoder::CreateEncoder(NV_ENC_INITIALIZE_PARAMS* pEncoderParams)
{
    if (!encoder_)
    {
        jerror("CreateEncoder !encoder_");
        return;
    }

    if (!pEncoderParams)
    {
        jerror("CreateEncoder !pEncoderParams");
        return;
    }

    if (pEncoderParams->encodeWidth == 0 || pEncoderParams->encodeHeight == 0)
    {
        jerror("CreateEncoder pEncoderParams->encodeWidth == 0 || pEncoderParams->encodeHeight == 0");
        return;
    }

    if (pEncoderParams->encodeGUID != NV_ENC_CODEC_H264_GUID && pEncoderParams->encodeGUID != NV_ENC_CODEC_HEVC_GUID)
    {
        jerror("pEncoderParams->encodeGUID != NV_ENC_CODEC_H264_GUID && pEncoderParams->encodeGUID != NV_ENC_CODEC_HEVC_GUID");
        return;
    }

    pEncoderParams->version = NV_ENC_INITIALIZE_PARAMS_VER;

    if (pEncoderParams->encodeConfig)
    {
        memcpy(&encodeConfig_, pEncoderParams->encodeConfig, sizeof(encodeConfig_));
        encodeConfig_.version = NV_ENC_CONFIG_VER;
        encodeConfig_.profileGUID = NV_ENC_H264_PROFILE_BASELINE_GUID;
    }
    else
    {
        NV_ENC_PRESET_CONFIG presetConfig = { NV_ENC_PRESET_CONFIG_VER, { NV_ENC_CONFIG_VER } };
        NVENCSTATUS ret = nvenc_.nvEncGetEncodePresetConfigEx(encoder_, pEncoderParams->encodeGUID, pEncoderParams->presetGUID, pEncoderParams->tuningInfo, &presetConfig);
        if (ret != NV_ENC_SUCCESS)
        {
            jerror("nvEncGetEncodePresetConfigEx %d", ret);
        }
        memcpy(&encodeConfig_, &presetConfig.presetCfg, sizeof(NV_ENC_CONFIG));
        encodeConfig_.profileGUID = NV_ENC_H264_PROFILE_BASELINE_GUID;

    }
    pEncoderParams->encodeConfig->profileGUID  = NV_ENC_H264_PROFILE_BASELINE_GUID;
    NVENCSTATUS ret = nvenc_.nvEncInitializeEncoder(encoder_, pEncoderParams);
    if (ret != NV_ENC_SUCCESS)
    {
        jerror("nvEncInitializeEncoder %d", ret);
    }

    encoderInitialized_ = true;
    width_ = pEncoderParams->encodeWidth;
    height_ = pEncoderParams->encodeHeight;

    NV_ENC_CREATE_BITSTREAM_BUFFER createBitstreamBuffer = { NV_ENC_CREATE_BITSTREAM_BUFFER_VER };
    ret = nvenc_.nvEncCreateBitstreamBuffer(encoder_, &createBitstreamBuffer);
    if (ret != NV_ENC_SUCCESS)
    {
        jerror("nvEncCreateBitstreamBuffer %d", ret);
    }
    bitstreamOutputBuffer_ = createBitstreamBuffer.bitstreamBuffer;
}

void CuVideoEncoder::DestroyEncoder()
{
    if (!encoder_)
    {
        return;
    }
    cuCtxPushCurrent(*CuCommon::instance().getCudaContext(cudaId_, gpuIndex_));
    nvenc_.nvEncDestroyBitstreamBuffer(encoder_, bitstreamOutputBuffer_);
    nvenc_.nvEncDestroyEncoder(encoder_);
    cuCtxPopCurrent(NULL);
    encoder_ = nullptr;
    encoderInitialized_ = false;
}

void CuVideoEncoder::GetSequenceParams(std::vector<uint8_t> &seqParams)
{
    uint8_t spsppsData[1024]; // Assume maximum spspps data is 1KB or less
    memset(spsppsData, 0, sizeof(spsppsData));
    NV_ENC_SEQUENCE_PARAM_PAYLOAD payload = { NV_ENC_SEQUENCE_PARAM_PAYLOAD_VER };
    uint32_t spsppsSize = 0;

    payload.spsppsBuffer = spsppsData;
    payload.inBufferSize = sizeof(spsppsData);
    payload.outSPSPPSPayloadSize = &spsppsSize;
    NVENCSTATUS ret = nvenc_.nvEncGetSequenceParams(encoder_, &payload);
    seqParams.clear();
    seqParams.insert(seqParams.end(), &spsppsData[0], &spsppsData[spsppsSize]);
}

NVENCSTATUS CuVideoEncoder::DoEncode(const std::shared_ptr<CuEncoderInputNV12Frame>& f)
{
    NV_ENC_PIC_PARAMS picParams = {};
    picParams.version = NV_ENC_PIC_PARAMS_VER;
    picParams.pictureStruct = NV_ENC_PIC_STRUCT_FRAME;
    picParams.inputBuffer = f->getFrameBuffer();
    picParams.bufferFmt = NV_ENC_BUFFER_FORMAT_NV12;
    picParams.inputWidth = width_;
    picParams.inputHeight = height_;
    picParams.outputBitstream = bitstreamOutputBuffer_;
    picParams.completionEvent = nullptr;
    NVENCSTATUS nvStatus = nvenc_.nvEncEncodePicture(encoder_, &picParams);
    if (nvStatus == NV_ENC_SUCCESS || nvStatus == NV_ENC_ERR_NEED_MORE_INPUT)
    {
        NV_ENC_LOCK_BITSTREAM lockBitstreamData = { NV_ENC_LOCK_BITSTREAM_VER };
        lockBitstreamData.outputBitstream = bitstreamOutputBuffer_;
        lockBitstreamData.doNotWait = false;
        NVENCSTATUS ret = nvenc_.nvEncLockBitstream(encoder_, &lockBitstreamData);

        if (ret != NV_ENC_SUCCESS)
        {
            jerror("nvEncLockBitstream %d", ret);
        }
        else
        {
            std::shared_ptr<Frame> f = Frame::CreateFrame((FrameFormat)fmt_);
            if (f)
            {
                f->setWidth(width_);
                f->setHeight(height_);
                f->setGroupId(getGroupId());
                
                if (isIDRNALU((uint8_t *)lockBitstreamData.bitstreamBufferPtr, lockBitstreamData.bitstreamSizeInBytes, fmt_))
                {
                    std::vector<uint8_t> seqParams;
                    GetSequenceParams(seqParams);
                    f->createFrameBuffer(lockBitstreamData.bitstreamSizeInBytes + seqParams.size());
                    memcpy(f->getFrameBuffer(), seqParams.data(), seqParams.size());
                    memcpy(f->getFrameBuffer() + seqParams.size(), (uint8_t *)lockBitstreamData.bitstreamBufferPtr, lockBitstreamData.bitstreamSizeInBytes);
                }
                else
                {
                    f->createFrameBuffer(lockBitstreamData.bitstreamSizeInBytes);
                    memcpy(f->getFrameBuffer(), (uint8_t *)lockBitstreamData.bitstreamBufferPtr, lockBitstreamData.bitstreamSizeInBytes);
                }
                
                deliverFrame(f);
                printOutputStatus("CuVideoEncoder");
            }

            ret = nvenc_.nvEncUnlockBitstream(encoder_, lockBitstreamData.outputBitstream);
            if (ret != NV_ENC_SUCCESS)
            {
                jerror("nvEncUnlockBitstream %d", ret);
            }
        }
    }
    else
    {
        jerror("nvEncEncodePicture %d", nvStatus);
    }

    return nvStatus; 
}