// created by gyj 2024-3-4
#ifndef P_FileFramePipeline_h
#define P_FileFramePipeline_h

#include "../FramePipeline.h"
#include <thread>
#include <stdio.h>

namespace panocom
{
    class Frame;
    class FileFramePipeline : public FramePipeline
    {
    public:
        static void RegistFileSource();
        static std::vector<std::string>& GetSupportFileSources();
        static bool IsSupportFileSource(const std::string &fileSourceName);
        static std::shared_ptr<FileFramePipeline> CreateFileSource(const std::string &fileSourceName, const std::string& jsonParams = "");

        static void RegistFileDestinations();
        static std::vector<std::string>& GetSupportFileDestinations();
        static bool IsSupportFileDestination(const std::string &fileDestinationName);
        static std::shared_ptr<FileFramePipeline> CreateFileDestination(const std::string &fileDestinationName, const std::string& jsonParams = "");

        FileFramePipeline(const std::string& jsonParams);
        virtual ~FileFramePipeline();
        
        void onFrame(const std::shared_ptr<Frame> &frame) override;
    private:
        static bool isRegistered;
        static std::vector<std::string> fileSources_;
        static std::vector<std::string> fileDestinations_;
        FILE* pf_;
    };
}

#endif