// created by guoyj 2024-6-20
#ifndef P_NetEqAudioDecoder_h
#define P_NetEqAudioDecoder_h
#include "AudioFrameDecoder.h"
#include <hv/EventLoopThread.h>
#include <ptoolkit/bytebuffer.h>
#if defined(USE_WEBRTC)
#include "api/audio/audio_frame.h"
#include "api/audio_codecs/builtin_audio_decoder_factory.h"
#include "modules/audio_coding/codecs/pcm16b/pcm16b.h"
#include "modules/audio_coding/neteq/default_neteq_factory.h"
#include "system_wrappers/include/clock.h"
#include "api/neteq/neteq.h"

using namespace webrtc;
using webrtc::NetEq;

namespace panocom
{
    class NetEqAudioDecoder : public AudioFrameDecoder
    {
    public:
        NetEqAudioDecoder(const std::string& jsonParams);
        ~NetEqAudioDecoder();
        void onFrame(const std::shared_ptr<Frame>& frame) override;

        void start() override;
        void stop() override;
        int updateParam(const std::string& jsonParams);
    private:
        void initNeteq(int sample_rate, int channels, int encode_type);
        void initNeteq(int sample_rate, int channels, int encode_type, std::string &codec_name);
        void initNeteq(int pt);
        void initNeteq();
        void freeNeteq();
        void setTargetDelay(int delay_ms);
        void RegisterPayloadType(int pt);
    private:
        std::unique_ptr<NetEq> neteq_;
        int fmt_;
        int samplerate_;
        int clockrate_;
        int chn_;
        int bitrate_;
        int encode_type_;
        std::string codec_name_;
        hv::EventLoopThread thread_;
        FILE* pf_ = nullptr;
        bool last_mute_;
        // hv::EventLoopThread stats_thread_;
        int delay_ms_ = 0;
    };
}

#elif defined(USE_NETEQ)

#include <webrtc-neteq/audio-neteq.h>

namespace panocom
{
    class NetEqAudioDecoder : public AudioFrameDecoder
    {
    public:
        NetEqAudioDecoder(const std::string& jsonParams);
        ~NetEqAudioDecoder();
        void onFrame(const std::shared_ptr<Frame>& frame) override;

        void start() override;
        void stop() override;

    private:
        void initNeteq(int sample_rate, int channels, int encode_type);
        void freeNeteq();
    
    private:
        std::shared_ptr<NeteqContext> neteq_;
        int fmt_;
        int samplerate_;
        int chn_;
        int encode_type_;
        int clockrate_;
        int bitrate_;
        hv::EventLoopThread thread_;
        FILE* pf_ = nullptr;
    };
}

#endif

#endif