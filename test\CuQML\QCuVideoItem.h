#ifndef P_QCuVideoItem_h
#define P_QCuVideoItem_h

#include "../QCuRender.h"
#include "MediaPipeline.h"
#include <json.hpp>
#include <ljcore/jlog.h>
#include <QQuickItem>
#include <QQuickWindow>
#include <unordered_set>

#include <resip/stack/TransactionUser.hxx>
#include <resip/stack/EventStackThread.hxx>
#include <rutil/SelectInterruptor.hxx>
#include <resip/stack/UdpTransport.hxx>
#include <resip/dum/MasterProfile.hxx>
#include <resip/dum/DumShutdownHandler.hxx>
#include <resip/dum/DialogUsageManager.hxx>
#include <resip/dum/InviteSessionHandler.hxx>
#include <resip/dum/DialogSetHandler.hxx>
#include <resip/dum/OutOfDialogHandler.hxx>
#include <resip/dum/RedirectHandler.hxx>
#include <resip/dum/SubscriptionHandler.hxx>
#include <resip/dum/RegistrationHandler.hxx>
#include <resip/dum/ServerRegistration.hxx>
#include <resip/dum/ServerPagerMessage.hxx>
#include <resip/dum/ClientPagerMessage.hxx>
#include <resip/dum/PagerMessageHandler.hxx>
#include <resip/dum/KeepAliveManager.hxx>
#include <resip/dum/InMemorySyncRegDb.hxx>
#include <rutil/Mutex.hxx>
#include <rutil/Logger.hxx>

#define RESIPROCATE_SUBSYSTEM resip::Subsystem::SIP
#include "MediaManagerInterface.h"

using namespace resip;

namespace panocom
{
    class QCuVideoItem : public QQuickItem, public resip::InviteSessionHandler
    {
        Q_OBJECT
        Q_PROPERTY(quint32 index READ index WRITE setIndex)

    public:
        QCuVideoItem();

        // Invite Session Handler /////////////////////////////////////////////////////
        virtual void onNewSession(resip::ClientInviteSessionHandle h, resip::InviteSession::OfferAnswerType oat, const resip::SipMessage &msg);
        virtual void onNewSession(resip::ServerInviteSessionHandle h, resip::InviteSession::OfferAnswerType oat, const resip::SipMessage &msg);
        virtual void onFailure(resip::ClientInviteSessionHandle h, const resip::SipMessage &msg);
        virtual void onEarlyMedia(resip::ClientInviteSessionHandle, const resip::SipMessage &, const resip::SdpContents &);
        virtual void onProvisional(resip::ClientInviteSessionHandle, const resip::SipMessage &msg);
        virtual void onConnected(resip::ClientInviteSessionHandle h, const resip::SipMessage &msg);
        virtual void onConnected(resip::InviteSessionHandle, const resip::SipMessage &msg);
        virtual void onStaleCallTimeout(resip::ClientInviteSessionHandle);
        virtual void onTerminated(resip::InviteSessionHandle h, resip::InviteSessionHandler::TerminatedReason reason, const resip::SipMessage *msg);
        virtual void onRedirected(resip::ClientInviteSessionHandle, const resip::SipMessage &msg);
        virtual void onAnswer(resip::InviteSessionHandle, const resip::SipMessage &msg, const resip::SdpContents &);
        virtual void onOffer(resip::InviteSessionHandle handle, const resip::SipMessage &msg, const resip::SdpContents &offer);
        virtual void onOfferRequired(resip::InviteSessionHandle, const resip::SipMessage &msg);
        virtual void onOfferRejected(resip::InviteSessionHandle, const resip::SipMessage *msg);
        virtual void onOfferRequestRejected(resip::InviteSessionHandle, const resip::SipMessage &msg);
        virtual void onRemoteSdpChanged(resip::InviteSessionHandle, const resip::SipMessage &msg, const resip::SdpContents &sdp);
        virtual void onInfo(resip::InviteSessionHandle, const resip::SipMessage &msg);
        virtual void onInfoSuccess(resip::InviteSessionHandle, const resip::SipMessage &msg);
        virtual void onInfoFailure(resip::InviteSessionHandle, const resip::SipMessage &msg);
        virtual void onRefer(resip::InviteSessionHandle, resip::ServerSubscriptionHandle, const resip::SipMessage &msg);
        virtual void onReferAccepted(resip::InviteSessionHandle, resip::ClientSubscriptionHandle, const resip::SipMessage &msg);
        virtual void onReferRejected(resip::InviteSessionHandle, const resip::SipMessage &msg);
        virtual void onReferNoSub(resip::InviteSessionHandle, const resip::SipMessage &msg);
        virtual void onMessage(resip::InviteSessionHandle, const resip::SipMessage &msg);
        virtual void onMessageSuccess(resip::InviteSessionHandle, const resip::SipMessage &msg);
        virtual void onMessageFailure(resip::InviteSessionHandle, const resip::SipMessage &msg);
        virtual void onForkDestroyed(resip::ClientInviteSessionHandle);

        quint32 index() const;
        void setIndex(quint32 index);
        Q_INVOKABLE void callUpdate();

	// 在播放的过程中，加个index=0的视频窗口往右边移动一段距离
        Q_INVOKABLE void moveVideoRight();
	// 在播放的过程中，在index=0的视频窗口右边添加个新的播放视频的小窗，不影响第一个窗口的播放(意思是新增个render)
        Q_INVOKABLE void addVideoPlayWindow();

        Q_INVOKABLE void removeVideoPlayWindow();

        Q_INVOKABLE void stopVideoPlayWindow();

        Q_INVOKABLE void setVideoPlayWindowZ();

        Q_INVOKABLE void call(const QString& ip);

        Q_INVOKABLE void hangup(const QString& ip);

        Q_INVOKABLE void process();
    public slots:
        void sync();
        void cleanup();

    private:
        void createInstance(int w, int h, int dev);
        void destroyInstance(int dev);
        void destroyWindowInstance();
        void initStack();
    private:
        QCuRender *m_renderer = nullptr;
        struct VideoInstance
        {
            int id;
            std::string ip;
            std::string callid;
            std::shared_ptr<VideoFrameCapturer> capturer;
            InviteSessionHandle mH;
        };
        std::map<int, VideoInstance> instances_;
        int curX_ = 0;
        int curY_ = 0;
        int index_ = 0;
        std::set<int> idleChn_;

        std::shared_ptr<resip::MasterProfile> mProfile;
        resip::FdPollGrp *mPollGrp;
        resip::EventThreadInterruptor *mEventInterruptor;
        resip::SipStack mStack;
        resip::DialogUsageManager mDum;
        resip::EventStackThread mStackThread;
        MediaManagerInterface mMmi;  

        int areaId_;
        std::unordered_set<int> video_ids_;
    };
}

#endif
