#ifdef ENABLE_VIDEO
#ifdef USE_FFMPEG
#ifndef P_VideoFileFrameSource_h
#define P_VideoFileFrameSource_h

#include "file/FileFramePipeline.h"
#include <hv/EventLoopThread.h>
extern "C" {
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
}
namespace panocom
{
    class VideoFileFrameSource : public FileFramePipeline
    {
    public:
        VideoFileFrameSource(const std::string& jsonParams);
        ~VideoFileFrameSource() override;
        void start() override;
        void stop() override;
        int getFps(){
            return fps_;
        }

        int getWidth(){
            return width_;
        }

        int getHeight(){
            return height_;
        }

        std::string getCodec(){
            return codec_;
        }

        int getGop(){
            return gop_;
        }
    private:
        void init();
        void readAVPackets();

    private:
        hv::EventLoopThread thread_;
        std::vector<std::shared_ptr<Frame>> frames_;
        int fps_;
        int index_;
        std::string path_;
        std::string codec_;
        int width_;
        int height_;
        int gop_;
        float speed_ = 1.0f;
        AVFormatContext* fmtCtx_ = nullptr;
        int videoStreamIndex_ = -1;
    };
}
#endif
#endif
#endif