#include "MediaPipeline.h"
#include <json.hpp>
#include <stdio.h>
#include <ljcore/jlog.h>
#include <hv/EventLoop.h>

using namespace panocom;

int main()
{
    auto loop = std::make_shared<hv::EventLoop>();
    jlog_init(nullptr);
    nlohmann::json j;
    auto capturer = VideoFrameCapturer::CreateVideoCapturer("V4L2VideoFrameCapturer", j.dump());
    capturer->setGroupId(1);
    j.clear();
    j["codec"] = "jpeg";
    auto decoder = VideoFrameDecoder::CreateVideoDecoder("CuVideoEncoder", j.dump());
    decoder->setGroupId(1);
    j.clear();
    j["videoLayout"] = nlohmann::json::array();
    nlohmann::json r;

    r["id"] = 1;
    r["rect"]["x"] = 0;
    r["rect"]["y"] = 0;
    r["rect"]["w"] = 1280 / 2;
    r["rect"]["h"] = 720 / 2;
    j["videoLayout"].push_back(r);

    r["id"] = 1;
    r["rect"]["x"] = 0;
    r["rect"]["y"] = 720 / 2;
    r["rect"]["w"] = 1280 / 2;
    r["rect"]["h"] = 720 / 2;
    j["videoLayout"].push_back(r);

    r["id"] = 1;
    r["rect"]["x"] = 1280 / 2;
    r["rect"]["y"] = 0;
    r["rect"]["w"] = 1280 / 2;
    r["rect"]["h"] = 720 / 2;
    j["videoLayout"].push_back(r);

    r["id"] = 1;
    r["rect"]["x"] = 1280 / 2;
    r["rect"]["y"] = 720 / 2;
    r["rect"]["w"] = 1280 / 2;
    r["rect"]["h"] = 720 / 2;
    j["videoLayout"].push_back(r);
    auto mixer = VideoFrameMixer::CreateVideoMixer("CuVideoFrameMixer", j.dump());
    mixer->setGroupId(1);
    j.clear();
    j["codec"] = "h264";
    auto encoder = VideoFrameEncoder::CreateVideoEncoder("CuVideoEncoder", j.dump());
    j.clear();
    j["path"] = "encode.h264";
    auto fileEncoded = FileFramePipeline::CreateFileDestination("file", j.dump());
    auto mixDeviceToHost = FramePipeline::CreateFramePipeline("CuFrameDeviceToHostNV12");
    j.clear();
    j["path"] = "mix.yuv";
    auto fileMix = FileFramePipeline::CreateFileDestination("file", j.dump());
    capturer->addVideoDestination(decoder);
    decoder->addVideoDestination(mixer);
    mixer->addVideoDestination(mixDeviceToHost);
    mixer->addVideoDestination(encoder);
    mixDeviceToHost->addVideoDestination(fileMix);
    encoder->addVideoDestination(fileEncoded);
    loop->run();
    return 0;
}