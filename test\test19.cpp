#include "MediaPipeline.h"
#include <json.hpp>
#include <stdio.h>
#include <ljcore/jlog.h>
#include <hv/EventLoop.h>


extern int mmi_record_audio;
extern int mmi_record_video;
extern int mmi_print_state;

using namespace panocom;

int main(int argc, char **argv)
{
    for (size_t i = 1; i < argc; i++)
    {
        if (strcmp(argv[i], "-ps") == 0)
        {
            printf("opt print state\n");
            mmi_print_state = 1;
        }
        if (strcmp(argv[i], "-ra") == 0)
        {
            printf("opt record audio\n");
            mmi_record_audio = 1;
        }
        if (strcmp(argv[i], "-rv") == 0)
        {
            printf("opt record video\n");
            mmi_record_video = 1;
        }
    }
    
    signal(SIGSEGV, [](int) {
        jlog_uninit();
        (void) signal(SIGSEGV, SIG_DFL);  
    }); // 设置退出信号

    signal(SIGINT, [](int) {
        jlog_uninit();
        (void) signal(SIGINT, SIG_DFL);  
    }); // 设置退出信号

    signal(SIGTERM, [](int) {
        (void) signal(SIGTERM, SIG_DFL);  
        jlog_uninit();
    });
    auto loop = std::make_shared<hv::EventLoop>();
    jlog_init(nullptr);
    nlohmann::json j;
    auto capturer = VideoFrameCapturer::CreateVideoCapturer("FfVideoCapturer", j.dump());
    j.clear();
    j["codec"] = "mjpeg";
    auto decoder = VideoFrameDecoder::CreateVideoDecoder("FfVideoDecoder", j.dump());
    j.clear();
    j["codec"] = "libx264";
    auto encoder = VideoFrameEncoder::CreateVideoEncoder("FfVideoEncoder", j.dump());
    j.clear();
    j["payloadType"] = 96;
    j["hz"] = 90000;
    j["dst"] = nlohmann::json::array();
    nlohmann::json dst;
    dst["ip"] = "127.0.0.1";
    dst["port"] = 4444;
    j["dst"].push_back(dst);
    auto rtpSender = RtpEngine::CreateRTPEngine("JRtpEngine", j.dump());
    capturer->addVideoDestination(decoder);
    decoder->addVideoDestination(encoder);
    encoder->addVideoDestination(rtpSender);
    loop->run();
    return 0;
}