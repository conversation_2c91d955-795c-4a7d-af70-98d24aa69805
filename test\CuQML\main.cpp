#include "QCuVideoItem.h"
#include <QGuiApplication>
#include <QQmlApplicationEngine>
#include <ljcore/jlog.h>
#include <ljcore/jini.h>
#include <signal.h>

#define JLOG_CFG_FILE   "jlog.ini"

using namespace panocom;

extern int mmi_print_state;

int main(int argc, char *argv[])
{
    signal(SIGSEGV, [](int) {
        jlog_uninit();
        (void) signal(SIGSEGV, SIG_DFL);  
    }); // 设置退出信号

    signal(SIGINT, [](int) {
        jlog_uninit();
        (void) signal(SIGINT, SIG_DFL);  
    }); // 设置退出信号

    signal(SIGTERM, [](int) {
        jlog_uninit();
        (void) signal(SIGTERM, SIG_DFL);  
    });
    void *hd = NULL;
    jlog_cfg_t cfg = {0};
    hd = jini_init(JLOG_CFG_FILE);
    if (hd) 
    {
        cfg.buf_size = jini_get_int(hd, "jlog", "buf_size", 1024) << 10;
        cfg.wake_size = jini_get_int(hd, "jlog", "wake_size", 64) << 10;
        cfg.level = jini_get_int(hd, "jlog", "level", JLOG_LEVEL_INFO);
        cfg.mode = jini_get_int(hd, "jlog", "mode", JLOG_TO_NET);

        cfg.file.file_size = jini_get_int(hd, "jlog", "file_size", 1024) << 10;
        cfg.file.file_count = jini_get_int(hd, "jlog", "file_count", 10);
        cfg.file.file_path = jini_get(hd, "jlog", "file_path", "jlog");

        cfg.net.is_ipv6 = jini_get_int(hd, "jlog", "is_ipv6", 0) ;
        cfg.net.ip_port = jini_get_int(hd, "jlog", "ip_port", 9999) ;
        cfg.net.ip_addr = jini_get(hd, "jlog", "ip_addr", "***********") ;
        printf("jlog mode=%d addr=%s:%d\n", cfg.mode, cfg.net.ip_addr, cfg.net.ip_port);
        jlog_init(&cfg);
        jini_uninit(hd);
    }
    else
    {
        jlog_init(NULL);
    }
    
    mmi_print_state = 1;
    QCoreApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
    QCoreApplication::setAttribute(Qt::AA_UseDesktopOpenGL);

    QGuiApplication app(argc, argv);
    
    qmlRegisterType<QCuVideoItem>("an.OpenGLItem", 1, 0, "OpenGLItem");
    QQmlApplicationEngine engine;
    engine.load(QUrl(QStringLiteral("qrc:/main.qml")));
    if (engine.rootObjects().isEmpty())
        return -1;

    return app.exec();
}

