#ifndef P_MediaManagerInterface_h
#define P_MediaManagerInterface_h

#include <string>
#include <memory>
#include <functional>
#include "VideoLayout.h"
#include "CodecInfo.h"
#include "DevInfo.h"
#ifdef USE_CUSTOM_RENDER
#include "FramePipeline.h"
#endif

#ifdef _WINDOWS
#ifdef MediaPipeline_EXPORTS
#define MM_API __declspec(dllexport)
#else
#define MM_API __declspec(dllimport)
#endif
#else
#define MM_API
#endif

namespace panocom
{
#ifdef USE_ALSA
    struct AudioHWDeviceInfo {
        int index;              // 业务索引
        int card;               // ALSA 声卡号
        int device;             // ALSA 设备号
        std::string id;         // 设备ID（短名称）
        std::string name;       // 设备名称
        std::string type;       // 设备类型（"Playback" 和 "Capture"）
        std::string hw_string;  // 用于snd_pcm_open，如 hw:0,0
    };
    struct AudioDeviceInfo {
        int index;              // 业务索引
        std::string name;       // 设备名
        std::string desc;       // 设备描述
        bool can_capture = false;
        bool can_playback = false;
    };
#endif
    class MediaManagerInterfaceImp;
    class MM_API MediaManagerInterface
    {
    public:
        /*
         * @brief 创建一个媒体管理实例
         * @param jsonParams 参数是实例的通用配置，参考pipeline.json
         */
        MediaManagerInterface(const std::string& jsonParams = "");

        enum MMStatus
        {
            MM_SUCCESS = 0,         //成功
            MM_SESSION_EXIST,       //会话已存在
            MM_SESSION_NOT_FOUND,   //会话不存在
            MM_INVALID_PARAMETER,   //参数无效
            MM_FETCH_IP_FAIL,       //获取IP失败
            MM_PIPELINE_FAIL,       //创建pipeline失败
            MM_NEGOTIATE_FAIL,      //协商失败
            MM_NO_SUPPOETED_CODEC,  //没有支持的编码

            MM_ERROR_INVALID_ID,    //无效的设备id
            MM_ERROR_DEVICE_ALREADY_OPENED,//设备已被打开
            MM_ERROR_DEVICE_NOT_FOUND,//设备不存在
            MM_ERROR_DEVICE_NOT_OPENED,//设备未打开
            MM_ERROR_DEVICE_NOT_CLOSED,//设备未关闭
            MM_ERROR_DEVICE_NOT_READY,//设备未准备好
            MM_ERROR_DEVICE_NOT_SUPPORTED,//设备不支持
            MM_ERROR_DEVICE_NOT_AVAILABLE,//设备不可用
            MM_ERROR_DEVICE_BUSY,//设备忙
            MM_ERROR_DEVICE_TIMEOUT,//设备超时
            MM_ERROR_DEVICE_UNKNOWN,//未知错误
            MM_ERROR_DEVICE_NOT_INITIALIZED,//设备未初始化
            MM_ERROR_DEVICE_NOT_CONFIGURED,//设备未配置
            MM_ERROR_DEVICE_NOT_CONNECTED,//设备未连接
            MM_ERROR_DEVICE_NOT_AUTHORIZED,//设备未授权

            MM_ERROR_CONFIG_INVALID,//配置无效

            MM_ERROR_CONFIG_FILE_NOT_FOUND,//配置文件未找到
            MM_ERROR_CONFIG_FILE_INVALID_CONTENT,//配置文件内容无效

            MM_ERROR_IMPL_NOT_FOUND,//实现未找到
        };
        /**
         * @brief 创建一个会话
         * @param id 会话id，自定义
         * @param jsonParams 参数当前功能是决定音视频的输入输出通道，例如
         * {
                "//": "本端输出，即音视频采集通道，-1则不启用，可用于备流"，
                "local"：
                {
                    "audio":
                    {
                        "dev": 0,
                        "//": "回声消除"
                        "aec": 1,
                        "//": "降噪"
                        "ns": 1,
                        "//": "自动增益"
                        "agc": 1,
                        "//": "录音"
                        "record-audio": 1
                        "//": "延迟"
                        "delay": 1,
                        "//": "算法打印"
                        "log": 0,
                        "lp":
                        {
                            "//": "小包算法"
                            "enabled": true,
                            "bind-port": 8765,
                            "multicast-ip": "***********",
                            "multicast-port": 8765,
                            "//": "是否大包闭音"
                            "muteNotLP": false,
                        },
                        "//": "是否混音，固定为true"
                        "use-mixer": true, 
                        "//": "混音通道数组"
                        "devs": [0, 4],
                        "//": "额外使用hdmi播放本地声音，value为设备号"
                        "external-hdmi": 0,
                        "//": "使用鹅颈或全向，1为使用鹅颈，2为使用全向麦克"
                        "mic-type": 1,
                        "//": 从串口采集数据
                        "use-uart": 1,
                        "//": 语音period time, 单位ms
                        "ptime": 20,
                    },
                    "video":
                    {
                        "dev": 0
                    }
                },
                "//": "远端输入，即音视频渲染通道，-1则不启用，可用于备流"，
                "remote"：
                {
                    "audio":
                    {
                        "dev": 0,
                        "//": "额外使用hdmi播放远端声音，value为设备号"
                        "external-hdmi": 0,
                        "//": 输出数据到串口
                        "use-uart": 1,
                    },
                    "video":
                    {
                        "dev": 0
                    }
                },
                "isH323": false
            }
            @return 参考MMStatus
         */
        int CreateSession(const std::string& id, const std::string& jsonParams = "");
        
        /**
         * @brief 释放一个会话
         * @param id 会话id，CreateSession使用的id
         * @return 参考MMStatus
         */
        int ReleaseSession(const std::string& id);
        
        /**
         * @brief 获取本端SDP用于主叫
         * @param id 会话id，CreateSession使用的id
         * @param jsonParams 当前包括以下参数
         * {
                "//": "是否启用视频，默认不启用"，
                "video"：true，
                "//": "是否指定本端IP，默认取首个网卡的首个IP"，
                "ip"："127.0.0.1"，
                "//": "是否指定sdp的方向，默认sendrecv"，
                "dir"："sendonly"，
                "//": "是否指定本端网卡，默认取首个网卡的首个IP"，
                "if"："eth0"，
                "//": "是否启用DTLS，默认不启用"，
                "DTLS"：true，
            }
            @return 参考MMStatus
         */
        int GetLocalSDP(const std::string& id, std::string& sdp, const std::string& jsonParams = "");

        /**
         * @brief 设置远端SDP用于主叫收到回复SDP；或者用于被叫应答同时生成本端SDP
         * @param id 会话id，CreateSession使用的id
         * @param sdp 远端SDP
         * @param localSdp 生成本端SDP，主叫无效
         * @param jsonParams 当前包括以下参数
         * {
                "//": "是否启用视频，默认不启用",
                "video"：true,
                "//": "是否指定本端IP，默认取首个网卡的首个IP",
                "ip"："127.0.0.1",
                "//": "是否指定sdp的方向，默认sendrecv",
                "dir"："sendonly",
                "//": "是否指定本端网卡，默认取首个网卡的首个IP",
                "if"："eth0",
                "//": "是否启用DTLS，默认不启用",
                "DTLS"：true,
                "method": "INVITE",
                "//": "sip响应状态码",
                "code": 200
            }
            @return 参考MMStatus
         */
        int SetRemoteSDP(const std::string& id, const std::string& sdp, std::string& localSdp, const std::string& jsonParams = "");
        std::string GetDefaultIP();

        /**
         * @brief 设置上行音频文件，用于向对端发录音，仅支持16K16bit的PCM格式
         * @param id 会话id，CreateSession使用的id
         * @param path 文件路径
         * @param jsonParams 暂无功能
         * @return 参考MMStatus
         */
        int StartUplinkAudioFile(const std::string& id, const std::string& path, const std::string& jsonParams = "");
        int StopUplinkAudioFile(const std::string& id);

        /**
         * @brief 设置上行视频文件，用于向对端发录像，仅支持H264
         * @param id 会话id，CreateSession使用的id
         * @param path 文件路径
         * @param jsonParams 暂无功能
         * @return 参考MMStatus
         */
        int StartUplinkVideoFile(const std::string& id, const std::string& path, const std::string& jsonParams = "");
        int StopUplinkVideoFile(const std::string& id);

        /**
         * @brief 设置下行音频文件，用于播放录音，如拨号音、测试音等，仅支持16K16bit的PCM格式
         * @param id 会话id，CreateSession使用的id
         * @param path 文件路径
         * @param jsonParams 设备通道id，格式如下
         * {
                "dev": 0,
                "loop": true
            }
            @return 参考MMStatus
         */
        int StartDownlinkAudioFile(const std::string& id, const std::string& path, const std::string& jsonParams = "");
        int StopDownlinkAudioFile(const std::string& id);

        /**
         * @brief 设置下行视频文件，用于播放视频，如测试视频等
         * @param id 会话id，CreateSession使用的id
         * @param path 文件路径
         * @param jsonParams 设备通道id，格式如下
         * {
                "dev": 0
            }
            @return 参考MMStatus
         */
        int StartDownlinkVideoFile(const std::string& id, const std::string& path, const std::string& jsonParams = "");
        int StopDownlinkVideoFile(const std::string& id);
        
        using SDPNotifyCallback = std::function<void(const std::string&, const std::string&, int)>;
        
        /**
         * @brief 异步获得本端SDP用于主叫，仅使用stun功能才有意义
         * @param id 会话id，CreateSession使用的id
         * @param jsonParams 当前包括以下参数
         * {
                "//": "是否启用视频，默认不启用"，
                "video"：true，
                "//": "是否指定本端IP，默认取首个网卡的首个IP"，
                "ip"："127.0.0.1"，
                "//": "是否指定sdp的方向，默认sendrecv"，
                "dir"："sendonly"，
                "//": "是否指定本端网卡，默认取首个网卡的首个IP"，
                "if"："eth0"，
                "//": "是否启用DTLS，默认不启用"，
                "DTLS"：true，
                "//": "stun服务器"，
                "stun-server"：“127.0.0.1”，
                //": "stun服务器端口"，
                "stun-port"：3478
            }
            @return 参考MMStatus
         */
        int GetLocalSDP(const std::string& id, const std::string& jsonParams, const SDPNotifyCallback& cb);

        /**
         * @brief 异步设置远端SDP用于主叫收到回复SDP；或者用于被叫应答同时生成本端SDP; 仅使用stun功能才有意义
         * @param id 会话id，CreateSession使用的id
         * @param sdp 远端SDP
         * @param localSdp 生成本端SDP，主叫无效
         * @param jsonParams 当前包括以下参数
         * {
                "//": "是否启用视频，默认不启用"，
                "video"：true，
                "//": "是否指定本端IP，默认取首个网卡的首个IP"，
                "ip"："127.0.0.1"，
                "//": "是否指定sdp的方向，默认sendrecv"，
                "dir"："sendonly"，
                "//": "是否指定本端网卡，默认取首个网卡的首个IP"，
                "if"："eth0"，
                "//": "是否启用DTLS，默认不启用"，
                "DTLS"：true，
                "//": "stun服务器"，
                "stun-server"：“127.0.0.1”，
                //": "stun服务器端口"，
                "stun-port"：3478
            }
            @return 参考MMStatus
         */
        int SetRemoteSDP(const std::string& id, const std::string& sdp, const std::string& jsonParams, const SDPNotifyCallback& cb);

        using WorkingMediaCallback = std::function<void(const std::string&, const std::string&)>;
        using WorkingMediaCallback2 = std::function<void(const std::string&, const std::string&, const std::string&)>;
        using MediaTimeoutCallback = std::function<void(const std::string&, bool /*isVideo*/)>;
        using MediaTimeoutCallback2 = std::function<void(const std::string&, bool /*isVideo*/, bool /*connected*/)>;

        /**
         * @brief 设置媒体超时通知，注意会覆盖AddToMediaPool的设置，调用AddToMediaPool之后不应再调用此接口
         * @param id 会话id，CreateSession使用的id
         * @param timeoutMS 超时时长，毫秒
         * @param cb 断流超时通知
         * @return 参考MMStatus
         */
        int SetRemoteMediaTimeoutListener(const std::string& id, int timeoutMS, const MediaTimeoutCallback& cb);
        int SetRemoteMediaTimeoutListener2(const std::string &id, int timeoutMS, const MediaTimeoutCallback2 &cb);
        
        /**
         * @brief 创建一个媒体热备池，并增加热备对象
         * @param poolId 热备池id，自定义，用于标识一个唯一的热备池
         * @param id 会话id，CreateSession使用的id
         * @param cb 发生热备切换的通知回调
         * @return 参考MMStatus
         */
        int AddToMediaPool(const std::string poolId, const std::string& id, const WorkingMediaCallback& cb);
        int AddToMediaPool2(const std::string poolId, const std::string& id, const WorkingMediaCallback2& cb);

        /**
         * @brief 在一个媒体热备池中移除热备对象，如果该id正在使用，会生成切换
         * @param poolId 热备池id，AddToMediaPool使用的id
         * @param id 会话id，CreateSession使用的id
         * @return 参考MMStatus
         */
        int RemoveFromMediaPool(const std::string poolId, const std::string& id);

        /**
         * @brief 释放热备池，此接口产生切换通知
         * @param poolId 热备池id，AddToMediaPool使用的id
         * @return 参考MMStatus
         */
        int ClearMediaPool(const std::string poolId);

        /**
         * @brief 设置热备池使用某个热备对象
         * @param poolId 热备池id，AddToMediaPool使用的id
         * @param id 会话id，CreateSession使用的id
         * @return 参考MMStatus
         */
        int SetActiveMedia(const std::string poolId, const std::string& id);

        /**
         * @brief 获取热备池正在使用的热备对象id
         * @param poolId 热备池id，AddToMediaPool使用的id
         * @param id 返回的会话id
         * @return 参考MMStatus
         */
        int GetActiveMediaSession(const std::string& poolId, std::string &id);

        /**
         * @brief 对pipeline功能的设置，如AEC/NS等
         * @param id 会话id，CreateSession使用的id
         * @param jsonParams 设备通道id，格式如下
         * {
                "//": "除了H3之外都设置为false，还要在pipeline.json中添加回声消除模块配置",
                "innerAec": false,
                "//": "空间回声消除,大于1为打开",
                "aec": 2,
                "//": "线路回声消除,大于1为打开",
                "lec": 2,
                "//": "降噪,大于1为打开",
                "ns": 2,
                "//": "自动增益,大于1为打开",
                "agc": 2,
                "//": "背景噪声补偿,大于1为打开",
                "cng": 2,
                "//": "录音",
                "record-audio": 1,
                "//": "屏蔽组播混音地址",
                "mute-addr": ["*************", "*************"],
                "//": "使用耳机麦克风",
                "use-headset": false,
                "//": "输入增益db，[-12, 12]",
                "local-gain": 0,
                "//": "输出增益db，[-12, 12]", 
                "remote-gain": 0,
                "//": 静默麦克风和扬声器，true为静默。NOTE: 该参数会影响到其他会话，慎用。（当前只有H3支持）
                "muted": false,
                "//": "静默会话发送",
                "local-muted": false,
                "//": "静默会话接收",
                "remote-muted": false,                
		 * }
         * @return 参考MMStatus
         */
        int MediaCtrl(const std::string& id, const std::string& jsonParams);
        
        /**
         * @brief 设置合屏布局
         * @param id 会话id，CreateSession使用的id，用于输出合屏
         * @param layout 布局
         * @param jsonParams 暂无功能
         * @return 参考MMStatus
         */
        int SetMediaLayout(const std::string& id, const std::list<Region>& layout, const std::string& jsonParams = "");

        /**
         * @brief 获得支持音频编码列表
         * @return 支持音频编码的列表
         */
        std::list<CodecInst> GetSupportAudioCodecs();

        /**
         * @brief 获得支持视频编码列表
         * @return 支持视频编码的列表
         */
        std::list<CodecInst> GetSupportVideoCodecs();

        /**
         * @brief 设置音频编码优先级
         * @param codecs 支持音频编码列表中重新排序删减获得的参数
         * @param id 指定某个通道的音频编译优先级，默认为不指定
         */
        void SetAudioCodecPriority(const std::list<CodecInst>& codecs, const std::string& id = "");

        /**
         * @brief 设置视频编码优先级
         * @param codecs 支持视频编码列表中重新排序删减获得的参数
         * @param id 指定某个通道的视频编译优先级，默认为不指定
         */
        void SetVideoCodecPriority(const std::list<CodecInst>& codecs, const std::string& id = "");

        /**
         * @brief 连接PSTN和外部喇叭麦克风的通道
         * @param id 会话id，CreateSession使用的id
         */
        int StartPSTN(const std::string& id);

        /**
         * @brief 断开PSTN和外部喇叭麦克风的连接通道
         * @param id 会话id，CreateSession使用的id
         */
        int StopPSTN(const std::string& id);
        
        //不需要该接口时，不开此宏可避免引入更多头文件
#ifdef USE_CUSTOM_RENDER
        /**
         * @brief 替换pipeline.json定义的视频渲染器
         * @param id 会话id，CreateSession使用的id
         * @param ptr 视频渲染器
         */
        int AddVideoRender(const std::string& id, const FramePipeline::Ptr& ptr);

        /**
         * @brief 恢复pipeline.json定义的视频渲染器
         * @param id 会话id，CreateSession使用的id
         */
        int RemoveVideoRender(const std::string& id);
#endif
        /**
         * @brief RTP状态监听回调
         * @param id 会话id，CreateSession使用的id
         * @param json 关心的参数的统计值，收包跟发包统计一般不同时存在，音频和视频统计一般不同时存在
         * {
                "//": "收包统计",
                "recv":
                {
                    "//": "丢包统计",
                    "lost": 3,
                    "//": "乱序统计",
                    "disorder": 2,
                    "audio": {
                        "codec": "g711",
                        "bps": 8000
                    },
                    "vidoe": {
                        "codec": "h264",
                        "bps": 1000000,
                        "fps": 25
                    },
                }
                "//": "发包统计",
                "send":
                {
                    "audio": {
                        "codec": "g711",
                        "bps": 8000
                    },
                    "vidoe": {
                        "codec": "h264",
                        "bps": 1000000,
                        "fps": 25
                    },
                }
         * }
         */
        using RTPStatisticsNotifyCallback = std::function<void(const std::string& /*id*/, const std::string& /*json*/)>;
        /**
         * @brief 设置RTP状态监听
         * @param id 会话id，CreateSession使用的id
         * @param jsonParams 关心的参数，暂未实现，默认全开
         * {
                "//": "收包统计",
                "recv":
                {
                    "audio": {
                        "codec": 1,
                        "bps": 1,
                        "//": "乱序统计",
                        "disorder": 1,
                        "//": "丢包统计",
                        "lost": 1,
                        "recvPacketCount": 0,
                        "packetRate": 0,
                    },
                    "video": {
                        "codec": 1,
                        "bps": 1,
                        "fps": 1,
                        "//": "乱序统计",
                        "disorder": 1,
                        "//": "丢包统计",
                        "lost": 1,
                        "recvPacketCount": 0,
                        "packetRate": 0,
                    },
                }
                "//": "发包统计",
                "send":
                {
                    "audio": {
                        "codec": 1,
                        "bps": 1,
                        "sendPacketCount": 0,
                        "packetRate": 0,
                        "delay": 0,
                    },
                    "video": {
                        "codec": 1,
                        "bps": 1,
                        "fps": 1,
                        "sendPacketCount": 0,
                        "packetRate": 0,
                        "delay": 0,
                    },
                }
                "//": "编码器统计信息"
                "encoder":
                {
                    "audio": 
                    {
                        "codecType": "opus",
                    },
                    "video":
                    {
                        "codecType": "h264",
                        "width": 640,
                        "height": 480,
                    }
                },
                "//": "解码器统计信息"
                "decoder":
                {
                    "audio":
                    {
                        "codecType": "opus",
                    },
                    "video":
                    {
                        "codecType": "h264"",
                        "width": 640,
                        "height": 480,
                        "fps": 15, // 解码帧率
                    }
                }
         * }
         * @param cb 回调
         * @param use_async 是否异步调用
         */
        void SetRTPStatisticsListener(const std::string& id, const std::string& jsonParam, const RTPStatisticsNotifyCallback& cb, bool use_async=true);

        /**
         * @brief 获取connector连接状态
         * @param jsonParam 
         * {
                "//": "设备index",
                "dev": 0,
                "//": "显卡index",
                "card": 0
         * }
         */
        bool GetConnectorStatus(const std::string& jsonParam);

        /**
         * @brief 获取Camera连接状态
         * @param jsonParam 
         * {
                "//": "设备index",
                "dev": 0,
                "//": "显卡index",
                "gpuIndex": 0
         * }
         */
        bool GetCameraStatus(const std::string& jsonParam);

        /**
         * @brief 播放文件接口
         * @param jsonParam 
         * {
                "audio": {
                    "dev": 0,
                    "path": "test.pcm"
                },
                "video": {
                    "dev": 0,
                }
         * }
         */
        int StartPlayFile(const std::string& jsonParams);
        int StopPlayFile(const std::string& jsonParams);

        int UpdateAudioCapturer(const std::string &id, const std::string &jsonParams);
        int UpdateAudioRender(const std::string &id, const std::string &jsonParams);
        
        /**
         * @brief 修改混音通道
         * @param id 会话id，自定义
         * @param jsonParams 参数当前功能是决定音视频的输入输出通道，例如
         * {
                "//": "本端混音"，
                "local"：
                {
                    "audio":
                    {
                        "//": "是否混音，固定为true"
                        "use-mixer": true, 
                        "//": "混音通道数组"
                        "devs": [0, 4]
                    }
                },
                "//": "接收混音，未实现"，
                "remote"：
                {
                    "audio":
                    {
                        "use-mixer": true,
                        "devs": [0, 4]
                    }
                }
            }
            @return 参考MMStatus
         */
        int UpdateAudioMixer(const std::string &id, const std::string &jsonParams);
        /**
         * @brief 修改音频输出通道
         * @param id 会话id，自定义
         * @param jsonParams 参数当前功能是决定音视频的输入输出通道，例如
         * {
                "local"：
                {
                    "audio":
                    {
                        "devs": [0, 4]
                    }
                },
                "//": "音频输出通道"，
                "remote"：
                {
                    "audio":
                    {
                        "devs": [0, 4]
                    }
                }
            }
            @return 参考MMStatus
         */
        int UpdateAudioDispatcher(const std::string &id, const std::string &jsonParams);
        /**
         * @brief 自环测试接口
         * @param id 自环测试索引，用于结束此次测试 
         * @param jsonParam 
         * {
                "audio": {
                    "capturer": {
                        "//"： AudioSpec： 使用libPNPCM；AAlsaAudioFrameCapturer：使用alsa(HDMI)
                        "name": "AudioSpec",
                        "samplerate": 16000,
                        "channel": 1,
                        "dev": -1,
                        "maxChn": 8
                    },
                    "renderer": {
                        "//"： AudioSpec： 使用libPNPCM；AAlsaAudioFrameRender：使用alsa
                        "name": "AudioSpec",    
                        "samplerate": 16000,
                        "channel": 1,
                        "dev": -1,
                        "maxChn": 8
                    }
                },
                "video": {
                    "capturer": {
                        "name": "V4L2DMAVideoFrameCapturer",
                        "width": 1920,
                        "height": 1080,
                        "fps": 30,
                        "format": "bgr3",
                        "dev": -1,
                    "app": "vst"
                    },
                    "render": {
                        "name": "RKVideoRender",
                        "frameCount": 8,
                        "dev": -1
                    }
                }
         * }
         */
        int LoopbackTest(const std::string &id, const std::string& jsonParm);
        int StopLoopbackTest(const std::string &id);

        /**
         * @brief 打开画中画
         * @param id session id
         * @param jsonParam 
         * {
         *      "dev": 0, // 输入源设备号
         *      "layout-type": 0, // 画中画布局类型
         *      "reverse": false, // 画中画布局反转，1：反转；0：不反转；默认不反转
         * }
         */
        /*
           布局类型：
            0: 单画面布局
            1: 双画面水平布局
            2: 四分之一画面上方左上角布局
            3: 四分之一画面上方右上角布局
            4: 四分之一画面下方右下角布局
            5: 四分之一画面下方左下角布局
        */
        int OpenPip(const std::string &id, const std::string& jsonParm);
        int ClosePip(const std::string &id);
        
        /**
         * @brief 打开字幕PC
         * @param id session id
         * @param jsonParam
         * {
         *      "dev": 2, // 字幕PC设备号
         *      "layout": {
         *          "x-num": 0, // 字幕PC x坐标
         *          "x-den": 1920,
         *          "y-num": 0, // 字幕PC y坐标
         *          "y-den": 1080,
         *          "w-num": 1920, // 字幕PC 宽度
         *          "w-den": 1920,
         *          "h-num": 1080, // 字幕PC 高度
         *          "h-den": 1080,
         *          "srcX-num": 0, // 字幕PC 源x坐标"
         *          "srcX-den": 1920,
         *          "srcY-num": 0, // 字幕PC 源y坐标"
         *          "srcY-den": 1080,
         *          "srcW-num": 1920, // 字幕PC 源宽度"
         *          "srcW-den": 1920,
         *          "srcH-num": 1080, // 字幕PC 源高度"
         *          "srcH-den": 1080
         *      },
         * }
         */
        int OpenCaption(const std::string &id, const std::string& jsonParm);
        int CloseCaption(const std::string &id);        

        /**
         * @brief 创建音频采集器
         * @param id 音频采集器id, 取值范围[0-256]
         * @param jsonParam 音频采集器参数信息
         * @return 0: 成功；非零：失败（错误码）   
         * {
         *      "type": "audio", // 类型，固定为"audio"
         *      "name": "", // 名称
         *      "channels": 2, // 声道数，取值范围：[1-8]
         *      "samplerate": 48000, // 采样率，取值范围：[16000-96000] 
         *      "format": "pcm", // 音频格式，"pcm"、"aac"
         * }
         */
        int CreateAudioCapturer(const int &id, const std::string &jsonParam);        
        int DestroyAudioCapturer(const int &id);

        /**
         * @brief 创建音频渲染器
         * @param id 音频渲染器id, 取值范围[0-256]
         * @param jsonParam 音频渲染器参数信息
         * @return 0: 成功；非零：失败（错误码）   
         * {
         *      "type": "audio", // 类型，固定为"audio"
         *      "name": "", // 名称
         *      "channels": 2, // 声道数，取值范围：[1-8]
         *      "samplerate": 48000, // 采样率，取值范围：[16000-96000] 
         *      "format": "pcm", // 音频格式，"pcm"、"aac"
         * }
         */
        int CreateAudioRenderer(const int &id, const std::string & jsonParam);
        int DestroyAudioRenderer(const int &id);

        using AudioCapturerNotifyCallback = std::function<void(const int &id, const std::string & jsonParam)>;
        /**
         * @brief 设置音频采集器状态回调函数
         * @param id 音频采集器id, 取值范围[0-256]
         * @param jsonParam 配置参数
         * @param callback 回调函数
         * @return 0: 成功；非零：失败（错误码）   
         * @note 回调函数中jsonParam的格式如下：
         * {
         *      "volume": 50, // 音量，取值范围：[0-100]
         *      "mute": false, // 静音，true:静音；false：非静音
         *      "channels": 2, // 声道数，取值范围：[1-8]
         *      "samplerate": 48000 // 采样率，取值范围：[16000-96000]
         *      "connected": true // 连接状态，true:已连接；false：未连接
         * }
         */
        int SetAudioCapturerNotifyCallback(const int &id, const std::string& jsonParam, const AudioCapturerNotifyCallback& callback);

        using AudioRendererNotifyCallback = std::function<void(const int &id, const std::string & jsonParam)>;
        /**
         * @brief 设置音频渲染器状态回调函数
         * @param id 音频渲染器id, 取值范围[0-256]
         * @param jsonParam 配置参数
         * @param callback 回调函数
         * @return 0: 成功；非零：失败（错误码）
         * @note 回调函数中jsonParam的格式如下：
         * {
         *      "deviceId": 0, // 设备id
         *      "volume": 50, // 音量，取值范围：[0-100]
         *      "mute": false, // 静音，true:静音；false：非静音
         *      "channels": 2, // 声道数，取值范围：[1-8]
         *      "samplerate": 48000 // 采样率，取值范围：[16000-96000]
         *      "connected": true // 连接状态，true:已连接；false：未连接
         * }
         */       
        int SetAudioRendererNotifyCallback(const int &id, const std::string& jsonParam, const AudioRendererNotifyCallback& callback);

        /**
         * @note deprecated
         * @brief 获取音频输入设备状态和参数信息（包括音量、静音、声道数、采样率、连接状态、音频格式等）
         * @param id 音频音频输入设备号，0-31 
         * @param[out] jsonParam 音频输入设备参数信息
         * @return 0: 成功；非零：失败（错误码）   
         * {
         *      "deviceId": 0, // 设备id
         *      "volume": 50, // 音量，取值范围：[0-100]
         *      "mute": false, // 静音，true:静音；false：非静音
         *      "channels": 2, // 声道数，取值范围：[1-8]
         *      "samplerate": 48000 // 采样率，取值范围：[16000-96000]
         *      "connected": true // 连接状态，true:已连接；false：未连接
         * }
         */
        int GetAudioCapturerStatus(const int id, std::string &jsonParam);

#ifdef USE_ALSA
        /**
         * @brief 获取音频设备信息（现仅支持hw）
         * @return 音频设备信息列表
         */
        std::vector<AudioHWDeviceInfo> ListAudioHWDeviceInfos();

        /**
         * @brief 获取所有音频设备信息（包括hw，plughw，sysdefault， dmix等）
         * @return 音频设备信息列表
         */
        std::vector<AudioDeviceInfo> ListAudioDeviceInfos();
#endif
    private:
        std::shared_ptr<MediaManagerInterfaceImp> imp_;
    };
}

#endif