#ifndef SPEC_SHMPCM_H_
#define SPEC_SHMPCM_H_
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/ioctl.h>
#include <sys/mman.h>
#include "fcntl.h"

#define PCM_MAX_NR                          (32)
#define PCM_PAGE_SIZE                       (4096)
#define PCM_BUF_SPACE                          (PCM_PAGE_SIZE)  // 使用最大16K16BIT/100ms计算 32 * 100 = 3200, 在扩展为一个PAGE取整

struct pcm_shmem_head_str
{
    unsigned int max_ch;                    // 实际最大通道数量 <= PCM_MAX_NR
    unsigned int buff_max_size;             // 缓冲管理最大数量 <  缓冲最大空间
    unsigned int buff_max_space;            // 缓冲最大空间
    unsigned int hw_frame_len;              // 处理帧长，读写必须为帧长的整数倍
    // unsigned int rcv_wr;                    // 读FPGA缓冲统一写指针
    const unsigned int rcv_wr[PCM_MAX_NR];  // 读FPGA缓冲写指针
    unsigned int rcv_rd[PCM_MAX_NR];        // 读FPGA缓冲读指针
    unsigned int snd_wr[PCM_MAX_NR];        // 写FPGA缓冲写指针
    const unsigned int snd_rd[PCM_MAX_NR];  // 写FPGA缓冲读指针
    const unsigned int ref_wr[PCM_MAX_NR];  // AEC参考缓冲写指针
    unsigned int ref_rd[PCM_MAX_NR];        // AEC参考缓冲读指针
};

struct pcm_shmem_str
{
    struct pcm_shmem_head_str shm_head;
    // 使用保留字段，开头一个PAGE(4K)为控制字段，4K以后作为PCM buffer使用
    unsigned char reserve[PCM_PAGE_SIZE - sizeof(struct pcm_shmem_head_str)];
    unsigned char rcv_buf[PCM_MAX_NR][PCM_BUF_SPACE];
    unsigned char snd_buf[PCM_MAX_NR][PCM_BUF_SPACE];
    unsigned char ref_buf[PCM_MAX_NR][PCM_BUF_SPACE];
};
#define PCM_SHMEM_SIZE          (sizeof(struct pcm_shmem_str))

typedef struct ioctl_str
{
	unsigned int type;
	unsigned int ch;
	unsigned int len;
	unsigned int data[1];
}ioctl_s;

#define IOCTL_CMD_MAGIC                 (0x86)
#define IOCTL_CMD_PCM_IRQ_SYNC                                      _IOR(IOCTL_CMD_MAGIC, 210, int)		// PCM 中断同步
#define IOCTL_CMD_PCM_GET_FPGA_TICK                             _IOR(IOCTL_CMD_MAGIC, 211, int)		// PCM 获取FPGA TICK

namespace panocom
{
class ShmPcm
{
private:
    /* data */
    ShmPcm(/* args */);
    ~ShmPcm() = default;
public:
    static ShmPcm& inst();
    int init();
    int deinit();
	int ReadPcm(unsigned int nChn, unsigned char* buf, unsigned int size, bool sync = false);
	int WritePcm(unsigned int nChn, unsigned char* pPcm, unsigned int uiPcmSize, bool sync = false);
    int ReadPcmRef(unsigned int nChn, unsigned char* buf, unsigned int size, bool sync = false);
	unsigned int Get_RecvLen(unsigned int nChn);
	unsigned int Get_SendFreeLen(unsigned int nChn);
    int ClearReadBuf(unsigned int nChn);
    // int ClearWriteBuf(unsigned int nChn);
    int Sync();
    int GetTickFPGA();
    int GetFd() { return fdDev_; }
private:
    int fdDev_;
    struct pcm_shmem_str * shmem;
};

} // namespace panocom

#endif