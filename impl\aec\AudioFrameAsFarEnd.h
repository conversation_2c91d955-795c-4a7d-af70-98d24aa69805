// created by gyj 2024-3-28
#ifndef P_AudioFrameAsFarEnd_h
#define P_AudioFrameAsFarEnd_h

#include "AudioFrameProcesser.h"

namespace panocom
{
    class Frame;
    class AudioFrameAsFarEnd : public AudioFrameProcesser
    {
    public:
        AudioFrameAsFarEnd(const std::string& jsonParams);
        ~AudioFrameAsFarEnd() override;

        void onFrame(const std::shared_ptr<Frame>& frame) override;
    };
}

#endif