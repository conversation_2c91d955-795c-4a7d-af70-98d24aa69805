#ifndef DISPLAY_RKVIDEOFRAMERENDER_H_
#define DISPLAY_RKVIDEOFRAMERENDER_H_
#include <chrono>
#include <future>
#include <vector>
#include <queue>
#include <map>
#include <unordered_map>
#include <unordered_set>

#include <xf86drm.h>
#include <xf86drmMode.h>
#include <hv/EventLoopThread.h>

#include "VideoFrameRender.h"
#include "VideoLayout.h"
#include "drm_utils.h"
#include "rk/rk_frame_buffer.h"
#include "rk/RKVideoLayout.h"

#define USE_MULTI_PLANES 0
#define USE_SINGLE_PLANE 1
// #ifdef __cplusplus
// extern "C" {
// #endif
// #include <ft2build.h>
// #ifdef __cplusplus
// }
// #endif
// #include FT_FREETYPE_H
// #include FT_GLYPH_H
// #include FT_OUTLINE_H

// #include "osd/osd_common.h"

namespace panocom {
typedef enum {
  OSD_PIX_FMT_NONE = -1,
  OSD_PIX_FMT_YUV420P,
  OSD_PIX_FMT_NV12,
  OSD_PIX_FMT_NV21,
  OSD_PIX_FMT_YUV422P,
  OSD_PIX_FMT_NV16,
  OSD_PIX_FMT_NV61,
  OSD_PIX_FMT_YUYV422,
  OSD_PIX_FMT_UYVY422,
  OSD_PIX_FMT_YUV444SP,
  OSD_PIX_FMT_RGB332,
  OSD_PIX_FMT_RGB565,
  OSD_PIX_FMT_BGR565,
  OSD_PIX_FMT_RGB888,
  OSD_PIX_FMT_BGR888,
  OSD_PIX_FMT_ARGB8888,
  OSD_PIX_FMT_ABGR8888,
  OSD_PIX_FMT_RGBA8888,
  OSD_PIX_FMT_BGRA8888,
  // Compound type
  OSD_PIX_FMT_FBC0,
  OSD_PIX_FMT_FBC2,
  OSD_PIX_FMT_MJPEG,
  OSD_PIX_FMT_NB
} OSDPixelFormat;
typedef struct {
  int x;
  int y;
  int w;
  int h;
  void *data;
  OSDPixelFormat fmt;
  unsigned char enable;
} OsdInfo;

struct CustomRect {
    CustomRect() : left(0), top(0), width(1920), height(1080) {}
    int32_t left;
    int32_t top;
    uint32_t width;
    uint32_t height;
    uint32_t zpos;
};

class RKVideoFrameRender : public VideoFrameRender
{
private:
	/* data */
public:
	RKVideoFrameRender(int fd, const std::string& jsonParams);
	~RKVideoFrameRender();
	void onFrame(const std::shared_ptr<Frame>& frame) override;
	static bool GetConnectorStatus(int fd, int dev);
	int updateParams(const std::string &jsonParams) override;
	void setFrameTimeoutNotify(int ms, void* param, const TimeoutNotifyCallback& cb) override;
	static uint32_t get_property_id(int fd, drmModeObjectProperties *props, const char *name);
	static int32_t get_property_value(int fd, drmModeObjectProperties *props, const char *name);
#if USE_MULTI_PLANES
    bool setPlane(int32_t id, const std::string& jsonParams) override;
    bool unsetPlane(int32_t id) override;
#elif USE_SINGLE_PLANE
	void addInput(int inputId, const LayoutRect& layout) override;
	void removeInput(int inputId) override;
	void updateLayout(int inputId, const LayoutRect& layout);
    void updateLayout(std::map<int, LayoutRect> &layouts);
    void loadPresetLayouts(int presetIndex, bool reverse) override;
    void setSourceId(void *source, int32_t id) override;
    void unsetSourceId(void *source) override;
    bool getSourceId(void *source, int32_t &id);
#endif
private:
	bool Init();

	bool GetCrtc(drmModeRes *res);
	bool GetConnector(drmModeRes *res);
    bool GetPlane();
    bool GetPipPlane();
	
    struct FB {
        uint32_t fb_handle;
        uint32_t index;
        FB()
            : fb_handle(0)
            , index(0) {}
    };
	
    bool SetCrtc(const FB &fb);
    bool SetPlane(const FB &fb);
	bool PageFlip(const FB &fb);
	bool RenderBuffer();

	void ResetStatistics();
	void LogStatistics();

	// int CreateFont(const char *font_path, int font_size);
	// int DestoryFont();
#if USE_MULTI_PLANES
    bool InitMulti();
    bool GetPlane(int id);
#elif USE_SINGLE_PLANE
    bool InitSingle();
	std::shared_ptr<Frame> getNextValidFrame(int inputId);
#endif
private:
	bool running_;
	bool displaying_;
    bool connecotr_connected_;
	int fd_;
	buffer_object bo_[2];
    buffer_object pip_bo_[2];

	int32_t crtc_index_;
	int32_t dev_;
    uint32_t crtc_id_;
    uint32_t con_id_;
    uint32_t encoder_id_;
    uint32_t plane_id_;
    uint32_t pip_plane_id_;

	drmModeModeInfo mode_;
    drmModeModeInfo first_mode_;
    std::string format_str_;
    unsigned int width_;
    unsigned int height_;
	unsigned int format_;
	bool interlace_;
    struct CrtcRect {
		CrtcRect() : left(0), top(0), width(1920), height(1080) {}
        int32_t left;
        int32_t top;
        uint32_t width;
        uint32_t height;
        uint32_t zpos;
    };
	CrtcRect crtc_rect_;
	int32_t fps_;
	int64_t renderGap_;
	std::chrono::steady_clock::time_point render_time_;
	bool first_frame_;
	std::vector<Region> video_layout_;
	std::mutex layout_mutex_;

	std::future<bool> display_future_;
	std::mutex mutex_;
	std::condition_variable cv_;

	std::queue<std::shared_ptr<Frame>> frame_queue_;
    std::queue<std::shared_ptr<Frame>> pip_frame_queue_;

	std::chrono::steady_clock::time_point statistics_start_time_;
	uint32_t frames_recieved_;
	uint32_t frames_to_render_;
    uint32_t local_frames_recieved_;
    uint32_t local_frames_to_render_;
    bool pip_;

	hv::EventLoopThread check_timeout_thread_;

	// FT_Library library_;
	// FT_Face face_;
	// FT_GlyphSlot slot_;
	// FT_Vector pen_;

	// int font_size_;
	// uint font_color_;
	// std::string font_path_;

  	// std::unordered_map<int, OsdInfo> osds;
#if USE_MULTI_PLANES
    
    struct PlaneLayout
    {
        PlaneLayout() : plane_id(-1){}
        int32_t plane_id;
        float top;
        float left;
        float width;
        float height;
        uint32_t crop_x;
        uint32_t crop_y;
        uint32_t crop_width;
        uint32_t crop_heigth;
        uint32_t zpos;
        buffer_object bo[2];
        int8_t index;
        uint32_t property_fb_id;
        std::queue<std::shared_ptr<Frame>> frame_queue;
    };
    
    std::unordered_map<int, PlaneLayout> plane_layouts_;
#elif USE_SINGLE_PLANE
	struct InputChannel {
		std::deque<CachedFrame> cache;
		std::shared_ptr<Frame> last_frame;
		std::mutex mtx;
	};
	std::map<int, InputChannel> input_channels_;
	std::map<int, LayoutRect> layout_map_;
	std::mutex layout_mtx_;

	int max_cache_size_;
	int timeoutMs_;

    // 布局改变时刷黑屏一次
    bool need_clear_screen_ = false;
    std::unordered_map<void*, int32_t> source_id_map_;
#endif



};

} // namespace panocom

#endif