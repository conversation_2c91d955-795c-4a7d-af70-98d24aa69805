#include "drm_utils.h"

#include <fcntl.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/mman.h>

#include <drm/drm.h>
#include <drm/drm_fourcc.h>
#include <xf86drm.h>
#include <xf86drmMode.h>

#include <rga/im2d.hpp>
#include <rga/rga.h>
#include <ljcore/jlog.h>

namespace panocom
{

DrmUtils::DrmUtils(/* args */)
{
}

DrmUtils::~DrmUtils()
{
} 

int DrmUtils::drm_format_to_bpp(uint32_t format) {
    switch (format) {
        case DRM_FORMAT_C8:
        case DRM_FORMAT_RGB332:
        case DRM_FORMAT_BGR233: return 8;
        case DRM_FORMAT_NV12:
        case DRM_FORMAT_NV21:
        case DRM_FORMAT_YUV420: return 12;
        case DRM_FORMAT_YUV422:
        case DRM_FORMAT_NV16:
        case DRM_FORMAT_NV61:

        case DRM_FORMAT_XRGB1555:
        case DRM_FORMAT_XBGR1555:
        case DRM_FORMAT_RGBX5551:
        case DRM_FORMAT_BGRX5551:
        case DRM_FORMAT_ARGB1555:
        case DRM_FORMAT_ABGR1555:
        case DRM_FORMAT_RGBA5551:
        case DRM_FORMAT_BGRA5551:

        case DRM_FORMAT_RGB565:
        case DRM_FORMAT_BGR565: return 16;
        case DRM_FORMAT_YUV444:
        case DRM_FORMAT_NV24:
        case DRM_FORMAT_NV42:

        case DRM_FORMAT_RGB888:
        case DRM_FORMAT_BGR888: return 24;
    }
    return 32;
}

int32_t DrmUtils::modeset_create_fb2(int dev_fd, struct buffer_object *bo, bool mapped) {
	int32_t ret = -1;
    drm_mode_create_dumb create {};
	uint32_t handles[4] = {0}, pitches[4] = {0}, offsets[4] = {0};
    // create a dumb-buffer
    create.width = bo->width;
    create.height = bo->height;
    create.bpp = drm_format_to_bpp(bo->format);
    ret = drmIoctl(dev_fd, DRM_IOCTL_MODE_CREATE_DUMB, &create);
    if (ret != 0) {
        jerror("drmIoctl failed, ret = %d", ret);
        return ret;
    }

    // TODO: 适配不同colorspace
    // bind the dumb-buffer to an FB object
    bo->pitch = create.pitch;
    bo->size = create.size;
    bo->handle = create.handle;

	handles[0] = bo->handle;
	pitches[0] = bo->width;
	offsets[0] = 0;
	handles[1] = bo->handle;
	pitches[1] = bo->width;
	offsets[1] = bo->width * UPALIGNTO(bo->height, 16);

	ret = drmModeAddFB2(dev_fd, bo->width, bo->height, bo->format, handles,
						pitches, offsets, &bo->fb_id, 0);
    if (ret != 0) {
        jerror("drmModeAddFB2 failed, ret = %d", ret);
        return ret;
    }

	ret = drmPrimeHandleToFD(dev_fd, create.handle, 0, &bo->dst_fd);
    if (ret != 0) {
        jerror("drmPrimeHandleToFD failed, ret = %d", ret);
        return ret;
    }
	jinfo("modeset_create_fb2 dst_fd = %d size = %d", bo->dst_fd, bo->size);

    if (mapped) {
        drm_mode_map_dumb map {};
        // map the dumb-buffer to userspace
        map.handle = create.handle;
        ret = drmIoctl(dev_fd, DRM_IOCTL_MODE_MAP_DUMB, &map);
        if (ret != 0) {
            jerror("drmIoctl failed, ret = %d", ret);
            return ret;
        }
        bo->vaddr = (uint8_t *)mmap(0, create.size, PROT_READ | PROT_WRITE, MAP_SHARED, dev_fd, map.offset);
        set_black(bo);
    }
    return 0;
}

void DrmUtils::modeset_destroy_fb(int dev_fd, struct buffer_object *bo) {
    drm_mode_destroy_dumb destroy {};
    if (bo->fb_id > 0) {
        drmModeRmFB(dev_fd, bo->fb_id);
        bo->fb_id = 0;
    }

    if (bo->vaddr) {
        munmap(bo->vaddr, bo->size);
        bo->vaddr = nullptr;
    }

    destroy.handle = bo->handle;
    drmIoctl(dev_fd, DRM_IOCTL_MODE_DESTROY_DUMB, &destroy);
    if (bo->dst_fd != -1) {
        close(bo->dst_fd);
        bo->dst_fd = -1;
    }
}

void DrmUtils::create_dumb_buffer(int fd, buffer_object &bo) {
    struct drm_mode_create_dumb create_dumb;
    memset(&create_dumb, 0x0, sizeof(create_dumb));

    create_dumb.width = UPALIGNTO(bo.width, 16);
    create_dumb.height = UPALIGNTO(bo.height, 16);
    create_dumb.bpp = bo.bpp;
    create_dumb.flags = 3;

    if (drmIoctl(fd, DRM_IOCTL_MODE_CREATE_DUMB, &create_dumb) < 0) {
        perror("DRM_IOCTL_MODE_CREATE_DUMB");
        return;
    }
    bo.handle = create_dumb.handle;
    bo.pitch = create_dumb.pitch;
    bo.size = create_dumb.size;
}

int DrmUtils::convert_to_dma_buf_fd(int dev_fd, uint32_t handle) {
    struct drm_prime_handle prime_handle = {0};
    prime_handle.handle = handle;
    prime_handle.flags = DRM_CLOEXEC | DRM_RDWR;
    if (drmIoctl(dev_fd, DRM_IOCTL_PRIME_HANDLE_TO_FD, &prime_handle) < 0) {
        perror("DRM_IOCTL_PRIME_HANDLE_TO_FD");
        return -1;
    }
    return prime_handle.fd;
    // int ret_fd = -1;
    // int ret = drmPrimeHandleToFD(fd, handle, 0, &ret_fd);
    // return ret_fd;
}

void* DrmUtils::mmap_dumb_handle(int dev_fd, uint32_t handle, int size) {
    struct drm_mode_map_dumb mmap_arg;  
    memset(&mmap_arg, 0x0, sizeof(mmap_arg));  
    mmap_arg.handle = handle;
    int ret = drmIoctl(dev_fd, DRM_IOCTL_MODE_MAP_DUMB, &mmap_arg);
    if (ret) {
        perror("failed to create map dumb\n");
    }

    void *map = mmap(0, size, PROT_READ | PROT_WRITE, MAP_SHARED, dev_fd, mmap_arg.offset);
    if (map == MAP_FAILED) {
        perror("failed to mmap buffer");
    }
    return map;
}

uint32_t DrmUtils::get_fb_id(int dev_fd, const buffer_object &bo) {
    uint32_t handles[4] = {0}, pitches[4] = {0}, offsets[4] = {0};
    handles[0] = bo.handle;
    pitches[0] = bo.pitch;
    offsets[0] = 0;
    uint32_t res = 0;
    if (drmModeAddFB2(dev_fd, bo.width, bo.height, DRM_FORMAT_NV16, handles, pitches, offsets, &res, 0) < 0) {
        perror("drmModeAddFB2");
    }
    return res;
}

bool DrmUtils::rga_copy(int32_t dst_fd, int32_t dst_width, int32_t dst_height, int32_t dst_format, 
                 int32_t src_fd, int32_t src_width, int32_t src_height, int32_t src_format) {
    int usage = 0;
    IM_STATUS ret = IM_STATUS_NOERROR;
    im_opt_t opt { 0 };
    rga_buffer_t pat { 0 };
    im_rect srect { 0 };
    im_rect drect { 0 };
    im_rect prect { 0 };
    rga_buffer_t src;
    rga_buffer_t dst;

    src = wrapbuffer_fd(src_fd, src_width, src_height, src_format, UPALIGNTO(dst_width, 16), UPALIGNTO(dst_width, 16));
    dst = wrapbuffer_fd(dst_fd, dst_width, dst_height, dst_format, UPALIGNTO(dst_width, 16), UPALIGNTO(dst_height, 16));

    usage |= IM_SYNC;

    srect.width = src_width & ~1;
    srect.height = src_height & ~1;
    drect.x = 0;
    drect.y = 0;
    drect.width = dst_width & ~1;
    drect.height = dst_height & ~1;
    ret = improcess(src, dst, pat, srect, drect, prect, -1, NULL, &opt, usage);
    if (ret == IM_STATUS_SUCCESS)
        return true;
    jerror("rag copy failed %dx%d(%dx%d) => %dx%d(%dx%d)", src_width, src_height, UPALIGNTO(src_width, 16), UPALIGNTO(src_height, 16)
                                                         , dst_width, dst_height, UPALIGNTO(dst_width, 16), UPALIGNTO(dst_height, 16));
    jerror("improcess failed: %d", ret);
    return false;
}

bool DrmUtils::rga_copy(int32_t dst_fd, int32_t dst_width, int32_t dst_height, int32_t dst_format, 
                 int32_t src_fd, int32_t src_width, int32_t src_height, int32_t hor_stride, int32_t ver_stride, int32_t src_format) {
    int usage = 0;
    IM_STATUS ret = IM_STATUS_NOERROR;
    im_opt_t opt { 0 };
    rga_buffer_t pat { 0 };
    im_rect srect { 0 };
    im_rect drect { 0 };
    im_rect prect { 0 };
    rga_buffer_t src;
    rga_buffer_t dst;

    src = wrapbuffer_fd(src_fd, src_width, src_height, src_format, hor_stride, ver_stride);
    dst = wrapbuffer_fd(dst_fd, dst_width, dst_height, dst_format, UPALIGNTO(dst_width, 16), UPALIGNTO(dst_height, 16));

    usage |= IM_SYNC;

    srect.width = src_width & ~1;
    srect.height = src_height & ~1;
    drect.x = 0;
    drect.y = 0;
    drect.width = dst_width & ~1;
    drect.height = dst_height & ~1;
    ret = improcess(src, dst, pat, srect, drect, prect, -1, NULL, &opt, usage);
    if (ret == IM_STATUS_SUCCESS)
        return true;
    jerror("rag copy failed %dx%d(%dx%d) => %dx%d(%dx%d)", src_width, src_height, UPALIGNTO(src_width, 16), UPALIGNTO(src_height, 16)
                                                         , dst_width, dst_height, UPALIGNTO(dst_width, 16), UPALIGNTO(dst_height, 16));
    jerror("improcess failed: %d", ret);
    return false;
}

// TODO: 现只处理连续内存的
void DrmUtils::set_black(struct buffer_object *bo) {
    if (!bo->vaddr)
        return;
    switch (bo->format) {
        case DRM_FORMAT_NV12:
        case DRM_FORMAT_NV21:
        case DRM_FORMAT_YUV420:
        case DRM_FORMAT_YUV422:
        case DRM_FORMAT_NV16:
        case DRM_FORMAT_NV61:
        case DRM_FORMAT_YUV444:
        case DRM_FORMAT_NV24:
        case DRM_FORMAT_NV42: {
            int Y_size = bo->width * bo->height;
            memset(bo->vaddr, 0, Y_size);
            memset(bo->vaddr + Y_size, 128, bo->size - Y_size);
        } break;
        case DRM_FORMAT_RGB888:
        case DRM_FORMAT_BGR888: {
            memset(bo->vaddr, 0, bo->size);
        } break;

        default: break;
    }
}
} // namespace panocom
