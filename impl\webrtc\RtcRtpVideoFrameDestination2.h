#ifndef IMPL_WEBRTC_RTCRTPVIDEOFRAMEDESTINATION2_H_
#define IMPL_WEBRTC_RTCRTPVIDEOFRAMEDESTINATION2_H_

#include "RtpEngine.h"
#include "FramePipeline.h"
#include "SsrcGenerator.h"
#include "WebRTCTaskRunner.h"
#include "CustomVideoEncoderFactory.h"

#include <api/transport/network_control.h>
#include <call/call.h>
#include <call/rtp_transport_controller_send.h>
#include <modules/rtp_rtcp/include/rtp_rtcp.h>
#include <modules/rtp_rtcp/source/rtp_sender_video.h>
#include <rtc_base/event.h>
#include <rtc_base/random.h>
#include <rtc_base/rate_limiter.h>
#include <rtc_base/time_utils.h>
#include "api/video/video_bitrate_allocator_factory.h"

#include <json.hpp>

#include "Frame.h"

namespace panocom
{
class RtcRtpVideoFrameDestination2 : public RtpEngine, public webrtc::Transport {
public:
    RtcRtpVideoFrameDestination2(const std::string& jsonParams);
    ~RtcRtpVideoFrameDestination2();

    void onFrame(const std::shared_ptr<Frame> &f) override;
    void start() override;
    void stop() override;

    // Implement webrtc::Transport
    bool SendRtp(const uint8_t* packet,
        size_t length,
        const webrtc::PacketOptions& options) override;
    bool SendRtcp(const uint8_t* packet, size_t length) override;
    std::string GetStats2() override;
private:
    bool init();
    uint32_t convertToRTPTimestamp(struct timeval tv);
    uint32_t presetNextTimestamp();

    bool resetSendStream();
private:
    webrtc::Random random_;
    uint32_t ssrc_ = 0;
    SsrcGenerator* const ssrcGenerator_ = nullptr;

    uint32_t startBitrate_ = 0;
    uint32_t minBitrate_;
    uint32_t maxBitrate_;
    uint32_t avgBitrate_ = 0;
    uint32_t curBitrate_ = 0;

    typedef std::shared_ptr<webrtc::Call> CallPtr;
    CallPtr call_;

    std::shared_ptr<webrtc::RtcEventLog> eventLog_;
    std::shared_ptr<rtc::TaskQueue> taskQueue_;
    std::shared_ptr<webrtc::TaskQueueFactory> taskQueueFactory_;

    std::unique_ptr<CustomVideoEncoderFactory> videoEncoderFactory_;
    std::unique_ptr<webrtc::VideoBitrateAllocatorFactory> video_bitrate_allocator_factory_;
      
    webrtc::VideoSendStream* sendStream_ = nullptr;

    rtc::Event started_;
    rtc::Event stopped_;
    rtc::Event stats_ready_;

    std::mutex mutex_;

    bool fNextTimestampHasBeenPreset_ = false;
    uint32_t fTimestampBase_ = 0;

    uint32_t encoded_width_ = 1920;
    uint32_t encoded_height_ = 1080;

    int payload_type_ = -1;
    uint8_t red_payload_type_ = 116;
    uint8_t ulpfec_payload_type_ = 127;

    bool nack_enabled_;
    bool ulpfec_enabled_;
    bool red_enabled_;
    bool ajb_enabled_;
    nlohmann::json rtp_extmap_;
    nlohmann::json remote_rtp_extmap_;
    nlohmann::json remote_rtp_rtcpfb_; 

    webrtc::VideoSendStream::Stats stats_;

    FrameFormat fmt_;
};
} // namespace panocom

#endif