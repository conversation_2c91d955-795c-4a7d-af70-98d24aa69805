#include "RtcRtpVideoFrameSource.h"
#include "SsrcGenerator.h"
#include "RtpHeader.h"
#include "thread/StaticTaskQueueFactory.h"
#include "thread/ProcessThreadProxy.h"

#include <future>
#include <system_wrappers/include/field_trial.h>
#include <api/transport/field_trial_based_config.h>
#include <modules/video_coding/include/video_error_codes.h>
#include <modules/video_coding/timing.h>
#include <rtc_base/time_utils.h>
#include <json.hpp>
#include <ljcore/jlog.h>

namespace panocom {

// #define RED_90000_PT        116 // REDundancy (RFC 2198)

static std::unique_ptr<webrtc::FieldTrialBasedConfig> g_fieldTrial= []()
{
    auto config = std::make_unique<webrtc::FieldTrialBasedConfig>();
    /**/
    webrtc::field_trial::InitFieldTrialsFromString(
        "WebRTC-KeyframeInterval/"
        "max_wait_for_keyframe_ms:500,"
        "max_wait_for_frame_ms:1500/"
        "WebRTC-TaskQueuePacer/Enabled/");
    return config;
}();

static void AppendPayloadType(const nlohmann::json &fmtp, std::map<int, int> & payloadTypes)
{
    for (auto iter = fmtp.begin(); iter != fmtp.end(); iter++)
    {
        int pt = (*iter)["payload"];
        payloadTypes[pt] = pt;
    }
}

int32_t RtcRtpVideoFrameSource::AdapterDecoder::InitDecode(const webrtc::VideoCodec* config, int32_t number_of_cores)
{
    RTC_DLOG(LS_INFO) << "AdapterDecoder InitDecode ";
    if (config) {
        RTC_DLOG(LS_INFO) << "AdapterDecoder InitDecode " << config->codecType;
        parent_->codec_ = config->codecType;
    }
    return 0;
}

int32_t RtcRtpVideoFrameSource::AdapterDecoder::Decode(const webrtc::EncodedImage& encodedImage,
    bool missing_frames,
    int64_t render_time_ms)
{
    RTC_DLOG(LS_VERBOSE) << "AdapterDecoder Decode";
    FrameFormat format = FRAME_FORMAT_UNKNOWN;
    if (parent_->first_) {
        if (encodedImage._frameType != webrtc::VideoFrameType::kVideoFrameKey) {
            return WEBRTC_VIDEO_CODEC_OK_REQUEST_KEYFRAME;
        } else {
            parent_->first_ = false;
            //codec_ = videoRecvStream_->GetVideoCodecType();
            //RTC_LOG(LS_WARNING) << "AdapterDecoder::Decode codec = " << m_codec;
        }
    }
    
    switch (parent_->codec_) {
    case webrtc::VideoCodecType::kVideoCodecVP8:
        format = FRAME_FORMAT_VP8;
        break;
    case webrtc::VideoCodecType::kVideoCodecVP9:
        format = FRAME_FORMAT_VP9;
        break;
    case webrtc::VideoCodecType::kVideoCodecH264:
        format = FRAME_FORMAT_H264;
        break;
    case webrtc::VideoCodecType::kVideoCodecH265:
        format = FRAME_FORMAT_H265;
        break;
    case webrtc::VideoCodecType::kVideoCodecAV1:
        format = FRAME_FORMAT_AV1;
        break;
    default:
        RTC_LOG(LS_WARNING) << "Unknown FORMAT " << parent_->codec_;
        format = FRAME_FORMAT_H264;
        break;
    }
    std::shared_ptr<Frame> f = Frame::CreateFrame(format);
    if (f)
    {
        f->createFrameBuffer(encodedImage.size());
        memcpy(f->getFrameBuffer(), encodedImage.data(), encodedImage.size());
        parent_->deliverFrame(f);
    }
    return 0;
}

RtcRtpVideoFrameSource::RtcRtpVideoFrameSource(const std::string &jsonParams)
    : started_(/*manual_reset=*/false, /*initially_signaled=*/false)
    , stopped_(/*manual_reset=*/false, /*initially_signaled=*/false)
    , nack_enabled_(true)
    , ulpfec_enabled_(false)
    , red_enabled_(false)
    , ajb_enabled_(false) {
    FN_BEGIN;
    name_ = "RtcRtpVideoFrameSource";
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    format_ = FRAME_FORMAT_H264;
    if (j.contains("fmt")) {
        format_ = j["fmt"];
    }
    payloadType_ = 96;
    if (j.contains("payloadType"))
    {
        payloadType_ = j["payloadType"];
    }
    if (j.contains("local_payload_type")) {
        payloadType_ = j["local_payload_type"];
    }
    ssrc_ = 0;
    if (j.contains("ssrc"))
    {
        ssrc_ = j["ssrc"];
    }
    red_payload_type_ = 116;
    if (j.contains("local_red_payload_type"))
    {
        red_payload_type_ = j["local_red_payload_type"];
    }
    ulpfec_payload_type_ = 127;
    if (j.contains("local_ulpfec_payload_type"))
    {
        ulpfec_payload_type_ = j["local_ulpfec_payload_type"];
    }
    if (j.contains("NACK")) {
        nack_enabled_ = j["NACK"];
    }
    if (j.contains("ULPFEC")) {
        ulpfec_enabled_ = j["ULPFEC"];
    }
    if (j.contains("AJB")) {
        ajb_enabled_ = j["AJB"];
    }
    if (j.contains("remote_media"))
    {
        if (j["remote_media"].contains("ext")) {
            rtp_extmap_ = j["remote_media"]["ext"];
        }
        if (j["remote_media"].contains("rtcpFb")) {
            rtp_rtcpfb_ = j["remote_media"]["rtcpFb"];
        }
    }
    mapPayloadTypes_[payloadType_] = payloadType_;
    if (j.contains("local_media") && j["local_media"].contains("fmtp")) {
        AppendPayloadType(j["local_media"]["fmtp"], mapPayloadTypes_);
    }
    if (j.contains("remote_media") && j["remote_media"].contains("fmtp")) {
        AppendPayloadType(j["remote_media"]["fmtp"], mapPayloadTypes_);
    }
    eventLog_ = std::make_shared<webrtc::RtcEventLogNull>();
    taskQueueFactory_ = createStaticTaskQueueFactory(TaskQueueType::kDecoding);
    start();
    FN_END;
}

RtcRtpVideoFrameSource::~RtcRtpVideoFrameSource()
{
    FN_BEGIN;
    stop();
    FN_END;
}

void RtcRtpVideoFrameSource::start()
{
    if (taskQueue_) return;
    FN_BEGIN;
    started_.Reset();
    {
        std::unique_lock<std::mutex> locker(mutex_);
        taskQueue_ = std::make_shared<rtc::TaskQueue>(taskQueueFactory_->CreateTaskQueue("CallTaskQueue", webrtc::TaskQueueFactory::Priority::NORMAL));
        taskQueue_->PostTask([this]() {
            webrtc::Call::Config call_config(eventLog_.get());
            call_config.task_queue_factory = taskQueueFactory_.get();
            call_config.trials = g_fieldTrial.get();

            rtc::scoped_refptr<webrtc::SharedModuleThread> moduleThread = webrtc::SharedModuleThread::Create(webrtc::ProcessThread::Create("ModuleProcessThread"), nullptr);

            // Empty thread for pacer
            std::unique_ptr<webrtc::ProcessThread> pacerThreadProxy = std::make_unique<ProcessThreadProxy>(nullptr);

            call_.reset(webrtc::Call::Create(call_config, webrtc::Clock::GetRealTimeClock(), moduleThread, std::move(pacerThreadProxy)));

            if (ssrc_ != 0)
            {
                CreateReceiveVideo(format_, payloadType_);
            }
            started_.Set();
        });
    }
    started_.Wait(rtc::Event::kForever);
    FN_END;
}

void RtcRtpVideoFrameSource::stop()
{
    FN_BEGIN;
    std::unique_lock<std::mutex> locker(mutex_);
    if (taskQueue_)
    {
        taskQueue_->PostTask([this]() {
            call_->SignalChannelNetworkState(webrtc::MediaType::VIDEO, webrtc::NetworkState::kNetworkDown);
            if (videoRecvStream_) 
            {
                RTC_DLOG(LS_INFO) << "Destroy VideoReceiveStream with SSRC: " << ssrc_;
                call_->DestroyVideoReceiveStream(videoRecvStream_);
                videoRecvStream_ = nullptr;
            }
            call_.reset();
            stopped_.Set();
        });
        stopped_.Wait(rtc::Event::kForever);
        taskQueue_.reset();
    }
    FN_END;
}

void RtcRtpVideoFrameSource::OnFrame(const webrtc::VideoFrame& video_frame) {}

//static char kTransportSequenceNumberUri[] =
//      "http://www.ietf.org/id/"
//      "draft-holmer-rmcat-transport-wide-cc-extensions-01";

void RtcRtpVideoFrameSource::CreateReceiveVideo(FrameFormat format, int payload_type)
{
    //taskQueue()->PostTask([this, format, payload_type]() {
        if (!videoRecvStream_) {
            RTC_LOG(LS_INFO) << "Create VideoReceiveStream with SSRC: " << ssrc_;
            // Create Receive Video Stream
            webrtc::VideoReceiveStream::Config default_config(this);
            default_config.rtp.local_ssrc = SsrcGenerator::GetSsrcGenerator()->CreateSsrc(); //kLocalSsrc; 流控需要区分SSRC
            default_config.rtp.rtcp_mode = webrtc::RtcpMode::kReducedSize;
            default_config.rtp.remote_ssrc = ssrc_;
            if (red_enabled_) {
                default_config.rtp.red_payload_type = red_payload_type_;
            }
            if (ulpfec_enabled_) {
                default_config.rtp.ulpfec_payload_type = ulpfec_payload_type_;
            }
            default_config.rtp.nack.rtp_history_ms = 500;
            RTC_LOG(LS_INFO) << "RtcRtpVideoFrameSource SetRemoteBitrateObserver " << this;
            //if (transport_cc_) {
            //    RTC_LOG(LS_INFO) << "TransportSequenceNumber Extension Enabled";
            //    default_config.rtp.transport_cc = true;
            //    default_config.rtp.extensions.emplace_back(
            //        kTransportSequenceNumberUri, transport_cc_);
            //} else {
            //    default_config.rtp.transport_cc = false;
            //}
            default_config.rtp.transport_cc = false;
            if (rtp_rtcpfb_.is_array())
            {
                for (auto fb = rtp_rtcpfb_.begin(); fb != rtp_rtcpfb_.end(); fb++)
                {
                    if ((*fb)["type"] == "transport-cc")
                    {
                        // 发送端sdp声明rtcp-fb支持 "transport-cc"反馈
                        RTC_LOG(LS_INFO) << "TransportSequenceNumber Extension Enabled";
                        default_config.rtp.transport_cc = true;
                        break;
                    }
                }
            }
            /*
            default_config.rtp.nack.rtp_history_ms = rtp_history_ms;
            // Enable RTT calculation so NTP time estimator will work.
            default_config.rtp.rtcp_xr.receiver_reference_time_report =
                receiver_reference_time_report;
            */
            default_config.renderer = this;

            webrtc::VideoReceiveStream::Config video_recv_config(default_config.Copy());
            video_recv_config.decoder_factory = this;
            video_recv_config.decoders.clear();
            /*
            if (!video_send_config.rtp.rtx.ssrcs.empty()) {
              video_recv_config.rtp.rtx_ssrc = video_send_config.rtp.rtx.ssrcs[i];
              video_recv_config.rtp.rtx_associated_payload_types[kSendRtxPayloadType] =
                  video_send_config.rtp.payload_type;
            }
            */
            webrtc::VideoReceiveStream::Decoder decoder;
            switch (format)
            {
            case FRAME_FORMAT_H264:
                decoder.payload_type = payload_type;
                decoder.video_format = webrtc::SdpVideoFormat(webrtc::CodecTypeToPayloadString(webrtc::VideoCodecType::kVideoCodecH264));
                RTC_DLOG(LS_INFO) << "Config add decoder:" << decoder.ToString();
                video_recv_config.decoders.push_back(decoder);
                break;
            case FRAME_FORMAT_H265:
                decoder.payload_type = payload_type;
                decoder.video_format = webrtc::SdpVideoFormat(webrtc::CodecTypeToPayloadString(webrtc::VideoCodecType::kVideoCodecH265));
                RTC_DLOG(LS_INFO) << "Config add decoder:" << decoder.ToString();
                video_recv_config.decoders.push_back(decoder);
                break;
            default:
                break;
            }

            // 对端sdp extmap:id传入底层rtp接收解析rtp扩展头
            if (ajb_enabled_ && rtp_extmap_.is_array())
            {
                video_recv_config.rtp.extensions.clear(); //先清空，不能重复
                for (auto iter = rtp_extmap_.begin(); iter != rtp_extmap_.end(); iter++)
                {
                    webrtc::RtpExtension rtpext;
                    rtpext.uri = (*iter)["uri"];
                    rtpext.id = (*iter)["value"];
                    if (webrtc::RtpExtension::IsSupportedForVideo(rtpext.uri))
                    {
                        video_recv_config.rtp.extensions.push_back(rtpext);
                    }
                }
            }

            RTC_DLOG(LS_INFO) << "VideoReceiveStream::Config " << video_recv_config.ToString();
            videoRecvStream_ = call_->CreateVideoReceiveStream(std::move(video_recv_config));
            videoRecvStream_->Start();
            call_->SignalChannelNetworkState(webrtc::MediaType::VIDEO, webrtc::NetworkState::kNetworkUp);
        }
    //});
}

std::vector<webrtc::SdpVideoFormat> RtcRtpVideoFrameSource::GetSupportedFormats() const
{
    return std::vector<webrtc::SdpVideoFormat>{
        webrtc::SdpVideoFormat(
            webrtc::CodecTypeToPayloadString(webrtc::VideoCodecType::kVideoCodecVP8)),
        webrtc::SdpVideoFormat(
            webrtc::CodecTypeToPayloadString(webrtc::VideoCodecType::kVideoCodecVP9)),
        webrtc::SdpVideoFormat(
            webrtc::CodecTypeToPayloadString(webrtc::VideoCodecType::kVideoCodecH264)),
        webrtc::SdpVideoFormat(
            webrtc::CodecTypeToPayloadString(webrtc::VideoCodecType::kVideoCodecH265)),
        webrtc::SdpVideoFormat(
            webrtc::CodecTypeToPayloadString(webrtc::VideoCodecType::kVideoCodecAV1))
    };
}

std::unique_ptr<webrtc::VideoDecoder> RtcRtpVideoFrameSource::CreateVideoDecoder(
    const webrtc::SdpVideoFormat& format)
{
    return std::make_unique<AdapterDecoder>(this);
}

void RtcRtpVideoFrameSource::onFrame(const std::shared_ptr<Frame> &f)
{
    if (f->getFrameFormat() == FRAME_FORMAT_RTP)
    {
        std::unique_lock<std::mutex> locker(mutex_);
        if (taskQueue_)
        {
            taskQueue_->PostTask([this, f](){ 
                if (ssrc_ == 0)
                {
                    RTPHeader* head = (RTPHeader*)(f->getFrameBuffer());
                    if (f->getFrameSize() < RTPHeader::MIN_SIZE
                        || head->getVersion() != 2 
                        || mapPayloadTypes_.find(head->getPayloadType()) == mapPayloadTypes_.end()
                        )
                    {
                        // rtp端口收到的第一个udp包有可能不是rtp包（如:华为box600）
                        return;
                    }
                    if (head->getVersion() != 2) return;
                    ssrc_ = head->getSSRC();
                    payloadType_ = head->getPayloadType();
                    CreateReceiveVideo(format_, payloadType_);
                }
                rtc::CopyOnWriteBuffer buffer(f->getFrameBuffer(), f->getFrameSize());
                call_->Receiver()->DeliverPacket(
                                        webrtc::MediaType::VIDEO,
                                        buffer,
                                        rtc::TimeUTCMicros()); 
            });
        }
    }
}

bool RtcRtpVideoFrameSource::SendRtp(const uint8_t* data, size_t len, const webrtc::PacketOptions& options)
{
    RTC_LOG(LS_WARNING) << "RtcRtpVideoFrameSource SendRtp called";
    return true;
}

bool RtcRtpVideoFrameSource::SendRtcp(const uint8_t* data, size_t len)
{
    std::shared_ptr<Frame> f = Frame::CreateFrame(FRAME_FORMAT_RTCP);
    if (f)
    {
        //jinfo("RtcRtpVideoFrameSource::SendRtcp %d", len);
        f->createFrameBuffer(len);
        memcpy(f->getFrameBuffer(), data, len);
        deliverFrame(f);
        return true;
    }
    return false;
}

// TODO:每个ssrc分别评估
void RtcRtpVideoFrameSource::OnReceiveBitrateChanged(const std::vector<uint32_t>& ssrcs, uint32_t bitrate)
{
    if (currentBitrate_ != bitrate) 
    {
        currentBitrate_ == bitrate;
        //RTC_LOG(LS_WARNING) << "OnReceiveBitrateChanged " << bitrate;
        if (cb_)
        {
            uint32_t ssrc = ssrcs.size() > 0 ? ssrcs[0] : 0;
            cb_(ssrc, currentBitrate_);
        }
    }
}

std::string RtcRtpVideoFrameSource::GetStats2()
{
    last_received_packet_count_ = stats_.rtp_stats.packet_counter.packets;
    stats_ready_.Reset();
    taskQueue_->PostTask([this]() {
        if (!videoRecvStream_) return;
        stats_ = videoRecvStream_->GetStats();
        stats_ready_.Set();
    });
    if (!stats_ready_.Wait(100)) return "";
    nlohmann::json j;
    j["bps"] = stats_.total_bitrate_bps;
    j["fps"] = stats_.network_frame_rate;
    j["jitter"] = (float)stats_.rtp_stats.jitter / 1000.0f;
    j["lost"] = stats_.rtp_stats.packets_lost;
    j["recvPacketCount"] = stats_.rtp_stats.packet_counter.packets;
    return j.dump();
}

} // namespace rtc_adapter
