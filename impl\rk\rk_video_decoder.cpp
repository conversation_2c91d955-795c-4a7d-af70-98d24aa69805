#include "rk_video_decoder.h"
#include <json.hpp>

#include "ljcore/jlog.h"

#include "Frame.h"


// TODO: add log
namespace panocom {
static std::map<int, std::list<MppBufferGroup>> s_idle_grps;
static std::mutex s_grps_mutex;
static MppBufferGroup getMppBufferGroup(int buf_size)
{
    std::lock_guard<std::mutex> lock(s_grps_mutex);
    MppBufferGroup group;
    if (s_idle_grps.find(buf_size) == s_idle_grps.end() || s_idle_grps[buf_size].size() == 0) {
        /* If buffer group is not set create one and limit it */
        MPP_RET ret = mpp_buffer_group_get_internal(&group, MPP_BUFFER_TYPE_DRM);
        if (ret) {
            // ELOG_ERROR_T("get mpp buffer group failed ret %d\n", ret);
            return NULL;
        }

        /* Use limit config to limit buffer count to 24 with buf_size */
        ret = mpp_buffer_group_limit_config(group, buf_size, 16);
        if (ret) {
            //ELOG_ERROR_T("limit buffer group failed ret %d\n", ret);
        }
    } else {
        group = s_idle_grps[buf_size].front();
        s_idle_grps[buf_size].pop_front();
    }
    return group;
}

static void releaseMppBufferGroup(int buf_size, MppBufferGroup group)
{
    std::lock_guard<std::mutex> lock(s_grps_mutex);
    jinfo("releaseMppBufferGroup size: %d", buf_size);
    s_idle_grps[buf_size].push_back(group);
}

RKVideoDecoder::RKVideoDecoder(const std::string &jsonParams)
    : running_(false)
	, mpp_ctx_(nullptr)
    , mpp_api_(nullptr)
	, mpp_packet_(nullptr)
	, buf_group_(nullptr)
    , packet_size_(4096)
	, codec_type_(MPP_VIDEO_CodingAVC)
    , width_(1920)
    , height_(1080)
	, group_buffer_size_(0)
	, max_queue_size_(300)
    , max_gop_count_(3)
	, frame_count_(0) {
    name_ = "RKVideoDecoder";
    nlohmann::json j;
    if (jsonParams != "") {
		j = nlohmann::json::parse(jsonParams);
		if (j.contains("codec")) {
            std::string codec_name = j["codec"];
			if (strcasecmp(codec_name.c_str(), "H264") == 0) codec_type_ = MPP_VIDEO_CodingAVC;
			else if (strcasecmp(codec_name.c_str(), "H265") == 0) codec_type_ = MPP_VIDEO_CodingHEVC;
		}
		if (j.contains("width"))
			width_ = j["width"];
		if (j.contains("height"))
			height_ = j["height"];
	}
    hor_stride_ = MPP_ALIGN(width_, 16);
    ver_stride_ = MPP_ALIGN(height_, 16);
	last_ = std::chrono::steady_clock::now();
    last_send_feedback_time_ = std::chrono::steady_clock::now();
	InitDecode();
}

RKVideoDecoder::~RKVideoDecoder()
{
    running_ = false;
    cond_.notify_all();
    if (decode_future_.valid()) {
        auto status = decode_future_.wait_for(std::chrono::milliseconds(2000));
        if (std::future_status::timeout == status) {
            // TODO: 释放超时处理
            jerror("RK video decoder stop timeout");
        } else if (std::future_status::ready == status) {
            bool flag = decode_future_.get();
            jerror("RK video decoder stop sucess: %d", flag);
        }
    }
    if (notifycallbacks_thread_.isRunning()) {
        notifycallbacks_thread_.stop(true);
        notifycallbacks_thread_.join();
    }
	Release();
    if (ofs_.is_open()) ofs_.close();
}
// TODO: 接收第一帧数据时创建解码线程
int32_t RKVideoDecoder::InitDecode() {
    MPP_RET ret = MPP_OK;
    MppParam param      = NULL;
    MpiCmd mpi_cmd = MPP_CMD_BASE;
    RK_U32 need_split = 1;
	
	ret = mpp_packet_new(&mpp_packet_);
	if (MPP_OK != ret) {
		Release();
		return ret;
	}

    ret = mpp_create(&mpp_ctx_, &mpp_api_);
    if (MPP_OK != ret) {
		Release();
        return ret;
    }
    mpi_cmd = MPP_DEC_SET_PARSER_SPLIT_MODE;
    param = &need_split;
    ret = mpp_api_->control(mpp_ctx_, mpi_cmd, param);
    if (MPP_OK != ret) {
        Release();
        return ret;
    }
    // MppPollType timeout = MPP_POLL_BLOCK;
    // ret = mpp_api_->control(mpp_ctx_, MPP_SET_OUTPUT_TIMEOUT, &timeout);
    // MppPollType default
    ret = mpp_init(mpp_ctx_, MPP_CTX_DEC, codec_type_);
    if (MPP_OK != ret) {
        Release();
        return ret;
    }
    auto oformat = MPP_FMT_YUV420SP;
    ret = mpp_api_->control(mpp_ctx_, MPP_DEC_SET_OUTPUT_FORMAT, &oformat);
    if (ret != MPP_OK) {
        jerror("Failed to set output format (ret = %d)", ret);
        return ret;
    }
    running_ = true;
    decode_future_ = std::async([this]() -> bool {
        MPP_RET ret = MPP_OK;
        MppFrame frame = nullptr;
        ResetStatistics();
        while (running_) {
            std::shared_ptr<Frame> packet;
            {
                std::unique_lock<std::mutex> lock(mutex_);
                if (running_ && packet_queue_.empty()) {
                    cond_.wait(lock, [this] { return !running_ || !packet_queue_.empty(); });
                }
                if (!running_)
                    return true;
                packet = packet_queue_.front();
                packet_queue_.pop();
                if (Frame::isIDRFrame(packet)) {
                    gop_count_--;
                }
            }
            frames_to_decode_++;
			LogStatistics();
            uint8_t *buf = packet->getFrameBuffer();
            int32_t size = packet->getFrameBufferSize();

            mpp_packet_set_data(mpp_packet_, buf);
            mpp_packet_set_size(mpp_packet_, size);
            mpp_packet_set_pos(mpp_packet_, buf);
            mpp_packet_set_length(mpp_packet_, size);
            bool pkt_done = false;
            do {                         					
                RK_S32 times = 3;
                if (!pkt_done) {
                    ret = mpp_api_->decode_put_packet(mpp_ctx_, mpp_packet_);
                    if (MPP_OK == ret) {
                        pkt_done = true;
                    } else {
                        jwarn("decode_put_packet failed: %d", ret);   
                    }
                }
                do {
                    RK_S32 get_frm = 0;
                    RK_U32 frm_eos = 0;
                try_again:
                    ret = mpp_api_->decode_get_frame(mpp_ctx_, &frame);
                    if (MPP_ERR_TIMEOUT == ret) {
                        if (times > 0) {
                            --times;
                            jinfo("try again");
                            std::this_thread::sleep_for(std::chrono::milliseconds(2));
                            goto try_again;
                        }
                    }
                    if (MPP_OK != ret) {
                        jerror("mpp decode_get_frame failed: %d", ret);
                        break;
                    }
                    if (frame) {
                        frm_eos = mpp_frame_get_eos(frame);
                        if (mpp_frame_get_info_change(frame)) {
                            RK_U32 buf_size = mpp_frame_get_buf_size(frame);
                            decode_width_ = mpp_frame_get_width(frame);
                            decode_height_ = mpp_frame_get_height(frame);
                            jinfo("RKVideoDecoder mpp frame info changed: size %d -> %dx%d", buf_size, decode_width_, decode_height_);
                            if (buf_group_ && group_buffer_size_ != 0) {
                                releaseMppBufferGroup(group_buffer_size_, buf_group_);
                            }
                            buf_group_ = getMppBufferGroup(buf_size);
                            if (!buf_group_) {
                                std::this_thread::sleep_for(std::chrono::milliseconds(1000));
                                jerror("InitDecode getMppBufferGroup failed");
                                continue;
                            }
                            group_buffer_size_ = buf_size;
                            /* Set buffer to mpp decoder */
                            ret = mpp_api_->control(mpp_ctx_, MPP_DEC_SET_EXT_BUF_GROUP, buf_group_);
                            if (MPP_OK != ret) {
                                jerror("mpp_api_->control MPP_DEC_SET_EXT_BUF_GROUP failed ret %d", ret);
                                break;
                            }
                            /*
                             * All buffer group config done. Set info change ready to let
                             * decoder continue decoding
                             */
                            ret = mpp_api_->control(mpp_ctx_, MPP_DEC_SET_INFO_CHANGE_READY, NULL);
                            if (ret) {
                                jerror("mpp_api_->control MPP_DEC_SET_INFO_CHANGE_READY failed ret %d", ret);
                                break;
                            }
                            mpp_frame_deinit(&frame);
                            frame = nullptr;
                        } else {
                            RK_U32 err_info = mpp_frame_get_errinfo(frame) | mpp_frame_get_discard(frame);
                            if (err_info) {
                                jinfo("decoder_get_frame get err info:%d discard:%d.", mpp_frame_get_errinfo(frame), mpp_frame_get_discard(frame));
                                mpp_frame_deinit(&frame);
                                frame = nullptr;
                                SendFeedbackFrame();
                            } else {
                                MppBuffer buffer = mpp_frame_get_buffer(frame);
                                if (!buffer) {
                                    jerror("mpp_frame_get_buffer failed");
                                    break;
                                }
                                auto out_frame = std::make_shared<WrapRKMppFrame>(frame);
                                frame_count_++;
								auto now = std::chrono::steady_clock::now();
								auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - last_).count();
								last_ = now;
                                // jinfo("###################groupId = %d frame_count = %d duration = %ld\n", getGroupId(), frame_count_, duration);
								out_frame->setGroupId(getGroupId());
                                deliverFrame(out_frame);
                                // auto buf1 = mpp_frame_get_buffer(frame);
                                // int size1 = mpp_frame_get_buf_size(frame);
                                // if (!ofs_.is_open()) ofs_.open("recv.yuv", std::ios::binary | std::ios::out);
                                // ofs_.write(mpp_buffer_get_ptr(buf1), size1);
                            }
                        }
                        if (frame) {
                            frame = nullptr;
                        } 
                        get_frm = 1;
                        fps_out_++;
                        frames_decoded_++;
                    }
                    if (frm_eos) {
                        jinfo("found last frame");
                        break;
                    }
                    if (get_frm)
                        continue;
                    break;
                } while (running_);
                if (pkt_done || !running_)
                    break;
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
            } while (running_);
        }
        jinfo("video decoder thread exit");
        return true;
    });
    notifycallbacks_thread_.loop()->setInterval(1000, [this](hv::TimerID id) {
        if (running_ && notifycallbacks_.find("VideoDecoderStatus") != notifycallbacks_.end() && notifycallbacks_["VideoDecoderStatus"].cb2) {
            nlohmann::json notify;
            notify["decoder"]["type"] = "VideoDecoderStatus";
            notify["decoder"]["codecType"] = "H264";
            notify["decoder"]["width"] = decode_width_;
            notify["decoder"]["height"] = decode_height_;
            notify["decoder"]["fps"] = fps_out_;
            fps_out_ = 0;
            notifycallbacks_["VideoDecoderStatus"].cb2(
                notifycallbacks_["VideoDecoderStatus"].id, "VideoDecoderStatus", notify.dump(),
                notifycallbacks_["VideoDecoderStatus"].param);
        }
        // SendFeedbackFrame();
    });
    notifycallbacks_thread_.start();
    return ret;
}

void RKVideoDecoder::Release() {
	if (mpp_packet_) {
		mpp_packet_deinit(&mpp_packet_);
		mpp_packet_ = nullptr;
	}
	if (buf_group_ && group_buffer_size_ != 0) {
		releaseMppBufferGroup(group_buffer_size_, buf_group_);
		buf_group_ = nullptr;
		group_buffer_size_ = 0;
	}
	if (mpp_ctx_) {
		if (mpp_api_) {
			mpp_api_->reset(mpp_ctx_);
			mpp_api_ = nullptr;
		}
		mpp_destroy(mpp_ctx_);
		mpp_ctx_ = nullptr;
	}
    jinfo("Release RKVideoDecoder");
}


void RKVideoDecoder::onFrame(const std::shared_ptr<Frame> &frame) {
	std::unique_lock<std::mutex> lock(mutex_);
    // jinfo("frame size = %d", frame->getFrameBufferSize());
	if (!running_) return;
    frames_recieved_++;
    if (Frame::isIDRFrame(frame)) gop_count_++;
    // if (gop_count_ > max_gop_count_) {
    //     while (running_ && !packet_queue_.empty() && !Frame::isKeyFrame(packet_queue_.front())) {
    //         packet_queue_.pop();
    //     }
    // }
    // if (!ofs_.is_open()) ofs_.open("recv.h264", std::ios::binary | std::ios::out);
    // ofs_.write(frame->getFrameBuffer(), frame->getFrameBufferSize());
	if (packet_queue_.size() > 300) {
		// TODO: 缓存过多处理
		jinfo("[RKVideoDecoder] Too much packet, queue size: %d, frame size: %d, gop count: %d", packet_queue_.size(), frame->getFrameBufferSize(), gop_count_);
        packet_queue_.pop();
	}
	packet_queue_.emplace(frame);
	cond_.notify_one();
}

void RKVideoDecoder::ResetStatistics() {
	statistics_start_time_ = std::chrono::steady_clock::now();
	frames_recieved_ = 0;
	frames_to_decode_ = 0;
    frames_decoded_ = 0;
}

void RKVideoDecoder::LogStatistics() {
	auto current_time = std::chrono::steady_clock::now();
	auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - statistics_start_time_).count();
	if (duration > 10000) {
		jinfo("RKVideoDecoder[%p]] Duartion: %dms. Packet received: %d. Packet to decode: %d. Frame decoded: %d, Packet queue size: %d, GOP count: %d.", this, duration, frames_recieved_, frames_to_decode_, frames_decoded_, packet_queue_.size(), gop_count_);
		ResetStatistics();
	}
}

void RKVideoDecoder::SendFeedbackFrame() {
    auto current_time = std::chrono::steady_clock::now();
    if (std::chrono::duration_cast<std::chrono::milliseconds>(current_time - last_send_feedback_time_).count() > 2000) {
        last_send_feedback_time_ = current_time;
        nlohmann::json j;
        j["fir"] = true;
        j["pli"] = false;
        std::shared_ptr<Frame> feedback_frame = Frame::CreateFrame(FRAME_FORMAT_VIDEO_FEEDBACK);
        if (feedback_frame) {
            feedback_frame->createFrameBuffer(j.dump().size());
            memcpy(feedback_frame->getFrameBuffer(), j.dump().c_str(), j.dump().size());
            deliverFeedbackFrame(feedback_frame);
        }
    }
}
}