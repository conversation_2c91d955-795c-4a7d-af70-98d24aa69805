#ifndef P_MediaManagerInterfaceImp_h
#define P_MediaManagerInterfaceImp_h

#include <string>
#include <set>
#include <unordered_map>
#include <mutex>
#include <memory>
#include <json.hpp>
#include "FramePipeline.h"
#include "VideoLayout.h"
#include "CodecInfo.h"
#include "DevInfo.h"

namespace panocom
{
    class MediaEndpoint
    {
    public:
        int id = -1;
        std::string ip;
        std::shared_ptr<uint16_t> rtpPort;
        std::shared_ptr<uint16_t> rtcpPort;
        std::set<FramePipeline::Ptr> pipelines;
        std::set<FramePipeline::Ptr> rtcps;
        FramePipeline::Ptr file;
        std::list<FramePipeline::WPtr> temps;
        CodecInst codec;
        nlohmann::json params;
        int dev = -1;
    };

    class MediaSession
    {
    public:
        enum
        {
            INACTIVE,
            SENDONLY,
            RECVONLY,
            SENDRECV,
        };
        int dir;
        std::string id;
        MediaEndpoint local;
        MediaEndpoint remote;
    };

    class MediaDevWorker
    {
    public:
        std::string id;
        std::string jsonParms;
    };

    class MediaManagerInterfaceImp
    {
    public:
        MediaManagerInterfaceImp(const std::string& jsonParams);
        ~MediaManagerInterfaceImp();

        void LoadConfig(const std::string& path);
        int CreateSession(const std::string& id, const std::string& jsonParams);
        int StartSession(const std::string& id);
        int PauseSession(const std::string& id);
        int StopSession(const std::string& id);
        int ReleaseSession(const std::string& id);

        int GetLocalSDP(const std::string& id, std::string& sdp, const std::string& jsonParams = "");
        int SetRemoteSDP(const std::string& id, const std::string& sdp, std::string& localSdp, const std::string& jsonParams = "", bool startSession = true);
        std::string GetDefaultIP();

        int StartUplinkAudioFile(const std::string& id, const std::string& path, const std::string& jsonParams = "");
        int StopUplinkAudioFile(const std::string& id);

        int StartUplinkVideoFile(const std::string& id, const std::string& path, const std::string& jsonParams = "");
        int StopUplinkVideoFile(const std::string& id);

        int StartDownlinkAudioFile(const std::string& id, const std::string& path, const std::string& jsonParams = "");
        int StopDownlinkAudioFile(const std::string& id);

        int StartDownlinkVideoFile(const std::string& id, const std::string& path, const std::string& jsonParams = "");
        int StopDownlinkVideoFile(const std::string& id);

        using SDPNotifyCallback = std::function<void(const std::string&, const std::string&, int)>;
        int GetLocalSDP(const std::string& id, const std::string& jsonParams, const SDPNotifyCallback& cb);
        int SetRemoteSDP(const std::string& id, const std::string& sdp, const std::string& jsonParams, const SDPNotifyCallback& cb);

        int AddToMediaPool(const std::string poolId, const std::string& id);
        int RemoveFromMediaPool(const std::string poolId, const std::string& id);

        /*
        {
            "object": "local",
            "type": "video",
            "action": "render",
            "dev": 0,
            "layout": "",
        }
        */
        int MediaCtrl(const std::string& id, const std::string& jsonParams);
        int SetMediaLayout(const std::list<Region>& layout);

        std::list<CodecInst> GetSupportAudioCodecs();
        std::list<CodecInst> GetSupportVideoCodecs();

        void SetAudioCodecPriority(const std::list<CodecInst>& codecs);
        void SetVideoCodecPriority(const std::list<CodecInst>& codecs);

        enum CodecType
        {
            AUDIO,
            VIDEO,
        };

    private:
        std::shared_ptr<uint16_t> getPort();
        std::string getIP(const std::string& ifdev);
        CodecInst codecInstFromJson(const nlohmann::json& rtp, int ptime = 0);
        std::list<CodecInst> GetSupportCodecs(CodecType type);
        bool isSupportCodec(const CodecInst& codec, CodecType type);
        int getLocalSDP(const std::string& id, std::string& sdp, const std::string& jsonParams, const std::list<CodecInst>& audioCodecs, const std::list<CodecInst>& videoCodecs);
        std::list<CodecInst> findMatchCodec(const std::list<CodecInst>& codecs, CodecType type);
        void makeLocalSession(const std::string& id, nlohmann::json& media, const std::string& ip, const std::string& dir, const std::list<CodecInst>& codecs, CodecType type);
        void makeRemoteSession(const std::string& id, const CodecInst& codec, const std::string& ip, int port, const std::string& dir, CodecType type);
    private:
        using MediaSessions = std::vector<std::unordered_map<std::string, MediaSession>>;
        MediaSessions sessions_;
        
        using JsonTemplate = std::vector<nlohmann::json>;
        JsonTemplate templates_;

        nlohmann::json config_;

        using MediaCodecs = std::vector<std::list<CodecInst>>;
        MediaCodecs codecs_;

        std::unordered_map<std::string, MediaDevWorker> devWorkers_;
        std::mutex mutex_;
        std::set<uint16_t> ports_;
    };
}

#endif