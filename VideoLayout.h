
#ifndef P_VideoLayout_h
#define P_VideoLayout_h

#include <list>
#include <stdint.h>

#include "VideoHelper.h"
namespace panocom {

/**
 * the configuration of VideoLayout element definition
 *    An example of a video layout with 5 regions is:

      +-------+---+
      |       | 2 |
      |   1   +---+
      |       | 3 |
      +---+---+---+
      |   5   | 4 |
      +---+---+---+

      {
        "videoLayout":
        [{"id": 0, "rect": {"x": 0, "y": 0, "w": 100, "h": "200"}}, {"id": 1, "rect": {"x": 100, "y": 0, "w": 100, "h": "200"}}]
      }

 */

struct Rectangle {
  int x;
  int y;
  int w;
  int h;
};

struct Region {
    int id;
    struct Rectangle rect;
};

struct InputRegion {
    int input;
    std::string streamId;
    int pos;
    int type;
    int state;
    std::string avatar;
    Region region;
    bool speaking;
    int borderPixel;
    panocom::YUVColor bgColor;
};

typedef std::list<InputRegion> LayoutSolution;

struct LayoutRect {
  float x, y, w, h; // 输出位置和大小
  float srcX, srcY, srcW, srcH; // 输入源的裁剪区域
  bool isCrop = false;// 是否需要裁剪
  LayoutRect() {}
  LayoutRect(float x, float y, float w, float h) :x(x), y(y), w(w), h(h), srcX(0.0f), srcY(0.0f), srcW(1.0f), srcH(1.0f) {}
  LayoutRect(float x, float y, float w, float h, float sx, float sy, float sw, float sh)
      :x(x), y(y), w(w), h(h), srcX(sx), srcY(sy), srcW(sw), srcH(sh) {}
  LayoutRect(float x, float y, float w, float h, bool isCrop) 
    :x(x), y(y), w(w), h(h), srcX(0.0f), srcY(0.0f), srcW(1.0f), srcH(1.0f), isCrop(isCrop){}
  LayoutRect(float x, float y, float w, float h, float sx, float sy, float sw, float sh, bool isCrop) : 
    x(x), y(y), w(w), h(h), srcX(sx), srcY(sy), srcW(sw), srcH(sh), isCrop(isCrop){}
};
}
#endif
