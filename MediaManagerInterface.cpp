#include "MediaManagerInterface.h"
#include "MediaManagerInterfaceImp.h"

using namespace panocom;

#define CHECK_IMP() if (!imp_) { printf("MediaManager is not initialized\n"); return; }
#define CHECK_IMP_RET(ret) if (!imp_) { printf("MediaManager is not initialized\n"); return ret; }

MediaManagerInterface::MediaManagerInterface(const std::string& jsonParams)
{
    imp_ = std::make_shared<MediaManagerInterfaceImp>(jsonParams);
}

int MediaManagerInterface::CreateSession(const std::string& id, const std::string& jsonParams)
{
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->CreateSession(id, jsonParams);
}

int MediaManagerInterface::ReleaseSession(const std::string& id)
{
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->ReleaseSession(id);
}

int MediaManagerInterface::GetLocalSDP(const std::string& id, std::string& sdp, const std::string& jsonParams)
{
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->GetLocalSDP(id, sdp, jsonParams);
}

int MediaManagerInterface::SetRemoteSDP(const std::string& id, const std::string& sdp, std::string& localSdp, const std::string& jsonParams)
{
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->SetRemoteSDP(id, sdp, localSdp, jsonParams);
}

std::string MediaManagerInterface::GetDefaultIP()
{
    CHECK_IMP_RET("");
    return imp_->GetDefaultIP();
}

int MediaManagerInterface::StartUplinkAudioFile(const std::string &id, const std::string &path, const std::string &jsonParams)
{
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->StartUplinkAudioFile(id, path, jsonParams);
}

int MediaManagerInterface::StopUplinkAudioFile(const std::string &id)
{
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->StopUplinkAudioFile(id);
}

int MediaManagerInterface::StartUplinkVideoFile(const std::string &id, const std::string &path, const std::string &jsonParams)
{
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->StartUplinkVideoFile(id, path, jsonParams);
}

int MediaManagerInterface::StopUplinkVideoFile(const std::string &id)
{
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->StopUplinkVideoFile(id);
}

int MediaManagerInterface::StartDownlinkAudioFile(const std::string &id, const std::string &path, const std::string &jsonParams)
{
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->StartDownlinkAudioFile(id, path, jsonParams);
}

int MediaManagerInterface::StopDownlinkAudioFile(const std::string &id)
{
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->StopDownlinkAudioFile(id);
}

int MediaManagerInterface::StartDownlinkVideoFile(const std::string &id, const std::string &path, const std::string &jsonParams)
{
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->StartDownlinkVideoFile(id, path, jsonParams);
}

int MediaManagerInterface::StopDownlinkVideoFile(const std::string &id)
{
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->StopDownlinkVideoFile(id);
}

int MediaManagerInterface::GetLocalSDP(const std::string& id, const std::string& jsonParams, const SDPNotifyCallback& cb)
{
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->GetLocalSDP(id, jsonParams, cb);
}

int MediaManagerInterface::SetRemoteSDP(const std::string& id, const std::string& sdp, const std::string& jsonParams, const SDPNotifyCallback& cb)
{
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->SetRemoteSDP(id, sdp, jsonParams, cb);
}

int MediaManagerInterface::SetRemoteMediaTimeoutListener(const std::string& id, int timeoutMS, const MediaTimeoutCallback& cb)
{
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->SetRemoteMediaTimeoutListener(id, timeoutMS, cb);
}

int MediaManagerInterface::SetRemoteMediaTimeoutListener2(const std::string &id, int timeoutMS, const MediaTimeoutCallback2 &cb) {
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->SetRemoteMediaTimeoutListener2(id, timeoutMS, cb);
}

int MediaManagerInterface::AddToMediaPool(const std::string poolId, const std::string& id, const WorkingMediaCallback& cb)
{
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->AddToMediaPool(poolId, id, cb);
}

int MediaManagerInterface::AddToMediaPool2(const std::string poolId, const std::string& id, const WorkingMediaCallback2& cb)
{
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->AddToMediaPool2(poolId, id, cb);
}

int MediaManagerInterface::RemoveFromMediaPool(const std::string poolId, const std::string& id)
{
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->RemoveFromMediaPool(poolId, id);
}

int MediaManagerInterface::ClearMediaPool(const std::string poolId)
{
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->ClearMediaPool(poolId);
}

int MediaManagerInterface::SetActiveMedia(const std::string poolId, const std::string& id)
{
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->SetActiveMedia(poolId, id);
}

int MediaManagerInterface::GetActiveMediaSession(const std::string& poolId, std::string &id) {
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->GetActiveMediaSession(poolId, id);
}

int MediaManagerInterface::MediaCtrl(const std::string& id, const std::string& jsonParams)
{
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->MediaCtrl(id, jsonParams);
}

int MediaManagerInterface::SetMediaLayout(const std::string& id, const std::list<Region>& layout, const std::string& jsonParams)
{
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->SetMediaLayout(id, layout, jsonParams);
}

std::list<CodecInst> MediaManagerInterface::GetSupportAudioCodecs()
{
    CHECK_IMP_RET({});
    return imp_->GetSupportAudioCodecs();
}

std::list<CodecInst> MediaManagerInterface::GetSupportVideoCodecs()
{
    CHECK_IMP_RET({});
    return imp_->GetSupportVideoCodecs();
}

void MediaManagerInterface::SetAudioCodecPriority(const std::list<CodecInst>& codecs, const std::string& id)
{
    CHECK_IMP();
    return imp_->SetAudioCodecPriority(codecs, id);
}

void MediaManagerInterface::SetVideoCodecPriority(const std::list<CodecInst>& codecs, const std::string& id)
{
    CHECK_IMP();
    return imp_->SetVideoCodecPriority(codecs, id);
}

int MediaManagerInterface::StartPSTN(const std::string& id)
{
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->StartPSTN(id);
}

int MediaManagerInterface::StopPSTN(const std::string& id)
{
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->StopPSTN(id);
}
#ifdef USE_CUSTOM_RENDER
int MediaManagerInterface::AddVideoRender(const std::string& id, const FramePipeline::Ptr& ptr)
{
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->AddVideoRender(id, ptr);
}

int MediaManagerInterface::RemoveVideoRender(const std::string& id)
{
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->RemoveVideoRender(id);
}
#endif

void MediaManagerInterface::SetRTPStatisticsListener(const std::string& id, const std::string& jsonParam, const RTPStatisticsNotifyCallback& cb, bool use_async)
{
    CHECK_IMP();
    return imp_->SetRTPStatisticsListener(id, jsonParam, cb, true);
}

bool MediaManagerInterface::GetConnectorStatus(const std::string& jsonParam) {
    CHECK_IMP_RET(false);
    return imp_->GetConnectorStatus(jsonParam);
}

bool MediaManagerInterface::GetCameraStatus(const std::string& jsonParam) {
    CHECK_IMP_RET(false);
    return imp_->GetCameraStatus(jsonParam);
}

int MediaManagerInterface::StartPlayFile(const std::string& jsonParams) {
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->StartPlayFile(jsonParams);
}
int MediaManagerInterface::StopPlayFile(const std::string& jsonParams) {
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->StopPlayFile(jsonParams);
}

int MediaManagerInterface::UpdateAudioCapturer(const std::string &id, const std::string &jsonParams) {
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->UpdateAudioCapturer(id, jsonParams);
}

int MediaManagerInterface::UpdateAudioRender(const std::string &id, const std::string &jsonParams) {
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->UpdateAudioRender(id, jsonParams);
}
int MediaManagerInterface::UpdateAudioMixer(const std::string &id, const std::string &jsonParams) {
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    // return MM_SUCCESS;
    return imp_->UpdateAudioMixer(id, jsonParams);
}
int MediaManagerInterface::UpdateAudioDispatcher(const std::string &id, const std::string &jsonParams) {
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return MM_SUCCESS;
}
int MediaManagerInterface::LoopbackTest(const std::string &id, const std::string &jsonParams) {
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->LoopbackTest(id, jsonParams);
}
int MediaManagerInterface::StopLoopbackTest(const std::string &id) {
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->StopLoopbackTest(id);
}

int MediaManagerInterface::OpenPip(const std::string &id, const std::string& jsonParm) {
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->OpenPip(id, jsonParm);
}
int MediaManagerInterface::ClosePip(const std::string &id) {
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->ClosePip(id);
}

int MediaManagerInterface::OpenCaption(const std::string &id, const std::string& jsonParm) {
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->OpenCaption(id, jsonParm);
}
int MediaManagerInterface::CloseCaption(const std::string &id) {
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->CloseCaption(id);
}    

int MediaManagerInterface::CreateAudioCapturer(const int &id, const std::string &jsonParam) {
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->CreateAudioCapturer(id, jsonParam);
}

int MediaManagerInterface::DestroyAudioCapturer(const int &id) {
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->DestroyAudioCapturer(id);
}

int MediaManagerInterface::CreateAudioRenderer(const int &id, const std::string &jsonParam) {
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->CreateAudioRenderer(id, jsonParam);
}

int MediaManagerInterface::DestroyAudioRenderer(const int &id) {
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->DestroyAudioRenderer(id);
}

int MediaManagerInterface::GetAudioCapturerStatus(const int id, std::string &jsonParam) {
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->GetAudioCapturerStatus(id, jsonParam);
}

int MediaManagerInterface::SetAudioCapturerNotifyCallback(const int &id, const std::string& jsonParam, const AudioCapturerNotifyCallback& callback) {
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->SetAudioCapturerNotifyCallback(id, jsonParam, callback);
}

int MediaManagerInterface::SetAudioRendererNotifyCallback(const int &id, const std::string& jsonParam, const AudioRendererNotifyCallback& callback) {
    CHECK_IMP_RET(MM_ERROR_IMPL_NOT_FOUND);
    return imp_->SetAudioRendererNotifyCallback(id, jsonParam, callback);
}
#ifdef USE_ALSA
std::vector<AudioHWDeviceInfo> MediaManagerInterface::ListAudioHWDeviceInfos() {
    std::vector<AudioHWDeviceInfo> ret;
    CHECK_IMP_RET(ret);
    auto devices = imp_->ListAudioHWDeviceInfos();
    for (auto &device: devices) {
        ret.push_back({device.index, device.card, device.device, device.id, device.name, device.type, device.hw_string});
    }
    return ret;
}

std::vector<AudioDeviceInfo> MediaManagerInterface::ListAudioDeviceInfos() {
    std::vector<AudioDeviceInfo> ret;
    CHECK_IMP_RET(ret);
    auto devices = imp_->ListAudioDeviceInfos();
    for (auto &device: devices) {
        ret.push_back({device.index, device.name, device.desc, device.can_capture, device.can_playback});
    }
    return ret;
}
#endif