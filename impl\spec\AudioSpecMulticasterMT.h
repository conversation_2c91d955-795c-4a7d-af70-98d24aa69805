// created by gyj 2024-4-24
#ifndef P_AudioSpecMulticasterMT_h
#define P_AudioSpecMulticasterMT_h

#include "FramePipeline.h"
#include <ptoolkit/bytebuffer.h>
#include <hv/EventLoopThread.h>
#include <malloc.h>
#include <stdio.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <arpa/inet.h>
#include <net/if.h>
#include <string.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <pthread.h>
#include <fcntl.h>
#include <stdint.h>
#include <unordered_map>
#include <thread>
#include <condition_variable>
#include <PNPcm/HDPcm.h>
#include "CspH3Chn.h"
#include "ring_buffer.h"
#include <resampler/resampler.h>
#include <resampler/push_resampler.h>
namespace panocom
{
    class AudioSpecMulticasterMT : public FramePipeline
    {
    public:
        AudioSpecMulticasterMT(const std::string& jsonParams);
        ~AudioSpecMulticasterMT() override;

        static std::shared_ptr<AudioSpecMulticasterMT> CreateInstance(const std::string& jsonParams);
        static void ReleaseInstance();

        void onFrame(const std::shared_ptr<Frame>& frame) override;
        void start() override;
        void stop() override;
    protected:
        void writeLoop();
        void readLoop();
        void downlinkLoop();
        void uplinkLoop();
        void aecLoop();
    public:
        int socketFd_ = -1;
        int pcmFd_ = -1;
        std::unique_ptr<std::thread> writeThread_;
        std::unique_ptr<std::thread> readThread_;
        std::unique_ptr<std::thread> downlinkThread_;
        std::unique_ptr<std::thread> uplinkThread_;
        std::unique_ptr<std::thread> aecThread_;
        std::mutex aecMutex_;
        std::mutex downlinkMutex_;
        std::mutex uplinkMutex_;
        int writeRunning_ = 0;
        int readRunning_ = 0;
        int downlinkRunning_ = 0;
        int uplinkRunning_ = 0;
        int aecRunning_ = 0;
        const static int kMaxCount = 3;

        RingBuffer* downlinkBuffer_ = nullptr;
        RingBuffer* uplinkBuffer_ = nullptr;
        RingBuffer* aecBuffer_ = nullptr;

        std::mutex downlinkBufferMutex_;
        std::condition_variable cvDownlink_;
        std::mutex uplinkBufferMutex_;
        std::condition_variable cvUplink_;
        std::mutex aecBufferMutex_;
        std::condition_variable cvAec_;

        float gain_radio_;
        std::string bindIP_;
        int bindPort_ = 0;
        std::string dstIP_;
        int dstPort_ = 0;
        std::string multiIP_;
        uint32_t index_ = 0;
        std::string devName_;
        int dev_;
        std::unordered_map<uint64_t, int> devChnMap_;
        int chnIndex_ = 1;
        int samplerate_ = 16000;
        int ptime_;
        int ptimeDataLen_;
        hv::EventLoopThread thread_;
        hv::EventLoopThread threadPacer_;
        ByteBuffer bbuf_;
        bool useMulticast_ = false;
        std::shared_ptr<void> aec_;
        FILE* pf_ = nullptr;
        static std::shared_ptr<AudioSpecMulticasterMT> inst_;
        int enableAEC_ = 1;
        long readTick_ = 0;
        long readBytes_ = 0;
        long writeTick_ = 0;
        long writeBytes_ = 0;

#ifdef WEBRTC_RESAMPLE_ANOTHER
        nswebrtc::PushResampler<int16_t> resampler_;
#else
        nswebrtc::Resampler resampler_;
#endif       
        bool initResampler_ = false;
        int source_samplerate_;
    };
}

#endif