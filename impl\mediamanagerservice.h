/**
 * generated by rpcgen, please inherit this class and implement the methods
 * Created on Apr 25 2024 13:46:05
 */

#pragma once
#include <memory>
#include <functional>
#include <RpcProxy/prpcproxy.h>
#include <proto/panocom.mediamanager.pb.h>

template <typename T>
class MediaManagerService {
public:
    MediaManagerService() {}
    MediaManagerService(std::shared_ptr<PRPC::RpcProxy> pProxyPtr) {
        Init(pProxyPtr);
    }
    void Init(std::shared_ptr<PRPC::RpcProxy> pProxyPtr) {
        if (!pProxyPtr) {
            rpcProxy_ = std::make_shared<PRPC::RpcProxy>();
        } else {
            rpcProxy_ = pProxyPtr;
        }

        rpcProxy_->RegisterHandler<panocom::mediamanager::CreateSessionRequest, panocom::mediamanager::CreateSessionReply>("CreateSession", std::bind(&MediaManagerService::ProcCreateSession, this, std::placeholders::_1, std::placeholders::_2));
        rpcProxy_->RegisterHandler<panocom::mediamanager::ReleaseSessionRequest, panocom::mediamanager::ReleaseSessionReply>("ReleaseSession", std::bind(&MediaManagerService::ProcReleaseSession, this, std::placeholders::_1, std::placeholders::_2));
        rpcProxy_->RegisterHandler<panocom::mediamanager::GetLocalSDPRequest, panocom::mediamanager::GetLocalSDPReply>("GetLocalSDP", std::bind(&MediaManagerService::ProcGetLocalSDP, this, std::placeholders::_1, std::placeholders::_2));
        rpcProxy_->RegisterHandler<panocom::mediamanager::SetRemoteSDPRequest, panocom::mediamanager::SetRemoteSDPReply>("SetRemoteSDP", std::bind(&MediaManagerService::ProcSetRemoteSDP, this, std::placeholders::_1, std::placeholders::_2));
    }

    PRPC::Status ProcCreateSession(const panocom::mediamanager::CreateSessionRequest& rCreateSessionRequest, panocom::mediamanager::CreateSessionReply& rCreateSessionReply){
        T& derived = static_cast<T&>(*this);
        return derived.CreateSessionImpl(rCreateSessionRequest, rCreateSessionReply);
    }

    PRPC::Status ProcReleaseSession(const panocom::mediamanager::ReleaseSessionRequest& rReleaseSessionRequest, panocom::mediamanager::ReleaseSessionReply& rReleaseSessionReply){
        T& derived = static_cast<T&>(*this);
        return derived.ReleaseSessionImpl(rReleaseSessionRequest, rReleaseSessionReply);
    }

    PRPC::Status ProcGetLocalSDP(const panocom::mediamanager::GetLocalSDPRequest& rGetLocalSDPRequest, panocom::mediamanager::GetLocalSDPReply& rGetLocalSDPReply){
        T& derived = static_cast<T&>(*this);
        return derived.GetLocalSDPImpl(rGetLocalSDPRequest, rGetLocalSDPReply);
    }

    PRPC::Status ProcSetRemoteSDP(const panocom::mediamanager::SetRemoteSDPRequest& rSetRemoteSDPRequest, panocom::mediamanager::SetRemoteSDPReply& rSetRemoteSDPReply){
        T& derived = static_cast<T&>(*this);
        return derived.SetRemoteSDPImpl(rSetRemoteSDPRequest, rSetRemoteSDPReply);
    }

public:
    std::shared_ptr<PRPC::RpcProxy> rpcProxy_ = {nullptr};
};



/****************************** service impl example ******************************
    
/*
class MediaManagerServiceImpl : public MediaManagerService<MediaManagerServiceImpl> {
public:
    MediaManagerServiceImpl(std::shared_ptr<PRPC::RpcProxy> pProxyPtr) : MediaManagerService<MediaManagerServiceImpl>(pProxyPtr) {}


    PRPC::Status CreateSessionImpl(const panocom::mediamanager::CreateSessionRequest& rCreateSessionRequest, panocom::mediamanager::CreateSessionReply& rCreateSessionReply);
    PRPC::Status ReleaseSessionImpl(const panocom::mediamanager::ReleaseSessionRequest& rReleaseSessionRequest, panocom::mediamanager::ReleaseSessionReply& rReleaseSessionReply);
    PRPC::Status GetLocalSDPImpl(const panocom::mediamanager::GetLocalSDPRequest& rGetLocalSDPRequest, panocom::mediamanager::GetLocalSDPReply& rGetLocalSDPReply);
    PRPC::Status SetRemoteSDPImpl(const panocom::mediamanager::SetRemoteSDPRequest& rSetRemoteSDPRequest, panocom::mediamanager::SetRemoteSDPReply& rSetRemoteSDPReply);
};

PRPC::Status MediaManagerServiceImpl::CreateSessionImpl(const panocom::mediamanager::CreateSessionRequest& rCreateSessionRequest, panocom::mediamanager::CreateSessionReply& rCreateSessionReply){
    // todo fix me!!!
    return PRPC::StatusCode::OK;
}

PRPC::Status MediaManagerServiceImpl::ReleaseSessionImpl(const panocom::mediamanager::ReleaseSessionRequest& rReleaseSessionRequest, panocom::mediamanager::ReleaseSessionReply& rReleaseSessionReply){
    // todo fix me!!!
    return PRPC::StatusCode::OK;
}

PRPC::Status MediaManagerServiceImpl::GetLocalSDPImpl(const panocom::mediamanager::GetLocalSDPRequest& rGetLocalSDPRequest, panocom::mediamanager::GetLocalSDPReply& rGetLocalSDPReply){
    // todo fix me!!!
    return PRPC::StatusCode::OK;
}

PRPC::Status MediaManagerServiceImpl::SetRemoteSDPImpl(const panocom::mediamanager::SetRemoteSDPRequest& rSetRemoteSDPRequest, panocom::mediamanager::SetRemoteSDPReply& rSetRemoteSDPReply){
    // todo fix me!!!
    return PRPC::StatusCode::OK;
}




/****************************** client impl example ******************************
    可使用成员函数或者lambda函数，如果处理函数代码不多建议使用lambda函数
    调用函数时尽量使用方法常量，避免拼写错误
/*
class clientExample {
    inline static const std::string kRpcCreateSession = "CreateSession";
    inline static const std::string kRpcReleaseSession = "ReleaseSession";
    inline static const std::string kRpcGetLocalSDP = "GetLocalSDP";
    inline static const std::string kRpcSetRemoteSDP = "SetRemoteSDP";
public:
    clientExample(std::shared_ptr<PRPC::RpcProxy> pProxyPtr) : pProxyPtr_(pProxyPtr) {
        // call rpc [CreateSession] 
        pProxyPtr_->RpcCallWithResponse<panocom::mediamanager::CreateSessionReply>(kRpcCreateSession, panocom::mediamanager::CreateSessionRequest(), 
                                            std::bind(&clientExample::ProcCreateSessionResp, this, std::placeholders::_1),
                                            std::bind(&clientExample::ProcCallError, this, std::placeholders::_1, std::placeholders::_2, "call CreateSession fail!!!"));
        pProxyPtr_->RpcCallWithResponse<panocom::mediamanager::CreateSessionReply>(kRpcCreateSession,
            panocom::mediamanager::CreateSessionRequest(), 
            [this]( const panocom::mediamanager::CreateSessionReply &rCreateSessionReply){
                // todo fix me!!!  proc response data 
            },
            [this](PRPC::StatusCode code, const std::string& msgError){
                std::cout << "call error code: " << static_cast<uint32_t>(code) << " msg: " << msgError << std::endl;
            });


        // call rpc [ReleaseSession] 
        pProxyPtr_->RpcCallWithResponse<panocom::mediamanager::ReleaseSessionReply>(kRpcReleaseSession, panocom::mediamanager::ReleaseSessionRequest(), 
                                            std::bind(&clientExample::ProcReleaseSessionResp, this, std::placeholders::_1),
                                            std::bind(&clientExample::ProcCallError, this, std::placeholders::_1, std::placeholders::_2, "call ReleaseSession fail!!!"));
        pProxyPtr_->RpcCallWithResponse<panocom::mediamanager::ReleaseSessionReply>(kRpcReleaseSession,
            panocom::mediamanager::ReleaseSessionRequest(), 
            [this]( const panocom::mediamanager::ReleaseSessionReply &rReleaseSessionReply){
                // todo fix me!!!  proc response data 
            },
            [this](PRPC::StatusCode code, const std::string& msgError){
                std::cout << "call error code: " << static_cast<uint32_t>(code) << " msg: " << msgError << std::endl;
            });


        // call rpc [GetLocalSDP] 
        pProxyPtr_->RpcCallWithResponse<panocom::mediamanager::GetLocalSDPReply>(kRpcGetLocalSDP, panocom::mediamanager::GetLocalSDPRequest(), 
                                            std::bind(&clientExample::ProcGetLocalSDPResp, this, std::placeholders::_1),
                                            std::bind(&clientExample::ProcCallError, this, std::placeholders::_1, std::placeholders::_2, "call GetLocalSDP fail!!!"));
        pProxyPtr_->RpcCallWithResponse<panocom::mediamanager::GetLocalSDPReply>(kRpcGetLocalSDP,
            panocom::mediamanager::GetLocalSDPRequest(), 
            [this]( const panocom::mediamanager::GetLocalSDPReply &rGetLocalSDPReply){
                // todo fix me!!!  proc response data 
            },
            [this](PRPC::StatusCode code, const std::string& msgError){
                std::cout << "call error code: " << static_cast<uint32_t>(code) << " msg: " << msgError << std::endl;
            });


        // call rpc [SetRemoteSDP] 
        pProxyPtr_->RpcCallWithResponse<panocom::mediamanager::SetRemoteSDPReply>(kRpcSetRemoteSDP, panocom::mediamanager::SetRemoteSDPRequest(), 
                                            std::bind(&clientExample::ProcSetRemoteSDPResp, this, std::placeholders::_1),
                                            std::bind(&clientExample::ProcCallError, this, std::placeholders::_1, std::placeholders::_2, "call SetRemoteSDP fail!!!"));
        pProxyPtr_->RpcCallWithResponse<panocom::mediamanager::SetRemoteSDPReply>(kRpcSetRemoteSDP,
            panocom::mediamanager::SetRemoteSDPRequest(), 
            [this]( const panocom::mediamanager::SetRemoteSDPReply &rSetRemoteSDPReply){
                // todo fix me!!!  proc response data 
            },
            [this](PRPC::StatusCode code, const std::string& msgError){
                std::cout << "call error code: " << static_cast<uint32_t>(code) << " msg: " << msgError << std::endl;
            });


    }

    void ProcCallError(PRPC::StatusCode code, const std::string& msgError, const std::string& cusTips ){
        std::cout << "call error code: " << static_cast<uint32_t>(code) << " msg: " << msgError << std::endl;
    }
}

void ProcCreateSessionResp(const panocom::mediamanager::CreateSessionReply &rCreateSessionReply){
    // todo fix me!!! proc response data 
}

void ProcReleaseSessionResp(const panocom::mediamanager::ReleaseSessionReply &rReleaseSessionReply){
    // todo fix me!!! proc response data 
}

void ProcGetLocalSDPResp(const panocom::mediamanager::GetLocalSDPReply &rGetLocalSDPReply){
    // todo fix me!!! proc response data 
}

void ProcSetRemoteSDPResp(const panocom::mediamanager::SetRemoteSDPReply &rSetRemoteSDPReply){
    // todo fix me!!! proc response data 
}

*/
