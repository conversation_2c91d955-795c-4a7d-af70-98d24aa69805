#include "MixedAudioSpec.h"
#include "Frame.h"
#include "Utils.h"
#include <json.hpp>
#include <ljcore/jlog.h>
#include "audiomixer/CAudioMix.h"
using namespace panocom;

extern int mmi_record_audio;
extern int mmi_print_state;

std::string MixedAudioSpec::devName_;
int MixedAudioSpec::pcmFd_ = -1;
int MixedAudioSpec::maxChn_ = 4;
std::vector<int> MixedAudioSpec::chnState_;
std::mutex MixedAudioSpec::mutex_;

void MixedAudioSpec::openDev(int dev)
{
    std::unique_lock<std::mutex> locker(mutex_);
    if (pcmFd_ < 0)
    {
        jinfo("MixedAudioSpec openDev %s dev[%d]", devName_.c_str(), dev);
        pcmFd_ = openPCM(devName_.c_str());
        if (pcmFd_ < 0)
        {
            jerror("%s open %s fail", __FUNCTION__, devName_.c_str());
            return;
        }
        else
        {
            for (size_t i = 0; i < maxChn_; i++)
            {
                Set_Type(i, IOCTL_CMD_PCM_TYPE_HIFI);
                chnState_.push_back(0);
            }
        }
    }
    if (dev >= 0 && dev < chnState_.size())
    {
        chnState_[dev]++;
        jinfo("MixedAudioSpec openDev start dev[%d] ref[%d]", dev, chnState_[dev]);
    }
}

void MixedAudioSpec::closeDev(int dev)
{
    std::unique_lock<std::mutex> locker(mutex_);
    if (dev >= 0 && dev < chnState_.size())
    {
        if (chnState_[dev])
            chnState_[dev]--;
        jinfo("MixedAudioSpec closeDev stop dev[%d] ref[%d]", dev, chnState_[dev]);
    }
    for (size_t i = 0; i < chnState_.size(); i++)
    {
        if (chnState_[i])
        {
            return;
        }
    }
    // chnState_.clear();
    // if (pcmFd_ >= 0)
    // {
    //     jinfo("MixedAudioSpec closeDev closePCM");
    //     closePCM();
    //     pcmFd_ = -1;
    // }
}

MixedAudioSpec::MixedAudioSpec(const std::string &jsonParams)
{
    FN_BEGIN;
    jinfo("MixedAudioSpec %s", jsonParams.c_str());
    name_ = "MixedAudioSpec";
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    {
        std::unique_lock<std::mutex> locker(mutex_);
        if (devName_ == "")
        {
            devName_ = "/dev/pcm_dev0";
            if (j.contains("devName"))
            {
                devName_ = j["devName"];
            }
            maxChn_ = 4;
            if (j.contains("maxChn"))
            {
                maxChn_ = j["maxChn"];
            }
        }
    }
    
    dev_ = 0;
    if (j.contains("dev"))
    {
        dev_ = j["dev"];
    }
    if (j.contains("ptime"))
    {
        ptime_ = j["ptime"];
    }
    if (j.contains("samplerate"))
    {
        samplerate_ = j["samplerate"];
    }
    samples_per_channel_ = static_cast<size_t>(samplerate_ * ptime_ / 1000);
    max_buffer_size_ = samples_per_channel_ * number_of_channels_ * 2 * 100;
    renderOnly_ = false;
    if (j.contains("RenderOnly"))
    {
        renderOnly_ = j["RenderOnly"];
    }
    jinfo("create MixedAudioSpec %s dev[%d]", devName_.c_str(), dev_);
    // start();
    FN_END;
}

MixedAudioSpec::~MixedAudioSpec()
{
    FN_BEGIN;
    stop();
    FN_END;
}

void MixedAudioSpec::start()
{
    if (renderOnly_) {
        render_ref++;
        jinfo("MixedAudioSpec start render_ref %d", render_ref);
    }
    if (readThread_.isRunning() || writeThread_.isRunning()) return;
    FN_BEGIN;
    openDev(dev_);
    if (!renderOnly_)
    {
        readThread_.loop()->setInterval(2, [this](hv::TimerID id){
            if (!isSetReadThreadAffinity)
            {
                setThreadAffinity(6);
                isSetReadThreadAffinity = true;
            }
            char buffer[320] = { 0 };
            int ret = ReadPcm(dev_, buffer, 320);
            if (ret > 0)
            {
                bbuf_.WriteBytes(buffer, ret);
                if (mmi_record_audio)
                {
                    if (!sendf)
                    {
                        std::string fileName = name_ + std::to_string(samplerate_) + "-send-" + std::to_string((long)this) + ".pcm";
                        sendf = fopen(fileName.c_str(), "w+b");
                    }
                    if (sendf)
                    {
                        fwrite(buffer, 1, ret, sendf);
                    }
                }
            } 
            int sampleBytes = samplerate_ / 1000 * ptime_ * 2;
            while (bbuf_.Length() >= sampleBytes)
            {
                std::shared_ptr<Frame> f = Frame::CreateFrame(FRAME_FORMAT_PCM_16000_1);
                f->createFrameBuffer(sampleBytes);
                memcpy(f->getFrameBuffer(), bbuf_.Data(), sampleBytes);
                bbuf_.Consume(sampleBytes);
                deliverFrame(f);
                printOutputStatus("MixedAudioSpec");
            }
        });

        readThread_.start();
    }
    if (!writeThread_.isRunning() && renderOnly_ && need_mixer_) {
        writeThread_.loop()->setInterval(ptime_, [this](hv::TimerID id) {
            if (!isSetWriteThreadAffinity)
            {
                setThreadAffinity(7);
                isSetWriteThreadAffinity = true;
            }
            int16_t* output_data = nullptr;
            int16_t* input_data = nullptr;
            int AmountOfMixedAudioSources = 0;
            {
                std::lock_guard<std::mutex> lock(sources_mutex_);
                input_data = new int16_t[(audio_sources_.size() + 1) * samples_per_channel_ * number_of_channels_];
                output_data = new int16_t[samples_per_channel_ * number_of_channels_];
                for (auto &kv: audio_sources_) {
                    if (kv.second.Length() >= samples_per_channel_ * number_of_channels_ * 2) {
                        
                        memcpy(
                            (char *)input_data + AmountOfMixedAudioSources * samples_per_channel_ * number_of_channels_ * 2, kv.second.Data(),
                            samples_per_channel_ * number_of_channels_ * 2);
                        kv.second.Consume(samples_per_channel_ * number_of_channels_ * 2);
                        ++AmountOfMixedAudioSources;
                        // printf("AmountOfMixedAudioSources: %p size = %d\n", kv.first, audio_sources_.size());
                    }
                }
            }
            if (input_data && AmountOfMixedAudioSources != 0) {
                // CAudioMix::MixSoundsBySimplyAdd(
                CAudioMix::AddAndNormalization(
                    input_data, AmountOfMixedAudioSources, samples_per_channel_ * number_of_channels_, output_data);
                WritePcm(dev_, (uint8_t*)output_data, samples_per_channel_ * number_of_channels_ * 2);
            }
            if (input_data) delete[] input_data;
            if (output_data) delete[] output_data;
        });        
    }
    if (!writeThread_.isRunning()) {
        writeThread_.start(true);
    }
    FN_END;
}

void MixedAudioSpec::stop()
{
    if (!readThread_.isRunning() && !writeThread_.isRunning()) return;
    if (renderOnly_) {
        render_ref--;
        jinfo("MixedAudioSpec stop render_ref %d", render_ref);
        if (render_ref <= 0) {
            std::lock_guard<std::mutex> lock(sources_mutex_);
            audio_sources_.clear();
        } else {
            return;
        }
    }
    readThread_.stop(true);
    // writeThread_.stop(true);
    readThread_.join();
    // writeThread_.join();
    closeDev(dev_);
    initResampler_ = false;
    isSetReadThreadAffinity = false;
    isSetWriteThreadAffinity = false;
}

void MixedAudioSpec::onFrame(const std::shared_ptr<Frame> &frame)
{
    if (need_mixer_) {
        fillMixSource(frame);
        return;
    }
    int samplerate = 0;
    int chn = 0;
    if (Frame::getSamplerate(frame->getFrameFormat(), samplerate, chn))
    {
        if (!writeThread_.isRunning()) return;
        writeThread_.loop()->runInLoop([this, frame, samplerate](){
            auto f = frame;
            if (!isSetWriteThreadAffinity)
            {
                setThreadAffinity(7);
                isSetWriteThreadAffinity = true;
            }
            if (samplerate_ != samplerate)
            {
                if (!initResampler_ || source_samplerate_ != samplerate)
                {
                    source_samplerate_ = samplerate;
                    initResampler_ = true;
#ifdef WEBRTC_RESAMPLE_ANOTHER
                    resampler_.InitializeIfNeeded(samplerate, samplerate_, 1);
#else
                    resampler_.ResetIfNeeded(samplerate, samplerate_, 1);
#endif
                }
                auto frame = Frame::CreateFrame(FRAME_FORMAT_PCM_16000_1);
                frame->createFrameBuffer(f->getFrameSize() * samplerate_ / samplerate);
                memset(frame->getFrameBuffer(), 0, frame->getFrameSize());
#ifdef WEBRTC_RESAMPLE_ANOTHER
                resampler_.Resample((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2);
#else
                size_t outlen = 0;
                resampler_.Push((int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(), frame->getFrameSize() / 2, outlen);
#endif
                f = frame;
            }
            if (pcmFd_ >= 0)
            {
                int index = 0;
                uint8_t* buf = f->getFrameBuffer();
                int bufSize = f->getFrameSize();
                while (bufSize > 0)
                {
                    int ret = WritePcm(dev_, buf, bufSize);
                    if (ret > 0)
                    {
                        if (mmi_record_audio)
                        {
                            if (!recvf)
                            {
                                std::string fileName = name_ + std::to_string(samplerate_) + "-recv-" + std::to_string((long)this) + ".pcm";
                                recvf = fopen(fileName.c_str(), "w+b");
                            }
                            if (recvf)
                            {
                                fwrite(buf, 1, ret, recvf);
                            }
                        }
                        buf += ret;
                        index += ret;
                        bufSize -= ret;
                    }
                    else
                    {
                        // jerror("WritePcm ret = %d", ret);
                        break;
                    }
                }
            }
            printInputStatus("MixedAudioSpec");
        });
    }
    else
    {
        jerror("MixedAudioSpec::onFrame error format[%d]", frame->getFrameFormat());
    }
}
// TODO
int MixedAudioSpec::updateParam(const std::string &jsonParams) {
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    int dev = -1;
    if (j.contains("dev")) {
        dev = j["dev"];
    }
    if (dev != -1 && dev != dev_) {
        closeDev(dev_);
        jinfo("MixedAudioSpec updateParams %s", jsonParams.c_str());
        jinfo("UpdateParam MixedAudioSpec %s dev[%d]", devName_.c_str(), dev_);
        openDev(dev_);
        dev_ = dev;
    }
    return 0;
}

void MixedAudioSpec::addAudioSource(const FramePipeline::Ptr &source) {
    FramePipeline::addAudioSource(source);
    addSource(source);
}

void MixedAudioSpec::removeAudioSource(const FramePipeline::Ptr &source) {
    removeSource(source);
    FramePipeline::removeAudioSource(source);
}

void MixedAudioSpec::addSource(const std::shared_ptr<FramePipeline> &source) {
    std::lock_guard<std::mutex> lock(sources_mutex_);
    if (audio_sources_.count(source.get()) != 0) {
        jwarn("Source %p already added to MixedAudioSpec mixer", source.get());
        return;
    }
    audio_sources_[source.get()] = ByteBuffer();
    jinfo("MixedAudioSpec addSource %p, size %d", source.get(), audio_sources_.size());
}
void MixedAudioSpec::removeSource(const std::shared_ptr<FramePipeline> &source) {
    std::lock_guard<std::mutex> lock(sources_mutex_);
    if (audio_sources_.count(source.get()) == 0) {
        jwarn("Source %p not present in MixedAudioSpec mixer", source.get());
        return;
    }
    audio_sources_.erase(source.get());
    jinfo("MixedAudioSpec removeSource %p size %d", source.get(), audio_sources_.size());
}

void MixedAudioSpec::fillMixSource(const std::shared_ptr<Frame> &frame) {
    auto source = frame->getSource();
    int sample_rate = 0;
    int channels = 0;
    auto f = frame;
    if (Frame::getSamplerate(frame->getFrameFormat(), sample_rate, channels)) {
        if (samplerate_ != sample_rate || number_of_channels_ != channels) {
            auto frame = Frame::CreateFrame(FRAME_FORMAT_PCM_16000_1);
            frame->createFrameBuffer(f->getFrameSize() * samplerate_ / sample_rate * number_of_channels_ / channels);
            memset(frame->getFrameBuffer(), 0, frame->getFrameSize());
            if (!initResampler_ || source_samplerate_ != sample_rate) {
                source_samplerate_ = sample_rate;
                initResampler_ = true;
#ifdef WEBRTC_RESAMPLE_ANOTHER
                resampler_.InitializeIfNeeded(sample_rate, samplerate_, 1);
#else
                resampler_.ResetIfNeeded(sample_rate, samplerate_, 1);
#endif
            }
#ifdef WEBRTC_RESAMPLE_ANOTHER
            resampler_.Resample(
                (int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(),
                frame->getFrameSize() / 2 / number_of_channels_ * channels);
#else
            size_t outlen = 0;
            resampler_.Push(
                (int16_t *)f->getFrameBuffer(), f->getFrameSize() / 2, (int16_t *)frame->getFrameBuffer(),
                frame->getFrameSize() / 2 / number_of_channels_ * channels, outlen);
#endif
            // TODO:
            if (number_of_channels_ == 2 && channels == 1) {
                uint16_t *data = (int16_t *)frame->getFrameBuffer();
                uint32_t frame_size = frame->getFrameBufferSize() / number_of_channels_ / 2;
                if (number_of_channels_ == 2 && channels == 1)
                    mono_to_stereo(data, frame_size);
            }
            f = frame;
        }
    }
    std::lock_guard<std::mutex> lock(sources_mutex_);
    // printf("source = %p size = %d\n", source, audio_sources_.size());
    if (audio_sources_.count(source) != 0) {
        audio_sources_[source].WriteBytes(f->getFrameBuffer(), f->getFrameBufferSize());
        if (audio_sources_[source].Length() > max_buffer_size_) {
            size_t size = audio_sources_[source].Length() - max_buffer_size_;
            audio_sources_[source].Consume(size);
        }
    } else {
        jwarn("Source not present in mixer");
    }
}

MixedAudioSpecManager::~MixedAudioSpecManager() {
    renders_.clear();
}

MixedAudioSpecManager &MixedAudioSpecManager::instance() {
    static MixedAudioSpecManager obj;
    return obj;
}

std::shared_ptr<MixedAudioSpec> MixedAudioSpecManager::CreateMixedAudioSpec(const std::string &jsonParams) {
    nlohmann::json j;
    if (jsonParams != "")
        j = nlohmann::json::parse(jsonParams);
    int dev = -1;
    if (j.contains("dev"))
        dev = j["dev"];
    if (dev < 0)
        return nullptr;
    if (renders_.count(dev) == 0)
        renders_[dev] = std::make_shared<MixedAudioSpec>(jsonParams);
    // if (renders_[dev]) renders_[dev]->start();
    return renders_[dev];
}
