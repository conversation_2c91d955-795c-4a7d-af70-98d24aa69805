/*
 * @Author: luo <EMAIL>
 * @Date: 2024-03-04 11:31:25
 * @LastEditors: luo <EMAIL>
 * @LastEditTime: 2025-04-27 16:17:58
 * @FilePath: /mediapipeline/rk/rk_video_encoder.h
 * @Description: RK编码器
 */
#ifndef RK_RK_VIDEO_ENCODER_H_
#define RK_RK_VIDEO_ENCODER_H_
#include <future>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <fstream>
#include <chrono>
#include "VideoFrameEncoder.h"
#include "rk_frame_buffer.h"

#include <rockchip/rk_type.h>
#include <rockchip/rk_mpi.h>
#include <rockchip/mpp_log.h>
// #include <osal/mpp_mem.h>
// #include <osal/mpp_env.h>
// #include <osal/mpp_time.h>
// #include <osal/mpp_common.h>
#include "hv/EventLoopThread.h"
namespace panocom {
enum ConfigChange {
	STRIDE_CHANGE,
	BITRATE_CHANGE
};
class RKVideoEncoder : public VideoFrameEncoder
{
private:
	/* data */
public:
	RKVideoEncoder(const std::string& jsonParams);
	virtual ~RKVideoEncoder();
	void onFrame(const std::shared_ptr<Frame>& frame) override;
	int updateParam(const std::string& jsonParams) override;
    void onFeedbackFrame(const std::shared_ptr<Frame> &f) override;
    void SetBitrate(const std::string& jsonParams);
private:
	MPP_RET InitEncode();
	void Release();
	void Encode(MppFrame &f);
	MPP_RET MppSetup();
	MPP_RET MppSetup2();

	void ResetStatistics();
	void LogStatistics();

	MPP_RET ChangeEncConfig(uint32_t change);
	bool CheckConfigChange() { return true; }
private:
	// codec
	int16_t fps_;
	int32_t gop_;
	int32_t width_;
	int32_t height_;
	int32_t hor_stride_;
	int32_t ver_stride_;
	int32_t bitrate_;	// Kbps
	int32_t max_bitrate_;
	int32_t min_bitrate_;
	std::string codec_name_;
	FrameFormat fmt_;

	MppCodingType codec_type_;
	MppFrameFormat frm_fmt_;
	MppEncRcMode rc_mode_;

	MppCtx mpp_ctx_;
	MppApi* mpp_api_;
	MppBuffer frm_buf_;
	
	std::list<MppPacket> m_packets;
	static MppBufferGroup m_buf_grp;
	static MppBufferGroup m_frm_buf_grp;
	MppBuffer m_md_info;
	std::atomic<bool> m_requestKeyFrame;
	
	bool running_;
	std::future<bool> encode_future_;
	std::mutex mutex_;
	std::condition_variable cond_;

	std::queue<std::shared_ptr<Frame>> frame_queue_;
	uint16_t max_queue_size_;

	std::shared_ptr<hv::EventLoopThread> loop_thread_;
	hv::EventLoopPtr loop_;
	std::ofstream ofs_;
	
	MppEncCfg m_cfg;

	std::chrono::steady_clock::time_point statistics_start_time_;
	uint32_t frames_recieved_;
	uint32_t frames_encoded_;

	int32_t input_fps_;
    /*
     * H.264 profile_idc parameter
     * 66  - Baseline profile
     * 77  - Main profile
     * 100 - High profile
     */
    int32_t profile_idc_;
    /*
     * H.264 level_idc parameter
     * 10 / 11 / 12 / 13    - qcif@15fps / cif@7.5fps / cif@15fps / cif@30fps
     * 20 / 21 / 22         - cif@30fps / half-D1@@25fps / D1@12.5fps
     * 30 / 31 / 32         - D1@25fps / 720p@30fps / 720p@60fps
     * 40 / 41 / 42         - 1080p@30fps / 1080p@30fps / 1080p@60fps
     * 50 / 51 / 52         - 4K@30fps
     */
    int32_t level_idc_;

	int32_t cabac_en_;

	std::shared_ptr<RKBufferManager> frameBufferManager_;

	int32_t change_bitrate_;	// Kbps
    int64_t lastChangeBitrateTick_ = 0;
    bool pending_change_bitrate_ = false;

	// 实际编码信息
	RK_S32 encode_width_;
	RK_S32 encode_height_;
	int32_t encode_fps_;

	hv::EventLoopThread notifycallbacks_thread_;
};
}
#endif