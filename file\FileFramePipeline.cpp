#include "FileFramePipeline.h"
#include "Frame.h"
#ifdef ENABLE_VIDEO
#include "file/Nv12FileFrameSource.h"
#include "file/H2645FileFrameSource.h"
#include "file/VideoFileFrameSource.h"
#ifdef RK_PLATFORM
#include "rk/RKFileFrameReader.h"
#endif
#endif
#include "file/PCMFileFrameSource.h"

#include <algorithm>
#include <json.hpp>

#ifdef USE_FFMPEG
extern "C" {
#include <libavutil/opt.h>
#include <libavcodec/avcodec.h>
#include <libswscale/swscale.h>
}
#endif

using namespace panocom;

std::vector<std::string> FileFramePipeline::fileSources_;
std::vector<std::string> FileFramePipeline::fileDestinations_;
bool FileFramePipeline::isRegistered = false;

void FileFramePipeline::RegistFileSource()
{
    if (!isRegistered)
    {
        fileSources_.push_back("Nv12FileFrameSource");
        fileSources_.push_back("H2645FileFrameSource");
#ifdef USE_FFMPEG
        fileSources_.push_back("VideoFileFrameSource");
#endif
        fileSources_.push_back("PCMFileFrameSource");
        isRegistered = true;
    }
}

std::vector<std::string> &FileFramePipeline::GetSupportFileSources()
{
    RegistFileSource();
    return fileSources_;
}

bool FileFramePipeline::IsSupportFileSource(const std::string &fileSourceName)
{
    RegistFileSource();
    return std::find(fileSources_.begin(), fileSources_.end(), fileSourceName) != fileSources_.end();
}

std::shared_ptr<FileFramePipeline> FileFramePipeline::CreateFileSource(const std::string &fileSourceName, const std::string &jsonParams)
{
    RegistFileSource();
    std::shared_ptr<FileFramePipeline> ret;
#ifdef ENABLE_VIDEO
#ifdef USE_CUDA
    if (fileSourceName == "Nv12FileFrameSource")
    {
        ret = std::shared_ptr<FileFramePipeline>(new Nv12FileFrameSource(jsonParams));
    }
#endif
    if (fileSourceName == "H2645FileFrameSource")
    {
        ret = std::shared_ptr<FileFramePipeline>(new H2645FileFrameSource(jsonParams));
    }
#ifdef USE_FFMPEG
    if (fileSourceName == "VideoFileFrameSource")
    {
        ret = std::shared_ptr<FileFramePipeline>(new VideoFileFrameSource(jsonParams));
    }
#endif
#ifdef RK_PLATFORM
    if (fileSourceName == "RKFileFrameReader")
    {
        ret = RKFileFrameReader::Create(jsonParams);
    }
#endif
#endif
    if (fileSourceName == "PCMFileFrameSource")
    {
        ret = std::shared_ptr<FileFramePipeline>(new PCMFileFrameSource(jsonParams));
    }
    return ret;
}

void FileFramePipeline::RegistFileDestinations()
{
    if (!isRegistered)
    {
        fileDestinations_.push_back("file");
        isRegistered = true;
    }
}

std::vector<std::string>& FileFramePipeline::GetSupportFileDestinations()
{
    return fileDestinations_;
}

bool FileFramePipeline::IsSupportFileDestination(const std::string &fileDestinationName)
{
    RegistFileDestinations();
    return std::find(fileDestinations_.begin(), fileDestinations_.end(), fileDestinationName) != fileDestinations_.end();
}

std::shared_ptr<FileFramePipeline> FileFramePipeline::CreateFileDestination(const std::string &fileDestinationName, const std::string& jsonParams)
{
    std::shared_ptr<FileFramePipeline> ret;
    if (fileDestinationName == "file")
    {
        ret = std::shared_ptr<FileFramePipeline>(new FileFramePipeline(jsonParams));
    }
    return ret;
}

FileFramePipeline::FileFramePipeline(const std::string& jsonParams)
{
    nlohmann::json j;
    if (jsonParams != "") j = nlohmann::json::parse(jsonParams);
    std::string path;
    if (j.contains("path"))
    {
        path = j["path"];
    }
    pf_ = fopen(path.c_str(), "a+b");
}

FileFramePipeline::~FileFramePipeline()
{
    if (pf_)
    {
        fclose(pf_);
    }
}

void FileFramePipeline::onFrame(const std::shared_ptr<Frame> &frame)
{
    //jinfo("FileFramePipeline::onFrame %d", frame->getFrameFormat());
    if (pf_)
    {
        switch (frame->getFrameFormat())
        {
#ifdef USE_FFMPEG
        case FRAME_FORMAT_FFAVFRAME:
        {
            FFAVFrame* f = (FFAVFrame*)frame.get();
            AVFrame* avframe = (AVFrame*)f->getAVFrame();
            if (avframe->format == AV_PIX_FMT_NV12)
            {
                fwrite(avframe->data[0], 1, avframe->linesize[0] * avframe->height, pf_);
                fwrite(avframe->data[1], 1, avframe->linesize[1] * avframe->height, pf_);
            }
            else if (avframe->format == AV_PIX_FMT_YUVJ420P || avframe->format == AV_PIX_FMT_YUV420P)
            {
                fwrite(avframe->data[0], 1, avframe->linesize[0] * avframe->height, pf_);
                fwrite(avframe->data[1], 1, avframe->linesize[1]/ 2 * avframe->height, pf_);
                fwrite(avframe->data[2], 1, avframe->linesize[2]/ 2 * avframe->height, pf_);

            }else if (avframe->format == AV_PIX_FMT_YUVJ422P)
            {
                fwrite(avframe->data[0], 1, avframe->linesize[0] * avframe->height, pf_);
                fwrite(avframe->data[1], 1, avframe->linesize[1]* avframe->height, pf_);
                fwrite(avframe->data[2], 1, avframe->linesize[2] * avframe->height, pf_);
            }
            break;
        }
#endif
        case FRAME_FORMAT_CU_FRAME:
        case FRAME_FORMAT_CU_DECODED_NV12:
        case FRAME_FORMAT_CU_ENCODE_NV12:
        {

            break;
        }
        default:
            //jinfo("FileFramePipeline::onFrame write %d", frame->getFrameSize());
            fwrite(frame->getFrameBuffer(), 1, frame->getFrameSize(), pf_);
            break;
        }
    }
}