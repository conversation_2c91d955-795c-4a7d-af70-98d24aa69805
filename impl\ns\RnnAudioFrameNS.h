// created by gyj 2024-3-28
#ifndef P_RnnAudioFrameNS_h
#define P_RnnAudioFrameNS_h

#include "AudioFrameProcesser.h"
#include <rnnoise.h>
#include <hv/EventLoopThread.h>
#include <resampler/resampler.h>
#include <resampler/push_resampler.h>

namespace panocom
{
    class RnnAudioFrameNS : public AudioFrameProcesser
    {
    public:
        RnnAudioFrameNS(const std::string& jsonParams);
        ~RnnAudioFrameNS() override;

        void onFrame(const std::shared_ptr<Frame>& frame) override;
        void start() override;
        void stop() override;
    private:
        std::shared_ptr<DenoiseState> ds_;
        hv::EventLoopThread thread_;
        FILE* pf_ = nullptr;
        bool first_ = true;
#ifdef WEBRTC_RESAMPLE_ANOTHER
        nswebrtc::PushResampler<int16_t> resampler_;
#else
        nswebrtc::Resampler resampler_;
#endif       
        bool initResampler_ = false;
        int source_samplerate_;
    };
}

#endif