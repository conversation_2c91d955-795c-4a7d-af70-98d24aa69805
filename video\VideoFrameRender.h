#ifndef P_VideoFrameRender_h
#define P_VideoFrameRender_h

#include "../FramePipeline.h"
#include "VideoLayout.h"
namespace panocom
{
    class VideoFrameRender : public FramePipeline
    {
    public:
        static void RegistRenders();
        static std::vector<std::string>& GetSupportRenders();
        static bool IsSupportRender(const std::string &renderName);
        static std::shared_ptr<VideoFrameRender> CreateVideoRender(const std::string &renderName, const std::string &jsonParams = "");
        static void ReleaseVideoRender(const std::string &renderName, int dev, int gpu_index);
        static void ReleaseVideoRender(const FramePipeline::Ptr& ptr);
        static bool isVideoFrameRender(const FramePipeline::Ptr& ptr);
        static bool GetConnectorStatus(int dev, int card = 0);

        virtual ~VideoFrameRender() = default;
        virtual bool setPlane(int32_t id, const std::string &jsonParams) { return true; }
        virtual bool unsetPlane(int32_t id) { return true; }
        virtual int updateParams(const std::string &jsonParams) { return 0; }
        virtual void addInput(int inputId, const LayoutRect& layout) {}
        virtual void removeInput(int inputId) {}
        virtual void loadPresetLayouts(int presetIndex, bool reverse) {}
        virtual void setSourceId(void *source, int32_t id) {}
        virtual void unsetSourceId(void *source) {}
        virtual bool getSourceId(void *source, int32_t &id) { return false;}
    protected:
        static bool isRegistered;
        static std::vector<std::string> renders_;
        struct RendererInfo
        {
            std::string name;
            int dev;
            int gpu_index;
            bool operator ==(const struct RendererInfo& d) const {
                return name == d.name && dev == d.dev && gpu_index == d.gpu_index;
            }
        };

        struct InfoHash
        {
            std::size_t operator() (const RendererInfo& info) const {
                std::size_t h1 = std::hash<std::string>{}(info.name);
                std::size_t h2 = std::hash<int>{}(info.dev);
                std::size_t h3 = std::hash<int>{}(info.gpu_index);
                return h1 ^ (h2 << 1) ^ (h3 << 2);
            }
        };
        struct RendererRef
        {
            std::shared_ptr<VideoFrameRender> renderer;
            int ref;
        };
        static std::unordered_map<RendererInfo, RendererRef, InfoHash> existed_renderers_;
        // static std::unordered_map<std::shared_ptr<VideoFrameRender>, int> renderer_refs_;
    };
}

#endif