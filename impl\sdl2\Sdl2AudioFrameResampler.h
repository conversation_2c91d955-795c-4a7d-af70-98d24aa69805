// created by gyj 2024-3-28
#ifndef P_Sdl2AudioFrameResampler_h
#define P_Sdl2AudioFrameResampler_h

#include "AudioFrameProcesser.h"
#include <SDL2/SDL.h>
#include <set>
#include <hv/EventLoopThread.h>

namespace panocom
{
    class Sdl2AudioFrameResampler : public AudioFrameProcesser
    {
    public:
        Sdl2AudioFrameResampler(const std::string& jsonParams);
        ~Sdl2AudioFrameResampler() override;

        void onFrame(const std::shared_ptr<Frame>& frame) override;
    private:
        hv::EventLoopThread thread_;
        std::shared_ptr<SDL_AudioStream> stream_;
    };
}

#endif