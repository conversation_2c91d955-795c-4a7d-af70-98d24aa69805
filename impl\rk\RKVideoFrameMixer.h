#ifndef VIDEOMIXER_RKVIDEOFRAMEMIXER_
#define VIDEOMIXER_RKVIDEOFRAMEMIXER_
#include <chrono>
#include <future>
#include <queue>
#include <vector>
#include <unordered_map>

#include "Frame.h"
#include "VideoFrameMixer.h"
#include "VideoLayout.h"
#include "rk/rk_frame_buffer.h"
#include <hv/EventLoopThread.h>
#include "rk/RKVideoLayout.h"
#include "RgaComposer.h"
namespace panocom {

class RKVideoFrameMixer : public VideoFrameMixer
{
private:
	/* data */
public:
	RKVideoFrameMixer(const std::string &jsonParams);
	~RKVideoFrameMixer();
	void onFrame(const std::shared_ptr<Frame> &frame) override;
	void start() override;
	void stop() override;
private:
	void generateOutputFrame(uint32_t width, uint32_t height);
	bool layout_regions();

	std::shared_ptr<Frame> getNextValidFrame(int inputId);

	void addInput(int inputId, const LayoutRect& layout);
	void removeInput(int inputId);
	void updateLayout(int inputId, const LayoutRect& layout);
	// 获取合成帧
	std::shared_ptr<Frame> getComposedFrame();
	// 合成一次
	void doCompose();
	// 渲染线程主循环
	void renderLoop();

private:        
	long outputGap_;
    long outputTick_;
	std::chrono::steady_clock::time_point output_time_;
	std::vector<Region> video_layout_;
	int x_;
	int y_;
	int width_;
	int height_;
	std::string format_str_;
	// std::shared_ptr<Frame> outputFrame_;

	std::future<bool> mixer_future_;
	std::mutex mutex_;
	std::condition_variable cv_;	

	std::queue<std::shared_ptr<RKVideoFrameBuffer>> frameQueue_;
	std::shared_ptr<RKBufferManager> frameBufferManager_;

	std::shared_ptr<hv::EventLoopThread> loop_thread_;
	hv::EventLoopPtr loop_;

	std::queue<std::shared_ptr<Frame>> frame_queue_;
	std::shared_ptr<WrapRKMppFrame> output_frame_;

	int output_width_;
	int output_height_;
	int format_;
	int fps_;

	struct InputChannel {
		std::deque<CachedFrame> cache;
		std::shared_ptr<Frame> last_frame;
		std::mutex mtx;
	};
	std::unordered_map<int, InputChannel> input_channels_;
	std::unordered_map<int, LayoutRect> layout_map_;
	std::mutex layout_mtx_;

	int max_cache_size_;
	int timeoutMs_;

	std::future<bool> render_future_;
	std::mutex render_mtx_;
	std::condition_variable render_cv_;
	bool running_;
	hv::EventLoopThread render_thread_;
};
}

#endif