#pragma once
#include <stdint.h>
#include <mutex>
#include <vector>
#include <list>

#if defined(WIN32)
#include <windows.h>
#else
#include <sys/shm.h>
#endif

using namespace std;
#define MAX_YUV (3840 * 2160 * 3)
#define MAX_FRAME_BUFFER 3
#define YUVSHM_KEY ("YUVShm")

#pragma pack(push, 1)
struct YUVShm
{
	volatile uint32_t is_valid;
	volatile uint32_t id;
	uint64_t tick;
	uint64_t pts;
	uint32_t width;
	uint32_t height;
	uint32_t linesize[4];
	uint32_t crc;
	uint32_t pixfmt;
	uint8_t res[32768 - sizeof(uint32_t) * 14];      //����ͷ32K����
	char data[MAX_YUV];
};
#pragma pack(pop)

class ShmVideoProc
{
public:
	ShmVideoProc();
	~ShmVideoProc();
	static ShmVideoProc* Inst();
	static void Free();
	void init(uint8_t maxChn, uint32_t shmStartId);

	YUVShm* GetWriteShm(uint8_t channel);
	YUVShm* GetCurrentShm(uint8_t channel);
	void ResetShmData(uint8_t channel);

private:
	uint8_t m_maxChn = 0;

	static ShmVideoProc* s_obj;

	std::vector<char*> m_shm;
	std::vector<std::list<YUVShm*>> m_shms;
	std::vector<unsigned int> m_id;
	std::vector<YUVShm*> m_currentYuv;

#if defined(WIN32)
	std::vector<HANDLE> m_handle;
#endif

};
